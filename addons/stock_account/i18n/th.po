# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# Wil Odo<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"\n"
"Affected valuation layers: %s"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr "อัปเดตต้นทุนผลิตภัณฑ์จาก %(previous)s เป็น %(new_cost)s"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_quant.py:0
msgid " [Accounted on %s]"
msgstr " [ลงบัญชีเมื่อวันที่ %s]"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid " lot/serial number cost updated from %(previous)s to %(new_cost)s."
msgstr "ต้นทุนหมายเลขล็อต/ซีเรียลอัพเดตจาก %(previous)s เป็น %(new_cost)s"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(record)s"
msgstr ""
"%(user)s เปลี่ยนต้นทุนจาก %(previous)s เป็น %(new_price)s - %(record)s"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - %(product)s\n"
"%(reason)s"
msgstr ""
"%(user)sเปลี่ยนแปลงมูลค่าสต็อกจาก %(previous)s เป็น %(new_value)s- %(product)s\n"
"%(reason)s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 fst-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""
")\n"
"                            <small class=\"mx-2 fst-italic\">ใช้มูลค่าเพิ่มเชิงลบเพื่อบันทึกมูลค่าสินค้าที่ลดลง</small>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "6.00"
msgstr "6.00"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr "<b>ตั้งค่าบัญชีขาเข้า/ขาออกแบบเฉพาะ </b>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
#: model_terms:ir.ui.view,arch_db:stock_account.view_production_lot_form_stock_account
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">การประเมินมูลค่า</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>สินค้า</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>จำนวน</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr "<span>SN/LN</span>"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "เทมเพลตแผนภูมิบัญชี"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "ย้ายบัญชี"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "คุณสมบัติสต็อคของบัญชี"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "วันที่ลงบัญชี"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "รายการบัญชี"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "ข้อมูลทางบัญชี"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "เพิ่มการประเมินมูลค่าด้วยตนเอง"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr "เพิ่มค่าใช้จ่ายเพิ่มเติม (ค่าขนส่ง ศุลกากร ...) ในมูลค่าของสินค้า"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr "เพิ่มมูลค่า"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr "เพิ่มมูลค่า"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_revalue_layers
msgid "Adjust Valuation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""
"ส่งผลกระทบต่อต้นทุนที่ดินในการดำเนินการต้อนรับ "
"และแยกออกเป็นสินค้าเพื่ออัปเดตราคาต้นทุน"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "บัญชีวิเคราะห์"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_ids
msgid "Analytic Account Line"
msgstr "รายการบัญชีวิเคราะห์"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_plan
msgid "Analytic Plans"
msgstr "แผนการวิเคราะห์"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "อัตโนมัติ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_stock_accounting_automatic
msgid "Automatic Stock Accounting"
msgstr "การบัญชีสต็อกอัตโนมัติ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__avg_cost
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__avg_cost
msgid "Average Cost"
msgstr "ต้นทุนเฉลี่ย"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "Average Cost (AVCO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "BC46282798"
msgstr "BC46282798"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "Bacon"
msgstr "เบคอน"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "ยกเลิก"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""
"ไม่พบบัญชีการป้อนข้อมูลสต็อคสำหรับสินค้า %s "
"คุณต้องกำหนดหนึ่งในประเภทสินค้าหรือในตำแหน่ง "
"ก่อนที่จะประมวลผลการดำเนินการนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""
"ไม่พบบัญชีสต็อกสินค้าสำหรับสินค้า%s "
"คุณต้องกำหนดหนึ่งในประเภทสินค้าหรือในตำแหน่ง "
"ก่อนที่จะประมวลผลการดำเนินการนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""
"การเปลี่ยนวิธีการคิดต้นทุนเป็นการเปลี่ยนแปลงที่สำคัญที่จะส่งผลต่อการประเมินมูลค่าสินค้าคงคลังของคุณ"
" คุณแน่ใจหรือไม่ว่าต้องดำเนินการเปลี่ยนแปลงดังกล่าว"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Choose a date to get the valuation at that date"
msgstr "เลือกวันที่เพื่อรับการประเมินมูลค่า ณ วันดังกล่าว"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__cogs_origin_id
msgid "Cogs Origin"
msgstr "ต้นกำเนิดของฟันเฟือง"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Company"
msgstr "บริษัท"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""
"เกิดข้อผิดพลาดในการกำหนดค่า "
"โปรดกำหนดค่าบัญชีส่วนต่างราคาของสินค้าหรือหมวดหมู่เพื่อประมวลผลการดำเนินการนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid "Correction of %s (modification of past move)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__standard_price
msgid "Cost"
msgstr "ต้นทุน"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__cost_method
msgid "Costing Method"
msgstr "วิธีการคิดต้นทุน"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Costing method change for product category %(category)s: from %(old_method)s"
" to %(new_method)s."
msgstr ""
"การเปลี่ยนแปลงวิธีการคิดต้นทุนสำหรับหมวดหมู่ผลิตภัณฑ์ %(category)s: จาก "
"%(old_method)s เป็น %(new_method)s"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "บัญชีคู่"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""
"รายการสมุดรายวันคู่สำหรับการย้ายสต็อกที่เข้ามาทั้งหมดจะถูกผ่านรายการในบัญชีนี้ เว้นแต่จะมีบัญชีการประเมินมูลค่า\n"
"               เฉพาะที่ตั้งค่าไว้ในตำแหน่งที่ตั้งต้นทาง นี่คือค่าเริ่มต้นสำหรับสินค้าทั้งหมดในหมวดหมู่นี้ นอกจากนี้ยังสามารถตั้งค่าได้โดยตรงในแต่ละสินค้า"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "รหัสประเทศ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr "ปริมาณปัจจุบัน"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "ค่าปัจจุบัน"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Date"
msgstr "วันที่"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr ""
"วันที่จะสร้างรายการบัญชีในกรณีการประเมินมูลค่าสินค้าคงคลังอัตโนมัติ "
"หากว่างเปล่า ระบบจะใช้วันที่ของสินค้าคงคลัง"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "หน่วยวัดเริ่มต้นที่ใช้สำหรับการปฏิบัติการสต๊อกทั้งหมด"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "คำอธิบาย"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "แสดงล็อตและหมายเลขซีเรียลในใบแจ้งหนี้"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "แสดงหมายเลขซีเรียลและหมายเลขล็อตบนใบแจ้งหนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Due to a change of product category (from %(old_category)s to "
"%(new_category)s), the costing method has changed for product %(product)s: "
"from %(old_method)s to %(new_method)s."
msgstr ""
"เนื่องจากการเปลี่ยนแปลงหมวดหมู่ผลิตภัณฑ์ (จาก %(old_category)s เป็น "
"%(new_category)s) วิธีการคิดต้นทุนจึงมีการเปลี่ยนแปลงสำหรับผลิตภัณฑ์ "
"%(product)s: จาก %(old_method)s เป็น %(new_method)s"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr "First In First Out (FIFO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "จัดกลุ่มโดย…"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Has Remaining Qty"
msgstr "มีปริมาณคงเหลือ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "ไอดี"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__lot_valuated
#: model:ir.model.fields,help:stock_account.field_product_template__lot_valuated
msgid "If checked, the valuation will be specific by Lot/Serial number."
msgstr ""
"หากทำเครื่องหมายไว้ การประเมินราคาจะระบุเฉพาะตามหมายเลขล็อต/หมายเลขซีเรียล"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Incoming"
msgstr "ขาเข้า"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "ตำแหน่งคลังสินค้า"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Inventory Valuation"
msgstr "การประเมินมูลค่าสินค้าคงคลัง"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_line_id
msgid "Invoice Line"
msgstr "รายการในใบแจ้งหนี้"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__price_diff_value
msgid "Invoice value correction with invoice currency"
msgstr "การแก้ไขมูลค่าใบแจ้งหนี้ด้วยสกุลเงินของใบแจ้งหนี้"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "สมุดบันทึก"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Journal Entry"
msgstr "รายการบันทึกประจำวัน"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "รายการบันทึก"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "ต้นทุนแฝง"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "ที่เชื่อมโยงกับ"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Lot %(lot)s has a negative quantity in stock.\n"
"Correct this quantity before enabling/disabling lot valuation."
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Lot value manually modified (from %(old)s to %(new)s)"
msgstr "มูลค่าล็อตถูกปรับเปลี่ยนด้วยตนเอง (จาก %(old)s เป็น %(new)s)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_lot
msgid "Lot/Serial"
msgstr "ล็อต/ซีเรียล"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__lot_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Lot/Serial Number"
msgstr "ล็อต/หมายเลขซีเรียล"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Lot/Serial number Revaluation"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move_line.py:0
msgid "Lot/Serial number is mandatory for product valuated by lot"
msgstr ""
"หมายเลขล็อต/ซีเรียลเป็นสิ่งจำเป็นสำหรับผลิตภัณฑ์ที่ประเมินมูลค่าตามล็อต"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the invoice"
msgstr "ล็อตและหมายเลขซีเรียลจะแสดงบนใบแจ้งหนี้"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "ด้วยตัวเอง"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "Manual Stock Valuation: %s."
msgstr "การประเมินมูลค่าสต็อกด้วยตนเอง: %s"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"ด้วยตนเอง: รายการบัญชีเพื่อประเมินมูลค่าสินค้าคงคลังจะไม่ผ่านรายการโดยอัตโนมัติ\n"
"อัตโนมัติ: รายการทางบัญชีจะถูกสร้างขึ้นโดยอัตโนมัติเพื่อประเมินมูลค่าสินค้าคงคลังเมื่อสินค้าเข้าหรือออกจากบริษัท"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "มูลค่าใหม่"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr "มูลค่าใหม่ตามปริมาณ"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "No Reason Given"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "ข้อมูลอื่นๆ"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Outgoing"
msgstr "ขาออก"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "สินค้า"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Product %(product)s has quantity in valued location %(location)s without any lot.\n"
"Please assign lots to all your quantities before enabling lot valuation."
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product Category"
msgstr "หมวดหมู่สินค้า"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "ย้ายสินค้า ( ไลน์เคลื่อนย้ายสต๊อก )"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Product Revaluation"
msgstr "การประเมินมูลค่าสินค้าใหม่"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
msgid "Product Variant"
msgstr "ตัวแปรสินค้า"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Product value manually modified (from %(original_price)s to %(new_price)s)"
msgstr ""
"แก้ไขมูลค่าผลิตภัณฑ์ด้วยตนเอง (จาก %(original_price)s เป็น %(new_price)s)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_company__account_production_wip_account_id
msgid "Production WIP Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_company__account_production_wip_overhead_account_id
msgid "Production WIP Overhead Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Quantity"
msgstr "ปริมาณ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__quantity_svl
msgid "Quantity Svl"
msgstr "ปริมาณ Svl"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "วิเคราะห์เชิงปริมาณ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "เหตุผล"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr "เหตุผลในการประเมินมูลค่าใหม่"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__warehouse_id
msgid "Receipt WH"
msgstr "ใบเสร็จรับเงิน WH"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__reference
msgid "Reference"
msgstr "การอ้างอิง"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__lot_id
msgid "Related lot/serial number"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr "สินค้าที่เกี่ยวข้อง"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "ปริมาณคงเหลือ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "มูลค่าคงเหลือ"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "รายการการส่งคืนสินค้า"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "Revaluation of %s"
msgstr "การประเมินมูลค่าใหม่ของ %s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr "ประเมินราคาใหม่"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Select an existing lot/serial number to be reevaluated"
msgstr "เลือกล็อต/หมายเลขซีเรียลที่มีอยู่เพื่อประเมินใหม่"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "ราคามาตรฐาน"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
#: model:ir.model.fields,help:stock_account.field_stock_quant__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"ราคามาตรฐาน: สินค้ามีมูลค่าตามต้นทุนมาตรฐานที่กำหนดไว้บนสินค้า\n"
"Average Cost (AVCO): สินค้ามีมูลค่าตามต้นทุนถัวเฉลี่ยถ่วงน้ำหนัก\n"
"First In First Out (FIFO): สินค้ามีมูลค่าโดยสมมติว่าสินค้าที่เข้าบริษัทก่อนจะออกก่อนเช่นกัน"

#. module: stock_account
#: model:res.groups,name:stock_account.group_stock_accounting_automatic
msgid "Stock Accounting Automatic"
msgstr "การบัญชีสต็อกอัตโนมัติ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "บัญชีสต็อกสินค้าขาเข้า"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "สมุดรายวันสต็อก"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "ย้ายสต็อก"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "บัญชีสต็อกสินค้าขาออก"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "ประวัติปริมาณสต็อค"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "รายงานการเติมสต็อก"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "แจ้งขอนับสินค้าคงคลัง"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.js:0
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_report_action
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Stock Valuation"
msgstr "การประเมินมูลค่าสต็อก"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "บัญชีประเมินมูลค่าสต็อก"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "บัญชีประเมินมูลค่าสต็อก (ขาเข้า)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "บัญชีประเมินมูลค่าสต็อก (ขาออก)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "ชั้นการประเมินมูลค่าสต็อก"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__company_currency_id
msgid ""
"Technical field to correctly show the currently selected company's currency "
"that corresponds to the totaled value of the product's valuation layers"
msgstr ""
"ฟิลด์เทคนิคเพื่อแสดงสกุลเงินของบริษัทที่เลือกในปัจจุบันอย่างถูกต้อง "
"ซึ่งสอดคล้องกับมูลค่ารวมของชั้นการประเมินมูลค่าของสินค้า"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"โค้ดประเทศ ISO ในสองตัวอักษร\n"
"คุณสามารถใช้ช่องนี้เพื่อการค้นหาอย่างรวดเร็ว"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr ""
"บัญชีสต็อกสินค้าเข้าและ/หรือสินค้าออก "
"ไม่สามารถเหมือนกับบัญชีประเมินมูลค่าสต็อกได้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr "การดำเนินการนี้จะสร้างรายการสมุดรายวัน ซึ่งคุณไม่มีสิทธิ์ในการเข้าถึง"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "The added value doesn't have any impact on the stock valuation"
msgstr "มูลค่าเพิ่มไม่มีผลกระทบต่อการประเมินมูลค่าสต็อก"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""
"รายการย้ายไม่อยู่ในสถานะที่สอดคล้องกัน: "
"บางส่วนกำลังเข้าและบางส่วนกำลังจะออกจากบริษัท"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""
"รายการย้ายไม่อยู่ในสถานะที่สอดคล้องกัน: "
"กำลังดำเนินการระหว่างบริษัทในขั้นตอนเดียว "
"ในขณะที่ควรเดินทางผ่านตำแหน่งขนส่งระหว่างบริษัท"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr ""
"รายการย้ายไม่อยู่ในสถานะที่สอดคล้องกัน: "
"ไม่ได้ใช้บริษัทต้นทางหรือปลายทางเดียวกัน"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The stock accounts should be set in order to use the automatic valuation."
msgstr "ควรตั้งค่าบัญชีสต็อกเพื่อใช้การประเมินมูลค่าอัตโนมัติ"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move_line.py:0
msgid ""
"The stock valuation of a move is based on the type of the source and "
"destination locations. As the move is already processed, you cannot modify "
"the locations in a way that changes the valuation logic defined during the "
"initial processing."
msgstr ""
"การประเมินมูลค่าสต็อกของการย้ายนั้นขึ้นอยู่กับประเภทของสถานที่ต้นทางและปลายทาง"
" เนื่องจากการย้ายนั้นได้รับการดำเนินการแล้ว "
"คุณจึงไม่สามารถแก้ไขสถานที่ในลักษณะที่เปลี่ยนตรรกะการประเมินมูลค่าที่กำหนดไว้ในระหว่างการประมวลผลเริ่มต้นได้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"The value of a stock valuation layer cannot be negative. Landed cost could "
"be use to correct a specific transfer."
msgstr ""
"ค่าของชั้นการประเมินมูลค่าสต็อกไม่สามารถเป็นค่าลบได้ "
"ค่าปันส่วนต้นทุนสินค้าสามารถใช้เพื่อแก้ไขการโอนเฉพาะได้"

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_report_action
msgid ""
"There are no valuation layers. Valuation layers are created when there are "
"product moves that impact the valuation of the stock."
msgstr ""
"ไม่มีชั้นการประเมินมูลค่า "
"ชั้นการประเมินค่าจะถูกสร้างขึ้นเมื่อมีการย้ายสินค้า "
"ซึ่งส่งผลกระทบต่อการประเมินมูลค่าสต็อก"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/fields/boolean_confirm.js:0
msgid ""
"This operation might lead in a loss of data. Valuation will be identical for"
" all lots/SN. Do you want to proceed ? "
msgstr ""
"การดำเนินการนี้อาจนำไปสู่การสูญเสียข้อมูล "
"การประเมินค่าจะเหมือนกันสำหรับล็อต/SN ทั้งหมด คุณต้องการดำเนินการต่อหรือไม่?"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"This product is valuated by lot/serial number. Changing the cost will update"
" the cost of every lot/serial number in stock."
msgstr ""
"สินค้านี้ประเมินราคาตามหมายเลขล็อต/ซีเรียล "
"การเปลี่ยนแปลงราคาจะอัปเดตราคาของแต่ละล็อต/ซีเรียลนัมเบอร์ในสต็อก"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Moved Quantity"
msgstr "ปริมาณการเคลื่อนย้ายทั้งหมด"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Quantity"
msgstr "ปริมาณคงเหลือทั้งหมด"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Value"
msgstr "มูลค่าคงเหลือทั้งหมด"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__total_value
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__total_value
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Total Value"
msgstr "มูลค่ารวม"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "โอน"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr ""
"กระตุ้นให้ปริมาณ การส่งมอบ/รับ ลดลงใน ใบสั่งขาย/ใบสั่งซื้อ ที่เกี่ยวข้อง"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Unit"
msgstr "หน่วย"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Unit Cost"
msgstr "ต้นทุนต่อหน่วย"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "มูลค่าต่อหน่วย"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "หน่วยวัด"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "อัปเดตปริมาณใน SO/PO"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid "Updating lot valuation for product %s."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"ใช้สำหรับการประเมินมูลค่าสินค้าคงคลังแบบเรียลไทม์ "
"เมื่อตั้งค่าบนตำแหน่งที่ตั้งเสมือน (ไม่ใช่ชนิดภายใน) "
"บัญชีนี้จะใช้เพื่อเก็บมูลค่าของสินค้าที่ถูกย้ายจากตำแหน่งที่ตั้งภายในไปยังตำแหน่งที่ตั้งนี้"
" แทนที่จะเป็นบัญชีสต็อกขาออกทั่วไปที่ตั้งค่าไว้ในสินค้า "
"การดำเนินการนี้จะไม่มีผลกระทบต่อตำแหน่งภายใน"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"ใช้สำหรับการประเมินมูลค่าสินค้าคงคลังแบบเรียลไทม์ "
"เมื่อตั้งค่าบนตำแหน่งที่ตั้งเสมือน (ไม่ใช่ชนิดภายใน) "
"บัญชีนี้จะใช้เพื่อเก็บมูลค่าของสินค้าที่ถูกย้ายออกจากตำแหน่งที่ตั้งนี้และไปยังตำแหน่งที่ตั้งภายใน"
" แทนที่จะเป็นบัญชีสต็อกขาออกทั่วไปที่ตั้งค่าไว้บนสินค้า "
"การดำเนินการนี้จะไม่มีผลกระทบต่อตำแหน่งภายใน"

#. module: stock_account
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Valuation"
msgstr "การประเมินมูลค่า"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__company_currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__company_currency_id
msgid "Valuation Currency"
msgstr "สกุลเงินของการประเมินมูลค่า"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__adjusted_layer_ids
msgid "Valuation Layers"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Valuation Report"
msgstr "รายงานการประเมินมูลค่า"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Valuation at Date"
msgstr "การประเมินมูลค่า ณ วันที่"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__lot_valuated
#: model:ir.model.fields,field_description:stock_account.field_product_template__lot_valuated
msgid "Valuation by Lot/Serial number"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Valuation method change for product category %(category)s: from "
"%(old_method)s to %(new_method)s."
msgstr ""
"การเปลี่ยนแปลงวิธีการประเมินมูลค่าสำหรับหมวดหมู่ผลิตภัณฑ์ %(category)s: จาก "
"%(old_method)s เป็น %(new_method)s"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__adjusted_layer_ids
msgid "Valuations layers being adjusted"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "ค่า"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.xml:0
msgid "Value On Hand:"
msgstr "มูลค่าคงเหลือ:"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__value_svl
msgid "Value Svl"
msgstr "มูลค่า Svl"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_lot__standard_price
msgid ""
"Value of the lot (automatically computed in AVCO).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""
"มูลค่าของล็อต (คำนวณอัตโนมัติใน AVCO)\n"
"        ใช้ในการประเมินมูลค่าผลิตภัณฑ์เมื่อไม่ทราบต้นทุนการซื้อ (เช่น การปรับสินค้าคงคลัง)\n"
"        ใช้ในการคำนวณมาร์จิ้นในการสั่งซื้อสินค้า"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid "Warning"
msgstr "คำเตือน"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"เมื่อเปิดใช้งานการประเมินมูลค่าสินค้าคงคลังอัตโนมัติในสินค้า "
"บัญชีนี้จะเก็บมูลค่าปัจจุบันของสินค้า"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""
"เมื่อทำการประเมินมูลค่าสินค้าคงคลังอัตโนมัติ รายการสมุดรายวันคู่สำหรับการย้ายสต็อกขาออกทั้งหมดจะถูกผ่านรายการในบัญชีนี้\n"
"                เว้นแต่จะมีบัญชีประเมินราคาเฉพาะที่ตั้งไว้ที่สถานที่ปลายทาง นี่คือค่าเริ่มต้นสำหรับสินค้าทั้งหมดในหมวดหมู่นี้\n"
"                นอกจากนี้ยังสามารถตั้งค่าได้โดยตรงในแต่ละสินค้า"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"เมื่อทำการประเมินมูลค่าสินค้าคงคลังแบบอัตโนมัติ นี่คือสมุดรายวันการบัญชี "
"ซึ่งรายการต่างๆจะถูกผ่านรายการโดยอัตโนมัติเมื่อมีการประมวลผลการย้ายสต็อก"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr "โปรแกรมแบบจำลอง เพื่อประเมินสินค้าคงคลังสำหรับสินค้าอีกครั้ง"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot adjust the valuation of a layer with zero quantity"
msgstr "คุณไม่สามารถปรับค่าของเลเยอร์ที่มีปริมาณเป็นศูนย์ได้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot adjust valuation without a product"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You cannot change the costing method of product valuated by lot/serial "
"number."
msgstr ""
"คุณไม่สามารถเปลี่ยนวิธีการคิดต้นทุนของผลิตภัณฑ์ที่ประเมินค่าโดยหมายเลขล็อต/หมายเลขซีเรียลได้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You cannot change the product category of a product valuated by lot/serial "
"number."
msgstr ""
"คุณไม่สามารถเปลี่ยนประเภทผลิตภัณฑ์ของผลิตภัณฑ์ที่ประเมินค่าโดยหมายเลขล็อต/หมายเลขซีเรียลได้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue a product with a standard cost method."
msgstr "คุณไม่สามารถประเมินมูลค่าสินค้าใหม่ด้วยวิธีต้นทุนมาตรฐานได้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue a product with an empty or negative stock."
msgstr "คุณไม่สามารถประเมินมูลค่าใหม่ของสินค้าที่มีสต็อกว่างเปล่าหรือติดลบได้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue multiple products at once"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_lot.py:0
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr ""
"คุณไม่สามารถอัปเดตต้นทุนของสินค้าในการประเมินมูลค่าอัตโนมัติได้ "
"เพราะจะนำไปสู่การสร้างรายการสมุดรายวัน ซึ่งคุณไม่มีสิทธิ์ในการเข้าถึง"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"คุณไม่มีบัญชีการประเมินมูลค่าขาเข้าที่กำหนดไว้ในหมวดหมู่สินค้าของคุณ "
"คุณต้องกำหนดหนึ่งรายการก่อนที่จะประมวลผลการดำเนินการนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any output valuation account defined on your product "
"category. You must define one before processing this operation."
msgstr ""
"คุณไม่มีบัญชีการประเมินมูลค่าผลผลิตที่กำหนดไว้ในหมวดหมู่ผลิตภัณฑ์ของคุณ "
"คุณต้องกำหนดหนึ่งรายการก่อนที่จะประมวลผลการดำเนินการนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr ""
"คุณไม่มีบัญชีการป้อนข้อมูลสต็อกที่กำหนดไว้ในหมวดหมู่สินค้าของคุณ "
"คุณต้องกำหนดหนึ่งรายการก่อนที่จะประมวลผลการดำเนินการนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""
"คุณไม่มีสมุดรายวันสต็อกที่กำหนดไว้ในหมวดหมู่สินค้าของคุณ "
"ตรวจสอบว่าคุณได้ติดตั้งผังบัญชีหรือไม่"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"คุณไม่มีบัญชีประเมินมูลค่าสต็อกที่กำหนดไว้ในหมวดหมู่สินค้าของคุณ "
"คุณต้องกำหนดหนึ่งรายการก่อนที่จะประมวลผลการดำเนินการนี้"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid "You must set a counterpart account on your product category."
msgstr "คุณต้องตั้งค่าบัญชีคู่ในหมวดหมู่สินค้าของคุณ"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "โดย"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "สำหรับ"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr "ตำแหน่ง"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "units"
msgstr "หน่วย"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_graph
msgid "valuation graph"
msgstr ""
