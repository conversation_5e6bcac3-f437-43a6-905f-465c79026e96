# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "# Inscriptions"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "'. Affichage des résultats pour '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "(only"
msgstr "(uniquement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ", at"
msgstr ", à"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>Fin</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<b>Sold Out</b>"
msgstr "<b>Complet</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>Début</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<em>Hidden for visitors</em>"
msgstr "<em>Masqué pour les visiteurs</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<em>Registrations <b>Closed</b></em>"
msgstr "<em>Les inscriptions <b>sont clôturées</b></em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr ""
"<em>Écrivez ici la citation d'un de vos participants. Cela donne du crédit à"
" vos événements.</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "<i class=\"fa fa-ban me-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban me-2\"/>Complet"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>Non publié"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-check me-2\" role=\"img\"/>Registered"
msgstr "<i class=\"fa fa-check me-2\" role=\"img\"/>Inscrit·e"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check me-2\"/>Registered"
msgstr "<i class=\"fa fa-check me-2\"/>Inscrit·e"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configurer\" "
"title=\"Configurer les tickets de l'événement\"/><em>Configurer les "
"tickets</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Site Web\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-map-marker me-2\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-map-marker me-2\" title=\"Lieu\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-map-marker\" title=\"Location\"/> Online event"
msgstr "<i class=\"fa fa-map-marker\" title=\"Lieu\"/> Événement en ligne"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid ""
"<i class=\"oi oi-chevron-left\"/>\n"
"                <span>All Events</span>"
msgstr ""
"<i class=\"oi oi-chevron-left\"/>\n"
"                <span>Tous les événements</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
msgid "<small class=\"fw-bold\">Ends</small>"
msgstr "<small class=\"fw-bold\">Se termine</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
msgid "<small class=\"fw-bold\">Starts</small>"
msgstr "<small class=\"fw-bold\">Commence</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
msgid "<small class=\"text-muted\">Add to calendar:</small>"
msgstr "<small class=\"text-muted\">Ajouter au calendrier :</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<small>Add this event to your calendar</small>"
msgstr "<small>Ajouter cet événement à votre calendrier</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge text-bg-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""
"<span class=\"badge text-bg-secondary text-uppercase "
"o_wevent_badge\">Intervenant</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<span class=\"fa fa-plus me-1\"/> Create an Event"
msgstr "<span class=\"fa fa-plus me-1\"/> Créer un événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span "
"class=\"o_countdown_metric pe-1\">days</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span "
"class=\"o_countdown_metric pe-1\">jour(s)</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                        <i class=\"fa fa-ban me-2\"/>Sold Out\n"
"                                    </span>"
msgstr ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                        <i class=\"fa fa-ban me-2\"/>Épuisé\n"
"                                    </span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>Événements en ligne</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr ""
"<strong>Vous avez commandé plus de tickets que de places "
"disponibles</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_event_upcoming_snippet_preview_data
msgid ""
"<time class=\"text-white\" datetime=\"2024-08-05 07:00:00\">\n"
"                                <span>July 26, 2024</span>\n"
"                                <span class=\"s_events_event_time\">\n"
"                                    - <span>9:00 AM</span>\n"
"                                    (Europe/Brussels)\n"
"                                </span>\n"
"                            </time>"
msgstr ""
"<time class=\"text-white\" datetime=\"2024-08-05 07:00:00\">\n"
"                                <span>26 juillet 2024</span>\n"
"                                <span class=\"s_events_event_time\">\n"
"                                    - <span>9 h 00</span>\n"
"                                    (Europe/Bruxelles)\n"
"                                </span>\n"
"                            </time>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_event_upcoming_snippet_preview_data
msgid ""
"<time class=\"text-white\" datetime=\"2024-08-10 08:00:00\">\n"
"                                <span>October 24, 2024</span>\n"
"                                <span class=\"s_events_event_time\">\n"
"                                    - <span>1:15 AM</span>\n"
"                                    (Europe/Brussels)\n"
"                                </span>\n"
"                            </time>"
msgstr ""
"<time class=\"text-white\" datetime=\"2024-08-10 08:00:00\">\n"
"                                <span>24 octobre 2024</span>\n"
"                                <span class=\"s_events_event_time\">\n"
"                                    - <span>1 h 15</span>\n"
"                                    (Europe/Bruxelles)\n"
"                                </span>\n"
"                            </time>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_event_upcoming_snippet_preview_data
msgid ""
"<time class=\"text-white\" datetime=\"2024-11-08 20:15:00\">\n"
"                                <span>November 23, 2024</span>\n"
"                                <span class=\"s_events_event_time\">\n"
"                                    - <span>8:15 PM</span>\n"
"                                    (Europe/Brussels)\n"
"                                </span>\n"
"                            </time>"
msgstr ""
"<time class=\"text-white\" datetime=\"2024-11-08 20:15:00\">\n"
"                                <span>23 novembre 2024</span>\n"
"                                <span class=\"s_events_event_time\">\n"
"                                    - <span>20 h 15</span>\n"
"                                    (Europe/Bruxelles)\n"
"                                </span>\n"
"                            </time>"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_2
msgid "A friend"
msgstr "Un ami"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "Un événement passé"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "About Us"
msgstr "À propos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "À propos de nous"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
msgid "Add to Google"
msgstr "Ajouter dans Google"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Add to Google Agenda"
msgstr "Ajouter dans Google Agenda"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
msgid "Add to Outlook"
msgstr "Ajouter dans Outlook"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
msgid "Add to iCal"
msgstr "Ajouter dans iCal"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Add to iCal/Outlook"
msgstr "Ajouter dans iCal/Outlook"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "All"
msgstr "Tous"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
msgid "All Countries"
msgstr "Tous les pays"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid "All Events"
msgstr "Tous les événements"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "Tous les pays"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_1
msgid "Allergies"
msgstr "Allergies"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr ""
"Permet d'afficher et de gérer des menus spécifiques à un événement sur le "
"site web."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"À seulement 13 ans, John DOE commençait déjà à développer ses premières "
"applications commerciales pour des clients. Après un Master Ingénieur Civil,"
" il a fondé TinyERP. Ce fut la première phase d'OpenERP qui deviendra plus "
"tard Odoo, le logiciel d'entreprise open source le plus installé au monde."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "Participants"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "Auteur"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid "Back to All Events"
msgstr "Revenir à tous les événements"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid "Back to {{event.name}}"
msgstr "Revenir à {{event.name}}"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_1
#: model:event.question.answer,name:website_event.event_5_question_0_answer_1
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_1
msgid "Blog Post"
msgstr "Article de blog"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Business Workshops"
msgstr "Ateliers commerciaux"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "Peut publier"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel"
msgstr "Annuler"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Card design"
msgstr "Card design"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "Fermer"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_1
msgid "Commercials"
msgstr "Publicités"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Community"
msgstr "Communauté"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "Menu communauté"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "Société"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Conference For Architects"
msgstr "Conférence pour les architectes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid "Configure event tickets"
msgstr "Configurer les tickets d'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Confirm Registration"
msgstr "Confirmer l'inscription"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_0
msgid "Consumers"
msgstr "Consommateurs"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Countries"
msgstr "Pays"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Cover Position"
msgstr "Position de la couverture"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "Propriétés de la couverture"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "Créé le"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Date"
msgstr "Date"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Date (nouvelle à ancienne)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Date (ancienne à nouvelle)"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_visibility
msgid ""
"Defines the Visibility of the Event on the Website and searches.\n"
"\n"
"            Note that the Event is however always available via its link."
msgstr ""
"Définit la visibilité de l'événement sur le site web et dans les recherches..\n"
"\n"
"            Notez que l'événement est cependant toujours disponible via son lien."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "Description"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "Afficher un menu dédié sur le site web"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "Afficher l'onglet communauté sur le site web"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Don't forget to click <b>save</b> when you're done."
msgstr ""
"N'oubliez pas de cliquer sur <b>enregistrer</b> lorsque vous avez terminé."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Don't miss out!"
msgstr "Ne manquez pas ça !"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_ticket_access
msgid "Download All Tickets"
msgstr "Télécharger tous les tickets"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_ticket_access
msgid "Download Tickets <i class=\"ms-1 fa fa-download\"/>"
msgstr "Télécharger les tickets <i class=\"ms-1 fa fa-download\"/>"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/website_event.js:0
msgid "Error"
msgstr "Erreur"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "Événement"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "Menus communauté de l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Event Cover Position"
msgstr "Position de la couverture de l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "Date de l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Event Details"
msgstr "Détails de l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Info"
msgstr "Informations sur l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "Lieu de l'événement"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "Menu de l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Event Name"
msgstr "Nom de l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Event Page"
msgstr "Page de l'événement"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_pages_list
msgid "Event Pages"
msgstr "Pages de l'événement"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "Inscription à l'événement"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__event_register_url
msgid "Event Registration Link"
msgstr "Lien d'inscription pour l'événement"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "Inscriptions à l'événement"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Subtitle"
msgstr "Sous-titre de l'événement"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag
msgid "Event Tag"
msgstr "Étiquette d'événement"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "Catégorie d'étiquette d'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_dynamic_snippet_options_template
msgid "Event Tags"
msgstr "Étiquettes événement"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "Modèle d'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Title"
msgstr "Titre de l'événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "Événement introuvable !"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "Événement publié"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "Événement non publié"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#: model:ir.ui.menu,name:website_event.menu_event_pages
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.snippets
msgid "Events"
msgstr "Événements"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Events Page"
msgstr "Page des événements"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_event_upcoming_snippet_preview_data
msgid "Excellence in Achievement Awards"
msgstr "Prix d'Excellence en Réalisations"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Expired"
msgstr "Expiré"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Filter by Country"
msgstr "Filtrer par pays"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
msgid "Filter by Date"
msgstr "Filtrer par date"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "Filters"
msgstr "Filtres"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr ""
"Découvrez ce que les gens voient et disent à propos de cet événement et "
"rejoignez la conversation."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Follow Us"
msgstr "Suivez-nous"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "Le contenu suivant apparaitra sur tous les événements."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get directions"
msgstr "Obtenir l'itinéraire"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Google Agenda"
msgstr "Google Agenda"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Great Reno Ballon Race"
msgstr "La Great Reno Balloon Race"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Grid"
msgstr "Grid"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_event_upcoming_snippet_preview_data
msgid "Harmony Under the Stars"
msgstr "Harmony Under the Stars"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Hidden (visitor only)"
msgstr "Masqué (uniquement pour les visiteurs)"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Hockey Tournament"
msgstr "Tournoi de hockey"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_1
msgid "How did you hear about us?"
msgstr "Comment avez-vous entendu parler de nous ?"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_2
#: model:event.question,title:website_event.event_1_question_0
#: model:event.question,title:website_event.event_5_question_0
#: model:event.question,title:website_event.event_type_data_sports_question_0
msgid "How did you learn about this event?"
msgstr "Comment avez-vous découvert cet événement ?"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "ID"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
msgid "Info"
msgstr "Infos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_event_upcoming_snippet_preview_data
msgid "Innovations in Technology and Society"
msgstr "Innovations dans la Technologie et la Société"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Inside content"
msgstr "Dans le contenu"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid "Introduction"
msgstr "Introduction"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "Menu d'introduction"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "Menus d'introduction"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "Est terminé"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "Est en cours"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "Participe"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "Est publié"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "John DOE"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Layout"
msgstr "Agencement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "List"
msgstr "Liste"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Live Music Festival"
msgstr "Festival de musique live"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Location"
msgstr "Emplacement"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "Menu lieu"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "Menus lieu"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_event__website_visibility__logged_users
msgid "Logged Users"
msgstr "Utilisateurs connectés"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr ""
"Superbe ! <b>Publions</b> maintenant cette page pour qu'elle devienne "
"<b>visible</b> sur votre site web !"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_0
msgid "Meal Type"
msgstr "Type de repas"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "Menu"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Type de Menu"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "Menus"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_0
msgid "Mixed"
msgstr "Mélangé"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__address_name
msgid "Name"
msgstr "Nom"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_event_action_add
msgid "New Event"
msgstr "Nouvel événement"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
msgid "Next Events"
msgstr "Prochains événements"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "Pas encore de rubrique de site web !"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No event matching your search criteria could be found."
msgstr "Aucun événement ne correspond à vos critères de recherche."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events scheduled yet"
msgstr "Aucun événement n'est planifié pour l'instant"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "Aucune inscription liée à ce visiteur"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "Aucun résultat trouvé pour '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Not Published"
msgstr "Non publié"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Online Events"
msgstr "Événements en ligne"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Online event"
msgstr "Événement en ligne"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "OpenWood Collection Online Reveal"
msgstr "Révélation en ligne de la collection OpenWood"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organisateur"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "Nos formations"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_0
msgid "Our website"
msgstr "Notre site web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_dates
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Outlook"
msgstr "Outlook"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "Past Events"
msgstr "Événements passés"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr "Pastafarian"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Photos"
msgstr "Photos"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_event__website_visibility__public
msgid "Public"
msgstr "Public"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Publish"
msgstr "Publier"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Published"
msgstr "Publié"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Quotes"
msgstr "Citations"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_2
#: model:event.question.answer,name:website_event.event_5_question_0_answer_2
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_2
msgid "Radio Ad"
msgstr "Spot radio"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Register"
msgstr "S'inscrire"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "Menu d'inscription"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "Menus d'inscription"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "Événements enregistrés"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid "Registration"
msgstr "Inscription"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "Inscription confirmée !"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registration failed! These tickets are not available anymore."
msgstr "Échec de l'inscription ! Ces tickets ne sont plus disponibles."

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "Inscriptions"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Registrations Closed"
msgstr "Inscriptions terminées"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Registrations not yet open"
msgstr "Les inscriptions ne sont pas encore ouvertes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations will open on"
msgstr "Les inscriptions débuteront le"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "Restant avant le début"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "Temps restant avant le début de l'événement (minutes)"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_2
msgid "Research"
msgstr "Recherche"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
#: model:ir.model.fields,help:website_event.field_event_tag__website_id
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_id
msgid "Restrict to a specific website."
msgstr "Restreindre à un site Web spécifique"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "Retournez à la liste d'événements."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimisé"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_1
msgid "Sales"
msgstr "Ventes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales end on"
msgstr "Fin des ventes le"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales start on"
msgstr "Début des ventes le"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_card
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_picture
msgid "Sample"
msgstr "Échantillon"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "Rechercher un événement..."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_tree
msgid "Selected Answers"
msgstr "Réponses sélectionnées"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "SEO nom"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Share"
msgstr "Partager"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "Afficher sur le site web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_dynamic_snippet_options_template
msgid "Show time"
msgstr "Afficher le temps"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sidebar"
msgstr "Barre latérale"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_0
#: model:event.question.answer,name:website_event.event_5_question_0_answer_0
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_0
msgid "Social Media"
msgstr "Réseaux sociaux"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sold Out"
msgstr "Complet"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "Désolé, l'événement demandé n'est plus disponible."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "Commence aujourd'hui"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Start → End"
msgstr "Début → Fin"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "démarre <span/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sub-menu (Specific)"
msgstr "Sous-menu (spécifique)"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Activité suspecte détectée par Google reCaptcha."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Template badge"
msgstr "Modèle de badge"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "L'URL complète pour accéder au document à travers le site web."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "The website must be from the same company as the event."
msgstr "Le site web doit appartenir à la même société que l'événement."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "This month"
msgstr "Ce mois"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "This operator is not supported"
msgstr "Cet opérateur n'est pas pris en charge"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "This shortcut will bring you right back to the event form."
msgstr "Ce raccourci vous ramènera directement au formulaire d'événement."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr "Ce menu technique affiche toutes les sous-rubriques de l'événement."

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
msgid "This ticket is not available for sale for this event"
msgstr "Ce ticket n'est pas disponible à la vente pour cet événement"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "Ticket #"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Tickets"
msgstr "Tickets"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "Today"
msgstr "Aujourd'hui"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Top"
msgstr "Haut"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Top Bar Filter"
msgstr "Filtre barre supérieure"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_visitor.py:0
msgid "Unsupported 'Not In' operation on visitors registrations"
msgstr ""
"Opération 'Not In' non prise en charge sur les inscriptions des visiteurs"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
msgid "Upcoming"
msgstr "À venir"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:website.snippet.filter,name:website_event.website_snippet_filter_event_list
msgid "Upcoming Events"
msgstr "Événements à venir"

#. module: website_event
#: model:website.snippet.filter,name:website_event.website_snippet_filter_event_list_unfinished
msgid "Upcoming and Ongoing Events"
msgstr "Événements à venir et en cours"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr ""
"Utilisez ce <b>raccourci</b> pour accéder facilement à la page web de votre "
"événement."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragraph to write a short text about your events or company."
msgstr ""
"Utilisez ce paragraphe pour écrire un court texte sur vos événements ou "
"votre entreprise."

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "Utilisé lorsqu'il ne s'agit pas d'un menu basé sur une URL"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "Value should be True or False (not %)"
msgstr "La valeur doit être vrai ou faux (pas %)"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr "Végétarien"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_event__website_visibility__link
msgid "Via a Link"
msgstr "Via un lien"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "Vue"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Visibility"
msgstr "Visibilité"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_visible_on_website
msgid "Visible On Website"
msgstr "Visible sur le site web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "Visible sur le site web actuel"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "Visiteur"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event matching your search for:"
msgstr ""
"Nous n'avons trouvé aucun événement correspondant à votre recherche de :"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event scheduled at this moment."
msgstr "Nous n'avons trouvé aucun événement planifié pour l'instant."

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_id
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
msgid "Website"
msgstr "Site Web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "Menu des événements du site web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "Menus des événements du site web"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "Page d’accueil du site web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "Menu du site web"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "Menus du site web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtre des snippets du site web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "Sous-menu du site web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "URL de site web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_visibility
msgid "Website Visibility"
msgstr "Visibilité du site web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "Visiteur du site web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "Méta-description du site web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "Méta mots-clés site web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "Méta titre site web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "Image opengraph site web "

#. module: website_event
#: model:event.question,title:website_event.event_5_question_1
msgid "What's your Hockey level?"
msgstr "Quel est votre niveau de hockey ?"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Si l'événement a commencé"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "Si l'événement va commencer aujourd'hui s'il n'est pas déjà en cours"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_0
msgid "Which field are you working in"
msgstr "Dans quel domaine travaillez-vous"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr ""
"Avec le bouton Modifier, vous pouvez <b>personnaliser</b>la page web que les"
" visiteurs verront lors de leur inscription."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "available)"
msgstr "disponible)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "e.g. \"Conference for Architects\""
msgstr "par ex. \"Conférence pour architectes\""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "time)"
msgstr "temps)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "w-100"
msgstr "w-100"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "w-100 py-sm-3 mt-sm-2"
msgstr "w-100 py-sm-3 mt-sm-2"
