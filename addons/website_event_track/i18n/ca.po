# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track
# 
# Translators:
# erii<PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# Harcogourmet, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <eant<PERSON>@users.noreply.github.com>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# ma<PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# jabiri7, 2024
# <PERSON> <<EMAIL>>, 2025
# Noem<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Noemi Pla, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"\n"
"    Dear <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t><br/>\n"
"    We are pleased to inform you that your proposal <t t-out=\"object.name or ''\">What This Event Is All About</t> has been accepted and confirmed for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
msgstr ""
"\n"
"    Benvolgut/da <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman:</t><br/>\n"
"    Ens complau informar-lo que la seva proposta <t t-out=\"object.name or ''\">En què consisteix aquest esdeveniment?</t> ha estat acceptada i confirmada per a l'esdeveniment <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    Trobarà més informació aquí:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            Visualitzar la xerrada\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Gràcies,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/website.py:0
msgid "\"Events App Name\" field is required."
msgstr "El camp \"Nom de l'aplicació d'esdeveniments\" és obligatori."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_count
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_count
msgid "# Wishlisted"
msgstr "# Llista de desitjos"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "%(name)s from %(company)s"
msgstr "%(name)s de%(company)s"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "%(name)s, %(function)s at %(company)s"
msgstr "%(name)s, %(function)s en %(company)s"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/website.py:0
msgid "%s Events"
msgstr "%sEsdeveniments "

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/controllers/webmanifest.py:0
msgid "%s Online Events Application"
msgstr "%sAplicació d'esdeveniments en línia "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "(timezone:"
msgstr "(timezone:"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_17
msgid "10 DIY Furniture Ideas For Absolute Beginners"
msgstr "Idees de mobiliari de 10 per a principiants absoluts"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_18
msgid "6 Woodworking tips and tricks for beginners"
msgstr "6 consells i trucs per a principiants"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr "<b>Correu</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr "<b>Telèfon</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr "<b>Proposat Per</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speaker Biography</b>:"
msgstr "<b>Biografia de l'altaveu</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr "<b>Introducció de converses</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>No publicat"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_search
msgid "<i class=\"fa fa-bell mx-2 text-muted\"/> Favorite Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "<span class=\"d-none d-md-block\">&amp;bull;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "<span class=\"fa fa-plus me-1\"/> Schedule Tracks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                    <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                    <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                </span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Introduction</span>\n"
"                                        <span>*</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Title</span>\n"
"                                        <span>*</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "<span class=\"text-muted\"> tracks</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid ""
"<span for=\"contact_name\">Name</span>\n"
"                    <span>*</span>"
msgstr ""
"<span for=\"contact_name\">Nom</span>\n"
"                    <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "<span id=\"search_number\" class=\"me-1\">0</span>Results"
msgstr "<span id=\"search_number\" class=\"me-1\">0</span>Resultats"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content_partner_info
msgid "<span> at </span>"
msgstr "<span> a </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<span>New track proposal</span>"
msgstr "<span>Nova proposta de pista</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<strong>Location:</strong>"
msgstr "<strong>Ubicació:</strong>"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr ""
"Una explicació tècnica de com utilitzar aplicacions de disseny d'ordinador"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""
"L'estat kanban d'una pista indica situacions especials que l'afecten:\n"
" * Grey és la situació predeterminada\n"
" * Red indica que alguna cosa està impedint el progrés d'aquesta pista\n"
" * Green indica que la pista està preparada per ser estirada a la següent etapa"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "Actiu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid ""
"Add a description to help your coworkers understand the meaning and purpose "
"of the stage."
msgstr ""
"També podeu afegir una descripció per ajudar els companys a entendre el "
"significat i l'objectiu d'aquest pas."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Add a description..."
msgstr "Afegir una descripció..."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr "Afegeix una nova etapa en el conducte de la tasca"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid ""
"Add tags to your tracks to help your attendees browse your event web pages."
msgstr ""
"Afegeixi etiquetes als seus enregistraments per a ajudar els seus assistents"
" a navegar en la seva pàgina web d'esdeveniments."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management: tips and tricks from the fields"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr "Informe avançat"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Agenda"
msgstr "Agenda"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
msgid "Agenda Color"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_main
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "All Talks"
msgstr "Totes les converses"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Allow Track Proposals"
msgstr "Permet les propostes de la pista"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Allow push notifications?"
msgstr "¿Permet les notificacions d'enviament?"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                            presentation, for publishing on our website."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlisted_by_default
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Always Wishlisted"
msgstr "Sempre en la llista de desitjos"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr "Anunciat"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
msgid "Application"
msgstr "Aplicació"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr "Arxivat"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_visitor__is_blacklisted
msgid ""
"As key track cannot be un-favorited, this field store the partner choice to "
"remove the reminder for key tracks."
msgstr ""
"Com l'enregistrament principal no es pot treure de la llista de favorits, "
"aquest camp manté l'elecció del soci d'eliminar el recordatori per als "
"enregistraments principals."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."
msgstr ""
"Com ja s'ha dit abans, fabricar els vostres propis mobles no és tan difícil ni tan complicat com creieu.\n"
"    De fet, alguns projectes són tan fàcils que qualsevol podria completar-los amb èxit. Per exemple, fent un tamboret bonic a partir de\n"
"    un pneumàtic vell és un tros de pastís i si necessites una taula de cafè, pots muntar-ne una fàcilment utilitzant-la\n"
"    caixes de fusta."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_1
msgid "Audience"
msgstr "Audiència"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "Etiquetes de pista disponibles"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
msgid "Available on all event Agenda pages"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_main
msgid "Back to All Talks"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Bandy clamp hack"
msgstr "Hack Bandy clamp"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Biography"
msgstr "Biografia"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage1
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage2
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage3
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage4
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage5
msgid "Blocked"
msgstr "Bloquejat"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_confirmation_end_page_hook
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Book your seats to the best talks"
msgstr "Reserva els teus escons a les millors xerrades"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Book your talks"
msgstr "Reserva les teves xerrades"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_14
msgid "Building a DIY cabin from the ground up"
msgstr "Construir una cabana de BRICOLATGE des de terra cap amunt"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_url
msgid "Button Target URL"
msgstr "URL de destí del botó"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_title
msgid "Button Title"
msgstr "Títol del botó"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_website_cta_live
msgid "CTA button is available"
msgstr "El botó CTA està disponible"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "Trucada de propostes"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__can_publish
msgid "Can Publish"
msgstr "Pot publicar"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Cancelled Stage"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Categories"
msgstr "Categories"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__category_id
msgid "Category"
msgstr "Categoria"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_12
msgid "Climate positive"
msgstr "Clima positiu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Close"
msgstr "Tancar"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__color
msgid "Color"
msgstr "Color"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr "Índex de color"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/controllers/event_track.py:0
msgid "Coming soon"
msgstr "Pròximament"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Coming soon ..."
msgstr "Pròximament ..."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__company_id
msgid "Company"
msgstr "Empresa"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_company_name
msgid "Company Name"
msgstr "Nom d'empresa"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of {{ object.name }}"
msgstr "Confirmació de {{ object.name }}"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "Confirmat"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_13
#: model_terms:event.track,description:website_event_track.event_7_track_3
msgid ""
"Considering to build a wooden house? Watch this video to find out more "
"information about a construction process and final result. Step by step "
"simple explanation! Interested?"
msgstr ""
"Pensa construir una casa de fusta? Mireu aquest vídeo per obtenir més "
"informació sobre un procés de construcció i el resultat final. Pas a pas, "
"simple explicació! Interessat?"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
msgid "Contact"
msgstr "Contacte"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Contact Details"
msgstr "Detalls del contacte"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_email
msgid "Contact Email"
msgstr "Correu electrònic del contacte"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact Information"
msgstr "Informació de contacte"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_phone
msgid "Contact Phone"
msgstr "Telèfon del contacte"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact me through a different email/phone"
msgstr "Contacta amb mi a través d'un correu electrònic/telèfon diferent"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Create a Track"
msgstr "Crea una pista"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid "Create a Track Location"
msgstr "Crea una ubicació de la pista"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid "Create a Track Tag"
msgstr "Crea una etiqueta de pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_date
msgid "Created on"
msgstr "Creat el"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Customer"
msgstr "Client/a"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_19
msgid "DIY Timber Cladding Project"
msgstr "Projecte d'enclavament de la fusta del DIY"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PROPOSAL PAGES "
"OF ALL EVENTS"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "Data"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_11
msgid "Day 2 Wrapup"
msgstr "Resum del 2 dia"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_8
msgid "Dealing with OpenWood Furniture"
msgstr "S'està gestionant amb OpenWood Furniture"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Define labels explaining kanban state management."
msgstr "Defineix les etiquetes que expliquen la gestió de l'estat del kanban."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_location__sequence
msgid "Define the order in which the location will appear on \"Agenda\" page"
msgstr "Defineix l'ordre en què la ubicació apareixerà a la pàgina «Agenda»"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."
msgstr ""
"Defineix els passos que s'utilitzaran en l'esdeveniment des de\n"
"            creació de la pista, fins al tancament de la pista.\n"
"            Utilitzarà aquestes etapes per a seguir el progrés\n"
"            resolent una pista d'esdeveniments."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__description
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Description"
msgstr "Descripció"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_tag_line
msgid "Description of the partner (name, function and company name)"
msgstr "Descripció del soci (nom, funció i nom de l'empresa)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr "Concurs de disseny (tota la tarda)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr "Concurs de disseny (dia sencer)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr "Full de ruta detallat dels nostres nous productes"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr "Descobriu el nostre nou equip de disseny"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta
msgid ""
"Display a Call to Action button to your Attendees while they watch your "
"Track."
msgstr ""
"Mostrar un botó de crida a l'acció per als seus assistents mentre veuen el "
"seu enregistrament."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
msgid "Done"
msgstr "Fet"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Dowel Hack"
msgstr "Dowel Hack"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "Durada"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_3
msgid "Easy Way To Build a Wooden House"
msgstr "Forma fàcil de construir una casa de fusta"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "Modificar la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Email"
msgstr "Correu electrònic"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr "Plantilla de correu electrònic"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Error"
msgstr "Error"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "Esdeveniment"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "Ubicació de l'esdeveniment"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
msgid "Event Locations"
msgstr "Ubicacions dels esdeveniments"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Event Page"
msgstr "Pàgina d'esdeveniment"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track_proposal
msgid "Event Proposals Menus"
msgstr "Menús de propostes d'esdeveniments"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Template"
msgstr "Plantilla d'esdeveniment"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "Pista de l'esdeveniment"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr "Ubicació de la gravació de l'esdeveniment"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr "Pista d'esdeveniments"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "Etiqueta de la pista d'esdeveniments"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag_category
msgid "Event Track Tag Category"
msgstr "Categoria de l'etiqueta de la gravació d'esdeveniments"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "Pistes d'esdeveniments"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track
msgid "Event Tracks Menus"
msgstr "Menús de pistes d'esdeveniments"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_21
msgid "Event Wrapup"
msgstr "Ajustament d'esdeveniments"

#. module: website_event_track
#: model:mail.template,name:website_event_track.mail_template_data_track_confirmation
msgid "Event: Track Confirmation"
msgstr "Esdeveniment: confirmació de la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,field_description:website_event_track.field_website__events_app_name
msgid "Events App Name"
msgstr "Nom de l'aplicació d'esdeveniments"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Events PWA"
msgstr "Esdeveniments PWA"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
msgid "Favorite On"
msgstr "Favorit en"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Favorites"
msgstr "Favorits"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Filter Tracks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Filter by Tags"
msgstr "Filtra per etiquetes"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Filters"
msgstr "Filtres"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Finished"
msgstr "Finalitzada"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_2
msgid "First Day Wrapup"
msgstr "Ajustament del primer dia"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr "Plegat al Kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_2
msgid "Format"
msgstr "Format"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_fully_accessible
msgid "Fully accessible"
msgstr "Totalment accessible"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr "Activitats futures"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_confirmation_end_page_hook
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Get prepared and"
msgstr "Prepareu-vos i"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Glue tip"
msgstr "Punt d'adhesió"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__done
msgid "Green"
msgstr "Verd"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_done
msgid "Green Kanban Label"
msgstr "Etiqueta kanban verda"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__normal
msgid "Grey"
msgstr "Gris"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Etiqueta kanban grisa"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "Agrupar per"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_11
msgid "Happy with OpenWood"
msgstr "Feliç amb OpenWood"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__2
msgid "High"
msgstr "Alta"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__3
msgid "Highest"
msgstr "El més alt"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Home page"
msgstr "Pàgina d'inici"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr "Casa de les Cultures Mundials"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "How can our team get in touch with you?"
msgstr "Com pot el nostre equip posar-se en contacte amb tu?"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr ""
"Com construir la vostra estratègia de màrqueting en un entorn competitiu"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr "Com comunicar-se amb la comunitat"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr "Com dissenyar un nou moble"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr "Com desenvolupar processos automatitzats"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr "Com seguir-nos a les xarxes socials"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr "Com millorar els vostres processos de qualitat"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr "Com integrar materials de maquinari en els vostres mobles"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr ""
"Com optimitzar les seves vendes, des d'iniciatives fins a comandes de venda."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__id
msgid "ID"
msgstr "ID"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_fully_accessible
msgid ""
"If checked, automatically publish tracks so that access links to customers "
"are provided."
msgstr ""
"Si està marcada, publica automàticament les pistes perquè es proporcionin "
"enllaços d'accés als clients."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "If checked, the related tracks will be visible in the frontend."
msgstr "Si està marcada, les pistes relacionades seran visibles al frontend."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr ""
"Si s'estableix, s'enviarà un correu electrònic al client quan la pista "
"arribi a aquest pas."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__wishlisted_by_default
msgid ""
"If set, the talk will be set as favorite for each attendee registered to the"
" event."
msgstr ""
"Si s'estableix, la conversa s'establirà com a preferida per a cada assistent"
" registrat a l'esdeveniment."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image_url
msgid "Image URL"
msgstr "URL de la imatge"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "In"
msgstr "Dins"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage1
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage2
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage3
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage4
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage5
msgid "In Progress"
msgstr "En curs"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_15
msgid "In this video we will see how lumber is made in a sawmill factory."
msgstr ""
"En aquest vídeo veurem com es fabrica la fusta en una fàbrica de serradores."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "In this video, I covered 6 tips and tricks to help out beginners:"
msgstr ""
"En aquest vídeo, vaig cobrir 6 consells i trucs per ajudar els principiants:"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
msgid "Install"
msgstr "Instal·lar "

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
msgid "Install Application"
msgstr "Instal·la l'aplicació"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Interactivity"
msgstr "Interactivitat"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "Introducció"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_website_cta_live
msgid "Is CTA Live"
msgstr "És CTA Live"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is Published"
msgstr "Està publicat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_reminder_on
msgid "Is Reminder On"
msgstr "Està activat el recordatori"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_done
msgid "Is Track Done"
msgstr "S'ha fet la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_live
msgid "Is Track Live"
msgstr "És Track Live"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_soon
msgid "Is Track Soon"
msgstr "És la pista aviat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_today
msgid "Is Track Today"
msgstr "Avui és la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_upcoming
msgid "Is Track Upcoming"
msgstr "S'acosta la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_wishlisted
msgid "Is Wishlisted"
msgstr "Està llistat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_blacklisted
msgid "Is reminder off"
msgstr "El recordatori està desactivat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_function
msgid "Job Position"
msgstr "Lloc de treball"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Job Title"
msgstr "Títul del treball"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Explicació del kanban bloquejat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Explicació del kanban en procés"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr "Estat kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state_label
msgid "Kanban State Label"
msgstr "Etiqueta de l'estat de Kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_done
msgid "Kanban Valid Explanation"
msgstr "Explicació del kanban vàlid"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr "Factors clau d'èxit venent els nostres mobles"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_9
msgid "Kitchens for the Future"
msgstr "Cuines per al futur"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr "Activitats endarrerides"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr "Últimes tendències"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_26
msgid "Less Furniture is More Furniture"
msgstr "Menys Furniture és més Furniture"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_4
msgid "Life at Home Around the World: William’s Story"
msgstr "Vida a Home Around the World: William's Story"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Lightning Talks"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_19
msgid ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"
msgstr ""
"Enllaça a Qamp;A aquí! Ha arribat el moment d'ocultar aquests vells murs de "
"bloqueig. Estimeu projectes senzills i de tipus de transformació com aquest!"
" :)-"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Live"
msgstr "En viu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Live Now"
msgstr "En viu ara"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_10
msgid "Live Testimonial"
msgstr "Testimonial en directe"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_25
msgid "Live Testimonials"
msgstr "Testimonials en directe"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Location"
msgstr "Ubicació"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_13
msgid "Log House Building"
msgstr "Edifici de la casa de registre"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_15
msgid "Logs to lumber"
msgstr "Troncs de fusta"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__0
msgid "Low"
msgstr "Baixa"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "Menjar"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta
msgid "Magic Button"
msgstr "Botó màgic"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Making a center marking gauge"
msgstr "Fer un gauge de marcatge central"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid ""
"Manage from here the places where you organize your tracks (e.g. Rooms, "
"Channels, ...)."
msgstr ""
"Gestioneu des d'aquí els llocs on organitzeu les vostres pistes (p. ex. "
"Sales, Canals...)."

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__1
msgid "Medium"
msgstr "Mitjà"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Tipus de menú"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr "Disseny mínim però eficient"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_start_remaining
msgid "Minutes before CTA starts"
msgstr "Minuts abans que comenci el CTA"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_remaining
msgid "Minutes before track starts"
msgstr "Minuts abans que comenci la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_relative
msgid "Minutes compare to track start"
msgstr "Minuts compara amb l'inici de la pista"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Miter saw tip"
msgstr "Consell de la serra de biaix"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "Pausa del matí"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr "Presentació global de la meva empresa"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Tracks"
msgstr "Les meves pistes"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__name
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name"
msgstr "Nom"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name of your website's Events Progressive Web Application"
msgstr "Nom de l'aplicació web Events Progressive del vostre lloc web"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr "Programa de certificació nou"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "Pista nova"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid "No Track Visitors yet!"
msgstr "Encara no hi ha visitants de pista!"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_action_from_visitor
msgid "No track favorited by this visitor"
msgstr "No hi ha cap pista preferida per aquest visitant"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "No track found."
msgstr "No s'ha trobat cap pista."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_tag__color
msgid "Note that colorless tags won't be available on the website."
msgstr ""
"Tingueu en compte que les etiquetes sense color no estaran disponibles al "
"lloc web."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_24
msgid "Old is New"
msgstr "El vell és el nou"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_20
msgid "Our Last Day Together!"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__partner_id
msgid "Partner"
msgstr "Empresa"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr "Programes d'associació"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Phone"
msgstr "Telèfon"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Picture"
msgstr "Imatge"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Plan your experience by adding your favorites talks to your wishlist"
msgstr ""
"Planifica la teva experiència afegint les xerrades preferides a la teva "
"llista d'espera"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
msgid "Please enter either a contact email address or a contact phone number."
msgstr ""
"Introduïu una adreça de correu electrònic de contacte o un número de telèfon"
" de contacte."

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
msgid "Please fill out the form correctly."
msgstr "Ompliu el formulari correctament."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr "Presentació del dossier"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_16
msgid "Pretty. Ugly. Lovely."
msgstr "Bonic. Lleig. Encantador."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Previous page"
msgstr "Pàgina anterior"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "Prioritat"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_7
msgid ""
"Probably one of the most asked questions I've gotten is how I got started "
"woodworking! In this video I share with you how/why I started building "
"furniture!"
msgstr ""
"Probablement una de les preguntes més plantejades que he tingut és com vaig "
"començar a treballar la fusta! En aquest vídeo comparteixo amb vosaltres "
"com/per què vaig començar a construir mobles!"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr "Proposta"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr "Propostes en el lloc web"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Published"
msgstr "Publicat"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr "S'estan creant les idees qualificades dels vostres clients"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage1
#: model:event.track.stage,legend_done:website_event_track.event_track_stage2
#: model:event.track.stage,legend_done:website_event_track.event_track_stage3
#: model:event.track.stage,legend_done:website_event_track.event_track_stage4
#: model:event.track.stage,legend_done:website_event_track.event_track_stage5
msgid "Ready for Next Stage"
msgstr "Preparat per propera etapa"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__blocked
msgid "Red"
msgstr "Vermell"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Etiqueta vermella de Kanban"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr "Rebutjada"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Regular Talks"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_relative
msgid "Relative time compared to track start (seconds)"
msgstr "Temps relatiu en comparació amb l'inici de la pista (segons)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta_start_remaining
msgid "Remaining time before CTA starts (seconds)"
msgstr "Temps restant abans que comenci la CTA (segons)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_remaining
msgid "Remaining time before track starts (seconds)"
msgstr "Temps restant abans que comenci la pista (segons)"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "Responsable"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_23
msgid "Restoring Old Woodworking Tools"
msgstr "Restauració d'eines de fusteria antigues"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_23
msgid "Restoring old woodworking tools"
msgstr "Restauració d'eines de fusteria antigues"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Right angle clamp jig"
msgstr "Eina de subjecció en angle recte"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimització SEO"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Search a talk ..."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_6
msgid "Securing your Lumber during transport"
msgstr "S'està assegurant la vostra fusta durant el transport"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
msgid "Select Categories"
msgstr ""

#. module: website_event_track
#: model:mail.template,description:website_event_track.mail_template_data_track_confirmation
msgid ""
"Sent to speakers whose track proposal is accepted (set the template on the "
"right stage)"
msgstr ""
"Enviat a altaveus la proposta dels quals s'accepta (establiu la plantilla en"
" l'etapa correcta)"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__seo_name
msgid "Seo name"
msgstr "Nom SEO"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
msgid "Set Favorite"
msgstr "Estableix com a preferit"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_delay
msgid "Show Button"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostra tots els registres en que la data de següent acció és abans d'avui"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Showcase Tracks"
msgstr "Mostra les pistes"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Speaker"
msgstr "Altaveu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker Bio"
msgstr "Biografia de l'altaveu"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "Speaker Email"
msgstr "Correu electrònic de l'altaveu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Speaker Photo"
msgstr "Foto de l'altaveu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker Profile"
msgstr ""

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "Speakers"
msgstr "Altaveus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr "Etapa"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Descripció de l'etapa i informació sobre eines"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr "Nom de la fase"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "Starting now!"
msgstr "Començant ara!"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr "Situació i estratègia"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "Acord de tramesa"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "Envia un pressupost"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_tag_line
msgid "Tag Line"
msgstr "Línia d'etiquetes"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag Name"
msgstr "Nom de l'etiqueta"

#. module: website_event_track
#: model:ir.model.constraint,message:website_event_track.constraint_event_track_tag_name_uniq
msgid "Tag name already exists!"
msgstr "¡El nom de l'etiqueta ja existeix!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Tags"
msgstr "Etiquetes"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Intro"
msgstr ""

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
msgid "Talk Proposals"
msgstr "Pressupostos de conversa"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Talk added to your Favorites"
msgstr "S'ha afegit la conversa als vostres preferits"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Talk already in your Favorites"
msgstr "Parlar ja amb els preferits"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Talk removed from your Favorites"
msgstr "S'ha suprimit la conversa dels preferits"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Talks"
msgstr "Xerrades"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "Tipus de converses"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "Tasca en curs. Feu clic per blocar o establir com s'ha fet."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"La tasca està bloquejada. Feu clic per desbloquejar o establir com s'ha fet."

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
msgid "Thank you for your proposal."
msgstr "Gràcies per la seva proposta."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr "L'URL completa per accedir al document a través del lloc web."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr "La nova estratègia de màrqueting"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr "La nova manera de promoure les vostres creacions"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"There are a lot of ideas worth exploring so start with the 10 DIY furniture "
"ideas for absolute beginners."
msgstr ""
"Hi ha un munt d'idees que val la pena explorar, així que comencem amb les 10"
" idees de mobles per a principiants absoluts."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"There are several variants of wood is available in the world but we are talking about most expensive\n"
"    ones in the world and keeping to the point we have arranged ten most expensive wood."
msgstr ""
"Hi ha diverses variants de fusta disponibles en el món, però estem parlant del més car\n"
"    Uns altres del món i mantenint el punt que hem disposat deu fusta més cara."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"These are 30 minutes talks on many different topics. Most topics are "
"accepted in lightning talks."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "These are standard talks with slides, alocated in slots of 60 minutes."
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"They will be created automatically once attendees start browsing your "
"events."
msgstr ""
"Es crearan automàticament quan els assistents comencin a navegar pels "
"vostres esdeveniments."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_website__app_icon
msgid ""
"This field holds the image used as mobile app icon on the website (PNG "
"format)."
msgstr ""
"Aquest camp conté la imatge utilitzada com a icona d'aplicació mòbil al lloc"
" web (format PNG)."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,help:website_event_track.field_website__events_app_name
msgid "This fields holds the Event's Progressive Web App name."
msgstr ""
"Aquest camp conté el nom de l'aplicació web progressiva de l'esdeveniment."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid ""
"This page hasn't been saved for offline reading yet.<br/>Please check your "
"network connection."
msgstr ""
"Aquesta pàgina encara no s'ha desat per a lectura fora de línia.<br/>Si us "
"plau, comproveu la connexió de xarxa."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Aquesta etapa es replega en la vista kanban quan no hi ha registres per "
"mostrar en aquesta."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "Aquest pas està fet. Faci clic per a bloquejar o posar en marxa."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "This track does not have a description."
msgstr "Aquesta pista no té cap descripció."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                            for publishing on our website."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "Títol"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr "Activitats d'avui"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_22
msgid "Tools for the Woodworking Beginner"
msgstr "Eines per al principiant de la fusta"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_5
msgid "Top 10 Most Expensive Wood in the World"
msgstr "Top 10 de fusta més cara del món"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"Top most expensive wood in the world is quite interesting topic and several people may be surprised\n"
"    that there are hundreds of wood types exist around the globe following different properties and use."
msgstr ""
"La fusta més cara del món és un tema molt interessant i diverses persones poden estar sorpreses\n"
"    que hi ha centenars de tipus de fusta a tot el món seguint diferents propietats i usos."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__track_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "Pista"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "Pista / Enllaç de visitant"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr "Pista bloquejada"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr "Comptador de pistes"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr "Data de la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date_end
msgid "Track End Date"
msgstr "Data de finalització de la pista"

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Track Locations"
msgstr "Ubicacions de la pista"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Track Proposals Menu Item"
msgstr "Element del menú Pressupostos del Track"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr "Pista preparada"

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr "Pista preparada per a la següent etapa"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr "Pista i escenari"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr "Pistes i estadístiques"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_tag_category_action
#: model:ir.ui.menu,name:website_event_track.event_track_tag_category_menu
msgid "Track Tag Categories"
msgstr "Categories d'etiquetes de la pista"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Track Tag Category"
msgstr "Categoria d'etiquetes de la pista"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr "Etiquetes de la pista"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_list
msgid "Track Tags Category"
msgstr "Categoria de les etiquetes de les pistes"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_form
msgid "Track Visitor"
msgstr "Seguiment dels visitants"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_visitor_action
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_track_visitor_ids
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_visitor_ids
#: model:ir.ui.menu,name:website_event_track.event_track_visitor_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_list
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Track Visitors"
msgstr "Seguiment de visitants"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"Track Visitors store statistics on your events, including how many times "
"tracks have been wishlisted."
msgstr ""
"Seguiu els visitants i emmagatzemeu estadístiques sobre els vostres "
"esdeveniments, incloent-hi quantes vegades s'han llistat les pistes "
"desitjades."

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr "Pista bloquejada"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_form
msgid "Tracks"
msgstr "Pistes"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Tracks Menu Item"
msgstr "Element del menú Pistes"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr "Pistes de pressupostos al lloc web"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define your event schedule. They can be talks, workshops or any "
"similar activity."
msgstr ""
"Les gravacions defineixen la planificació de l'esdeveniment. Poden ser "
"xerrades, tallers o qualsevol activitat similar."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
msgid "Tracks on Website"
msgstr "Pistes al lloc web"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "Unknown"
msgstr "Desconegut"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Unpublished"
msgstr "No publicat"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "Missatges sense llegir"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: code:addons/website_event_track/models/website_visitor.py:0
msgid "Unsupported 'Not In' operation on track wishlist visitors"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Use these simple steps to easily haul LONG lumber in a short box pickup truck.  A dose of carpenter's\n"
"    ingenuity along with a couple boards, a sturdy strap and a few screws are all I use to easily haul\n"
"    long boards from the lumberyard to the Next Level Carpentry shop or jobsite."
msgstr ""
"Utilitzeu aquests passos senzills per transportar fàcilment la fusta llarga en un camió de recollida curt.  Una dosi de fuster\n"
"    l'enginy juntament amb un parell de taulers, una corretja robusta i uns quants cargols són tot el que utilitzo per transportar fàcilment\n"
"    taulons llargs des de la llenya fins a la botiga d'automòbils de nivell següent o el lloc de treball."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Using a unique wrapping method for a tie down strap (NOT Bungee cords!!!) allows lumber to be\n"
"    cinched securely WITHOUT the need to tie and untie tricky or complicated knots."
msgstr ""
"L'ús d'un únic mètode d'embolcall per a una corretja de desempat (NO cordons Bungee!!!) permet que la fusta sigui\n"
"    Es va esquinçar amb seguretat sense la necessitat de lligar i deslligar nusos complicats o difícils."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr "Visualitza la pista"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "Visible in agenda"
msgstr "Visible a l'agenda"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr "Visible al lloc web actual"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__visitor_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Visitor"
msgstr "Visitant"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_ids
msgid "Visitor Wishlist"
msgstr "Llista de visitants"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.website_visitor_action_from_track
msgid "Visitors Wishlist"
msgstr "Llista de visitants"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_1
msgid "Voice from Customer"
msgstr "Veu del client"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.website_visitor_action_from_track
msgid "Wait for visitors to add this track to their list of favorites"
msgstr ""
"Espera que els visitants afegeixin aquesta pista a la seva llista de "
"preferits"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We could not find any track at this moment."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We could not find any track matching your search for:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr "Exigim als oradors que acceptin un acord en el qual es comprometin a:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                                presentations, from reports on academic and\n"
"                                commercial projects to tutorials and case\n"
"                                studies. As long as the presentation is\n"
"                                interesting and potentially useful to the\n"
"                                audience, it will be considered for\n"
"                                inclusion in the programme."
msgstr ""

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
msgid "We will evaluate your proposition and get back to you shortly."
msgstr "Avaluarem la seva proposta i tornarem a parlar-li en breu."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We're sorry, this event does not accept proposals anymore"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website
msgid "Website"
msgstr "Lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website__app_icon
msgid "Website App Icon"
msgstr "Icona de l'aplicació del lloc web"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr "Menú d'esdeveniments del lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image
msgid "Website Image"
msgstr "Imatge del lloc web"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_menu
msgid "Website Menu"
msgstr "Menú del lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr "L'URL del lloc web"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_visitor
msgid "Website Visitor"
msgstr "Visitant del lloc web"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr "Meta descripció del lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta paraules del lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr "Mata títol del lloc web"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imatge del lloc web"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_10
msgid "Welcome to Day 2"
msgstr "Benvingut al dia 2"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_1
msgid "What This Event Is All About"
msgstr "Sobre què tracta aquest esdeveniment"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "What is your talk about?"
msgstr "De què parles?"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Who will give this talk? We will show this to attendees to showcase your "
"talk."
msgstr ""
"Qui donarà aquesta xerrada? Mostrarem això als assistents per mostrar la "
"vostra xerrada."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_2
msgid "Who's OpenWood anyway?"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Wishlisted By"
msgstr "Llistat per"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_action_from_visitor
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_ids
msgid "Wishlisted Tracks"
msgstr "Pistes llistades"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Wishlists"
msgstr "Llista de preferits"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_7
msgid "Woodworking: How I got started!"
msgstr "Com vaig començar!"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
msgid "You cannot access this page."
msgstr "No podeu accedir a aquesta pàgina."

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid ""
"You have to enable push notifications to get reminders for your favorite "
"tracks."
msgstr ""
"Heu d'habilitar les notificacions emergents per a obtenir recordatoris per a"
" les vostres pistes preferides."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "You're offline!"
msgstr "Estàs fora de línia!"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "a few seconds"
msgstr "uns segons"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "ago"
msgstr "fa"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. \"John Doe was born in...\""
msgstr "Per exemple, \"John Doe va néixer en...\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "e.g. \"Main Conference Room\""
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. \"This talk will be about...\""
msgstr "P. ex. \"Aquesta xerrada serà sobre...\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Get Yours Now!"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "p. ex. Inspirant una xerrada de negocis"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. http://www.example.com"
msgstr "p. ex. http://www.example.com"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "hores"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_timer.js:0
msgid "in %s"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "minutes after Track start"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_confirmation_end_page_hook
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "register to your favorites talks now."
msgstr "registreu-vos a les converses preferides ara."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts in"
msgstr "comença a"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts on"
msgstr "comença el"
