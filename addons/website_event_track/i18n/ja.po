# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Jun<PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"\n"
"    Dear <t t-out=\"object.partner_id.name or object.partner_name or ''\"><PERSON></t><br/>\n"
"    We are pleased to inform you that your proposal <t t-out=\"object.name or ''\">What This Event Is All About</t> has been accepted and confirmed for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
msgstr ""
"\n"
"    <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t>様<br/>\n"
"    あなたが提案した <t t-out=\"object.name or ''\">このイベントのすべて</t> が承認され、イベント <t t-out=\"object.event_id.name or ''\">OpenWoodコレクションオンライン発表会</t>用に確定しました。\n"
"    <br/>\n"
"    詳細は以下よりご確認下さい:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            トークを見る\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    宜しくお願い致します。\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/website.py:0
msgid "\"Events App Name\" field is required."
msgstr "イベントアプリ名'フィールドは必須です。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_count
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_count
msgid "# Wishlisted"
msgstr "＃ウィッシュリスト"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "%(name)s from %(company)s"
msgstr "%(name)s %(company)sから"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "%(name)s, %(function)s at %(company)s"
msgstr "%(name)s、%(function)s (%(company)sでの)"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/website.py:0
msgid "%s Events"
msgstr "%sイベント"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/controllers/webmanifest.py:0
msgid "%s Online Events Application"
msgstr "%sオンラインイベントアプリケーション"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "(timezone:"
msgstr "(時間帯:"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_17
msgid "10 DIY Furniture Ideas For Absolute Beginners"
msgstr "絶対的な初心者のための10のDIY家具のアイデア"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_18
msgid "6 Woodworking tips and tricks for beginners"
msgstr "初心者のための木工のヒントとコツ6"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr "<b>メール</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr "<b>電話</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr "<b>提案者</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speaker Biography</b>:"
msgstr "<b>スピーカーの経歴</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr "<b>トーク紹介</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>未公開"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_search
msgid "<i class=\"fa fa-bell mx-2 text-muted\"/> Favorite Talks"
msgstr "<i class=\"fa fa-bell mx-2 text-muted\"/>お気に入りトーク "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "<span class=\"d-none d-md-block\">&amp;bull;</span>"
msgstr "<span class=\"d-none d-md-block\">&amp;bull;</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "<span class=\"fa fa-plus me-1\"/> Schedule Tracks"
msgstr "<span class=\"fa fa-plus me-1\"/>セッションを予定 "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                    <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                    <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                    <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                    <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Introduction</span>\n"
"                                        <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">トーク導入</span>\n"
"                                        <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Title</span>\n"
"                                        <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">トークタイトル</span>\n"
"                                        <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "<span class=\"text-muted\"> tracks</span>"
msgstr "<span class=\"text-muted\">セッション</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid ""
"<span for=\"contact_name\">Name</span>\n"
"                    <span>*</span>"
msgstr ""
"<span for=\"contact_name\">氏名</span>\n"
"                    <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "<span id=\"search_number\" class=\"me-1\">0</span>Results"
msgstr "<span id=\"search_number\" class=\"me-1\">0</span>結果"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content_partner_info
msgid "<span> at </span>"
msgstr "<span> 以下にて:</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<span>New track proposal</span>"
msgstr "<span>新規セッション提案</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<strong>Location:</strong>"
msgstr "<strong>ロケーション:</strong>"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr "コンピューター設計アプリの使い方の技術説明"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""
"セッションのカンバンステータスは、セッションに影響を与える特別な状況を示します。\n"
"*灰色はデフォルトの状況です\n"
"*赤は何かがこのセッションの進行を妨げていることを示します\n"
"*緑はセッションを次のステージに移行させる準備ができていることを示します"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "アクティブ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "活動"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外活動文字装飾"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid ""
"Add a description to help your coworkers understand the meaning and purpose "
"of the stage."
msgstr "同僚が理解できるようステージの意味と目的を説明してください。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Add a description..."
msgstr "説明を追加..."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr "タスクパイプラインに新しいステージを追加します"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid ""
"Add tags to your tracks to help your attendees browse your event web pages."
msgstr "セッションにタグを追加して、参加者がイベントのウェブページを閲覧しやすくしましょう。"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management: tips and tricks from the fields"
msgstr "高度なリード管理:現場からのヒントとコツ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr "高度なレポート"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Agenda"
msgstr "アジェンダ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
msgid "Agenda Color"
msgstr "アジェンダ色"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_main
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "All Talks"
msgstr "すべての講演"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Allow Track Proposals"
msgstr "トラック提案を許可する"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Allow push notifications?"
msgstr "プッシュ通知を許可しますか？"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                            presentation, for publishing on our website."
msgstr ""
"当社のウェブサイトで公開するために、\n"
"　　　　プレゼンテーションのビデオとオーディオの録音を許可します。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlisted_by_default
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Always Wishlisted"
msgstr "常にウィッシュリストに掲載"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr "告知"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
msgid "Application"
msgstr "アプリケーション"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_visitor__is_blacklisted
msgid ""
"As key track cannot be un-favorited, this field store the partner choice to "
"remove the reminder for key tracks."
msgstr ""
"キーセッションをお気に入りから外すことはできないため、このフィールドには、キーセッションのリマインダを削除するパートナーの選択肢が保存されます。"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."
msgstr ""
"聞いたことがあるかもしれませんが、自分で家具を作ることは、実際には思ったほど難しくも複雑でもありません。\n"
"実際、一部のプロジェクトは非常に簡単で、誰でも正常に完了することができます。\n"
"たとえば、古いタイヤでかわいいスツールを作るのは本物のケーキです。\n"
"コーヒーテーブルが必要な場合は、木枠を使って簡単に組み立てることができます。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_1
msgid "Audience"
msgstr "聴衆"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "利用可能なトラックタグ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
msgid "Available on all event Agenda pages"
msgstr "全てのアジェンダページで閲覧可能"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_main
msgid "Back to All Talks"
msgstr "全てのトークに戻る"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Bandy clamp hack"
msgstr "バンディクランプハック"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Biography"
msgstr "略歴"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage1
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage2
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage3
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage4
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage5
msgid "Blocked"
msgstr "不許可"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_confirmation_end_page_hook
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Book your seats to the best talks"
msgstr "最高の講演の席を予約する"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Book your talks"
msgstr "あなたの講演を予約する"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_14
msgid "Building a DIY cabin from the ground up"
msgstr "ゼロからDIYキャビンを構築する"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_url
msgid "Button Target URL"
msgstr "ボタンターゲットURL"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_title
msgid "Button Title"
msgstr "ボタンのタイトル"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_website_cta_live
msgid "CTA button is available"
msgstr "CTAボタンが利用可能です"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "提案を求めます"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__can_publish
msgid "Can Publish"
msgstr "公開可"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "取消済"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Cancelled Stage"
msgstr "取消済ステージ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Categories"
msgstr "カテゴリ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__category_id
msgid "Category"
msgstr "カテゴリー"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_12
msgid "Climate positive"
msgstr "気候変動"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Close"
msgstr "閉じる"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__color
msgid "Color"
msgstr "色"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr "カラーインデクス"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/controllers/event_track.py:0
msgid "Coming soon"
msgstr "近日公開"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Coming soon ..."
msgstr "近日公開 ..."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__company_id
msgid "Company"
msgstr "会社"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_company_name
msgid "Company Name"
msgstr "会社名"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of {{ object.name }}"
msgstr " {{ object.name }}の確認"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "確認済"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_13
#: model_terms:event.track,description:website_event_track.event_7_track_3
msgid ""
"Considering to build a wooden house? Watch this video to find out more "
"information about a construction process and final result. Step by step "
"simple explanation! Interested?"
msgstr ""
"木造住宅の建設をお考えですか?建設プロセスと最終結果の詳細については、このビデオをご覧ください。ステップバイステップの簡単なご説明をします!ご興味ありますか？"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
msgid "Contact"
msgstr "連絡先"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Contact Details"
msgstr "コンタクトの詳細"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_email
msgid "Contact Email"
msgstr "連絡先Eメール"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact Information"
msgstr "連絡先情報"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_phone
msgid "Contact Phone"
msgstr "連絡先電話番号"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact me through a different email/phone"
msgstr "別のEメール/電話から連絡をする"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Create a Track"
msgstr "セッションを作成"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid "Create a Track Location"
msgstr "セッションロケーションを作成"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid "Create a Track Tag"
msgstr "セッションタグを作成"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Customer"
msgstr "顧客"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_19
msgid "DIY Timber Cladding Project"
msgstr "DIY木材クラッディングプロジェクト"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PROPOSAL PAGES "
"OF ALL EVENTS"
msgstr "ビルディングブロックをここにドロップすると、全てのイベントの全ての提案ページで利用できるようになります。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "日付"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_11
msgid "Day 2 Wrapup"
msgstr "2日目のまとめ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_8
msgid "Dealing with OpenWood Furniture"
msgstr "OpenWood家具の取り扱い"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Define labels explaining kanban state management."
msgstr "かんばんステータス管理を説明するラベルを定義します。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_location__sequence
msgid "Define the order in which the location will appear on \"Agenda\" page"
msgstr " \"アジェンダ\" ページに表示されるロケーションの順番を定義します。"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."
msgstr ""
"トラックの作成からトラックの終了まで、イベントで使用されるステップを定義します。\n"
"これらのステージを使用して、イベントセッションの解決の進捗状況を追跡します。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "削除"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__description
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Description"
msgstr "説明"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_tag_line
msgid "Description of the partner (name, function and company name)"
msgstr "取引先の定義 (名前、職位、会社名)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr "デザインコンテスト(午後全 体)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr "デザインコンテスト(終日)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr "新製品の詳細なロードマップ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr "新しいデザインチームをご覧ください"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta
msgid ""
"Display a Call to Action button to your Attendees while they watch your "
"Track."
msgstr "セッション視聴中に行動を促すボタンを表示しましょう。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
msgid "Done"
msgstr "完了"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Dowel Hack"
msgstr "ダウエルハック"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "期間"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_3
msgid "Easy Way To Build a Wooden House"
msgstr "木造住宅を建てる簡単な方法"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "トラックの編集"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Email"
msgstr "メール"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr "メールテンプレート"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Error"
msgstr "エラー"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "イベント"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "イベントの場所"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
msgid "Event Locations"
msgstr "イベントの場所"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Event Page"
msgstr "イベントページ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track_proposal
msgid "Event Proposals Menus"
msgstr "イベント提案メニュー"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Template"
msgstr "イベントテンプレート"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "イベントセッション"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr "イベントセッションの場所"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr "イベントセッションステージ"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "イベントセッションタグ"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag_category
msgid "Event Track Tag Category"
msgstr "イベントセッションタグカテゴリ"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "イベントセッション"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track
msgid "Event Tracks Menus"
msgstr "イベントセッションメニュー"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_21
msgid "Event Wrapup"
msgstr "イベントのまとめ"

#. module: website_event_track
#: model:mail.template,name:website_event_track.mail_template_data_track_confirmation
msgid "Event: Track Confirmation"
msgstr "イベント: セッション確認"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,field_description:website_event_track.field_website__events_app_name
msgid "Events App Name"
msgstr "イベントアプリ名"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Events PWA"
msgstr "イベントPWA"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
msgid "Favorite On"
msgstr "お気に入りに"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Favorites"
msgstr "お気に入り"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Filter Tracks"
msgstr "セッションをフィルタ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Filter by Tags"
msgstr "タグごとのフィルタ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Filters"
msgstr "フィルタ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Finished"
msgstr "完了"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_2
msgid "First Day Wrapup"
msgstr "初日のまとめ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr "かんばんで折り込む"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_2
msgid "Format"
msgstr "フォーマット"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_fully_accessible
msgid "Fully accessible"
msgstr "フルアクセス可能"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr "今後の活動"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_confirmation_end_page_hook
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Get prepared and"
msgstr "準備して"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Glue tip"
msgstr "接着剤の先端"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__done
msgid "Green"
msgstr "緑"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_done
msgid "Green Kanban Label"
msgstr "グリーンかんばんラベル"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__normal
msgid "Grey"
msgstr "グレー"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "グレーかんばんラベル"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "グループ化"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_11
msgid "Happy with OpenWood"
msgstr "OpenWoodに満足"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__2
msgid "High"
msgstr "高"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__3
msgid "Highest"
msgstr "最高"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Home page"
msgstr "ホームページ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr "世界文化の家"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "How can our team get in touch with you?"
msgstr "当社のチームからどのように連絡を取ればよろしいでしょうか？"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr "競争の激しい環境でマーケティング戦略を構築する方法"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr "コミュニティとのコミュニケーション方法"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr "新しい家具をデザインする方法"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr "自動化されたプロセスを開発する方法"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr "ソーシャルメディアで私たちをフォローする方法"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr "品質プロセスを改善する方法"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr "家具にハードウェア素材を統合する方法"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr "リードから受注まで、販売を最適化する方法"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__id
msgid "ID"
msgstr "ID"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外活動を示すアイコン"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_fully_accessible
msgid ""
"If checked, automatically publish tracks so that access links to customers "
"are provided."
msgstr "チェックした場合、自動的にセッションを公開し、顧客へのアクセスリンクを提供します。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "If checked, the related tracks will be visible in the frontend."
msgstr "チェックすると、関連するセッションがフロントエンドに表示されます。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr "設定すると、セッションがこの段階に到達したときにメールが顧客に送信されます。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__wishlisted_by_default
msgid ""
"If set, the talk will be set as favorite for each attendee registered to the"
" event."
msgstr "設定された場合、イベントに登録された参加者ごとにトークがお気に入りに設定されます。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image_url
msgid "Image URL"
msgstr "画像URL"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "In"
msgstr ":"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage1
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage2
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage3
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage4
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage5
msgid "In Progress"
msgstr "進行中"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_15
msgid "In this video we will see how lumber is made in a sawmill factory."
msgstr "このビデオでは、製材工場で製材がどのように行われるかを見ていきます。"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "In this video, I covered 6 tips and tricks to help out beginners:"
msgstr "このビデオでは、初心者を支援するための6つのヒントとコツを取り上げました。"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
msgid "Install"
msgstr "インストール"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
msgid "Install Application"
msgstr "アプリケーションをインストールする"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Interactivity"
msgstr "交流"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "入門"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_website_cta_live
msgid "Is CTA Live"
msgstr "CTAライブですか"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is Published"
msgstr "公開済"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_reminder_on
msgid "Is Reminder On"
msgstr "リマインダはオンか"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_done
msgid "Is Track Done"
msgstr "セッションが完了しました"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_live
msgid "Is Track Live"
msgstr "セッションが現在進行中ですか"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_soon
msgid "Is Track Soon"
msgstr "セッションまでもうすぐです"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_today
msgid "Is Track Today"
msgstr "セッションは本日です"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_upcoming
msgid "Is Track Upcoming"
msgstr "セッションは今後予定されています"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_wishlisted
msgid "Is Wishlisted"
msgstr "ウィッシュリスト登録済"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_blacklisted
msgid "Is reminder off"
msgstr "リマインダーはオフか"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_function
msgid "Job Position"
msgstr "職位"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Job Title"
msgstr "役職"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "かんばん ブロック説明"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "かんばん 進行中の説明"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr "かんばん状態"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state_label
msgid "Kanban State Label"
msgstr "かんばんステータスラベル"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_done
msgid "Kanban Valid Explanation"
msgstr "かんばん 有効な説明"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr "私たちの家具を販売する主な成功要因"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_9
msgid "Kitchens for the Future"
msgstr "未来のキッチン"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr "遅れた活動"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr "最新トレンド"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_26
msgid "Less Furniture is More Furniture"
msgstr "家具が少ないほど家具が多い"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_4
msgid "Life at Home Around the World: William’s Story"
msgstr "世界中の家庭での生活:ウィリアムの物語"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Lightning Talks"
msgstr "フラッシュトーク"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_19
msgid ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"
msgstr "Q＆Aへのリンクはこちら'それらの古いブロック壁を隠す時が来ました。このようなシンプルで変革型のプロジェクトが大好きです' :)-"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Live"
msgstr "有効"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Live Now"
msgstr "今を生きる"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_10
msgid "Live Testimonial"
msgstr "ライブ参加者の声"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_25
msgid "Live Testimonials"
msgstr "ライブ参加者の声"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Location"
msgstr "ロケーション"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_13
msgid "Log House Building"
msgstr "ログハウスビル"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_15
msgid "Logs to lumber"
msgstr "材木への丸太"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__0
msgid "Low"
msgstr "低"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "ランチ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta
msgid "Magic Button"
msgstr "マジックボタン"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Making a center marking gauge"
msgstr "センターマーキングゲージの作成"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid ""
"Manage from here the places where you organize your tracks (e.g. Rooms, "
"Channels, ...)."
msgstr "セッションを整理する場所はここから管理できます (例: 部屋、チャンネル、...)"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__1
msgid "Medium"
msgstr "中"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "メニュータイプ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr "最小限だが効率的な設計"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_start_remaining
msgid "Minutes before CTA starts"
msgstr "CTA開始の数分前"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_remaining
msgid "Minutes before track starts"
msgstr "トラック開始の数分前"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_relative
msgid "Minutes compare to track start"
msgstr "分はトラック開始と比較します"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Miter saw tip"
msgstr "マイターソーチップ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "モーニングブレーク"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動期限"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr "自分の会社のグローバルプレゼンテーション"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Tracks"
msgstr "マイトラック"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__name
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name"
msgstr "名称"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name of your website's Events Progressive Web Application"
msgstr "ウェブサイトのイベントの名前プログレッシブWebアプリケーション"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr "新しい認証プログラム"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "新規セッション"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動概要"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid "No Track Visitors yet!"
msgstr "まだ表示可能なセッションがありません！"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_action_from_visitor
msgid "No track favorited by this visitor"
msgstr "この訪問者がお気に入りに登録したセッションはありません。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "No track found."
msgstr "セッションが見つかりません。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_tag__color
msgid "Note that colorless tags won't be available on the website."
msgstr "無色のタグはウェブサイトでは利用できないことに注意してください。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_24
msgid "Old is New"
msgstr "古いものは新しい"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_20
msgid "Our Last Day Together!"
msgstr "一緒に行う最終日です！"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__partner_id
msgid "Partner"
msgstr "取引先"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr "パートナーシッププログラム"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Phone"
msgstr "電話"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Picture"
msgstr "画像"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Plan your experience by adding your favorites talks to your wishlist"
msgstr "お気に入りの講演をウィッシュリストに追加して、参加の計画を立てましょう。"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
msgid "Please enter either a contact email address or a contact phone number."
msgstr "連絡先Eメールアドレスまたは連絡先電話番号のいずれかを入力して下さい。"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
msgid "Please fill out the form correctly."
msgstr "フォームに正しく記入して下さい。"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr "ポートフォリオのプレゼンテーション"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_16
msgid "Pretty. Ugly. Lovely."
msgstr "可愛い。醜い。美しい。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Previous page"
msgstr "前のページ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "優先度"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_7
msgid ""
"Probably one of the most asked questions I've gotten is how I got started "
"woodworking! In this video I share with you how/why I started building "
"furniture!"
msgstr "おそらく私が得た最もよくある質問の1つは、私が木工を始めた方法です'このビデオでは、私が家具を作り始めた方法/理由をあなたと共有します'"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr "提案"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed."
msgstr "提案は締切りました。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr "ウェブサイトでの提案"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Published"
msgstr "公開済"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr "顧客から良質なインサイトを得る"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage1
#: model:event.track.stage,legend_done:website_event_track.event_track_stage2
#: model:event.track.stage,legend_done:website_event_track.event_track_stage3
#: model:event.track.stage,legend_done:website_event_track.event_track_stage4
#: model:event.track.stage,legend_done:website_event_track.event_track_stage5
msgid "Ready for Next Stage"
msgstr "次ステージへの準備済"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__blocked
msgid "Red"
msgstr "赤"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "レッドかんばんビュー"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr "否認"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Regular Talks"
msgstr "通常トーク"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_relative
msgid "Relative time compared to track start (seconds)"
msgstr "トラック開始と比較した相対時間(秒)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta_start_remaining
msgid "Remaining time before CTA starts (seconds)"
msgstr "CTAが開始するまでの残り時間(秒)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_remaining
msgid "Remaining time before track starts (seconds)"
msgstr "トラック開始までの残り時間(秒)"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "担当者"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "担当ユーザ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_23
msgid "Restoring Old Woodworking Tools"
msgstr "古い木工ツールの復元"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_23
msgid "Restoring old woodworking tools"
msgstr "古い木工ツールの復元"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Right angle clamp jig"
msgstr "直角クランプ治具"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO最適化済"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Search a talk ..."
msgstr "トークを検索 ..."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_6
msgid "Securing your Lumber during transport"
msgstr "輸送中の木材の保護"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
msgid "Select Categories"
msgstr "カテゴリを選択"

#. module: website_event_track
#: model:mail.template,description:website_event_track.mail_template_data_track_confirmation
msgid ""
"Sent to speakers whose track proposal is accepted (set the template on the "
"right stage)"
msgstr "セッション提案が受理されたスピーカーに送付 (右ステージにテンプレートを設定)"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__seo_name
msgid "Seo name"
msgstr "SEO名"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
msgid "Set Favorite"
msgstr "お気に入りを設定"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_delay
msgid "Show Button"
msgstr "ボタンを表示"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Showcase Tracks"
msgstr "トラック紹介"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Speaker"
msgstr "スピーカー"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker Bio"
msgstr "スピーカー経歴"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "Speaker Email"
msgstr "スピーカーEメール"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Speaker Photo"
msgstr "スピーカー写真"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker Profile"
msgstr "スピーカー経歴"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
msgid "Speakers"
msgstr "スピーカー"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr "ステージ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "ステージ説明およびツールチップ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr "ステージ名"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "Starting now!"
msgstr "今始まります！"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr "ステータスと戦略"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づくステータス\n"
"遅延: 期限が既に過ぎています\n"
"今日: 活動日は今日です\n"
"予定: 将来の活動。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "提出契約"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "提案を提出する"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_tag_line
msgid "Tag Line"
msgstr "タグ明細"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag Name"
msgstr "タグ名"

#. module: website_event_track
#: model:ir.model.constraint,message:website_event_track.constraint_event_track_tag_name_uniq
msgid "Tag name already exists!"
msgstr "タグ名がすでに存在します！"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Tags"
msgstr "タグ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Intro"
msgstr "トークイントロ"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
msgid "Talk Proposals"
msgstr "トークプロポーザル"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Talk added to your Favorites"
msgstr "お気に入りにトークを追加"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Talk already in your Favorites"
msgstr "あなたのお気に入りですでに話してください"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid "Talk removed from your Favorites"
msgstr "お気に入りから削除されたトーク"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Talks"
msgstr "講演"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "講演タイプ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "タスク進行中。ブロックまたは完了済にするにはクリックして下さい。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr "タスクはブロックされています。ブロック解除または完了済にするにはクリックして下さい。"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
msgid "Thank you for your proposal."
msgstr "ご提案ありがとうございます。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr "サイト経由して、文書にアクセスする完全なURL。"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr "新しいマーケティング戦略"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr "あなたの作品を宣伝する新しい方法"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"There are a lot of ideas worth exploring so start with the 10 DIY furniture "
"ideas for absolute beginners."
msgstr "探求する価値のあるアイデアがたくさんあるので、まったくの初心者のための10のDIY家具のアイデアから始めてください。"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"There are several variants of wood is available in the world but we are talking about most expensive\n"
"    ones in the world and keeping to the point we have arranged ten most expensive wood."
msgstr ""
"世界にはいくつかの種類の木材がありますが、私たちは\n"
"世界で最も高価なものにを扱い、最も高価な10の木材を保持しています。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"These are 30 minutes talks on many different topics. Most topics are "
"accepted in lightning talks."
msgstr "様々なトピックに関する30分の講演です。ほとんどのトピックがフラッシュトークで受け入れられます。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "These are standard talks with slides, alocated in slots of 60 minutes."
msgstr "これらはスライドを使った標準的なトークで、60分の枠に割り当てられてます。"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"They will be created automatically once attendees start browsing your "
"events."
msgstr "参加者があなたのイベントを閲覧し始めると、自動的に作成されます。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_website__app_icon
msgid ""
"This field holds the image used as mobile app icon on the website (PNG "
"format)."
msgstr "このフィールドには、ウェブサイトでモバイルアプリアイコンとして使用される画像(PNG形式)が保持されます。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,help:website_event_track.field_website__events_app_name
msgid "This fields holds the Event's Progressive Web App name."
msgstr "このフィールドは、イベントのプログレッシブWebアプリ名を保持します。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid ""
"This page hasn't been saved for offline reading yet.<br/>Please check your "
"network connection."
msgstr "このページはまだオフラインで読むために保存されていません。<br/>ネットワーク接続を確認してください。"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr "このステージは、そのステージにレコードが表示されていないときに、かんばんのビューで折り込まれます。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "このステップは完了です。ブロックまたは進行中に設定するにはクリックして下さい。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "This track does not have a description."
msgstr "このセッションには説明がありません"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                            for publishing on our website."
msgstr ""
"当社のウェブサイトで公開するためのプレゼンテーション\n"
"　　　　　　　資料(スライド)のタイムリーなリリース。　"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "タイトル"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr "本日の活動"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_22
msgid "Tools for the Woodworking Beginner"
msgstr "木工初心者のためのツール"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_5
msgid "Top 10 Most Expensive Wood in the World"
msgstr "世界で最も高価な木材トップ10"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"Top most expensive wood in the world is quite interesting topic and several people may be surprised\n"
"    that there are hundreds of wood types exist around the globe following different properties and use."
msgstr ""
"世界で最も高価な木材は非常に興味深いトピックであり、さまざまな特性と用途に従って\n"
"世界中に何百もの木材タイプが存在することに驚く人もいるかもしれません。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__track_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "追跡"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "セッション/ビジターリンク"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr "トラックがブロックされました"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr "トラック数"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr "トラック日"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date_end
msgid "Track End Date"
msgstr "終了日を追跡する"

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Track Locations"
msgstr "場所を追跡する"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Track Proposals Menu Item"
msgstr "セッション提案メニュー項目"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr "トラックレディ"

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr "次のステージに向けてトラックの準備ができました"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr "トラックステージ"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr "トラックステージ"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_tag_category_action
#: model:ir.ui.menu,name:website_event_track.event_track_tag_category_menu
msgid "Track Tag Categories"
msgstr "タグカテゴリを追跡する"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Track Tag Category"
msgstr "トラックタグカテゴリ"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr "トラックタグ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_list
msgid "Track Tags Category"
msgstr "トラックタグカテゴリ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_form
msgid "Track Visitor"
msgstr "セッション訪問者を追跡する"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_visitor_action
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_track_visitor_ids
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_visitor_ids
#: model:ir.ui.menu,name:website_event_track.event_track_visitor_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_list
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Track Visitors"
msgstr "セッション訪問者"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"Track Visitors store statistics on your events, including how many times "
"tracks have been wishlisted."
msgstr "ウィッシュリストに登録されたセッション数など、イベントに関する統計情報をセッション訪問者が保存します。"

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr "トラックがブロックされました"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_form
msgid "Tracks"
msgstr "トラック"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Tracks Menu Item"
msgstr "セッションメニュー項目"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr "ウェブサイトで提案を追跡する"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define your event schedule. They can be talks, workshops or any "
"similar activity."
msgstr "セッションはイベントのスケジュールを定義します。講演、ワークショップ、その他類似の活動があります。"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
msgid "Tracks on Website"
msgstr "ウェブサイト上のトラック"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外活動の種類。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "Unknown"
msgstr "不明"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Unpublished"
msgstr "未公開"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: code:addons/website_event_track/models/website_visitor.py:0
msgid "Unsupported 'Not In' operation on track wishlist visitors"
msgstr "セッションのお気に入り訪問者に対する'含まれていない(Not In)' 操作はサポートされていません。"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Use these simple steps to easily haul LONG lumber in a short box pickup truck.  A dose of carpenter's\n"
"    ingenuity along with a couple boards, a sturdy strap and a few screws are all I use to easily haul\n"
"    long boards from the lumberyard to the Next Level Carpentry shop or jobsite."
msgstr ""
"これらの簡単な手順を使用して、短いボックスのピックアップトラックで長い材木を簡単に運搬します。\n"
"いくつかのボード、頑丈なストラップ、数本のネジに加えて、大工の創意工夫を駆使して、\n"
"長いボードを土場から次のレベルの大工店や現場に簡単に運ぶことができます。"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Using a unique wrapping method for a tie down strap (NOT Bungee cords!!!) allows lumber to be\n"
"    cinched securely WITHOUT the need to tie and untie tricky or complicated knots."
msgstr ""
"タイダウンストラップ(バンジーコードではありません!!!)に独自の包装方法を使用すると、\n"
"トリッキーな結び目や複雑な結び目を結び、解く必要なしに、木材をしっかりと締めることができます。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr "トラックを見る"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "Visible in agenda"
msgstr "アジェンダに表示"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr "現在のサイトに表示"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__visitor_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Visitor"
msgstr "訪問者"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_ids
msgid "Visitor Wishlist"
msgstr "ビジターウィッシュリスト"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.website_visitor_action_from_track
msgid "Visitors Wishlist"
msgstr "訪問者のウィッシュリスト"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_1
msgid "Voice from Customer"
msgstr "お客様からの声"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.website_visitor_action_from_track
msgid "Wait for visitors to add this track to their list of favorites"
msgstr "訪問者がこのセッションをお気に入りリストに加えるのを待ちましょう"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We could not find any track at this moment."
msgstr "現在のセッションが見つかりませんでした。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We could not find any track matching your search for:"
msgstr "以下の検索に一致するセッションが見つかりませんでした:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr "講演者は、次のことを約束する合意に同意する必要があります。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                                presentations, from reports on academic and\n"
"                                commercial projects to tutorials and case\n"
"                                studies. As long as the presentation is\n"
"                                interesting and potentially useful to the\n"
"                                audience, it will be considered for\n"
"                                inclusion in the programme."
msgstr ""
"学術・商業プロジェクトのレポートからチュートリアルやケーススタディまで、\n"
"幅広いプレゼンテーションを受け付けます。\n"
"プレゼンテーションが興味深く、聴衆にとって潜在的に有用である限り、\n"
"プログラムに含めることが検討されます。"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
msgid "We will evaluate your proposition and get back to you shortly."
msgstr "私たちはあなたの提案を評価し、すぐにあなたに連絡します。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We're sorry, this event does not accept proposals anymore"
msgstr "申し訳ございませんが、このイベントは提案を受け付けておりません。"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website
msgid "Website"
msgstr "ウェブサイト"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website__app_icon
msgid "Website App Icon"
msgstr "ウェブサイトアプリアイコン"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr "ウェブサイトイベントメニュー"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image
msgid "Website Image"
msgstr "ウェブサイトの画像"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_menu
msgid "Website Menu"
msgstr "ウェブサイトメニュー"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr "サイトURL"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_visitor
msgid "Website Visitor"
msgstr "サイト訪問者"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr "サイトメタディスクリプション"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr "サイトメタキーワード"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr "サイトメタタイトル"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr "サイトopengraph画像"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_10
msgid "Welcome to Day 2"
msgstr "2日目へようこそ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_1
msgid "What This Event Is All About"
msgstr "このイベントとは"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "What is your talk about?"
msgstr "あなたのトークは何についてですか？"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Who will give this talk? We will show this to attendees to showcase your "
"talk."
msgstr "このトークは誰が行うのですか？あなたのトークを紹介するために、参加者にこれをお見せします。"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_2
msgid "Who's OpenWood anyway?"
msgstr "そもそもOpenWoodとは？"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Wishlisted By"
msgstr "ウィッシュリスト登録者"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_action_from_visitor
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_ids
msgid "Wishlisted Tracks"
msgstr "ウィッシュリストにあるセッション"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Wishlists"
msgstr "ウィッシュリスト"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_7
msgid "Woodworking: How I got started!"
msgstr "木工:どうやって始めたの'"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
msgid "You cannot access this page."
msgstr "このページにアクセスできません。"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
msgid ""
"You have to enable push notifications to get reminders for your favorite "
"tracks."
msgstr "お気に入りのセッションのリマインダを受け取るには、プッシュ通知を有効にして下さい。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "You're offline!"
msgstr "あなたはオフラインです'"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "a few seconds"
msgstr "数秒"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "ago"
msgstr "前"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. \"John Doe was born in...\""
msgstr "例: \"ジョン・ドーの生まれは...\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "e.g. \"Main Conference Room\""
msgstr "例: \"メイン大会議室\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. \"This talk will be about...\""
msgstr "例: \"このトークは以下についてです...\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Get Yours Now!"
msgstr "例: 今すぐ入手！"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "例:刺激的なビジネストーク"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. http://www.example.com"
msgstr "例:http://www.example.com"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "時間"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_timer.js:0
msgid "in %s"
msgstr "%sで"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "minutes after Track start"
msgstr "分後(セッション開始から)"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_confirmation_end_page_hook
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "register to your favorites talks now."
msgstr "今すぐお気に入りのトークに登録してください。"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts in"
msgstr "で始まる"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts on"
msgstr "に始まります"
