# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2025\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr " Data Gətirildi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "# Satış Sifarişləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "Xətlərin #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "Satış Sifarişlərinin #"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "%(attribute)s: %(values)s"
msgstr "%(attribute)s: %(values)s"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "%s has been created"
msgstr "%s yaradıldı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'PRO-FORMA - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Təklif - %s' % (object.name)) or "
"'Sifariş - %s' % (object.name)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "2023-12-31"
msgstr "31-12-2023"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "27.00"
msgstr "27.00"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "31.05"
msgstr "31.05"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr "<b>Qiyməti özünüzə göndərin</b> və müştərinin nə alacağını yoxlayın."

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_payment_executed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        A payment with reference\n"
"        <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        amounting\n"
"        <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"        for your order\n"
"        <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"        <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            is pending.\n"
"            <br/>\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Your order will be confirmed once the payment is confirmed.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Once confirmed,\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"                will remain to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            has been confirmed.\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"                <br/>\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">$ 10.00</span>\n"
"                remains to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Thank you for your trust!\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Salam,\n"
"        <br/><br/>\n"
"        Aşağıdakı istinad nömrəsi ilə olan ödəniş\n"
"        <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        məbləği\n"
"        <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"        sizin sifarişiniz üçün\n"
"        <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"        <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            gözləmədədir.\n"
"            <br/>\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Sifarişiniz ödəniş təsdiqləndikdən sonra təsdiqlənəcək.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Təsdiqləndikdən sonra,\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"                məbləği ödənilməmiş qalacaq.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            təsdiqlənmişdir.\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"                <br/>\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">$ 10.00</span>\n"
"                məbləği hələ də ödənilməlidir.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Etimadınız üçün təşəkkür edirik!\n"
"        <br/>\n"
"        Hər hansı bir sualınız olarsa, bizimlə əlaqə saxlamaqdan çəkinməyin.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"         "

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Your order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse; white-space: nowrap;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Products</span></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Quantity</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            Tax Excl.\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Tax Incl.\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-set=\"line_subtotal\" t-value=\"                     line.price_subtotal                     if object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'                     else line.price_total                 \"/>\n"
"            <t t-set=\"current_subtotal\" t-value=\"current_subtotal + line_subtotal\"/>\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and (                     line.display_type in ['line_section', 'line_note']                     or line.product_type == 'combo'                 )\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section' or line.product_type == 'combo'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Taking care of Trees Course</span>\n"
"                                <t t-set=\"current_section\" t-value=\"line\"/>\n"
"                                <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold; white-space: nowrap;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-if=\"current_section and (                     line_last                     or object.order_line[line_index+1].display_type == 'line_section'                     or object.order_line[line_index+1].product_type == 'combo'                     or (                         line.combo_item_id                         and not object.order_line[line_index+1].combo_item_id                     )                 ) and not line.is_downpayment\">\n"
"                <t t-set=\"current_section\" t-value=\"None\"/>\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 100%\" align=\"right\">\n"
"                            <span style=\"font-weight: bold;\">Subtotal:</span>\n"
"                            <span t-out=\"format_amount(current_subtotal, object.currency_id) or ''\">$ 10.00</span>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Delivery:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Taxes:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Total:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Bill to:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Payment Method:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <span style=\"font-weight:bold;\">Ship to:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Shipping Method:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Shipping Description:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"/>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Salam,\n"
"        <br/><br/>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Sizin sifarişiniz <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> məbləği <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            təsdiqlənib.<br/>\n"
"            Etibarınıza görə təşəkkür edirik!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            gözləmədədir. Ödəniş alındıqdan sonra təsdiqlənəcək.\n"
"            <t t-if=\"object.reference\">\n"
"                Ödəniş referansınız <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents) &gt; 1\">\n"
"                Sizin üçün maraqlı ola biləcək əlavə sənədlər:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Sizin üçün maraqlı ola biləcək əlavə sənəd:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Hər hansı bir sualınız olarsa, bizimlə əlaqə saxlamaqdan çəkinməyin.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"    <t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"        <div style=\"margin: 0px; padding: 0px;\">\n"
"            <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse; white-space: nowrap;\">\n"
"                <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                    <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Məhsullar</span></td>\n"
"                    <td/>\n"
"                    <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Miqdar</span></td>\n"
"                    <td width=\"20%\" align=\"right\">\n"
"                        <span style=\"font-weight:bold;\">\n"
"                            <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                                Vergi daxil olmayan\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                Vergi daxil olan\n"
"                            </t>\n"
"                        </span>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"            <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"            <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"                <t t-set=\"line_subtotal\" t-value=\"line.price_subtotal if object.website_id.show_line_subtotals_tax_selection == 'tax_excluded' else line.price_total\"/>\n"
"                <t t-set=\"current_subtotal\" t-value=\"current_subtotal + line_subtotal\"/>\n"
"                <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and (line.display_type in ['line_section', 'line_note'] or line.product_type == 'combo')\">\n"
"                    <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                        <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                            <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                            <td colspan=\"4\">\n"
"                                <t t-if=\"line.display_type == 'line_section' or line.product_type == 'combo'\">\n"
"                                    <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Ağaclara Qulluq Kursu</span>\n"
"                                    <t t-set=\"current_section\" t-value=\"line\"/>\n"
"                                    <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"                                </t>\n"
"                                <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                    <i t-out=\"line.name or ''\">Ağaclara Qulluq Kursu</i>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </t>\n"
"                <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                    <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                        <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                            <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                            <td style=\"width: 150px;\">\n"
"                                <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                            </td>\n"
"                            <td align=\"left\" t-out=\"line.product_id.name or ''\">Ağaclara Qulluq Kursu</td>\n"
"                            <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                            <td width=\"20%\" align=\"right\">\n"
"                                <span style=\"font-weight:bold; white-space: nowrap;\">\n"
"                                    <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                                        <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                                    </t>\n"
"                                    <t t-else=\"\">\n"
"                                        <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                                    </t>\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </t>\n"
"                <t t-if=\"current_section and (line_last or object.order_line[line_index+1].display_type == 'line_section' or object.order_line[line_index+1].product_type == 'combo' or (line.combo_item_id and not object.order_line[line_index+1].combo_item_id)) and not line.is_downpayment\">\n"
"                    <t t-set=\"current_section\" t-value=\"None\"/>\n"
"                    <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                        <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                            <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                            <td style=\"width: 100%\" align=\"right\">\n"
"                                <span style=\"font-weight: bold;\">Ara cəmi:</span>\n"
"                                <span t-out=\"format_amount(current_subtotal, object.currency_id) or ''\">$ 10.00</span>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </t>\n"
"            </t>\n"
"        </div>\n"
"        <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"            <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"                <tr>\n"
"                    <td style=\"width: 60%\"/>\n"
"                    <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Çatdırılma:</span></td>\n"
"                    <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"width: 60%\"/>\n"
"                    <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Vergisiz Məbləğ:</span></td>\n"
"                    <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"                </tr>\n"
"            </table>\n"
"        </div>\n"
"        <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"            <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"                <tr>\n"
"                    <td style=\"width: 60%\"/>\n"
"                    <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Vergisiz Məbləğ:</span></td>\n"
"                    <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"                </tr>\n"
"            </table>\n"
"        </div>\n"
"        <div style=\"margin: 0px; padding: 0px;\">\n"
"            <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"                <tr>\n"
"                    <td style=\"width: 60%\"/>\n"
"                    <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Vergilər:</span></td>\n"
"                    <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 10.00</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"width: 60%\"/>\n"
"                    <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Ümumi:</span></td>\n"
"                    <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"                </tr>\n"
"            </table>\n"
"        </div>\n"
"        <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"            <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"                <tr>\n"
"                    <td style=\"padding-top: 10px;\">\n"
"                        <span style=\"font-weight:bold;\">Faktura Ünvanı:</span>\n"
"                        <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                        <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                        <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                        <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                        <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td>\n"
"                        <span style=\"font-weight:bold;\">Ödəniş Metodu:</span>\n"
"                        <t t-if=\"tx_sudo.token_id\">\n"
"                            <t t-out=\"tx_sudo.token_id.display_name or ''\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"/>\n"
"                        </t>\n"
"                        (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </div>\n"
"        <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"            <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"                <tr>\n"
"                    <td>\n"
"                        <br/>\n"
"                        <span style=\"font-weight:bold;\">Çatdırılma Ünvanı:</span>\n"
"                        <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                        <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                        <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                        <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                        <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"            <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"                <tr>\n"
"                    <td>\n"
"                        <span style=\"font-weight:bold;\">Çatdırılma Metodu:</span>\n"
"                        <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                        <t t-if=\"object.amount_delivery == 0.0\">\n"
"                            (Pulsuz)\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                    <td>\n"
"                        <strong>Çatdırılma Təsviri:</strong>\n"
"                        <t t-out=\"object.carrier_id.carrier_description\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </div>\n"
"    </t>\n"
"</div>"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Salam,\n"
"        <br/><br/>\n"
"        Sizin\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            məbləği <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> olan\n"
"            <t t-out=\"doc_name or ''\">təklif</t> üçün Pro forma fakturası <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (istinad: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> artıq mövcuddur.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            məbləği <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> olan\n"
"            <t t-out=\"doc_name or ''\">təklif</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (istinad: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            baxış üçün hazırdır.\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Sizi maraqlandıra biləcək əlavə sənədlər:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Sizi maraqlandıra biləcək əlavə sənəd:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Əlavə suallarınız olarsa, bizimlə əlaqə saxlamaqdan çəkinməyin.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_cancellation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"/>\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">user</t>,\n"
"        <br/><br/>\n"
"        Please be advised that your\n"
"        <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        has been cancelled. Therefore, you should not be charged further for this order.\n"
"        If any refund is necessary, this will be executed at best convenience.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"/>\n"
"        Hörmətli <t t-out=\"object.partner_id.name or ''\">istifadəçi</t>,\n"
"        <br/><br/>\n"
"        Xəbərdar edirik ki, sizin  \n"
"        <t t-out=\"doc_name or ''\">təklifiniz</t> <strong t-out=\"object.name or ''\">S00052</strong>  \n"
"        <t t-if=\"object.origin\">\n"
"            (istinad nömrəsi: <t t-out=\"object.origin or ''\">S00052</t>)\n"
"        </t>  \n"
"        ləğv edilmişdir. Buna görə də, bu sifariş üçün sizdən əlavə ödəniş tutulmayacaq.  \n"
"        Əgər hər hansı bir geri ödəniş tələb olunarsa, bu ən qısa zamanda həyata keçiriləcəkdir.\n"
"        <br/><br/>\n"
"        Hər hansı bir sualınız olarsa, bizimlə əlaqə saxlamaqdan çəkinməyin.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""
"<i class=\"fa fa-comment\"/>Yeni təklif almaq üçün bizimlə əlaqə saxlayın."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/>Rəy</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Authorized"
msgstr "<i class=\"fa fa-fw fa-check\"/>Səlahiyyətli</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check\"/>Ödənişli</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Reversed"
msgstr "<i class=\"fa fa-fw fa-check\"/>Geri çevrildi</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>İstifadə müddəti bitdi</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Waiting Payment"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>Ödəniş Gözləyir</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"
msgstr "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Təkliflər\" role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/>Ləğv edildi</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                    Locked"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                    Bağlanıb"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print me-1\"/>View Details"
msgstr "<i class=\"fa fa-print me-1\"/>Ətraflı Bax"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/>Rədd edin</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-usd me-1\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-usd me-1\" role=\"img\" aria-label=\"Satış sifarişi\" "
"title=\"Satış sifarişləri\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small class=\"text-muted\">Your contact</small>"
msgstr "<small class=\"text-muted\">Sizin əlaqəniz</small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">Sizin üstünlüyünüz</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Satış Sifarişi #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning me-1\" title=\"This "
"product is archived\" invisible=\"state not in ['draft', 'sent'] or not "
"is_product_archived\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning me-1\" title=\"This "
"product is archived\" invisible=\"state not in ['draft', 'sent'] or not "
"is_product_archived\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"flex-grow-1\">/ Month</span>"
msgstr "<span class=\"flex-grow-1\">/ Ay</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span class=\"mx-3\" invisible=\"not require_payment\">of</span>"
msgstr "<span class=\"mx-3\" invisible=\"not require_payment\"></span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Satıldı</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                                By paying,\n"
"                                            </span>"
msgstr ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                                Bunu ödəyərək, \n"
"                                            </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"1\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"
msgstr ""
"<span invisible=\"1\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\">% </span>"
msgstr ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\">%</span>"

#. module: sale
#: model_terms:web_tour.tour,rainbow_man_message:sale.sale_tour
msgid ""
"<span><b>Congratulations</b>, your first quotation is sent!<br>Check your email to validate the quote.\n"
"        </span>"
msgstr ""
"<span><b>Təbrik edirik</b> , ilk təklifiniz göndərildi!<br> Təklifi "
"təsdiqləmək üçün e-poçtunuzu yoxlayın.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>Məbləğ</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid ""
"<span>Are you sure you want to cancel this order? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Draft invoices for this order will be cancelled. <br/>\n"
"                        </span>"
msgstr ""
"<span>Bu sifarişi ləğv etmək istədiyinizə əminsiniz? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Bu sifariş üçün qaralama hesab-fakturalar ləğv ediləcək.<br/>\n"
"                        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>Endirim%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_kanban
msgid "<span>Sales visibility</span>"
msgstr "<span>Satış görünümlüyü</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Vergi</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: This quote contains archived product(s)</span>"
msgstr ""
"<span>Xəbərdarlıq: Bu təklifdə arxivləşdirilmiş məhsul(lar) var</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: this order might be a duplicate of</span>"
msgstr "<span>Xəbərdarlıq: bu sifarişin dublikat ola bilər => </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong class=\"d-block mt-3\">Shipping Address</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Yekun</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration</strong>"
msgstr "<strong>Bitmə tarixi:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Maliyyə Mövqeyi Qeyd:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson</strong>"
msgstr "<strong>Satış Meneceri</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>İmza</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>Təşəkkür edirik!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>Bu təklifin müddəti bitdi!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been cancelled.</strong>"
msgstr "<strong>Bu təklif ləğv edilib.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference</strong>"
msgstr "<strong>Sizin Referans</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "Təsdiqlənmiş satış sifarişi təsdiqləmə tarixini tələb edir."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "A line on these orders missing a product, you cannot confirm it."
msgstr "Bu sifarişlərdə məhsul yoxdur, siz onu təsdiq edə bilməzsiniz."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A note, whose content usually applies to the section or product above."
msgstr "Adətən yuxarıdakı bölməyə və ya məhsula aid olan qeyd."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"A sale order line's combo item must be among its linked line's available "
"combo items."
msgstr ""
"Satış sifarişi xəttinin kombo elementi onun əlaqəli xəttin mövcud kombo "
"elementləri arasında olmalıdır."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "A sale order line's product must match its combo item's product."
msgstr ""
"Satış sifarişi xəttinin məhsulu onun kombo elementinin məhsulu ilə uyğun "
"olmalıdır."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A section title"
msgstr "Bölmə başlığı"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for "
"invoicing,according to their invoicing policy (based on ordered or delivered"
" quantity)."
msgstr ""
"Standart hesab-faktura, faktura siyasətinə uyğun olaraq (sifariş edilmiş və "
"ya çatdırılan kəmiyyətə əsaslanaraq) faktura üçün hazır olan bütün sifariş "
"xətləri ilə verilir."

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "Xəbərdarlıq məhsula və ya müştəriyə təyin edilə bilər (Satış)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Satış sifarişlərində paket növünü seçmək və paket başına vahidlərin sayından"
" çox olan kəmiyyəti məcbur etmək bacarığı."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Pay Quotation"
msgstr "Təklifi qəbul et və ödə"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Sign Quotation"
msgstr "Təklifi qəbul et və imzala"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "Qəbul et &amp; Ödəmək"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "Qəbul et &amp; İmza"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "Giriş xəbərdarlığı"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Məhsulun konfiqurasiyasına uyğun olaraq, çatdırılan miqdar avtomatik olaraq mexanizmlə hesablana bilər:\n"
"  - Manual: kəmiyyət sətirdə əl ilə təyin edilir\n"
"  - Xərclərdən analitik: kəmiyyət dərc edilmiş xərclərdən alınan kəmiyyət məbləğidir\n"
"  - Vaxt cədvəli: kəmiyyət bu satış sətiri ilə əlaqəli tapşırıqlar üzrə qeydə alınan saatların cəmidir\n"
"  - Anbar hərəkətləri: miqdar təsdiq edilmiş qəbullardan gəlir\n"

#. module: sale
#: model:ir.model,name:sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Müxabir hesabları Şablonu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Hesab nömrəsi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr "Hesablanmış Gəlir Girişi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Gərəkli Əməliyyat"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
#: model:ir.ui.menu,name:sale.sale_menu_config_activities
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Faəliyyət istisnaetmə İşarəsi"

#. module: sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_plan
msgid "Activity Plans"
msgstr "Fəaliyyət Planları"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Fəaliyyət Statusu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Fəaliyyət Növü ikonu"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "Fəaliyyət Növləri"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                (e.g. \"Delivery scheduling\", \"Order Payment Follow-up\", ...)"
msgstr ""
"Fəaliyyət planları bir neçə kliklə fəaliyyətlərin siyahısını təyin etmək üçün istifadə olunur\n"
"                (məsələn, \"Çatdırılma cədvəli\", \"Sifarişin ödənilməsinin izlənməsi\", ...)"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Add"
msgstr "Əlavə et"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "Yeni qeyd əlavə edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "Məhsul əlavə et"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "Bölmə əlavə et"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add note"
msgstr "Qeydlər əlavə edin"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Add one"
msgstr "Birini əlavə et"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Add optional products"
msgstr "Seçimə bağlı məhsullar əlavə edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add product"
msgstr "Məhsul əlavə edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add section"
msgstr "Bölmə əlavə edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "Bir kataloqdan sifarişə bir neçə variant əlavə edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "Müştərilərinizə Pro-Forma Faktura göndərməyə imkan verir"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "Proforma hesab-faktura göndərməyə imkan verir."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""
"Sənədi satış çərçivəsində müştərilərinizlə paylaşmağa imkan verir.\n"
"Bu sənədi satış müştərisi ilə bölüşmək istəmirsinizsə, onu boş qoyun.\n"
"Sitat üzrə: sənəd istənilən vaxt müştərilərə göndəriləcək və müştərilər tərəfindən əlçatan olacaq.\n"
"məs. bu seçim Məhsul təsviri fayllarını paylaşmaq üçün faydalı ola bilər.\n"
"Sifariş təsdiqləndikdə: sənəd müştərilərə göndəriləcək və müştərilər tərəfindən əlçatan olacaq.\n"
"məs. bu seçim İstifadəçi Təlimatını və ya e-ticarətdən alınmış rəqəmsal məzmunu paylaşmaq üçün faydalı ola bilər. \n"
"Daxili təklif: Sənəd başlıq səhifələri və qiymət cədvəli arasında təklif və satış sifarişinin pdf-ə daxil ediləcək. "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__amount_paid
msgid "Already Paid"
msgstr "Artıq Ödənilib"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_invoiced
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_invoiced
msgid "Already invoiced"
msgstr "Artıq faktura verilib"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "Amazon Sync"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_amount
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Amount"
msgstr "Məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Endirimdən əvvəlki məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "Fakturalanmalı təkliflərin məbləği"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "Amount to invoice"
msgstr "Fakturalanmalı Məbləğ"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"            ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""
"Upsell Sifariş o zaman olur ki, çatdırılan miqdar ilkin sifariş olunan miqdardan çox olur\n"
"            və hesab-faktura siyasəti isə sifariş edilmiş miqdarlara əsaslanır."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analitik bölüşdürmə"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "Xərclərdən Analitik"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitik Sətir"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Analitik Planların Uygulanabilirliği"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Analitik Dəqiqlik"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Analitik Sətirlər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Apply"
msgstr "Tətbiq edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Satış sifarişi xətlərində əl ilə endirimlər tətbiq edin və ya qiymət "
"siyahılarından hesablanmış endirimləri göstərin (qiymət siyahısı "
"konfiqurasiyasında aktivləşdirmək üçün seçim)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the"
msgstr "Ləğv etmək istədiyinizə əminsiniz"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the selected item?"
msgstr "Seçilmiş elementi ləğv etmək istədiyinizə əminsiniz?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Səlahiyyətli tranzaksiyanı ləğv etmək istədiyinizə əminsiniz? Bu əməliyyat "
"geri qaytarıla bilməz."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"            to sell extra hours when all ordered hours have been consumed."
msgstr ""
"Nümunə olaraq, əvvəlcədən ödənilmiş saat xidmətləri satırsınızsa, Odoo sizə tövsiyə edir ki,\n"
"            bütün sifariş edilmiş saatlar tükəndikdən sonra əlavə saatlar satışını edəsiniz."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "Maya dəyərinə"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Atribut Dəyərləri"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Atributlar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__author_id
msgid "Author"
msgstr "Müəllif"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Səlahiyyətli Əməliyyatlar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "Avtomatik faktura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Average"
msgstr "Orta"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Bacon Burger"
msgstr "Bekon Burger"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Bank Adı"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "Müştəri ID-si əsasında"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "Sənəd Referansı əsasında"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Beat competitors with stunning quotations!"
msgstr "Çarpıcı sitatlar ilə rəqibləri məğlub edin!"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "Bloklanmış Mesaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Əsas məzmun şablonla eynidir"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Boost sales with online payments or signatures, upsells, and a great "
"customer portal."
msgstr ""
"Onlayn ödənişlər və ya imzalar, satışlar və əla müştəri portalı ilə "
"satışları artırın."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Satışlarınızı müxtəlif proqramlarla artırın: Kuponlar, Promosyonlar, Hədiyyə"
" Kartı, Abunəlik. Müxtəlif şərtlər təyin oluna bilər  (məhsullar, "
"müştərilər, minimum alış miqdarı, müddət).  Mükafatlar üçün endirim (% və ya"
" miqdar) və ya pulsuz məhsullar təyin oluna bilər."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Build your first quotation right here!"
msgstr "İlk kotirovkanızı elə burada qurun!"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying a <u>down payment</u> of"
msgstr "<u>İlkin ödəniş</u> etməklə"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying,"
msgstr "Ödəyərək,"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By signing, you confirm acceptance on behalf of"
msgstr "İmzalamaqla siz adından qəbul etdiyinizi təsdiq edirsiniz"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "Kampaniya"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__can_edit_body
msgid "Can Edit Body"
msgstr "Məzmunu Redaktə edə bilər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "Məhsulu Redaktə edə bilər"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model:ir.actions.act_window,name:sale.action_mass_cancel_orders
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Ləğv edin"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Cancel %s"
msgstr "%s ləğv edin"

#. module: sale
#: model:ir.model,name:sale.model_sale_mass_cancel_orders
msgid "Cancel multiple quotations"
msgstr "Çoxsaylı təklifləri ləğv edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Cancel quotations"
msgstr "Təklifləri ləğv edin"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Cannot create an invoice. No items are available to invoice.\n"
"\n"
"To resolve this issue, please ensure that:\n"
"   • The products have been delivered before attempting to invoice them.\n"
"   • The invoicing policy of the product is configured correctly.\n"
"\n"
"If you want to invoice based on ordered quantities instead:\n"
"   • For consumable or storable products, open the product, go to the 'General Information' tab and change the 'Invoicing Policy' from 'Delivered Quantities' to 'Ordered Quantities'.\n"
"   • For services (and other products), change the 'Invoicing Policy' to 'Prepaid/Fixed Price'.\n"
msgstr ""
"Faktura yaratmaq mümkün deyil. Faktura üçün heç bir element yoxdur.\n"
"\n"
"Bu problemi həll etmək üçün əmin olun:\n"
"   • Məhsullar faktura yazılmadan əvvəl çatdırılmışdır.\n"
"   • Məhsulun faktura siyasəti düzgün konfiqurasiya edilmişdir.\n"
"\n"
"Əvəzində sifariş edilmiş miqdarlar əsasında faktura etmək istəyirsinizsə:\n"
"   • İstehlak olunan və ya saxlanıla bilən məhsullar üçün məhsulu açın, 'Ümumi Məlumat' tabına keçin və 'Faktura Siyasəti'ni 'Çatdırılan Miqdarlar'dan 'Sifariş Edilən Miqdarlar'a dəyişin.\n"
"   • Xidmətlər (və digər məhsullar) üçün 'Faktura Siyasəti'ni 'Öncədən Ödənilmiş/Sabit Qiymət'ə dəyişin.\n"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "Tranzaksiyanı ələ keçirin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Catalog"
msgstr "Kataloq"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "Kateqoriya"

#. module: sale
#: model:product.template,name:sale.product_product_1_product_template
msgid "Chair floor protection"
msgstr "Kreslo döşəməsinin qorunması"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Changing the company of an existing quotation might need some manual "
"adjustments in the details of the lines. You might consider updating the "
"prices."
msgstr ""
"Mövcud təklifin şirkətinin dəyişdirilməsi nəticəsində elementlərin "
"təfərrüatlarında bəzi əl ilə düzəlişlərə ehtiyac ola bilər. Qiymətlərə "
"yenidən nəzər yetirin."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Check a sample. It's clean!"
msgstr "Nümunəni yoxlayın. Təmizdir!"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Click here to add some products or services to your quotation."
msgstr ""
"Təklifinizə bəzi məhsul və ya xidmətlər əlavə etmək üçün bura klikləyin."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/xml/sales_team_progress_bar_template.xml:0
msgid "Click to define an invoicing target"
msgstr "Faktura hədəfini müəyyən etmək üçün klikləyin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Bağlayın"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_combos
msgid "Combo Choices"
msgstr "Kombo Seçimləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__combo_item_id
msgid "Combo Item"
msgstr "Kombo elementi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_commission
msgid "Commissions"
msgstr "Komissiyalar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_provider__so_reference_type
msgid "Communication"
msgstr "Kommunikasiya"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Communication history"
msgstr "Kommunikasiya tarixçəsi"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Şirkət"

#. module: sale
#: model:ir.model,name:sale.model_base_document_layout
msgid "Company Document Layout"
msgstr "Şirkət Sənədinin Tərtibatı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Çatdırılma xərclərini hesabla və DHL ilə göndər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Göndərmə xərclərini hesablayın və Easypost ilə göndərin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Çatdırılma xərclərini hesabla və FedEx ilə göndər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "Göndərmə xərclərini hesablayın və Sendcloud ilə göndərin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "Göndərmə xərclərini hesablayın və Shiprocket ilə göndərin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "Göndərmə xərclərini hesablayın və Starshipit ilə göndərin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Çatdırılma xərclərini hesabla və UPS ilə göndər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Çatdırılma xərclərini hesabla və USPS ilə göndər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Çatdırılma xərclərini hesabla və bpost ilə göndər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Sifarişlərdə çatdırılma xərclərii hesablayın"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.js:0
msgid "Configure your product"
msgstr "Məhsulunuzu konfiqurasiya edin"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "Təsdiq edin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__confirmation_message
msgid "Confirmation Message"
msgstr "Təsdiq Mesajı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "Bağlayıcılar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__consolidated_billing
msgid "Consolidated Billing"
msgstr "Birləşdirilmiş Hesablaşma"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body
msgid "Contents"
msgstr "Məzmun"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Ölçü vahidləri arasındakı konversiya yalnız onlar eyni kateqoriyaya aid "
"olduqda baş verə bilər. Konversiya nisbətlərə əsasən həyata keçiriləcək."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__country_code
msgid "Country code"
msgstr "Ölkə Kodu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_loyalty
msgid "Coupons & Loyalty"
msgstr "Kuponlar və Sadiqlik"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "Tarix Yaradın"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Draft"
msgstr "Qaralama yaradın"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr "Hesab-Faktura yaradın"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Create Invoices"
msgstr "Hesab-Fakturalar yaradın"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid "Create a Sale Order Activity Plan"
msgstr "Satış Sifarişi Fəaliyyət Planı yaradın"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "Müştəri fakturası yaradın"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "Yeni məhsul yaradın"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a new quotation, the first step of a new sale!"
msgstr "Yeni təklif yaradın, yeni satışın ilk addımı!"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoice(s)"
msgstr "Hesab-Faktura(lar) yaradın"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""
"Hesab-fakturalar yaradın, ödənişləri qediyyata alın və müştərilərinizlə "
"aparılan müzakirələri izləyin."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__service_tracking
msgid "Create on Order"
msgstr "Sifariş yaradın"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__consolidated_billing
msgid ""
"Create one invoice for all orders related to same customer and same "
"invoicing address"
msgstr ""
"Eyni müştəri və eyni faktura ünvanı ilə əlaqəli bütün sifarişlər üçün bir "
"hesab-faktura yaradın"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "Yaradılma Tarixi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""
"Qaralama/göndərilmiş sifarişlərin yaranma tarixi,\n"
"Təsdiq edilmiş sifarişlərin təsdiq tarixi."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "Kredit və Debet kartı (Stripe vasitəsilə)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_report__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "Valyuta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Valyuta Məzənnəsi"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
msgid "Custom"
msgstr "Xüsusi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Fərdi Dəyərlər"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Fərdi ödəniş təlimatları"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Müştəri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "Müştərinin Ölkəsi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "Müştəri qurumu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "Müştəri Sənayesi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "Müştəri Portalı URL"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Müştəri Rəyi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "Müştəri İmzası"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__state_id
msgid "Customer State"
msgstr "Müştəri Əyaləti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_zip
msgid "Customer ZIP"
msgstr "Müştəri ZIP"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "Customer needs to pay at least %(amount)s to confirm the order."
msgstr "Sifarişi təsdiqləmək üçün müştəri ən azı %(amount)s ödəməlidir."

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_customer
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Müştərilər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express Connector"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Tarix"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Tarix:"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,help:sale.field_res_config_settings__quotation_validity_days
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Days between quotation proposal and expiration. 0 days means automatic "
"expiration is disabled"
msgstr ""
"Təklifin olunma və bitmə tarixi arasındakı günlər. 0 gün avtomatik bitmənin "
"söndürülməsi deməkdir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "İlkin ödənişləri çıxarın"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity"
msgstr "Defolt Təklif Etibarlılığı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__company_price_include
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_price_include
msgid "Default Sales Price Include"
msgstr "Defolt Satış Qiymətinə Daxildir"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__company_price_include
#: model:ir.model.fields,help:sale.field_sale_order_line__company_price_include
msgid ""
"Default on whether the sales price used on the product and invoices with "
"this Company includes its taxes."
msgstr ""
"Bu Şirkətlə məhsul və fakturalarda istifadə edilən satış qiymətinə onun "
"vergilərinin daxil olub-olmaması ilə bağlı defolt."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Default period during which the quote is valid and can still be accepted by "
"the customer. The default can be changed per order or template."
msgstr ""
"Kotirovkanın etibarlı olduğu və hələ də müştəri tərəfindən qəbul edilə "
"biləcəyi defolt müddət. Standart sifariş və ya şablona görə dəyişdirilə "
"bilər."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__sale_discount_product_id
msgid "Default product used for discounts"
msgstr "Endirimlər üçün istifadə edilən defolt məhsul"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "Məzmunu e-poçtla çatdırın"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "Çatdırıldı"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Delivered Quantity: %s"
msgstr "Çatdırılan Miqdar: %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "Çatdırılmış miqdarlar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Çatdırılma Ünvanı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "Çatdırılma tarixi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Təslimat Metodları"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivery Quantity"
msgstr "Çatdırılma Miqdarı"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Müştəriyə vəd edə biləcəyiniz çatdırılma tarixi, Xidmət məhsulları halında "
"sifariş xətlərinin minimum çatdırılma müddətindən hesablanır. Göndərmə "
"halında, sifariş xətlərinin minimum və ya maksimum çatdırılma müddətindən "
"istifadə etmək üçün sifarişin göndərmə siyasəti nəzərə alınacaqdır."

#. module: sale
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Depozit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Açıqlama"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "Endirim %"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Discard"
msgstr "Ləğv edin"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/wizard/sale_order_discount.py:0
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount"
msgstr "Endirim"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr "Endirim %"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%"
msgstr "Endirim %(percent)s%%"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%- On products with the following taxes %(taxes)s"
msgstr "Endirim %(percent)s%%- Aşağıdakı vergiləri olan məhsullarda %(taxes)s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Endirim (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "Endirim məbləği"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_discount_product_id
msgid "Discount Product"
msgstr "Endirimli məhsul"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_type
msgid "Discount Type"
msgstr "Endirim növü"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_discount
msgid "Discount Wizard"
msgstr "Endirim sehrbazı"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Xətlərdə endirim"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount- On products with the following taxes %(taxes)s"
msgstr "Endirim- Aşağıdakı vergiləri olan məhsullara %(taxes)s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount:"
msgstr "Endirim:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_discount_per_so_line
msgid "Discounts"
msgstr "Endirimlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_draft_invoice_warning
msgid "Display Draft Invoice Warning"
msgstr "Qaralama faktura xəbərdarlığını göstərin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_invoice_amount_warning
msgid "Display Invoice Amount Warning"
msgstr "Faktura Məbləği Xəbərdarlığını göstərin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "Göstəriləcək Form"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Dağıtım Analitik Hesabı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Documents"
msgstr "Sənədlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domen"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment"
msgstr "İlkin ödəniş"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (Cancelled)"
msgstr "İlkin ödəniş (Ləğv edilib)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (ref: %(reference)s on %(date)s)"
msgstr "İlkin ödəniş (ref: %(date)s üzrə %(reference)s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr "İlkin ödəniş məbləği (sabit)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment: %(date)s (Draft)"
msgstr "İlkin ödəniş: %(date)s (Qaralama)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payments"
msgstr "İlkin ödənişlər"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "İlkin ödəniş (sabit məbləğ)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "İlkin ödəniş (faizlə)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Down payment <br/>"
msgstr "İlkin ödəniş<br/>"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment invoice"
msgstr "İlkin ödəniş fakturası"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment of %s%%"
msgstr "%s%% ilkin ödəniş"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"İlkin ödənişlər satış sifarişindən hesab-fakturalar yaradılarkən həyata "
"keçirilir. Satış sifarişinin təkrarlanması zamanı onlar kopyalanmır."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_category__property_account_downpayment_categ_id
msgid "Downpayment Account"
msgstr "İlkin ödəniş hesabı"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Draft Invoices"
msgstr "Qaralama Fakturalar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Duplicated Documents"
msgstr "Dublikat sənədlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__duplicated_order_ids
msgid "Duplicated Order"
msgstr "Dublikat Sifariş"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost Connector"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_product_field.js:0
msgid "Edit Configuration"
msgstr "Konfiqurasiyanı redaktə edin"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr "Elektron imza"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "E-poçt"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Email tərtibi sehrbazı"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__invoice_mail_template_id
msgid "Email sent to the customer once the invoice is available."
msgstr "Faktura mövcud olduqdan sonra müştəriyə e-poçt göndərilir."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_template_attribute_line/product_template_attribute_line.js:0
msgid "Enter a customized value"
msgstr "Fərdiləşdirilmiş dəyər daxil edin"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Error importing attachment '%(file_name)s' as order (decoder=%(decoder)s)"
msgstr ""
"'%(file_name)s' qoşmasını sifariş olaraq idxal edərkən xəta baş verdi "
"(dekoder=%(decoder)s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Gözlənilən Tarix"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "Gözlənilir:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Expense"
msgstr "Xərclər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "Başaçatma Tarixi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Expiration Date:"
msgstr "Bitmə Tarixi:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Genişləndirilmiş Filtrlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Əlavə Dəyərlər"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Extra line with %s"
msgstr "%s ilə əlavə sətir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx birləşdiricisi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiskal Pozisyon"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""
"Fiskal mövqelər vergiləri və hesabları xüsusi müştərilər və ya satış "
"sifarişləri/qaimə-fakturaları üçün uyğunlaşdırmaq üçün istifadə olunur. "
"Defolt dəyər müştəridən gəlir."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__amount
msgid "Fixed Amount"
msgstr "Sabit Məbləğ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Follow, view or pay your orders"
msgstr "Sifarişlərinizi izləyin, baxın və ya ödəyin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Gözəl şriftli ikon, məsələn fa-tapşırıqlar"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr "Qeyri-məsuliyyətli satış sifarişi xəttində qadağan edilmiş dəyərlər"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Bu hesabatdan siz müştəriyə hesab-fakturaya göndərilən məbləğ haqqında ümumi"
" məlumat əldə edə bilərsiniz. Axtarış aləti həmçinin Faktura hesabatlarınızı"
" fərdiləşdirmək üçün istifadə edilə bilər və beləliklə, bu təhlili "
"ehtiyaclarınıza uyğunlaşdırın."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Full amount <br/>"
msgstr "Tam məbləğ<br/>"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Fully Invoiced"
msgstr "Tam fakturalanıb"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Gələcək Fəaliyyətlər"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Satış Ödəniş Linki yaradın"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "Ödəniş Linki yaradın"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "Ödəniş təsdiqləndiyində Fakturanı avtomatik tərtib et"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Generated Orders"
msgstr "Yaradılmış Sifarişlər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "Məhsullar və ya müştərilər üçün sifarişlərdə xəbərdarlıq alın"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__so_discount
msgid "Global Discount"
msgstr "Qlobal Endirim"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Go ahead and send the quotation."
msgstr "Davam edin və təklifi göndərin."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""
"Mallar maddi materiallar və təqdim etdiyiniz mallardır.\n"
"Xidmət sizin təqdim etdiyiniz qeyri-maddi məhsuldur."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "Satış sifarişi xətlərində endirimlər verin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Ümumi çəki"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Görə Qrupla"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_active_pricelist
msgid "Has Active Pricelist"
msgstr "Aktiv Qiymət siyahısı var"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_archived_products
msgid "Has Archived Products"
msgstr "Arxivləşdirilmiş Məhsullara malikdir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__has_confirmed_order
msgid "Has Confirmed Order"
msgstr "Sifarişi Təsdiqlədi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_fpos
msgid "Has Fiscal Position Changed"
msgstr "Fiskal Mövqeyi Dəyişdi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr "Qiymət Siyahısı Dəyişib"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "İlkin ödənişləri var"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__hidden
msgid "Hidden"
msgstr "Gizli"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "Simvol"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisna fəaliyyəti göstərən simvol."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşarələnibsə, bəzi mesajların çatdırılmasında xəta var."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__journal_id
msgid ""
"If set, the SO will invoice in this journal; otherwise the sales journal "
"with the lowest sequence is used."
msgstr ""
"Əgər təyin edilərsə, SO bu jurnalda faktura verəcək; əks halda ən aşağı "
"ardıcıllıqla satış jurnalından istifadə edilir."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"Satış kilidlidirsə, daha onu dəyişdirə bilməzsiniz. Bununla belə, siz yenə "
"də faktura və ya çatdıra biləcəksiniz."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr "Doğrudursa, qablaşdırma satış sifarişləri üçün istifadə edilə bilər"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""
"Qiymət siyahısını dəyişdirsəniz, yalnız yeni əlavə edilmiş xətlər "
"təsirlənəcək."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Immediate"
msgstr "Dərhal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "Amazon sifarişlərini idxal et və çatdırılmaları sinxronizə et"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Import Template for Products"
msgstr "Məhsullar üçün İdxal Şablonu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_view_search_catalog
msgid "In the Order"
msgstr "Sərəncamda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "O cümlədən vergi)"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Invalid discount amount"
msgstr "Yanlış endirim məbləği"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid order."
msgstr "Yanlış sifariş."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid signature data."
msgstr "Etibarsız imza datası."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move.py:0
msgid "Invoice %s paid"
msgstr "%s faktura ödənildi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Qaimə Ünvanı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr "Faktura Xəbərdarlığı"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Faktura Təsdiqləndi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "Hesab-Faktura Sayı"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Faktura Yaradıldı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "Faktura E-poçt Şablonu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Hesab- faktura sətirləri"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Faktura Satış Sifarişi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_report__line_invoice_status
msgid "Invoice Status"
msgstr "Faktura Vəziyyəti"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr ""
"Çatdırılmadan sonra hesab-faktura, çatdırılan miqdarlara əsasən, sifariş "
"verilmir."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "Bu xidmət satılan kimi sifariş edilən miqdarları faktura göndərin."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Cari ay üçün faktura gəliri. Bu, satış kanalının bu ay hesabladığı "
"məbləğdir. Kanban görünüşündə cari və hədəf gəlirin irəliləmə nisbətini "
"hesablamaq üçün istifadə olunur."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "Çatdırılanı faktura"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "Sifariş olunan faktura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "Fakturalanmış vergisiz məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_invoiced
msgid "Invoiced Amount"
msgstr "Vergiləndirilməmiş Fakturalanmış Məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Faktura Miqdarı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced_posted
msgid "Invoiced Quantity (posted)"
msgstr "Faktura Miqdarı (qeydə alınıb)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Invoiced Quantity: %s"
msgstr "Faktura Miqdarı: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Faktura Bu Ayda"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Hesab-Fakturalar yaradın"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Fakturaların təhlili"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Faktura Statistikası"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Hesab-Faktura və Çatdırılma Ünvanı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address"
msgstr "Faktura Ünvanı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__journal_id
msgid "Invoicing Journal"
msgstr "Faktura jurnalı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Fakturalama Qaydası"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "Faktura Hədəfi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address"
msgstr "Hesab-Faktura və Çatdırılma Ünvanı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__is_downpayment
msgid "Is Downpayment"
msgstr "İlkin Ödənişdir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__is_mail_template_editor
msgid "Is Editor"
msgstr "Redaktordur"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is Expired"
msgstr "Müddəti başa çatıb"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_product_archived
msgid "Is Product Archived"
msgstr "Məhsul Arxivdədir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "İlkin ödənişdir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "Xərcdir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "Məhsul konfiqurasiya edilə bilərmi?"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""
"Əgər satış sifarişi xətti xərcdən və ya satıcı hesablarından gəlirsə, "
"doğrudur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Issued Date"
msgstr "Buraxılış tarixi"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"Aşağıdakı sahələri kilidli qaydada dəyişdirmək qadağandır:\n"
"%s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "Jurnal Girişi"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "Jurnal Sətirləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__lang
msgid "Language"
msgstr "Dil"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Last Invoices"
msgstr "Son fakturalar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Ən son Əməliyyatlar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Göndərmə vaxtı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Qoy müştəriləriniz sənədlərini görmək üçün daxil olsunlar"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Let’s create a beautiful quotation in a few clicks ."
msgstr "Gəlin bir neçə kliklə gözəl sitat yaradaq."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Əlaqəli Satış Sətri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_ids
msgid "Linked Order Lines"
msgstr "Əlaqəli Sifariş Sətirləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_virtual_id
msgid "Linked Virtual"
msgstr "Əlaqəli Virtual"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Lock"
msgstr "Bağlama"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "Təsdiqlənmiş Satışları Kilidləyin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__locked
msgid "Locked"
msgstr "Blok edilib"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__locked
msgid "Locked orders cannot be modified."
msgstr "Kilidlənmiş sifarişlər dəyişdirilə bilməz."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__template_id
msgid "Mail Template"
msgstr "Məktub Şablonu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Make your quote attractive by adding header pages, product descriptions and "
"footer pages to your quote."
msgstr ""
"Təklifinizə başlıq səhifələri, məhsul təsvirləri və altbilgi səhifələri "
"əlavə etməklə təklifinizi cəlbedici edin."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Promotions, Coupons, Loyalty cards, Gift cards & eWallet"
msgstr ""
"Promosyonlar, Kuponlar, Sadiqlik kartları, Hədiyyə kartları və eWallet-i "
"idarə edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Sales & teams targets and commissions"
msgstr "Satış və komandaların hədəflərini və komissiyalarını idarə edin"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "Əl ilə"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "Manual Ödəniş"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "Sifariş üzrə miqdarları əl ilə təyin edin"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Sifariş üzrə miqdarı əl ilə təyin edin: Analitik hesab yaratmadan əl ilə daxil edilmiş kəmiyyətə əsaslanan faktura.\n"
"Müqavilə üzrə vaxt cədvəlləri: Müvafiq vaxt cədvəlində izlənilən saatlara əsaslanan faktura.\n"
"Tapşırıq yaradın və saatları izləyin: Satış sifarişinin təsdiqi ilə bağlı tapşırıq yaradın və iş saatlarını izləyin."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Marjalar"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr "Təklifi Göndərilmiş et"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "Marketinq"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "Reklam Vasitəsi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Message"
msgstr "Mesaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Satış Sifarişi üçün Mesaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Satış Sifariş Xətti üçün Mesaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Metod"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Çatdırılan miqdarı yeniləmək üsulu"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr "Hesabatlı satış sifarişi xəttində tələb olunan sahələr çatışmır."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Mitchell Admin"
msgstr "Mitchell Admin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mənim Fəaliyyətlərimin Son Tarixi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Mənim Sifarişlərim"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "Mənim Qiymət Təkliflərim"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Satış Sifariş Xətlərim"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "New"
msgstr "Yeni"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Yeni Qiymət Təklifi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Növbəti Fəaliyyət Təqvimi Tədbiri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Növbəti Fəaliyyətin Son Tarixi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Növbəti Fəaliyyət Xülasəsi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "Yeni Fəaliyyət Növü"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "Tapılmayanlar"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "Mesaj Yoxdur"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "No attachment was provided"
msgstr "Heç bir əlavə təmin edilmədi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "No further requirements for this payment"
msgstr "Bu ödəniş üçün əlavə tələblər yoxdur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "Təsdiqləndikdən sonra artıq sifarişləri redaktə etməyin"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr "Fakturaya sifariş tapılmadı"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr "Satış üçün heç bir sifariş tapılmadı."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "Qeyd"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__no
msgid "Nothing to Invoice"
msgstr "Fakturaya heç nə yoxdur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Number"
msgstr "Say"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Sifarişin təsdiqi ilə məhsulların müştəriyə göndərilməsi arasındakı günlərin"
" sayı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "Faktura üçün təkliflərin sayı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "Fakturaya satışların sayı"

#. module: sale
#: model:product.template,description_sale:sale.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr "Ofis kresloları döşəmənizə zərər verə bilər: onu qoruyun."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__sol_discount
msgid "On All Order Lines"
msgstr "Bütün Sifariş Xətlərində"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__sale_order
msgid "On confirmed order"
msgstr "Təsdiqlənmiş sifarişlə"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__quotation
msgid "On quote"
msgstr "Təkif üzrə"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"Once a sales order is confirmed, you can't remove one of its lines (we need to track if something gets invoiced or delivered).\n"
"                Set the quantity to 0 instead."
msgstr ""
"Satış sifarişi təsdiq edildikdən sonra siz onun sətirlərindən birini silə bilməzsiniz (biz nəyinsə faktura yazıldığını və ya çatdırılmasını izləməliyik).\n"
"                Bunun əvəzinə kəmiyyəti 0-a qoyun."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""
"Təklif müştəri tərəfindən təsdiqləndikdən sonra o, satış sifarişinə "
"çevrilir.<br> Siz faktura yarada və ödənişi yığa biləcəksiniz."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"Kotirovka təsdiqləndikdən sonra o, satış sifarişinə çevrilir.<br> Siz "
"faktura yarada və ödənişi yığa biləcəksiniz."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
msgid "Online Payment"
msgstr "Onlayn Ödəniş"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_menu
msgid "Online Payments"
msgstr "Onlayn ödənişlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
msgid "Online Signature"
msgstr "Onlayn İmza"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online payment"
msgstr "Onlayn ödəniş"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online signature"
msgstr "Onlayn imza"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_invoiced
msgid "Only confirmed down payments are considered."
msgstr "Yalnız təsdiqlənmiş ilkin ödənişlər nəzərə alınır."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Only draft orders can be marked as sent directly."
msgstr "Yalnız qaralama sifarişlər birbaşa göndərilmiş kimi qeyd edilə bilər."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr ""
"Satış Sifariş Xətti üzrə Atribut Dəyəri üçün yalnız bir Fərdi Dəyər icazə "
"verilir."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
msgid "Operation not supported"
msgstr "Əməliyyat dəstəklənmir"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option for: %s"
msgstr "%s üçün Seçim"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option: %s"
msgstr "Seçim: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "İtəyə bağlı Məhsullar"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""
"Müştəri *Səbətə əlavə et* (çarpaz satış strategiyası, məsələn, kompüterlər "
"üçün: zəmanət, proqram təminatı və s.) düyməsini vurduqda Könüllü Məhsullar "
"təklif olunur."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_cancel__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"E-poçt göndərərkən seçmək üçün əlavə tərcümə dili (ISO kodu). "
"Quraşdırılmayıbsa, ingilis versiyası istifadə olunacaq. Bu, adətən müvafiq "
"dili təmin edən yer tutucu ifadə olmalıdır, məs. {{ obyekt.partner_id.lang "
"}}."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_report__order_reference
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Sifariş"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Sifariş #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr "Sifariş Sayı"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Date"
msgstr "Sifariş Tarixi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Order Date:"
msgstr "Sifariş Tarixi:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Order Date: Last 365 Days"
msgstr "Sifariş Tarixi: Son 365 Gün"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__invoice_status
msgid "Order Invoice Status"
msgstr "Sifariş faktura statusu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Sifariş xətləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Sipariş Referansı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Sipariş vəziyyəti"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr "Upsell sifariş edin"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Order signed by %s"
msgstr "%s tərəfindən imzalanmış sifariş"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "Fakturaya sifariş"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr "Sifariş Miqdarı: %(old_qty)s -> %(new_qty)s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""
"Sifariş Miqdarı: Müştəri tərəfindən sifariş edilən faktura miqdarları.\n"
"Çatdırılma Miqdarı: Müştəriyə çatdırılan faktura miqdarı."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "Sifariş edilən Say"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Sifarişlər"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "Sifarişlər Fakturaya"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Satış üçün sifarişlər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Oscar Morgan"
msgstr "Oskar Morqan"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "Digər Məlumatlar"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "PDF Quote"
msgstr "PDF Təklif"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "PDF Quote builder"
msgstr "PDF Təklif yaradıcısı"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "PRO-FORMA Fakturası"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "Qablaşdırma"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Qablaşdırma Miqdarı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_credit_warning
msgid "Partner Credit Warning"
msgstr "Partnyor Kredit Xəbərdarlığı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "İndi ödəyin"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment provider"
msgstr "Başqa ödəniş provayderi ilə ödəyin"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment"
msgstr "Ödəniş"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Ödəniş Təlimatları"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Ödəniş Üsulu"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_method_menu
msgid "Payment Methods"
msgstr "Ödəniş Üsulları"

#. module: sale
#: model:ir.model,name:sale.model_payment_provider
msgid "Payment Provider"
msgstr "Ödəniş Provayderi"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_provider_menu
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Providers"
msgstr "Ödəniş Provayderləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr "Ödəniş Ref."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Ödəniş Şərtləri"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_token_menu
msgid "Payment Tokens"
msgstr "Ödəniş Tokenləri"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Ödəniş əməliyyatı"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_transaction_menu
msgid "Payment Transactions"
msgstr "Ödəniş Əməliyyatları"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_paid
msgid "Payment Transactions Amount"
msgstr "Ödəniş əməliyyatlarının məbləği"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr "Ödəniş Şərtləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_percentage
msgid "Percentage"
msgstr "Faiz"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "Porta Giriş URL-si"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_res_config_settings__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_sale_order__prepayment_percent
msgid "Prepayment percentage"
msgstr "İlkin ödəniş faizi"

#. module: sale
#. odoo-python
#: code:addons/sale/models/res_company.py:0
#: code:addons/sale/models/sale_order.py:0
msgid "Prepayment percentage must be a valid percentage."
msgstr "İlkin ödəniş faizi etibarlı faiz olmalıdır."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Preview"
msgstr "İlkin Baxış"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
msgid "Price"
msgstr "Qiymət"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Qiyməti Endirmə Vergisi xaric"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax incl"
msgstr "Qiymətin azaldılması vergisi daxil"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Qiymət siyahısı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__pricelist_item_id
msgid "Pricelist Item"
msgstr "Qiymət siyahısı maddəsi"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Qiymət siyahıları"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Qiymətləndirmə"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "PRO-FORMA Fakturası"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Pro-Forma Invoice #"
msgstr "Pro-Forma Qaimə #"

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Pro-Forma Fakturalar"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Məhsul"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Məhsul Atributunun Fərdi Dəyəri"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "Məhsul Kataloğu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__product_catalog_product_is_in_sale_order
msgid "Product Catalog Product Is In Sale Order"
msgstr "Məhsul Kataloq Məhsul Satılır Sifarişdədir"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_categories
msgid "Product Categories"
msgstr "Məhsul Kateqoriyaları"

#. module: sale
#: model:ir.model,name:sale.model_product_category
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Məhsul Kateqoriyası"

#. module: sale
#: model:ir.model,name:sale.model_product_document
msgid "Product Document"
msgstr "Məhsul Sənədi"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
#: code:addons/sale/static/src/js/product_card/product_card.xml:0
msgid "Product Image"
msgstr "Məhsul Şəkli"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "Məhsulun qablaşdırılması"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_tags
msgid "Product Tags"
msgstr "Məhsul Etiketləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "Məhsul Şablonu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Məhsulun Növü"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr "Məhsul Uom Yalnız oxunur"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Variant"
msgstr "Məhsul Çeşidi"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Məhsul Çeşidləri"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed according to pricelist %s."
msgstr ""
"Məhsul qiymətləri %s qiymət siyahısına uyğun olaraq yenidən hesablanıb."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed."
msgstr "Məhsulların qiymətləri yenidən hesablanıb."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product taxes have been recomputed according to fiscal position %s."
msgstr "Məhsul vergiləri %s fiskal mövqeyinə uyğun olaraq yenidən hesablanıb."

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.menu_reporting_product
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "Məhsullar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Promotions, Loyalty & Gift Card"
msgstr "Promosyonlar, Sadiqlik və Hədiyyə Kartı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Sayı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Miqdar Çatdırılır"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Qty Faktura"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "Sifariş edilən miqdar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Çatdırılma Miqdarı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Fakturaya Miqdar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "Satış sifarişlərindən faktura üçün miqdarlar"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Quantity"
msgstr "Miqdar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "Quantity To Invoice"
msgstr "Faktura üçün Miqdarı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "Miqdar:"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
msgid "Quotation"
msgstr "Qiymət Təklifi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Təklif #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr "Qiymət Sorğusunun Sayı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "Təklif Tarixi"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "Sitat Göndərilib"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/res_config_settings.py:0
msgid "Quotation Validity is required and must be greater or equal to 0."
msgstr ""
"Kotirovkanın etibarlılığı tələb olunur və 0-dan böyük və ya bərabər "
"olmalıdır."

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_viewed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_viewed
msgid "Quotation Viewed"
msgstr "Sitat baxıldı"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Sitat təsdiqləndi"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Sitat göndərildi"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Quotation viewed by customer %s"
msgstr "Kotirovkaya müştəri %s baxdı"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotations"
msgstr "Qiymət Təklifləri"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations & Orders"
msgstr "Sitatlar və Sifarişlər"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Sitatların təhlili"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Kotirovkalar və Satış"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Quotations to review"
msgstr "Nəzərdən keçirilməsi üçün sitatlar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__rating_ids
msgid "Ratings"
msgstr "Qiymətləndirmələr"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Costs"
msgstr "Yenidən Faktura Xərcləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr "Yenidən Faktura Siyasəti görünür"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__recipient_ids
msgid "Recipients"
msgstr "Qəbuledicilər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr "'Səbətə əlavə edərkən' və ya sitat gətirərkən tövsiyə edin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr "Bu qiymət siyahısı əsasında bütün qiymətləri yenidən hesablayın"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all taxes based on this fiscal position"
msgstr "Bu fiskal mövqeyə əsasən bütün vergiləri yenidən hesablayın"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request"
msgstr "Bu satış sifarişi sorğusunu yaradan sənədə istinad"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr "Adi faktura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr "Bu Sitatı rədd edin"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Remove"
msgstr "Sil"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Remove one"
msgstr "Birini Silin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__render_model
msgid "Rendering Model"
msgstr "Render modeli"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "Hesabatlıq"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid "Request a online payment from the customer to confirm the order."
msgstr "Sifarişi təsdiqləmək üçün müştəridən onlayn ödəniş tələb edin."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid "Request a online signature from the customer to confirm the order."
msgstr "Sifarişi təsdiqləmək üçün müştəridən onlayn imza tələb edin."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request a payment to confirm orders, in full (100%) or partial. The default "
"can be changed per order or template."
msgstr ""
"Sifarişləri tam (100%) və ya qismən təsdiqləmək üçün ödəniş tələb edin. "
"Standart sifariş və ya şablona görə dəyişdirilə bilər."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request customers to sign quotations to validate orders. The default can be "
"changed per order or template."
msgstr ""
"Sifarişləri təsdiqləmək üçün müştərilərdən kotirovkaları imzalamağı xahiş "
"edin. Standart sifariş və ya şablona görə dəyişdirilə bilər."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Requested date is too soon."
msgstr "Tələb olunan tarix çox tezdir."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "Məsul İstifadəçi"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid "Revenue Target for the current month (untaxed total of paid invoices)."
msgstr ""
"Cari ay üçün Gəlir Hədəfi (ödənilmiş fakturaların vergiyə cəlb edilməmiş "
"cəmi)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "Gəlirlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr "Kampaniya nəticəsində əldə edilən gəlirlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-in Çatdırılmasında xəta"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "SO0000"
msgstr "SO0000"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Sale"
msgstr "Satış"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr "Satılır: Görünür"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Sale Information"
msgstr "Satış Məlumatı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__sale_order_id
#: model:ir.model.fields.selection,name:sale.selection__account_analytic_applicability__business_domain__sale_order
msgid "Sale Order"
msgstr "Satış Sifarişi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_move__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "Satış Sifarişi Sayı"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_plan_action_sale_order
msgid "Sale Order Plans"
msgstr "Satış Sifariş Planları"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "Satış Sifarişləri Xəbərdarlıqları"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Sale Orders"
msgstr "Satiş Sifarişləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_orders_count
msgid "Sale Orders Count"
msgstr "Satış Sifarişləri Sayı"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_provider_onboarding_wizard
msgid "Sale Payment provider onboarding wizard"
msgstr "Satış Ödəniş provayderinin işə başlama ustası"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr "Satış Xəbərdarlıqları"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr "Seçilmiş ödəniş üsulu ilə satış"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_order_ids
msgid "Sale orders to cancel"
msgstr "Ləğv olunmalı Satış sifarişləri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_reporting_sales
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_form
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "Satışlar"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Satış üçün əvvəlcədən ödəmə fakturası"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Satışların Təhlili"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_customers
msgid "Sales Analysis By Customers"
msgstr "Müştərilər tərəfindən Satış Analizi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_products
msgid "Sales Analysis By Products"
msgstr "Məhsullar üzrə Satış Analizi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_salesperson
msgid "Sales Analysis By Salespersons"
msgstr "Satış işçiləri tərəfindən satış təhlili"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Satış Analizləri Hesabatı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr "Satış şəbəkəsinə giriş"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__order_reference__sale_order
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Order"
msgstr "Satış Sifarişi"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Satış Sifarişini Ləğv et"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Satış Sifarişi Təsdiqləndi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "Satış Sifarişi Məhsulu"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Satış Sifarişi Sətri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Satış Sifariş Sətirləri"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Satış Sifariş Sətirləri fakturaya hazırdır"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Mənim Satış Sifarişimlə əlaqəli Satış Sifariş Xətləri"

#. module: sale
#. odoo-python
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
msgid "Sales Order(s)"
msgstr "Satış Sifariş(lər)i"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Satış Sifarişləri"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Satış Komandası"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Satış Komandaları"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr "Satış Xəbərdarlıqları"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"Müştərinizə faktura yazmaq üçün sərf olunan vaxtın əlavə ediləcəyi satış "
"sifarişi maddəsi. Qeyri-fakturalı olmaq üçün vaxt cədvəli girişi üçün satış "
"sifarişi elementini silin."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "Satış qiyməti"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_cancellation
msgid "Sales: Order Cancellation"
msgstr "Satış: Sifarişin ləğvi"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales: Order Confirmation"
msgstr "Satış: Sifariş təsdiqi"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_payment_executed
msgid "Sales: Payment Done"
msgstr "Satış: Ödəniş Tamamlandı"

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales: Send Quotation"
msgstr "Satış: Təklif Göndər"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid "Sales: Untaxed Total"
msgstr "Satışlar: Vergiləndirilməmiş Cəmi Məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Satış Meneceri"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_salespeople
msgid "Salespersons"
msgstr "Satış Menecerləri"

#. module: sale
#: model:ir.model,name:sale.model_mail_scheduled_message
msgid "Scheduled Message"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Satış Sifarişini axtarın"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Search a customer name, or create one on the fly."
msgstr "Müştəri adı axtarın və ya birini yaradın."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "Bölmə"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Bölmə Adı (məs. Məhsullar, Xidmətlər)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "Təhlükəsizlik Tokeni"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Select a product, or create a new one on the fly."
msgstr "Bir məhsul seçin və ya dərhal yenisini yaradın."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__selected_combo_items
msgid "Selected Combo Items"
msgstr "Seçilmiş Kombo Elementlər"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"\"Xəbərdarlıq\" düyməsinin seçilməsi istifadəçiyə mesajla bildiriş "
"göndərəcək, \"Mesajın bloklanması\" seçimi mesajla birlikdə istisna "
"göndərəcək və axını blok edəcəkdir. Mesaj növbəti xanaya yazılmalıdır."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Məhsulları müxtəlif ölçü vahidlərində satmaq və almaq"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "Məhsulları paket başına # ədədinə görə satın"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr ""
"Atributlardan (ölçü, rəng və s.) istifadə edərək məhsulun variantlarını "
"satmaq."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "PRO-FORMA faktura göndərin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr "Faktura təsdiq edildikdən sonra məhsula xüsusi e-poçt göndərin"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_send_mail
msgid "Send an email"
msgstr "E-məktub Göndərin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Send and cancel"
msgstr "Göndər və ləğv et"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Emaillə Göndər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Sendcloud Konnektoru"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Accounting tab)."
msgstr ""
"Məhsul haqqında xüsusi məlumat və ya məzmun (təlimatlar, qaydalar, keçidlər,"
" media və s.) paylaşmaq lazımdırsa, e-poçt göndərmək faydalıdır. Məhsul "
"təfərrüatları formasından e-poçt şablonunu yaradın və təyin edin (Mühasibat "
"sekmesinde)."

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_cancellation
msgid "Sent automatically to customers when you cancel an order"
msgstr "Sifarişi ləğv etdiyiniz zaman avtomatik olaraq müştərilərə göndərilir"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_confirmation
msgid "Sent to customers on order confirmation"
msgstr "Sifarişin təsdiqi ilə müştərilərə göndərilir"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_payment_executed
msgid ""
"Sent to customers when a payment is received but doesn't immediately confirm"
" their order"
msgstr ""
"Ödəniş alındıqda müştərilərə göndərilir, lakin onların sifarişini dərhal "
"təsdiq etmir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Ardıcıllıq"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""
"Bir məhsul üçün müxtəlif qiymətlər, avtomatik endirimlər və s. təyin edin."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Təklifə çevirin"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Parametrlər"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "Paylaşın"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Shipping"
msgstr "Təslimat"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Shipping Address"
msgstr "Təslimat Ünvanı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Shiprocket birləşdiricisi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "Növbəti fəaliyyət tarixi bu günə qədər olan bütün qeydləri göstərin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "Sifarişlərdə kənarları göstərin"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Sign & Pay Quotation"
msgstr "Təklifə imza at və ödə"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr "İmza &amp; Ödəmək"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr "Onlayn daxil olun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "İmza"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Signature is missing."
msgstr "İmza yoxdur."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr "İmzalı"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr "Daxil olub"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "Satıldı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "Son 365 gündə satılıb"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"Some confirmed orders are selected. Their related documents might be\n"
"                        affected by the cancellation."
msgstr ""
"Bəzi təsdiqlənmiş sifarişlər seçilir. Onların əlaqəli sənədləri ola bilər\n"
"                        ləğv edilməsindən təsirlənir."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Some orders are not in a state requiring confirmation."
msgstr "Bəzi sifarişlər təsdiq tələb edən vəziyyətdə deyil."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "Mənbə"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Mənbə Sənəd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "Xüsusi E-poçt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr "Starshipit birləşdiricisi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Status"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Fəaliyyətlərə əsaslanan status\n"
"Gecikmiş: Gözlənilən tarixdən keçib\n"
"Bu gün: Fəaliyyət tarixi bu gündür\n"
"Planlaşdırılıb: Gələcək fəaliyyətlər."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__subject
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Subject"
msgstr "Mövzu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Yekun"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Quantity"
msgstr "Kəmiyyətin cəmi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr "Bütünün cəmi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Vergiləndirilməmiş Cəmi Məbləğin Cəmi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__amount_paid
msgid ""
"Sum of transactions made in through the online payment form that are in the "
"state 'done' or 'authorized' and linked to this order."
msgstr ""
"Onlayn ödəniş forması vasitəsilə edilən və bu sifarişlə əlaqələndirilmiş "
"\"bitmiş\" və ya \"səlahiyyətli\" vəziyyətdə olan əməliyyatların cəmi."

#. module: sale
#: model:ir.model,name:sale.model_ir_config_parameter
msgid "System Parameter"
msgstr "Sistem Parametri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "Etiketlər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax 15%"
msgstr "Vergi 15%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Vergi Hesablanmasında Yuvarlaqlaşdırma Metodu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_country_id
msgid "Tax Country"
msgstr "Vergi Ölkəsi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Excl."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax ID"
msgstr "Vergi İD-si"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Incl."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Tax Total"
msgstr "Vergi Cəmi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals
msgid "Tax Totals"
msgstr "Vergi cəmi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Vergi Hesablanmasında Yuvarlaqlaşdırma Metodu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__tax_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Vergi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_discount__tax_ids
msgid "Taxes to add on the discount line."
msgstr "Endirim xəttinə əlavə ediləcək vergilər."

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"cancelling them or archiving the team instead."
msgstr ""
"%(team_name)s komandasının %(sale_order_count)s aktiv satış sifarişi var. "
"Onları ləğv etməyi və ya əvəzinə komandanı arxivləşdirməyi düşünün."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__technical_price_unit
msgid "Technical Price Unit"
msgstr "Texniki qiymət vahidi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""
"Bu təklifdən niyə imtina etdiyinizi bizə deyin, bu, xidmətlərimizi "
"yaxşılaşdırmağa kömək edəcək."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr "Qaydalar və Şərtlər formatı"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Terms & Conditions: %s"
msgstr "Qaydalar və Şərtlər: %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Şərtlər &amp; Qaydalar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Şərtlər və Qaydalar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "Qaydalar və şərtlər..."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"İki simvolda ISO ölkə kodu. \n"
"Tez axtarış üçün bu sahəni istifadə edə bilərsiniz."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is cancelled. You cannot register"
" an expense on a cancelled Sales Order."
msgstr ""
"Yenidən hesablanacaq Satış Sifarişi %(order)s ləğv edildi. Ləğv edilmiş "
"Satış Sifarişi üzrə xərcləri qeyd edə bilməzsiniz."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is currently locked. You cannot "
"register an expense on a locked Sales Order."
msgstr ""
"Yenidən hesablanacaq Satış Sifarişi %(order)s hazırda kilidlənib. Siz "
"kilidlənmiş Satış Sifarişində xərcləri qeyd edə bilməzsiniz."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced must be validated before "
"registering expenses."
msgstr ""
"Yenidən hesablanacaq Satış Sifarişi %(order)s xərclər qeydə alınmazdan əvvəl"
" təsdiq edilməlidir."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "Giriş Tokeni işlək deyil."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "The amount to invoice = Sale Order Total - Confirmed Down Payments."
msgstr ""
"Faktura məbləği = Satış Sifarişinin Cəmi - Təsdiqlənmiş İlkin Ödənişlər."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The company is required, please select one before making any other changes "
"to the sale order."
msgstr ""
"Şirkət tələb olunur, lütfən, satış sifarişinə başqa dəyişikliklər etməzdən "
"əvvəl birini seçin."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The delivery date is sooner than the expected date. You may be unable to "
"honor the delivery date."
msgstr ""
"Çatdırılma tarixi gözlənilən tarixdən tezdir. Çatdırılma tarixinə riayət edə"
" bilməyəcəksiniz."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance."
msgstr "Əvvəlcədən hesab-fakturaya göndəriləcək sabit məbləğ."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid ""
"The following products cannot be restricted to the company %(company)s because they have already been used in quotations or sales orders in another company:\n"
"%(used_products)s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""
"Aşağıdakı məhsullar %(company)s şirkəti ilə məhdudlaşdırıla bilməz, çünki onlar artıq başqa şirkətdə kotirovkalarda və ya satış sifarişlərində istifadə olunub:\n"
"%(used_products)s\n"
"Siz bu məhsulları arxivləşdirə və əvəzində şirkət məhdudiyyətinizlə yenidən yarada və ya paylaşılan məhsul kimi buraxa bilərsiniz."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment provider.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment provider.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""
"Hesab-faktura avtomatik olaraq yaradılır və əməliyyat ödəniş provayderi tərəfindən təsdiq edildikdən sonra müştəri portalında mövcuddur.\n"
"Faktura ödənilmiş kimi qeyd olunur və ödəniş ödəniş provayderinin konfiqurasiyasında müəyyən edilmiş ödəniş jurnalında qeydə alınır.\n"
"Son fakturanı çatdırılmadan sonra deyil, sifariş zamanı versəniz, bu rejim tövsiyə olunur."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""
"Marja məhsulun satış qiymətlərinin onların təfərrüat formasında müəyyən "
"edilmiş dəyəri çıxılmaqla cəmi kimi hesablanır."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "The new invoice will deduct draft invoices linked to this sale order."
msgstr "Yeni faktura bu satış sifarişi ilə əlaqəli qaimə-fakturaları çıxacaq."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The number of selected combo items must match the number of available combo "
"choices."
msgstr ""
"Seçilmiş kombinasiya elementlərinin sayı mövcud kombo seçimlərinin sayına "
"uyğun olmalıdır."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "The order is not in a state requiring customer payment."
msgstr "Sifariş müştəri ödənişini tələb edən vəziyyətdə deyil."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The order is not in a state requiring customer signature."
msgstr "Sifariş müştərinin imzasını tələb edən vəziyyətdə deyil."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "The ordered quantity has been updated."
msgstr "Sifariş edilən miqdar yeniləndi."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr "Bu satış sifarişinin ödəniş rabitəsi."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "The payment should also be transmitted with love"
msgstr "Ödəniş sevgi ilə də göndərilməlidir"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance."
msgstr "Əvvəlcədən hesablanacaq məbləğin faizi."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__prepayment_percent
msgid ""
"The percentage of the amount needed that must be paid by the customer to "
"confirm the order."
msgstr ""
"Sifarişi təsdiqləmək üçün müştəri tərəfindən ödənilməli olan tələb olunan "
"məbləğin faizi."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,help:sale.field_res_config_settings__prepayment_percent
msgid "The percentage of the amount needed to be paid to confirm quotations."
msgstr "Kotirovkaları təsdiqləmək üçün ödənilməli olan məbləğin faizi."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "The product (%(product)s) has incompatible values: %(value_list)s"
msgstr "Məhsulun (%(product)s) uyğun olmayan dəyərləri var: %(value_list)s"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The provided parameters are invalid."
msgstr "Təqdim olunan parametrlər etibarsızdır."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "The value of the down payment amount must be positive."
msgstr "İlkin ödəniş məbləğinin dəyəri müsbət olmalıdır."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "Hesabınız üçün hazırda heç bir kotirovka yoxdur."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no sales orders for your account."
msgstr "Hazırda hesabınız üçün heç bir satış sifarişi yoxdur."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "There are existing"
msgstr "Mövcudlar var"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid ""
"There does not seem to be any discount product configured for this company "
"yet. You can either use a per-line discount, or ask an administrator to "
"grant the discount the first time."
msgstr ""
"Hələlik bu şirkət üçün konfiqurasiya edilmiş endirim məhsulu yoxdur. Siz ya "
"hər sətir endirimindən istifadə edə bilərsiniz, ya da ilk dəfə endirimin "
"verilməsini administratordan xahiş edə bilərsiniz."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_category__property_account_downpayment_categ_id
msgid "This account will be used on Downpayment invoices."
msgstr "Bu hesab İlkin Ödəniş fakturalarında istifadə olunacaq."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Bu standart dəyər yaradılmış hər hansı yeni məhsula tətbiq edilir. Bu, "
"məhsul təfərrüatları formunda dəyişdirilə bilər."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Bu, Fall_Drive, Christmas_Special kimi müxtəlif kampaniya səylərini izləməyə"
" kömək edən bir addır"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""
"Bu, müştəriyə vəd edilən çatdırılma tarixidir. Əgər təyin edilərsə, "
"çatdırılma sifarişi məhsulun çatdırılma vaxtlarına deyil, bu tarixə əsasən "
"planlaşdırılacaq."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "Bu, açıqca, e-poçt və ya banner reklamı kimi çatdırılma üsuludur"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Bu, axtarış mühərriki, digər domen və ya e-poçt siyahısı adı kimi istinad "
"mənbəyidir"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "This option or combination of options is not available"
msgstr "Bu seçim və ya seçimlərin birləşməsi mövcud deyil"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "This payment will confirm the quotation."
msgstr "Bu ödəniş kotirovkanı təsdiq edəcək."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""
"Bu məhsul %(pack_size).2f %(pack_name)s tərəfindən qablaşdırılıb. Siz "
"%(quantity).2f %(unit)s satmalısınız."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
#: model_terms:ir.actions.act_window,help:sale.action_order_report_customers
#: model_terms:ir.actions.act_window,help:sale.action_order_report_products
#: model_terms:ir.actions.act_window,help:sale.action_order_report_salesperson
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Bu hesabat kotirovkalarınız və satış sifarişləriniz üzrə təhlil aparır. "
"Analiz satış gəlirlərinizi yoxlayın və onu müxtəlif qrup meyarlarına görə "
"çeşidləyin (satıcı, tərəfdaş, məhsul və s.) Bu hesabatdan hələ fakturasız "
"satışlar üzrə təhlil aparmaq üçün istifadə edin. Əgər dövriyyənizi təhlil "
"etmək istəyirsinizsə, Mühasibat proqramında Faktura Təhlili hesabatından "
"istifadə etməlisiniz."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Bu hesabat kotirovkalarınız üzrə təhlil aparır. Analiz satış gəlirlərinizi "
"yoxlayın və onu müxtəlif qrup meyarlarına (satıcı, partnyor, məhsul və s.) "
"görə çeşidləyin. Hələ fakturasız satışlar üzrə təhlil aparmaq üçün bu "
"hesabatdan istifadə edin. Əgər dövriyyənizi təhlil etmək istəyirsinizsə, "
"Mühasibat proqramında Faktura Təhlili hesabatından istifadə etməlisiniz."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Bu hesabat satış sifarişləriniz üzrə təhlil aparır. Analiz satış "
"gəlirlərinizi yoxlayın və onu müxtəlif qrup meyarlarına görə çeşidləyin "
"(satıcı, tərəfdaş, məhsul və s.) Bu hesabatdan hələ fakturasız satışlar üzrə"
" təhlil aparmaq üçün istifadə edin. Əgər dövriyyənizi təhlil etmək "
"istəyirsinizsə, Mühasibat proqramında Faktura Təhlili hesabatından istifadə "
"etməlisiniz."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update all taxes based on the currently selected fiscal position."
msgstr ""
"Bu, hazırda seçilmiş fiskal mövqeyə əsasən bütün vergiləri yeniləyəcək."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update the unit price of all products based on the new pricelist."
msgstr ""
"Bu, yeni qiymət siyahısı əsasında bütün məhsulların vahid qiymətini "
"yeniləyəcək."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "Hesab-fakturaya"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr "Satışa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"B2B rejimində dəvətnamələr göndərmək üçün kontaktı açın və ya siyahıda "
"birdən çox əlaqə seçin və açılan *Action* menyusunda \"PORTALA girişin idarə"
" edilməsi\" seçimini basın ."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Bugünkü Fəaliyyətlər"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Cəmi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr "Cəmi vergi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Excluded"
msgstr "Ümumi Vergi xaric"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Included"
msgstr "Ümumi Vergi Daxildir"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.js:0
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Total: %s"
msgstr "Cəmi: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "İzləmə Xidməti"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "İzləmə"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Əməliyyatlar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__translated_product_name
msgid "Translated Product Name"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "Tipin Adı"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Type a message..."
msgstr "Mesaj yazın..."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Qeyddəki istisna fəaliyyət növü."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a customer..."
msgstr "Müştəri tapmaq üçün yazın..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a product..."
msgstr "Məhsul tapmaq üçün yazın..."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPS Konnektoru"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPS birləşdiricisi"

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "Kampaniyanın UTM kodu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_to_invoice
msgid "Un-invoiced Balance"
msgstr "Fakturasız Balans"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale.field_sale_report__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Vahid Qiymət"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "Vahid Qiymət:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Ölçü  Vahidi"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Ölçü  Vahidi"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Ölçü vahidləri Kateqoriyalar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unlock"
msgstr "Kilidi açın"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Vergiləndirilməmiş Məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "Fakturalanmış vergisiz məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "Fakturalanmalı Vergisiz Məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "Vergiləndirilməmiş Fakturalanmış Məbləğ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Vergiləndirilməmiş Cəmi Məbləğ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "Ölçü Vahidi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr "Qiymətləri Yeniləyin"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Taxes"
msgstr "Vergiləri yeniləyin"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Upsell %(order)s for customer %(customer)s"
msgstr "%(customer)s müştəri üçün %(order)s satışını artırın"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Satış fürsəti"

#. module: sale
#: model:mail.template,description:sale.email_template_edi_sale
msgid "Used by salespeople when they send quotations or proforma to prospects"
msgstr ""
"Satış işçiləri tərəfindən perspektivlərə kotirovkalar və ya proforma "
"göndərildikdə istifadə olunur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Etibarlılıq Müddəti"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "Sifarişi təsdiqləyin"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Validated expenses, vendor bills, or stock pickings (set up to track costs) "
"can be invoiced to the customer at either cost or sales price."
msgstr ""
"Təsdiq edilmiş xərclər, satıcı fakturaları və ya səhm seçimləri (xərcləri "
"izləmək üçün qurulmuşdur) müştəriyə ya dəyəri, həm də satış qiyməti ilə "
"hesablana bilər."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__validity_date
msgid ""
"Validity of the order, after that you will not able to sign & pay the "
"quotation."
msgstr ""
"Sifarişin etibarlılığı, bundan sonra siz kotirovkanı imzalaya və ödəyə "
"bilməyəcəksiniz."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr "Variant Şəbəkə Girişi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "View Details"
msgstr "Daha ətraflı"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "View Quotation"
msgstr "Sitata Baxın"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__virtual_id
msgid "Virtual"
msgstr "Virtual"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "Etibarsız Əməliyyat"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Həcmi"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/res_config_settings.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning"
msgstr "Xəbərdarlıq"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Warning for %s"
msgstr "%s üçün xəbərdarlıq"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Warning for the change of your quotation's company"
msgstr "Kotirovka şirkətinin dəyişdirilməsi ilə bağlı xəbərdarlıq"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Satış Sifarişi ilə bağlı xəbərdarlıq"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning when Selling this Product"
msgstr "Bu məhsulu satarkən xəbərdarlıq"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Veb sayt Mesajları"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Veb saytın kommunikasiya tarixçəsi"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "You can invoice goods before they are delivered."
msgstr "Malları çatdırılmadan əvvəl faktura edə bilərsiniz."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""
"Siz göndərilmiş kotirovkanı və ya təsdiqlənmiş satış sifarişini silə "
"bilməzsiniz. Əvvəlcə onu ləğv etməlisiniz."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"            or check every order and invoice them one by one."
msgstr ""
"Siz bütün sifarişləri seçə və toplu olaraq hesab-faktura verə "
"bilərsiniz,<br> və ya hər sifarişi yoxlayın və onları bir-bir hesablayın."

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_provider__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""
"Siz burada satış sifarişlərində görünəcək rabitə növünü təyin edə "
"bilərsiniz. Müştəri ödəniş üsulunu seçdiyi zaman rabitə ona veriləcək."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot cancel a locked order. Please unlock it first."
msgstr ""
"Siz kilidlənmiş sifarişi ləğv edə bilməzsiniz. Zəhmət olmasa əvvəlcə "
"kilidini açın."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot change the pricelist of a confirmed order !"
msgstr "Siz təsdiqlənmiş sifarişin qiymət siyahısını dəyişə bilməzsiniz!"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""
"Siz məhsulun növünü dəyişə bilməzsiniz, çünki o, artıq satış sifarişlərində "
"istifadə olunur."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"Siz satış sifarişi xəttinin növünü dəyişə bilməzsiniz. Bunun əvəzinə cari "
"xətti silməli və uyğun tipdə yeni bir xətt yaratmalısınız."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "You cannot modify the product of this order line."
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid ""
"You cannot set a negative number for the default quotation validity. Leave "
"empty (or 0) to disable the automatic expiration of quotations."
msgstr ""
"Defolt kotirovka etibarlılığı üçün mənfi rəqəm təyin edə bilməzsiniz. "
"Kotirovkaların avtomatik bitməsini söndürmək üçün boş (və ya 0) qoyun."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Satdığınız və ya satın aldığınız hər şey üçün bir məhsul müəyyənləşdirməlisiniz,\n"
"saxlana bilən məhsul, istehlak materialı və ya xidmət olsun."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "Your Orders"
msgstr "Sifarişləriniz"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Your Reference:"
msgstr "Referansınız:"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Your browser does not support iframe."
msgstr "Brauzeriniz iframe-i dəstəkləmir."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr "Rəyiniz..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr "Sifarişiniz təsdiqləndi."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""
"Sifarişiniz imzalanıb, lakin təsdiqlənmək üçün hələ də ödəniş edilməlidir."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr "Sifarişiniz imzalanıb."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr "Sifarişiniz rədd ediləcək vəziyyətdə deyil."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Kotirovkanızda %(product_company)s şirkətinin məhsulları var, təklifiniz isə %(quote_company)s şirkətinə aiddir. \n"
" Lütfən, təklif etdiyiniz şirkətin adını dəyişin və ya məhsulları digər şirkətlərdən çıxarın (%(bad_products)s)."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "add the price of your product."
msgstr "məhsulunuzun qiymətini əlavə edin."

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
msgid "automatic invoicing: send ready invoice"
msgstr "avtomatik faktura: hazır faktura göndərin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpost birləşdiricisi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "bağla"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "günlər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "for the"
msgstr "üçün"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "for this Sale Order."
msgstr "bu Satış sifarişi üçün."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "let's continue"
msgstr "davam edək"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "quote."
msgstr "təklif."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "sale order"
msgstr "satış sifarişi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"selected\n"
"                    items?"
msgstr ""
"seçilmişdir\n"
"                    məhsullar?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "units"
msgstr "ədədlər"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "you confirm acceptance on the behalf of"
msgstr "adından qəbul etdiyinizi təsdiq edirsiniz"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
#: model:mail.template,subject:sale.mail_template_sale_payment_executed
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Gözlənilən Sifariş' or 'Sifariş' }} (Ref {{ object.name "
"or 'n/a' }})"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Təklif') or 'Sifariş' }} (Ref {{ "
"object.name or 'n/a' }})"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_cancellation
msgid ""
"{{ object.company_id.name }} {{ object.type_name }} Cancelled (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.type_name }} Ləğv edildi (Ref {{ "
"object.name or 'n/a' }})"
