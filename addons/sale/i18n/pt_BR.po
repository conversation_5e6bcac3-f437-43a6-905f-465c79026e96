# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# a75f12d3d37ea5bf159c4b3e85eb30e7_0fa6927, 2024
# Wil Odoo, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Dados coletados"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "Nº de pedidos de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "Nº de linhas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "Nº de pedidos de venda"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "%(attribute)s: %(values)s"
msgstr "%(attribute)s: %(values)s"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "%s has been created"
msgstr "%s foi criado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'PRO FORMA - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Cotação - %s' % (object.name)) or "
"'Pedido - %s' % (object.name)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "2023-12-31"
msgstr "31-12-2023"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "27.00"
msgstr "27,00"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "31.05"
msgstr "31,05"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr ""
"<b>Envie a cotação</b> para si mesmo e verifique o que o cliente receberá."

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_payment_executed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        A payment with reference\n"
"        <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        amounting\n"
"        <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"        for your order\n"
"        <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"        <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            is pending.\n"
"            <br/>\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Your order will be confirmed once the payment is confirmed.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Once confirmed,\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"                will remain to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            has been confirmed.\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"                <br/>\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">$ 10.00</span>\n"
"                remains to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Thank you for your trust!\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Olá,\n"
"        <br/><br/>\n"
"        O pagamento com referência\n"
"        <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        e valor\n"
"        <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">US$ 10,00</span>\n"
"        do pedido\n"
"        <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"        <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            está pendente.\n"
"            <br/>\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Seu pedido será confirmado quando o pagamento for confirmado.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Uma vez confirmado, restarão\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">US$ 10,00</span>\n"
"                a pagar.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            foi cofirmado.\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"                <br/>Restam\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">US$ 10.00</span>\n"
"                a pagar.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Agradecemos pela confiança!\n"
"        <br/>\n"
"        Não hesite em entrar em contato conosco se tiver alguma dúvida.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Your order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse; white-space: nowrap;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Products</span></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Quantity</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            Tax Excl.\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Tax Incl.\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-set=\"line_subtotal\" t-value=\"                     line.price_subtotal                     if object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'                     else line.price_total                 \"/>\n"
"            <t t-set=\"current_subtotal\" t-value=\"current_subtotal + line_subtotal\"/>\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and (                     line.display_type in ['line_section', 'line_note']                     or line.product_type == 'combo'                 )\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section' or line.product_type == 'combo'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Taking care of Trees Course</span>\n"
"                                <t t-set=\"current_section\" t-value=\"line\"/>\n"
"                                <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold; white-space: nowrap;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-if=\"current_section and (                     line_last                     or object.order_line[line_index+1].display_type == 'line_section'                     or object.order_line[line_index+1].product_type == 'combo'                     or (                         line.combo_item_id                         and not object.order_line[line_index+1].combo_item_id                     )                 ) and not line.is_downpayment\">\n"
"                <t t-set=\"current_section\" t-value=\"None\"/>\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 100%\" align=\"right\">\n"
"                            <span style=\"font-weight: bold;\">Subtotal:</span>\n"
"                            <span t-out=\"format_amount(current_subtotal, object.currency_id) or ''\">$ 10.00</span>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Delivery:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Taxes:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Total:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Bill to:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Payment Method:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <span style=\"font-weight:bold;\">Ship to:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Shipping Method:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Shipping Description:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"/>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Olá,\n"
"        <br/><br/>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Seu pedido <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> valor de <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">US$ 10,00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            foi confirmado.<br/>\n"
"            Agradecemos por sua confiança!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            está pendente e será confirmado quando o pagamento for recebido.\n"
"            <t t-if=\"object.reference\">\n"
"                Sua referência de pagamento é <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Aqui estão alguns documentos adicionais que podem ser do seu interesse:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Aqui está um documento adicional que pode ser do seu interesse:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Não hesite em entrar em contato conosco se tiver alguma dúvida.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse; white-space: nowrap;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Produtos</span></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Quantidade</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            Sem impostos\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Com impostos\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-set=\"line_subtotal\" t-value=\"                     line.price_subtotal                     if object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'                     else line.price_total                 \"/>\n"
"            <t t-set=\"current_subtotal\" t-value=\"current_subtotal + line_subtotal\"/>\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and (                     line.display_type in ['line_section', 'line_note']                     or line.product_type == 'combo'                 )\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section' or line.product_type == 'combo'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Curso Como cuidar de árvores</span>\n"
"                                <t t-set=\"current_section\" t-value=\"line\"/>\n"
"                                <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Curso Como cuidar de árvores</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tCurso Como cuidar de árvores</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold; white-space: nowrap;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">US$ 10,00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">US$ 10,00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-if=\"current_section and (                     line_last                     or object.order_line[line_index+1].display_type == 'line_section'                     or object.order_line[line_index+1].product_type == 'combo'                     or (                         line.combo_item_id                         and not object.order_line[line_index+1].combo_item_id                     )                 ) and not line.is_downpayment\">\n"
"                <t t-set=\"current_section\" t-value=\"None\"/>\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 100%\" align=\"right\">\n"
"                            <span style=\"font-weight: bold;\">Subtotal:</span>\n"
"                            <span t-out=\"format_amount(current_subtotal, object.currency_id) or ''\">US$ 10,00</span>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Entrega:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">US$ 0,00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Valor sem impostos:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">US$ 10,00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Valor sem impostos:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">US$ 10,00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Impostos:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">US$ 0,00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Total:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">US$ 10,00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Endereço de cobrança:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Forma de pagamento:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">US$ 10,00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <span style=\"font-weight:bold;\">Endereço de entrega:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Método de entrega:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Grátis)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">US$ 10,00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Descrição:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"/>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Olá,\n"
"        <br/><br/>\n"
"        Sua\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            fatura pro forma da <t t-out=\"doc_name or ''\">cotação</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (com referência: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            no valor de <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">US$ 10,00</span> está disponível.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">cotação</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (com referência: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            no valor de <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">US$ 10,00</span> está pronta para revisão..\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Aqui há documentos adicionais que podem ser do seu interesse:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Aqui há outro documento que pode ser do seu interesse:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Não hesite em entrar em contato se tiver alguma dúvida..\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_cancellation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"/>\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">user</t>,\n"
"        <br/><br/>\n"
"        Please be advised that your\n"
"        <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        has been cancelled. Therefore, you should not be charged further for this order.\n"
"        If any refund is necessary, this will be executed at best convenience.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"/>\n"
"        Caro <t t-out=\"object.partner_id.name or ''\">usuário</t>,\n"
"        <br/><br/>\n"
"        Informamos que a sua\n"
"        <t t-out=\"doc_name or ''\">cotação</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (com a referência: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        foi cancelada. Portanto, nenhum valor adicional será cobrado por este pedido.\n"
"        Caso algum reembolso seja necessário, será processado o mais breve possível.\n"
"        <br/><br/>\n"
"        Em caso de dúvidas, entre em contato conosco.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""
"<i class=\"fa fa-comment\"/> Entre em contato conosco para obter uma nova "
"cotação."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> Feedback"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Authorized"
msgstr "<i class=\"fa fa-fw fa-check\"/> Autorizado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check\"/> Pago"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Reversed"
msgstr "<i class=\"fa fa-fw fa-check\"/> Revertido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Expirado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Waiting Payment"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Aguardando pagamento"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"
msgstr "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/> Cancelado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                    Locked"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                    Bloqueado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print me-1\"/>View Details"
msgstr "<i class=\"fa fa-print me-1\"/>Ver detalhes"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Rejeitar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-usd me-1\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-usd me-1\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small class=\"text-muted\">Your contact</small>"
msgstr "<small class=\"text-muted\">Seu contato</small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">Sua vantagem</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Pedido de venda nº</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning me-1\" title=\"This "
"product is archived\" invisible=\"state not in ['draft', 'sent'] or not "
"is_product_archived\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning me-1\" title=\"This "
"product is archived\" invisible=\"state not in ['draft', 'sent'] or not "
"is_product_archived\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"flex-grow-1\">/ Month</span>"
msgstr "<span class=\"flex-grow-1\">/mês</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span class=\"mx-3\" invisible=\"not require_payment\">of</span>"
msgstr "<span class=\"mx-3\" invisible=\"not require_payment\">de</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Vendido</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                                By paying,\n"
"                                            </span>"
msgstr ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                                Ao pagar,\n"
"                                            </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"1\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"
msgstr ""
"<span invisible=\"1\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\">% </span>"
msgstr ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\">% </span>"

#. module: sale
#: model_terms:web_tour.tour,rainbow_man_message:sale.sale_tour
msgid ""
"<span><b>Congratulations</b>, your first quotation is sent!<br>Check your email to validate the quote.\n"
"        </span>"
msgstr ""
"<span><b>Parabéns</b>, a sua primeira cotação foi enviada!<br>Verifique o seu e-mail para validar a cotação.\n"
"        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>Valor</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid ""
"<span>Are you sure you want to cancel this order? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Draft invoices for this order will be cancelled. <br/>\n"
"                        </span>"
msgstr ""
"<span>Tem certeza de que quer cancelar este pedido? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            As faturas provisórias do pedido serão canceladas. <br/>\n"
"                        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>% desc.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_kanban
msgid "<span>Sales visibility</span>"
msgstr "<span>Visibilidade em Vendas</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Impostos</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: This quote contains archived product(s)</span>"
msgstr "<span>Aviso: Esta cotação contém produto(s) arquivado(s) </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: this order might be a duplicate of</span>"
msgstr "<span>Aviso: este pedido parece uma duplicata de</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong class=\"d-block mt-3\">Shipping Address</strong>"
msgstr "<strong class=\"d-block mt-3\">Endereço de entrega</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Subtotal</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration</strong>"
msgstr "<strong>Expiração</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Observações sobre posição fiscal:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson</strong>"
msgstr "<strong>Vendedor</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Assinatura</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>Obrigado!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>Esta oferta expirou!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been cancelled.</strong>"
msgstr "<strong>Esta cotação foi cancelada.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference</strong>"
msgstr "<strong>Sua referência</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "Um pedido de venda confirmado necessita de uma data de confirmação."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "A line on these orders missing a product, you cannot confirm it."
msgstr "Há uma linha sem produto nesses pedidos, você não pode confirmá-lo.'"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A note, whose content usually applies to the section or product above."
msgstr "Uma anotação cujo conteúdo se aplica à seção ou produto acima."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"A sale order line's combo item must be among its linked line's available "
"combo items."
msgstr ""
"O item de combo de uma linha de pedido de venda deve estar entre os itens de"
" combo disponíveis de sua linha vinculada."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "A sale order line's product must match its combo item's product."
msgstr ""
"O produto de uma linha de pedido de venda deve corresponder ao produto de "
"seu item de combo."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A section title"
msgstr "Um título de seção"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for "
"invoicing,according to their invoicing policy (based on ordered or delivered"
" quantity)."
msgstr ""
"Uma fatura padrão é emitida com todas as linhas do pedido prontas para serem"
" faturadas de acordo com a política de faturamento (baseado na quantidade "
"solicitada ou entregue)."

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "Um aviso pode ser configurado em um produto ou um cliente (venda)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Possibilita selecionar um tipo de embalagem em pedidos de venda e forçar uma"
" quantidade que seja um múltiplo do número de unidades por embalagem."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Pay Quotation"
msgstr "Aceitar e pagar cotação"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Sign Quotation"
msgstr "Aceitar e assinar cotação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "Aceitar e pagar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "Aceitar e assinar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "Aviso de acesso"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"De acordo com a configuração do produto, a quantidade entregue pode ser calculada automaticamente pelo mecanismo:\n"
"- Manual: a quantidade é definida manualmente na linha\n"
"- Analítico das despesas: a quantidade é a soma da quantidade das despesas lançadas\n"
"- Planilha de horas: a quantidade é a soma das horas registradas nas tarefas vinculadas a essa linha de venda\n"
"- Movimentos de estoque: a quantidade vem de seleções confirmadas\n"

#. module: sale
#: model:ir.model,name:sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modelo de plano de contas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Número da conta"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr "Entrada de receita acumulada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Requer ação"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
#: model:ir.ui.menu,name:sale.sale_menu_config_activities
msgid "Activities"
msgstr "Atividades"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoração de atividade excepcional"

#. module: sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_plan
msgid "Activity Plans"
msgstr "Plano de atividades"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Status da atividade"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone do tipo de atividade"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipos de atividades"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                (e.g. \"Delivery scheduling\", \"Order Payment Follow-up\", ...)"
msgstr ""
"Os planos de atividades são usados para atribuir uma lista de atividades com"
" apenas alguns cliques (ex.: \"Agendamento de entrega\", \"Acompanhamento do"
" pagamento do pedido\", ...)"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Add"
msgstr "Adicionar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "Adicionar uma nota"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "Adicionar produto"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "Adiciona uma seção"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add note"
msgstr "Adicionar nota"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Add one"
msgstr "Adicionar um"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Add optional products"
msgstr "Adicionar produtos opcionais"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add product"
msgstr "Adicionar produto"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add section"
msgstr "Adicionar seção"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "Adicionar diversas variantes a um pedido a partir de uma grade"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "Permite enviar fatura pro forma aos seus clientes"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "Permite enviar uma fatura pro forma."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""
"Permite que você compartilhe o documento com seus clientes dentro de uma venda.\n"
"Deixe em branco se não quiser compartilhar esse documento com o cliente da venda.\n"
"Na cotação: o documento será enviado e poderá ser acessado pelos clientes a qualquer momento.\n"
"por exemplo, essa opção pode ser útil para compartilhar arquivos de descrição do produto.\n"
"Na confirmação do pedido: o documento será enviado e poderá ser acessado pelos clientes.\n"
"por exemplo, essa opção pode ser útil para compartilhar o Manual do Usuário ou o conteúdo digital comprado no e-Commerce. \n"
"Dentro da cotação: O documento será incluído no PDF da cotação e do pedido de venda entre as páginas de cabeçalho e a tabela de cotação."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__amount_paid
msgid "Already Paid"
msgstr "Já foi pago"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_invoiced
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_invoiced
msgid "Already invoiced"
msgstr "Já faturado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "Sincronização com a Amazon"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_amount
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Amount"
msgstr "Valor"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Total sem desconto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "Quantidade de cotações a faturar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "Amount to invoice"
msgstr "Total a faturar"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"            ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""
"Um pedido se torna upsell quando a quantidade entregue for maior que a quantidade\n"
"            inicialmente solicitada, e a política de faturamento é baseada na quantidade solicitada."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribuição analítica"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "Análise a partir de despesas"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha analítica"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Aplicações do plano analítico"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Precisão analítica"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Linhas analíticas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Apply"
msgstr "Aplicar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Aplicar descontos manuais em linhas do pedido ou mostrar descontos "
"calculados a partir de listas de preços (ativar opção nas configurações de "
"lista de preços)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the"
msgstr "Tem certeza de que deseja cancelar as"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the selected item?"
msgstr "Tem certeza de que quer cancelar o item selecionado?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Tem certeza de que deseja anular a transação autorizada? Esta ação não pode "
"ser desfeita."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"            to sell extra hours when all ordered hours have been consumed."
msgstr ""
"Por exemplo, se você vende horas pré-pagas de serviços, o Odoo recomenda\n"
"            vender horas extras quando todas as horas solicitadas tiverem sido consumidas."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "A preço de custo"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Contagem de anexos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Valores de atributos"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Atributos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__author_id
msgid "Author"
msgstr "Autor"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Transações autorizadas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "Fatura automática"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Average"
msgstr "Média"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Bacon Burger"
msgstr "Hambúrguer de bacon"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nome do banco"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "Baseado na ID do cliente"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "Baseado na referência do documento"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Beat competitors with stunning quotations!"
msgstr "Acabe com a concorrência com cotações impressionantes!"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "Mensagem de bloqueio"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body_has_template_value
msgid "Body content is the same as the template"
msgstr "O conteúdo do corpo do texto é igual ao do modelo"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Boost sales with online payments or signatures, upsells, and a great "
"customer portal."
msgstr ""
"Aumente as vendas com pagamentos ou assinaturas on-line, upsells e um "
"excelente portal do cliente."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Impulsione as suas vendas com vários tipos de programas: cupons, promoções, "
"cartões-presente, fidelidade. É possível definir condições específicas "
"(produtos, clientes, quantidade mínima de compra, período). As recompensas "
"podem ser descontos (% ou um valor fixo) ou produtos gratuitos."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Build your first quotation right here!"
msgstr "Crie sua primeira cotação aqui mesmo!"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying a <u>down payment</u> of"
msgstr "Ao pagar a <u>entrada</u> de"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying,"
msgstr "Ao pagar,"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By signing, you confirm acceptance on behalf of"
msgstr "Ao assinar, você confirma a aceitação em nome de"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "Campanha"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__can_edit_body
msgid "Can Edit Body"
msgstr "Corpo do texto pode ser editado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "Pode editar produto"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model:ir.actions.act_window,name:sale.action_mass_cancel_orders
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Cancelar"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Cancel %s"
msgstr "Cancelar %s"

#. module: sale
#: model:ir.model,name:sale.model_sale_mass_cancel_orders
msgid "Cancel multiple quotations"
msgstr "Cancelar várias cotações"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Cancel quotations"
msgstr "Cancelar cotações"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Cannot create an invoice. No items are available to invoice.\n"
"\n"
"To resolve this issue, please ensure that:\n"
"   • The products have been delivered before attempting to invoice them.\n"
"   • The invoicing policy of the product is configured correctly.\n"
"\n"
"If you want to invoice based on ordered quantities instead:\n"
"   • For consumable or storable products, open the product, go to the 'General Information' tab and change the 'Invoicing Policy' from 'Delivered Quantities' to 'Ordered Quantities'.\n"
"   • For services (and other products), change the 'Invoicing Policy' to 'Prepaid/Fixed Price'.\n"
msgstr ""
"Não foi possível criar uma fatura. Não há itens disponíveis para faturar.\n"
"\n"
"Para resolver essa questão, certifique-se de que:\n"
"   • Os produtos foram entregues antes da tentativa de faturá-los.\n"
"   • A política de faturamento do produto está configurada corretamente.\n"
"\n"
"   • Se, em vez disso, você quiser faturar com base em quantidades solicitadas:\n"
"   • Para produtos consumíveis ou armazenáveis, abra o produto, vá para a guia \"Informações gerais\" e altere a \"Política de faturamento\" de \"Quantidades entregues\" para \"Quantidades solicitadas\"\n"
"   • Para serviços (e outros produtos), altere a \"Política de faturamento\" para \"Pré-pago/Preço fixo\".\n"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "Capturar transação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Catalog"
msgstr "Catálogo"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "Categoria"

#. module: sale
#: model:product.template,name:sale.product_product_1_product_template
msgid "Chair floor protection"
msgstr "Proteção de piso para cadeiras"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Changing the company of an existing quotation might need some manual "
"adjustments in the details of the lines. You might consider updating the "
"prices."
msgstr ""
"Caso altere a empresa de uma cotação existente, pode ser necessário ajustes "
"manuais nos detalhes das linhas. Considere atualizar os preços."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Check a sample. It's clean!"
msgstr "Confira uma amostra. Está limpa!"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Click here to add some products or services to your quotation."
msgstr "Clique aqui para adicionar produtos ou serviços à sua cotação."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/xml/sales_team_progress_bar_template.xml:0
msgid "Click to define an invoicing target"
msgstr "Clique para definir uma meta de faturamento."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Fechar"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_combos
msgid "Combo Choices"
msgstr "Opções de combo"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__combo_item_id
msgid "Combo Item"
msgstr "Item do combo"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_commission
msgid "Commissions"
msgstr "Comissões"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_provider__so_reference_type
msgid "Communication"
msgstr "Comunicação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Communication history"
msgstr "Histórico de comunicação"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Empresa"

#. module: sale
#: model:ir.model,name:sale.model_base_document_layout
msgid "Company Document Layout"
msgstr "Layout do documento da empresa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Calcular custos de envio e enviar pela DHL"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Calcular custos de envio e enviar pelo Easypost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Calcular custos e enviar pelo FedEx"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "Calcular custos de envio e enviar pelo Sendcloud"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "Calcular os custos de envio e envie com a Shiprocket"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "Calcular custos de frete e enviar com Starshipit"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Calcular custos de envio e enviar pela UPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Calcular custos de envio e enviar pela USPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Calcular custos de envio e enviar pelo bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Calcular custos de envio nos pedidos"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Configuração"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.js:0
msgid "Configure your product"
msgstr "Configurar seus produtos"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "Confirmar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__confirmation_message
msgid "Confirmation Message"
msgstr "Mensagem de confirmação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "Conectores"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__consolidated_billing
msgid "Consolidated Billing"
msgstr "Faturamento consolidado"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body
msgid "Contents"
msgstr "Conteúdo"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Conversão entre unidades de medida só pode ocorrer se eles pertencerem à "
"mesma categoria. A conversão será feita com base nas proporções."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__country_code
msgid "Country code"
msgstr "Código do país"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_loyalty
msgid "Coupons & Loyalty"
msgstr "Cupons e fidelidade"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "Data de criação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Draft"
msgstr "Criar Rascunho"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr "Criar fatura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Create Invoices"
msgstr "Criar faturas"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid "Create a Sale Order Activity Plan"
msgstr "Criar um plano de atividade de pedidos de venda"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "Criar uma fatura de cliente"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "Criar um novo produto"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a new quotation, the first step of a new sale!"
msgstr "Crie uma nova cotação, o primeiro passo de uma nova venda!"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoice(s)"
msgstr "Criar Fatura(s)"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""
"Crie faturas, registre pagamentos e acompanhe as discussões com seus "
"clientes."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__service_tracking
msgid "Create on Order"
msgstr "Criar no pedido"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__consolidated_billing
msgid ""
"Create one invoice for all orders related to same customer and same "
"invoicing address"
msgstr ""
"Crie uma fatura para todos os pedidos relacionados ao mesmo cliente e "
"endereço de faturamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_date
msgid "Created on"
msgstr "Criado em"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "Data de criação"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""
"Data de criação de pedidos em rascunho/enviados,\n"
"Data de confirmação dos pedidos."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "Cartão de crédito e débito (via Stripe)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_report__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Taxa de câmbio"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
msgid "Custom"
msgstr "Personalizado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Valores personalizados"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Instruções de pagamento personalizadas"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "País do cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "Entidade do cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "Segmento do cliente"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "URL do portal do cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Referência do cliente"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "Assinatura do cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__state_id
msgid "Customer State"
msgstr "Situação do cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_zip
msgid "Customer ZIP"
msgstr "CEP do cliente"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "Customer needs to pay at least %(amount)s to confirm the order."
msgstr ""
"O cliente precisa pagar pelo menos %(amount)s para confirmar o pedido."

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_customer
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Clientes"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "Conector DHL Express"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Data"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Data:"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,help:sale.field_res_config_settings__quotation_validity_days
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Days between quotation proposal and expiration. 0 days means automatic "
"expiration is disabled"
msgstr ""
"Dias entre a proposta de cotação e expiração. 0 dias significa que a "
"expiração automática está desativada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "Deduzir valores de entrada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity"
msgstr "Validade padrão da cotação"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__company_price_include
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_price_include
msgid "Default Sales Price Include"
msgstr "Padrão - Preço de venda inclui"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__company_price_include
#: model:ir.model.fields,help:sale.field_sale_order_line__company_price_include
msgid ""
"Default on whether the sales price used on the product and invoices with "
"this Company includes its taxes."
msgstr ""
"Predeterminação sobre a inclusão de impostos no preço de venda usado nos "
"produtos e nas faturas dessa empresa."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Default period during which the quote is valid and can still be accepted by "
"the customer. The default can be changed per order or template."
msgstr ""
"Período padrão durante o qual a cotação é válida e ainda pode ser aceita "
"pelo cliente. O padrão pode ser alterado por pedido ou modelo."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__sale_discount_product_id
msgid "Default product used for discounts"
msgstr "Produto padrão utilizado para descontos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "Entregar conteúdo por e-mail"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "Entregue"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Delivered Quantity: %s"
msgstr "Quantidade entregue: %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "Quantidades entregues"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Endereço de entrega"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "Data de entrega"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Métodos de entrega"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivery Quantity"
msgstr "Quantidade entregue"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"A data de entrega prometida ao cliente é calculada a partir do menor tempo "
"de entrega das linhas do pedido no caso de produtos de serviços. No caso de "
"entregas (produtos), a política de envio do pedido será levada em "
"consideração para usar o menor ou maior tempo de entrega das linhas do "
"pedido."

#. module: sale
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Entrada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Descrição"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "% desc."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Discard"
msgstr "Descartar"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/wizard/sale_order_discount.py:0
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount"
msgstr "Desconto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr "Desconto %"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%"
msgstr "Desconto %(percent)s%%"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%- On products with the following taxes %(taxes)s"
msgstr ""
"Desconto %(percent)s%%- Em produtos com os seguintes impostos %(taxes)s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Desconto (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "Valor do desconto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_discount_product_id
msgid "Discount Product"
msgstr "Produto com desconto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_type
msgid "Discount Type"
msgstr "Tipo de desconto"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_discount
msgid "Discount Wizard"
msgstr "Assistente de descontos"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Desconto nas linhas"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount- On products with the following taxes %(taxes)s"
msgstr "Desconto - Em produtos com os seguintes impostos %(taxes)s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount:"
msgstr "Desconto:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_discount_per_so_line
msgid "Discounts"
msgstr "Descontos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_draft_invoice_warning
msgid "Display Draft Invoice Warning"
msgstr "Exibir aviso de fatura em rascunho"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_invoice_amount_warning
msgid "Display Invoice Amount Warning"
msgstr "Exibir aviso de quantia da fatura"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "Tipo de exibição"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Conta analítica de distribuição"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Documents"
msgstr "Documentos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domínio"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment"
msgstr "Entrada"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (Cancelled)"
msgstr "Pagamento adiantado (cancelado)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (ref: %(reference)s on %(date)s)"
msgstr "Pagamento adiantado (ref: %(reference)s em %(date)s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr "Valor da entrada (fixo)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment: %(date)s (Draft)"
msgstr "Entrada: %(date)s (rascunho)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payments"
msgstr "Entradas"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "Entrada (valor fixo)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "Entrada (porcentagem)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Down payment <br/>"
msgstr "Entrada <br/>"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment invoice"
msgstr "Fatura de entrada"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment of %s%%"
msgstr "Entrada no valor de %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"Entradas podem ser feitas ao criar faturas a partir de um pedido de venda. "
"Elas não são copiadas ao duplicar um pedido de venda."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_category__property_account_downpayment_categ_id
msgid "Downpayment Account"
msgstr "Conta de pagamento de entrada"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Draft Invoices"
msgstr "Faturas em rascunho"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Duplicated Documents"
msgstr "Documentos duplicados"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__duplicated_order_ids
msgid "Duplicated Order"
msgstr "Pedido duplicado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Conector Easypost"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_product_field.js:0
msgid "Edit Configuration"
msgstr "Editar configuração"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr "Assinatura eletrônica"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "E-mail"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistente de composição de e-mail"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__invoice_mail_template_id
msgid "Email sent to the customer once the invoice is available."
msgstr "E-mail enviado ao cliente quando a fatura estiver disponível."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_template_attribute_line/product_template_attribute_line.js:0
msgid "Enter a customized value"
msgstr "Inserir um valor personalizado"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Error importing attachment '%(file_name)s' as order (decoder=%(decoder)s)"
msgstr ""
"Erro ao importar anexo '%(file_name)s' como pedido (decoder=%(decoder)s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Data prevista"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "Previsto:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Expense"
msgstr "Despesa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "Expiração"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Expiration Date:"
msgstr "Data de expiração:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Filtros estendidos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Valores extras"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Extra line with %s"
msgstr "Linha extra com %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "Conector FedEx"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Posição fiscal"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""
"Posições fiscais são usadas para ajustar impostos e contas para clientes ou "
"pedidos/faturas específicos. O valor padrão é definido no cadastro do "
"cliente."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__amount
msgid "Fixed Amount"
msgstr "Valor fixo"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Follow, view or pay your orders"
msgstr "Acompanhe, visualize ou pague seus pedidos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (usuários)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ícone do Font Awesome, ex: fa-tasks"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr "Valores inválidos em uma linha não faturável do pedido de venda."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"A partir deste relatório, você pode ter uma visão geral do valor faturado ao"
" seu cliente. A ferramenta de busca também pode ser usada para personalizar "
"seus relatórios de faturas e ajustar esta análise de acordo com as suas "
"necessidades."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Full amount <br/>"
msgstr "Valor total <br/>"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Fully Invoiced"
msgstr "Totalmente faturado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Atividades futuras"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Gerar o link de pagamento"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "Gerar um link de pagamento "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""
"Gere a fatura automaticamente quando o pagamento online for confirmado."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Generated Orders"
msgstr "Pedidos gerados"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "Obter notificações em pedidos para produtos ou clientes"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__so_discount
msgid "Global Discount"
msgstr "Desconto global"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Go ahead and send the quotation."
msgstr "Vá em frente e envie a cotação."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""
"Mercadorias são materiais tangíveis e produtos fornecidos.\n"
"Um serviço é um produto não material oferecido."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "Conceder descontos em linhas de pedidos de vendas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Peso bruto"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_active_pricelist
msgid "Has Active Pricelist"
msgstr "Tem lista de preços ativa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_archived_products
msgid "Has Archived Products"
msgstr "Tem produtos arquivados"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__has_confirmed_order
msgid "Has Confirmed Order"
msgstr "Confirmou o pedido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_fpos
msgid "Has Fiscal Position Changed"
msgstr "Posição fiscal mudou"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "Tem uma mensagem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr "Houve mudança na lista de preços"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "Tem pagamento de entrada"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__hidden
msgid "Hidden"
msgstr "Oculto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ícone para indicar uma atividade excepcional."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se marcado, novas mensagens solicitarão sua atenção."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se marcado, algumas mensagens terão um erro de entrega."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__journal_id
msgid ""
"If set, the SO will invoice in this journal; otherwise the sales journal "
"with the lowest sequence is used."
msgstr ""
"Se definido, o pedido de venda será faturado nesse diário. Caso contrário, "
"será usado o diário de vendas com a sequência mais baixa."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"Se a venda estiver travada, você não poderá mais modificá-la. Porém, você "
"ainda pode gerar uma fatura ou realizar a entrega."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr "Se correto, a embalagem pode ser utilizada para pedidos de venda"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""
"Se você alterar a lista de preço, apenas novas linhas adicionadas serão "
"afetadas."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Immediate"
msgstr "Imediato"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "Importar pedidos da Amazon e sincronizar as entregas"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Import Template for Products"
msgstr "Importar modelo para produtos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_view_search_catalog
msgid "In the Order"
msgstr "No pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "imposto incluso)"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Invalid discount amount"
msgstr "Valor de desconto inválido"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid order."
msgstr "Pedido inválido."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid signature data."
msgstr "Dados de assinatura inválidos."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move.py:0
msgid "Invoice %s paid"
msgstr "Fatura %s paga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Endereço de cobrança"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr "Alerta de fatura"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Fatura confirmada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "Contagem de fatura"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Fatura criada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "Modelo de e-mail de fatura"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Linhas da fatura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Faturar pedido de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_report__line_invoice_status
msgid "Invoice Status"
msgstr "Status da fatura"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr ""
"Faturar após entrega, baseado na quantidade entregue, e não a solicitada."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "Faturar as quantidades pedidas assim que esse serviço for vendido."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Montante faturado no mês corrente. Esse é o montante que o canal de vendas "
"faturou esse mês. Isso é usado para calcular a razão de progressão da "
"receita atual e da meta na visão kanban."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "Faturar o que é entregue"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "Faturar o que é pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "Faturado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_invoiced
msgid "Invoiced Amount"
msgstr "Valor faturado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Quantidade faturada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced_posted
msgid "Invoiced Quantity (posted)"
msgstr "Quantidade faturada (lançada)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Invoiced Quantity: %s"
msgstr "Quantidade faturada: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Faturado este mês"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Faturas"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Análise de faturas"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Estatísticas das faturas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Faturamento"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address"
msgstr "Endereço de faturamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__journal_id
msgid "Invoicing Journal"
msgstr "Diário de faturas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Política de faturamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "Meta de faturamento"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address"
msgstr "Endereço de cobrança e entrega:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__is_downpayment
msgid "Is Downpayment"
msgstr "É entrada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__is_mail_template_editor
msgid "Is Editor"
msgstr "É editor"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is Expired"
msgstr "Expirou"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "É um seguidor"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_product_archived
msgid "Is Product Archived"
msgstr "É produto arquivado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "É um pagamento de entrada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "É despesa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "Este é um produto configurável?"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""
"É verdadeiro se a linha do pedido de venda vem de uma despesa ou fatura de "
"fornecedor"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Issued Date"
msgstr "Data de emissão"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"Não é possível modificar os seguintes campos em um pedido travado:\n"
"%s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "Entrada no diário"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "Item do diário"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__lang
msgid "Language"
msgstr "Idioma"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Last Invoices"
msgstr "Últimas faturas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Atividades atrasadas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Prazo de entrega"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Permitir que seus clientes façam login para ver os documentos deles"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Let’s create a beautiful quotation in a few clicks ."
msgstr "Vamos criar uma bela cotação com apenas alguns cliques."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Linha do pedido vinculada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_ids
msgid "Linked Order Lines"
msgstr "Linhas de pedidos vinculados"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_virtual_id
msgid "Linked Virtual"
msgstr "Virtual vinculado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Lock"
msgstr "Travar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "Travar vendas confirmadas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__locked
msgid "Locked"
msgstr "Travado"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__locked
msgid "Locked orders cannot be modified."
msgstr "Pedidos bloqueados não podem ser modificados."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__template_id
msgid "Mail Template"
msgstr "Modelo de e-mail"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Make your quote attractive by adding header pages, product descriptions and "
"footer pages to your quote."
msgstr ""
"Deixe sua cotação mais atrativa incluindo páginas de cabeçalho, descrições "
"de produto e páginas de rodapé."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Promotions, Coupons, Loyalty cards, Gift cards & eWallet"
msgstr ""
"Gerencie promoções, cupons, cartões de fidelidade, cartões-presente e "
"carteira eletrônica"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Sales & teams targets and commissions"
msgstr "Gerenciar metas e comissões de vendas e equipes"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "Manual"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "Pagamento manual"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "Definir quantidades no pedido manualmente"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Definir quantidades no pedido manualmente: faturar com base em quantidades inseridas manualmente, sem a criação de uma conta analítica.\n"
"Planilha de horas no contrato: faturar com base nas horas registradas na planilha de horas relacionada.\n"
"Criar uma tarefa e registrar horas: criar uma tarefa na validação do pedido de venda e registrar as horas trabalhadas."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Margens"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr "Marcar cotação como enviada"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "Marketing"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "Meio"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Message"
msgstr "Mensagem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "Erro na entrega da mensagem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Mensagem para o pedido de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Mensagem para a linha do pedido de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Método"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Método para atualizar a quantidade entregue"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr ""
"Campos obrigatórios ausentes em linha contabilizável do pedido de venda."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Mitchell Admin"
msgstr "Michel Admin"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo da minha atividade"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Meus pedidos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "Minhas cotações"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Minhas linhas de pedido de venda"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "New"
msgstr "Novo"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Nova cotação"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Evento no calendário para a próxima atividade"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prazo da próxima atividade"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Resumo da próxima atividade"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da próxima atividade"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "Não"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "Sem mensagens"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "No attachment was provided"
msgstr "Sem anexos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "No further requirements for this payment"
msgstr "Sem mais requisitos para este pagamento"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "Não editar pedidos depois de confirmados"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr "Nenhum pedido a faturar encontrado"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr "Nenhum pedido com upsell encontrado."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "Nota"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__no
msgid "Nothing to Invoice"
msgstr "Nada para faturar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Number"
msgstr "Número"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de ações"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Número de dias entre a confirmação do pedido e a entrega dos produtos ao "
"cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensagens que requerem ação"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com erro de entrega"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "Número de cotações a faturar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "Número de vendas a faturar"

#. module: sale
#: model:product.template,description_sale:sale.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr "As cadeiras de escritório podem danificar seu piso: proteja-o."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__sol_discount
msgid "On All Order Lines"
msgstr "Em todas linhas do pedido de venda"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__sale_order
msgid "On confirmed order"
msgstr "Na confirmação do pedido"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__quotation
msgid "On quote"
msgstr "Na cotação"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"Once a sales order is confirmed, you can't remove one of its lines (we need to track if something gets invoiced or delivered).\n"
"                Set the quantity to 0 instead."
msgstr ""
"Após o pedido de venda ser confirmado, você não pode remover linhas dele (precisamos monitorar se algo é entregue ou faturado).\n"
"                 Em vez disso, defina a quantidade como zero."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""
"Quando a cotação for confirmada pelo cliente, ela se tornará um pedido de "
"venda.<br> Em seguida, você poderá criar uma fatura e coletar o pagamento."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"Quando a cotação for confirmada, ela se tornará um pedido de venda.<br> Em "
"seguida, você poderá criar uma fatura e coletar o pagamento."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
msgid "Online Payment"
msgstr "Pagamento online"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_menu
msgid "Online Payments"
msgstr "Pagamentos online"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
msgid "Online Signature"
msgstr "Assinatura online"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online payment"
msgstr "Pagamento online"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online signature"
msgstr "Assinatura online"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_invoiced
msgid "Only confirmed down payments are considered."
msgstr "Somente pagamentos de entrada confirmados são considerados."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Only draft orders can be marked as sent directly."
msgstr ""
"Apenas pedidos em rascunho podem ser marcados como enviados diretamente."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr ""
"Apenas um valor personalizado é permitido por valor de atributo por linha do"
" pedido."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
msgid "Operation not supported"
msgstr "Operação não compatível."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option for: %s"
msgstr "Opção para: %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option: %s"
msgstr "Opção: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "Produtos opcionais"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""
"Produtos opcionais são sugeridos sempre que o cliente clica em *Adicionar ao"
" carrinho* (estratégia de venda cruzada, por exemplo, para computadores: "
"garantia, software etc.)."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_cancel__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma opcional de tradução (código ISO) para selecionar ao enviar um "
"e-mail. Se não for definido, a versão inglês será usada. Isso geralmente "
"deve ser uma expressão marcadora de posição que fornece o idioma apropriado,"
" por exemplo, {{ object.partner_id.lang }}."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_report__order_reference
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Pedido nº"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr "Contagem de pedidos"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Date"
msgstr "Data do pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Order Date:"
msgstr "Data do pedido:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Order Date: Last 365 Days"
msgstr "Data do pedido: últimos 365 dias"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__invoice_status
msgid "Order Invoice Status"
msgstr "Status da fatura do pedido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Linhas do pedido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Referência do pedido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Situação do pedido"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr "Upsell do pedido"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Order signed by %s"
msgstr "Pedido assinado por %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "Pedido a faturar"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr "Quantidade solicitada: %(old_qty)s -> %(new_qty)s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""
"Quantidade solicitada: faturar quantidades solicitadas pelo cliente.\n"
"Quantidade entregue: faturar quantidades entregues ao cliente."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "Quantidades solicitadas"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Pedidos"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "Pedidos a faturar"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "A fazer upsell"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Oscar Morgan"
msgstr "Oscar Moreno"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "Outras informações"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "PDF Quote"
msgstr "Cotação por PDF"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "PDF Quote builder"
msgstr "Construtor de cotações em PDF"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "Fatura PRO FORMA"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "Embalagem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Quantidade por embalagem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_credit_warning
msgid "Partner Credit Warning"
msgstr "Aviso de crédito do parceiro"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "Pagar agora"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment provider"
msgstr "Pagar com outro provedor de pagamento"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment"
msgstr "Pagamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Instruções de pagamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Método de pagamento"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_method_menu
msgid "Payment Methods"
msgstr "Formas de pagamento"

#. module: sale
#: model:ir.model,name:sale.model_payment_provider
msgid "Payment Provider"
msgstr "Provedor de serviços de pagamento"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_provider_menu
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Providers"
msgstr "Provedores de serviços de pagamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr "Ref. de pagamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Condições de pagamento"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_token_menu
msgid "Payment Tokens"
msgstr "Tokens de pagamentos"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transação do pagamento"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_transaction_menu
msgid "Payment Transactions"
msgstr "Transações de pagamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_paid
msgid "Payment Transactions Amount"
msgstr "Valor das transações de pagamento"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr "Condições de pagamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_percentage
msgid "Percentage"
msgstr "Porcentagem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "URL do acesso ao portal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_res_config_settings__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_sale_order__prepayment_percent
msgid "Prepayment percentage"
msgstr "Porcentagem do pagamento adiantado"

#. module: sale
#. odoo-python
#: code:addons/sale/models/res_company.py:0
#: code:addons/sale/models/sale_order.py:0
msgid "Prepayment percentage must be a valid percentage."
msgstr "A porcentagem de pagamento adiantado deve ser uma porcentagem válida."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Preview"
msgstr "Visualizar"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
msgid "Price"
msgstr "Preço"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Preço un. simplificado sem imposto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax incl"
msgstr "Preço un. simplificado com imposto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Lista de preços"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__pricelist_item_id
msgid "Pricelist Item"
msgstr "Item da lista de preços"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Listas de preços"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Preços"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "Fatura pro forma"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Pro-Forma Invoice #"
msgstr "Fatura pro forma nº"

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Faturas pro forma"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Produto"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Valor personalizado do atributo do produto"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "Catálogo de produtos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__product_catalog_product_is_in_sale_order
msgid "Product Catalog Product Is In Sale Order"
msgstr "Catálogo de produto, esse produto está em um pedido de venda"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_categories
msgid "Product Categories"
msgstr "Categorias de produtos"

#. module: sale
#: model:ir.model,name:sale.model_product_category
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Categoria do produto"

#. module: sale
#: model:ir.model,name:sale.model_product_document
msgid "Product Document"
msgstr "Documento do produto"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
#: code:addons/sale/static/src/js/product_card/product_card.xml:0
msgid "Product Image"
msgstr "Imagem do produto"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "Embalagem do produto"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_tags
msgid "Product Tags"
msgstr "Marcadores de produto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "Modelo de produto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Tipo de produto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr "Unidade de medida do produto somente leitura"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Variant"
msgstr "Variante do produto"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Variantes de produto"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed according to pricelist %s."
msgstr ""
"Os preços dos produtos foram recalculados de acordo com a lista de preços "
"%s."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed."
msgstr "Os preços dos produtos foram recalculados."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product taxes have been recomputed according to fiscal position %s."
msgstr ""
"Os impostos dos produtos foram recalculados de acordo com a posição fiscal "
"%s."

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.menu_reporting_product
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "Produtos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Promotions, Loyalty & Gift Card"
msgstr "Promoções, fidelidade e cartão-presente"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Qtd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Qtd entregue"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Qtd faturada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "Qtd solicitada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Qtd a entregar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Qtd a faturar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "Quantidades a faturar a partir de pedidos de vendas"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Quantity"
msgstr "Quantidade"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "Quantity To Invoice"
msgstr "Quantidade a faturar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "Quantidade:"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
msgid "Quotation"
msgstr "Cotação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Cotação nº"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr "Contagem de cotações"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "Data da cotação"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "Cotação enviada"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/res_config_settings.py:0
msgid "Quotation Validity is required and must be greater or equal to 0."
msgstr "A validade da cotação é necessária e deve ser superior a 0."

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_viewed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_viewed
msgid "Quotation Viewed"
msgstr "Cotação visualizada"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Cotação confirmada"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Cotação enviada"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Quotation viewed by customer %s"
msgstr "Cotação visualizada pelo cliente %s"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotations"
msgstr "Cotações"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations & Orders"
msgstr "Cotações e pedidos"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Análise das cotações"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Cotação e vendas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Quotations to review"
msgstr "Cotações a revisar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__rating_ids
msgid "Ratings"
msgstr "Classificações"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Costs"
msgstr "Refaturar custos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr "Política de refaturamento visível"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__recipient_ids
msgid "Recipients"
msgstr "Destinatários"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr "Recomendar ao \"Adicionar ao carrinho\" ou à cotação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr "Recalcular todos os preços com base nesta lista de preços"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all taxes based on this fiscal position"
msgstr "Recalcular todos os impostos com base nesta posição fiscal"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request"
msgstr "Referência do documento que gerou esta solicitação de pedido de venda"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr "Fatura normal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr "Rejeitar esta cotação"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Remove"
msgstr "Remover"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Remove one"
msgstr "Remover um"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__render_model
msgid "Rendering Model"
msgstr "Modelo de renderização"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "Relatórios"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid "Request a online payment from the customer to confirm the order."
msgstr "Solicite um pagamento online do cliente para confirmar o pedido."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid "Request a online signature from the customer to confirm the order."
msgstr "Solicite uma assinatura online do cliente para confirmar o pedido."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request a payment to confirm orders, in full (100%) or partial. The default "
"can be changed per order or template."
msgstr ""
"Solicite um pagamento para confirmar pedidos, total (100%) ou parcial. O "
"padrão pode ser alterado por pedido ou modelo."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request customers to sign quotations to validate orders. The default can be "
"changed per order or template."
msgstr ""
"Solicite que os clientes assinem cotações para validar pedidos. O padrão "
"pode ser alterado por pedido ou por modelo."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Requested date is too soon."
msgstr "A data do pedido está muito próxima."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "Usuário responsável"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid "Revenue Target for the current month (untaxed total of paid invoices)."
msgstr ""
"Meta de receita para o mês atual (total não tributado de faturas pagas)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "Receitas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr "Receitas geradas pela campanha"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro no envio de SMS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "SO0000"
msgstr "PV0000"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Sale"
msgstr "Venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr "Venda: Visível em"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Sale Information"
msgstr "Informação da venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__sale_order_id
#: model:ir.model.fields.selection,name:sale.selection__account_analytic_applicability__business_domain__sale_order
msgid "Sale Order"
msgstr "Pedido de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_move__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "Contagem de pedidos de vendas"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_plan_action_sale_order
msgid "Sale Order Plans"
msgstr "Planos de pedidos de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "Avisos do pedido de venda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Sale Orders"
msgstr "Pedidos de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_orders_count
msgid "Sale Orders Count"
msgstr "Contagem de pedidos de vendas"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_provider_onboarding_wizard
msgid "Sale Payment provider onboarding wizard"
msgstr "Assistente de integração de provedor de serviços de pagamento"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr "Avisos de venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr "Método de pagamento selecionado na integração da venda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_order_ids
msgid "Sale orders to cancel"
msgstr "Pedidos de venda a serem cancelados"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_reporting_sales
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_form
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "Vendas"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Fatura de vendas com pagamento de entrada"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Análise de vendas"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_customers
msgid "Sales Analysis By Customers"
msgstr "Análise de vendas por cliente"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_products
msgid "Sales Analysis By Products"
msgstr "Análise de vendas por produtos"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_salesperson
msgid "Sales Analysis By Salespersons"
msgstr "Análise de vendas por vendedor"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Relatório de análise de vendas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr "Entrada da grade de vendas"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__order_reference__sale_order
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Order"
msgstr "Pedido de venda"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Cancelar pedido de venda"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Pedido de venda confirmado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "Item do pedido de venda"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Linha do pedido de vendas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Linhas do pedido de venda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Linhas do pedido de venda prontas para serem faturadas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Linhas do pedido de venda relacionadas um pedido de venda meu"

#. module: sale
#. odoo-python
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
msgid "Sales Order(s)"
msgstr "Pedidos de venda"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Pedidos de vendas"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Equipe de vendas"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Equipes de vendas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr "Avisos de vendas"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"Item do pedido de venda ao qual o tempo gasto será adicionado para que seja "
"faturado ao cliente. Remova o item do pedido de venda para que a entrada na "
"planilha de horas seja não faturável."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "Preço de venda"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_cancellation
msgid "Sales: Order Cancellation"
msgstr "Vendas: cancelamento de pedido"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales: Order Confirmation"
msgstr "Vendas: confirmação de pedido"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_payment_executed
msgid "Sales: Payment Done"
msgstr "Vendas: pagamento efetuado"

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales: Send Quotation"
msgstr "Vendas: enviar cotação"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid "Sales: Untaxed Total"
msgstr "Vendas: total sem impostos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Vendedor"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_salespeople
msgid "Salespersons"
msgstr "Vendedores"

#. module: sale
#: model:ir.model,name:sale.model_mail_scheduled_message
msgid "Scheduled Message"
msgstr "Mensagem programada"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Buscar pedido de venda"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Search a customer name, or create one on the fly."
msgstr "Pesquise o nome de um cliente ou crie na hora."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "Seção"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Nome da seção (ex., produtos, serviços)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "Token de segurança"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Select a product, or create a new one on the fly."
msgstr "Selecione um produto ou crie um novo rapidamente."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__selected_combo_items
msgid "Selected Combo Items"
msgstr "Itens de combo selecionados"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Selecionar a opção de “Aviso” notificará o usuário com a mensagem. Se "
"selecionar “Mensagem de bloqueio”, isso lançará uma exceção com a mensagem e"
" bloqueará o fluxo. A mensagem tem de ser escrita no campo seguinte."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Venda e compre produtos em diferentes unidades de medidas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "Vender produtos em múltiplos do nº de unidades por embalagem"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr "Venda variantes de produtos utilizando atributos (tamanho, cor etc.)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "Enviar fatura PRO FORMA"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr "Envie um e-mail específico do produto quando uma fatura for validada"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_send_mail
msgid "Send an email"
msgstr "Envie um e-mail"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Send and cancel"
msgstr "Enviar e cancelar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Enviar por e-mail"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Conector Sendcloud"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Accounting tab)."
msgstr ""
"Enviar um e-mail é útil caso precise compartilhar informações ou conteúdos "
"específicos sobre um produto (instruções, regras, links, mídia, etc.). Crie "
"e defina o modelo de e-mail a partir do formulário de informações do produto"
" (na aba Contabilidade)."

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_cancellation
msgid "Sent automatically to customers when you cancel an order"
msgstr "Enviado automaticamente aos clientes quando um pedido é cancelado"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_confirmation
msgid "Sent to customers on order confirmation"
msgstr "Enviado aos clientes quando um pedido é confirmado"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_payment_executed
msgid ""
"Sent to customers when a payment is received but doesn't immediately confirm"
" their order"
msgstr ""
"Enviado aos clientes quando um pagamento é recebido, mas não confirma "
"imediatamente o pedido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Definir vários preços por produto, descontos automáticos etc."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Definir para cotação"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Definições"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "Compartilhar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Shipping"
msgstr "Entrega"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Shipping Address"
msgstr "Endereço de entrega"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Conector Shiprocket"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos os registros em que a próxima data de ação seja antes de hoje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "Mostrar margens em pedidos"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Sign & Pay Quotation"
msgstr "Assinar e pagar cotação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr "Assinar e pagar"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr "Assinar online"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "Assinatura"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Signature is missing."
msgstr "Assinatura ausente."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr "Assinado por"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr "Assinado em"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "Vendido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "Vendido nos últimos 365 dias"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"Some confirmed orders are selected. Their related documents might be\n"
"                        affected by the cancellation."
msgstr ""
"Alguns pedidos confirmados foram selecionados. Os documentos relacionados a esses pedidos podem ser\n"
"                        afetados pelo cancelamento."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Some orders are not in a state requiring confirmation."
msgstr "Alguns pedidos não estão em um estado que exija confirmação."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "Origem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Documento de origem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "E-mail específico"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr "Conector Starshipit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Status"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseado em atividades\n"
"Atrasado: data de vencimento já passou\n"
"Hoje: data da atividade é hoje\n"
"Planejado: atividades futuras."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__subject
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Subject"
msgstr "Assunto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Quantity"
msgstr "Soma da quantidade"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr "Soma do total"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Soma do total sem impostos"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__amount_paid
msgid ""
"Sum of transactions made in through the online payment form that are in the "
"state 'done' or 'authorized' and linked to this order."
msgstr ""
"Soma das transações feitas por meio do formulário de pagamento on-line, que "
"estão no estado \"Concluído\" ou \"Autorizado\" e vinculadas a esse pedido."

#. module: sale
#: model:ir.model,name:sale.model_ir_config_parameter
msgid "System Parameter"
msgstr "Parâmetros do sistema"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "Marcadores"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax 15%"
msgstr "Imposto de 15%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Método de arredondamento de imposto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_country_id
msgid "Tax Country"
msgstr "País fiscal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Excl."
msgstr "Sem impostos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax ID"
msgstr "CPF/CNPJ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Incl."
msgstr "Com impostos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Tax Total"
msgstr "Total de impostos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals
msgid "Tax Totals"
msgstr "Totais de impostos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Método de arredondamento de imposto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__tax_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Impostos"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_discount__tax_ids
msgid "Taxes to add on the discount line."
msgstr "Impostos a adicionar na linha de desconto."

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"cancelling them or archiving the team instead."
msgstr ""
"A equipe %(team_name)s tem %(sale_order_count)s pedidos de venda ativos. "
"Considere cancelá-los ou arquivar a equipe em vez disso."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__technical_price_unit
msgid "Technical Price Unit"
msgstr "Unidade técnica de preço"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""
"Conte-nos porque está recusando esta cotação. Isso nos ajudará a melhorar "
"nossos serviços."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr "Formato dos termos e condições"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Terms & Conditions: %s"
msgstr "Termos e condições: %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Termos e Condições"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Termos e Condições"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "Termos e condições…."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"O código ISO do país de dois caracteres. \n"
"Use este campo para fazer uma busca."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is cancelled. You cannot register"
" an expense on a cancelled Sales Order."
msgstr ""
"O pedido de venda %(order)s a refaturar está cancelado. Não é possível "
"registrar uma despesa em um pedido de vendas cancelado."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is currently locked. You cannot "
"register an expense on a locked Sales Order."
msgstr ""
"O pedido de venda %(order)s a refaturar está bloqueado no momento. Não é "
"possível registrar uma despesa em um pedido de vendas bloqueado."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced must be validated before "
"registering expenses."
msgstr ""
"O pedido de venda %(order)s a refaturar deve ser validado antes do registro "
"das despesas."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "O token de acesso é inválido."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "The amount to invoice = Sale Order Total - Confirmed Down Payments."
msgstr ""
"A quantia a faturar = total do pedido de venda - valores de entrada "
"confirmados."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The company is required, please select one before making any other changes "
"to the sale order."
msgstr ""
"A empresa é obrigatória, selecione uma antes de fazer qualquer outra "
"alteração no pedido de venda."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The delivery date is sooner than the expected date. You may be unable to "
"honor the delivery date."
msgstr ""
"A data de entrega é anterior à data prevista. É possível que você não "
"consiga cumprir a data de entrega."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance."
msgstr "O valor fixo a ser faturado antecipadamente."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid ""
"The following products cannot be restricted to the company %(company)s because they have already been used in quotations or sales orders in another company:\n"
"%(used_products)s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""
"Os produtos a seguir não podem ser restritos à empresa %(company)s porque eles já foram utilizados em cotações ou pedidos para outra empresa:\n"
"%(used_products)s\n"
"Você pode arquivar estes produtos e recriá-los com a restrição de empresa, ou pode mantê-los como produtos compartilhados."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment provider.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment provider.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""
"A fatura é gerada automaticamente e fica disponível no portal do cliente quando a transação é confirmada pelo provedor de pagamentos.\n"
"A fatura é marcada como paga e o pagamento é registrado no diário de pagamentos definido na configuração do provedor de serviços de pagamentos.\n"
"Este modo é recomendado se você emitir a fatura final ao gerar o pedido e não após a entrega."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""
"A margem é calculada a partir da soma dos preços de venda do produto menos o"
" custo definido no formulário de informações."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "The new invoice will deduct draft invoices linked to this sale order."
msgstr ""
"A nova fatura deduzirá faturas em rascunho vinculadas a esse pedido de "
"venda."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The number of selected combo items must match the number of available combo "
"choices."
msgstr ""
"O número de itens de combo selecionados deve corresponder ao número de "
"opções de combo disponíveis."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "The order is not in a state requiring customer payment."
msgstr "O pedido não está em um estado que exija o pagamento do cliente."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The order is not in a state requiring customer signature."
msgstr "O pedido não está em um estado que exige a assinatura do cliente."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "The ordered quantity has been updated."
msgstr "A quantidade solicitada foi atualizada."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr "A comunicação de pagamento deste pedido de venda."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "The payment should also be transmitted with love"
msgstr "O pagamento também deve ser transmitido com amor"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance."
msgstr "A porcentagem do valor a ser faturado antecipadamente."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__prepayment_percent
msgid ""
"The percentage of the amount needed that must be paid by the customer to "
"confirm the order."
msgstr "Porcentagem do valor que é necessário pagar para confirmar cotações."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,help:sale.field_res_config_settings__prepayment_percent
msgid "The percentage of the amount needed to be paid to confirm quotations."
msgstr "Porcentagem do valor que é necessário pagar para confirmar cotações."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "The product (%(product)s) has incompatible values: %(value_list)s"
msgstr "O produto (%(product)s) tem valores incompatíveis: %(value_list)s"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The provided parameters are invalid."
msgstr "Os parâmetros fornecidos são inválidos."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "The value of the down payment amount must be positive."
msgstr "A quantia da entrada deve ser um valor positivo."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "Não há cotações para a sua conta no momento."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no sales orders for your account."
msgstr "Não há pedidos de venda na sua conta no momento."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "There are existing"
msgstr "Há"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid ""
"There does not seem to be any discount product configured for this company "
"yet. You can either use a per-line discount, or ask an administrator to "
"grant the discount the first time."
msgstr ""
"Parece que ainda não há nenhum produto de desconto configurado para essa "
"empresa. Você pode usar um desconto por linha ou pedir a um administrador "
"que conceda o desconto na primeira vez."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_category__property_account_downpayment_categ_id
msgid "This account will be used on Downpayment invoices."
msgstr "Essa conta será usada nas faturas de pagamento de entrada."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Esse valor padrão é aplicado a qualquer novo produto criado. Isso pode ser "
"alterado no formulário de informações do produto."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Este é um nome que ajuda você a manter o controle de suas diferentes "
"campanhas, por exemplo, Black_Friday, Especial_de_Natal"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""
"Esta é a data de entrega prometida ao cliente. Se definido, o pedido de "
"entrega será agendado com base nesta data, em vez do prazo de entrega do "
"produto."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Este é o método de entrega, ex., cartão postal, e-mail ou anúncio de banner"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Esta é a origem do link, ex., mecanismo de busca, outro domínio ou nome na "
"lista de e-mail"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "This option or combination of options is not available"
msgstr "Esta opção ou combinação de opções não está disponível"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "This payment will confirm the quotation."
msgstr "Esse pagamento confirmará a cotação."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""
"Este produto está embalado por %(pack_size).2f %(pack_name)s. Você deve "
"vender %(quantity).2f %(unit)s."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
#: model_terms:ir.actions.act_window,help:sale.action_order_report_customers
#: model_terms:ir.actions.act_window,help:sale.action_order_report_products
#: model_terms:ir.actions.act_window,help:sale.action_order_report_salesperson
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Este relatório analisa suas cotações e pedidos de venda. A análise verifica "
"suas receitas de vendas e classifica-as por diferentes critérios de grupo "
"(vendedor, parceiro, produto etc). Use este relatório para realizar análises"
" em vendas que não foram faturadas ainda. Se você quiser analisar a receita "
"bruta, use o relatório de análise de faturamento no módulo Financeiro."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Este relatório analisa suas cotações. A análise verifica suas receitas de "
"vendas e classifica-as por diferentes critérios de grupo (vendedor, "
"parceiro, produto etc). Use este relatório para realizar análises em vendas "
"que não foram faturadas ainda. Se você quiser analisar a receita bruta, use "
"o relatório de análise de faturamento no módulo Financeiro."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Este relatório analisa seus pedidos de venda. A análise verifica suas "
"receitas de vendas e classifica-as por diferentes critérios de grupo "
"(vendedor, parceiro, produto etc). Use este relatório para realizar análises"
" em vendas que não foram faturadas ainda. Se você quiser analisar a receita "
"bruta, use o relatório de análise de faturamento no módulo Financeiro."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update all taxes based on the currently selected fiscal position."
msgstr ""
"Isso atualizará todos os impostos com base na posição fiscal selecionada "
"atualmente."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update the unit price of all products based on the new pricelist."
msgstr ""
"Isso atualizará o preço unitário de todos os produtos com base na nova lista"
" de preço."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "A faturar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr "Upsell"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Para enviar convites em modo B2B, abra um contato ou selecione vários na "
"visão de lista e clique em 'Conceder acesso ao portal' no menu de *Ação*."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Atividades de hoje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Total"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr "Total de impostos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Excluded"
msgstr "Total sem impostos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Included"
msgstr "Total com impostos"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.js:0
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Total: %s"
msgstr "Total: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "Serviço de rastreamento"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "Acompanhamento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Transações"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__translated_product_name
msgid "Translated Product Name"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "Título"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Type a message..."
msgstr "Digite uma mensagem..."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de atividade de exceção registrada."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a customer..."
msgstr "Digite para localizar um cliente..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a product..."
msgstr "Digite para localizar um produto..."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "Conector UPS"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "Conector USPS"

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "Campanha UTM"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_to_invoice
msgid "Un-invoiced Balance"
msgstr "Saldo não faturado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale.field_sale_report__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Preço unitário"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "Preço unitário:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Unidade de medida"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Categorias de unidade de medida"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unlock"
msgstr "Destravar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Valor sem impostos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "Total faturado sem impostos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "Total a faturar sem impostos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "Total faturado sem impostos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Total sem impostos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "UM"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr "Atualizar preços"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Taxes"
msgstr "Atualizar impostos"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Upsell %(order)s for customer %(customer)s"
msgstr "Faça upsell de %(order)s para o cliente %(customer)s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Oportunidade de upsell"

#. module: sale
#: model:mail.template,description:sale.email_template_edi_sale
msgid "Used by salespeople when they send quotations or proforma to prospects"
msgstr ""
"Utilizado pela equipe de vendas ao enviar cotações ou pro forma para leads"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Válido até"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "Validar pedido"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Validated expenses, vendor bills, or stock pickings (set up to track costs) "
"can be invoiced to the customer at either cost or sales price."
msgstr ""
"Despesas validadas, contas de fornecedores ou separações de estoque "
"(configuradas para rastrear custos) podem ser faturadas para o cliente pelo "
"preço de custo ou de venda."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__validity_date
msgid ""
"Validity of the order, after that you will not able to sign & pay the "
"quotation."
msgstr ""
"Validade do pedido. Depois disso, não será possível assinar e pagar a "
"cotação."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr "Entrada de grade de variante"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "View Details"
msgstr "Ver detalhes"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "View Quotation"
msgstr "Visualizar cotação"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__virtual_id
msgid "Virtual"
msgstr "Virtual"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "Transação anulada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Volume"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/res_config_settings.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning"
msgstr "Aviso"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Warning for %s"
msgstr "Aviso para %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Warning for the change of your quotation's company"
msgstr "Aviso para mudança de empresa na sua cotação"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Aviso no pedido de venda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning when Selling this Product"
msgstr "Avisar quando vender este produto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do site"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do site"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "You can invoice goods before they are delivered."
msgstr "Você pode faturar mercadorias antes de elas serem entregues."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""
"Você não pode deletar uma cotação enviada ou um pedido de venda confirmado. "
"Você deve primeiro cancelar o mesmo."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"            or check every order and invoice them one by one."
msgstr ""
"Você pode selecionar todos os pedidos e faturar em massa,<br>\n"
"            ou conferir cada pedido e faturá-los um por um."

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_provider__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""
"Você pode definir aqui o tipo de comunicação que aparecerá em pedidos de "
"venda. A comunicação será exibida ao cliente quando escolher o método de "
"pagamento."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot cancel a locked order. Please unlock it first."
msgstr "Não é possível cancelar um pedido bloqueado. Desbloqueie-o primeiro."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot change the pricelist of a confirmed order !"
msgstr "Não é possível alterar a lista de preços de um pedido confirmado."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""
"Você não pode alterar o tipo de produto porque já está sendo utilizado em "
"pedidos de venda."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"Não é possível mudar o tipo de linha do pedido de venda. É necessário "
"excluir a linha atual e criar uma nova linha do tipo adequado."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "You cannot modify the product of this order line."
msgstr "You cannot modify the product of this order line."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid ""
"You cannot set a negative number for the default quotation validity. Leave "
"empty (or 0) to disable the automatic expiration of quotations."
msgstr ""
"Não é possível definir um número negativo para a validade padrão da cotação."
" Deixe vazio (ou 0) para desativar a expiração automática das cotações."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Você deve definir um produto para tudo o que você vende ou compra,\n"
"                seja um produto armazenável, consumível, ou um serviço."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "Your Orders"
msgstr "Seu pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Your Reference:"
msgstr "Sua referência:"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Your browser does not support iframe."
msgstr "Seu navegador não oferece suporte a iframe."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr "Seu feedback..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr "Seu pedido foi confirmado."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""
"Seu pedido foi assinado, mas ainda precisa ser pago para ser confirmado."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr "Seu pedido foi assinado."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr "Seu pedido não está em um estado para ser rejeitado."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Sua cotação contém produtos da empresa %(product_company)s, e a sua cotação pertence à empresa %(quote_company)s.\n"
"Mude a empresa da sua cotação ou remova produtos de outras empresas (%(bad_products)s)."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "add the price of your product."
msgstr "adicione o preço de seu produto."

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
msgid "automatic invoicing: send ready invoice"
msgstr "faturamento automático: envie fatura pronta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "Conector bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "fechar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "dias"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "for the"
msgstr "para o/a"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "for this Sale Order."
msgstr "para este pedido de venda."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "let's continue"
msgstr "Vamos continuar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "quote."
msgstr "cotação."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "sale order"
msgstr "pedido de venda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"selected\n"
"                    items?"
msgstr ""
"itens\n"
"selecionados?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "units"
msgstr "unidades"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "you confirm acceptance on the behalf of"
msgstr "você confirma a aceitação em nome de"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
#: model:mail.template,subject:sale.mail_template_sale_payment_executed
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pedido pendente' or 'Pedido' }} (Ref {{ object.name or "
"'n/a' }})"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Pro forma' or 'Cotação') or 'Pedido' }} (Ref {{ "
"object.name or 'n/a' }})"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_cancellation
msgid ""
"{{ object.company_id.name }} {{ object.type_name }} Cancelled (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.type_name }} Cancelado (Ref {{ "
"object.name or 'n/a' }})"
