# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> Chalepiadou, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON> Psyllou, 2025
# <PERSON><PERSON>si<PERSON>, 2025
# Anastasia Mimou, 2025
# Charalampos Lou<PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Martin Trigaux, 2025\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Τα Δεδομένα Ελήφθησαν"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "# Παραγγελίες Πώλησης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "# Γραμμών"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "# Παραγγελιών Πωλήσεων"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "%(attribute)s: %(values)s"
msgstr "%(attribute)s: %(values)s"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "%s has been created"
msgstr "%s έχει δημιουργηθεί"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'ΠΡΟΦΟΡΜΑ - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Προσφορά - %s' % (object.name)) or "
"'Παραγγελία - %s' % (object.name)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "2023-12-31"
msgstr "2023-12-31"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "27.00"
msgstr "27.00"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "31.05"
msgstr "31.05"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr ""
"<b>Στείλτε την προσφορά</b> στον εαυτό σας και ελέγξτε τι θα λάβει ο "
"πελάτης."

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_payment_executed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        A payment with reference\n"
"        <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        amounting\n"
"        <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"        for your order\n"
"        <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"        <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            is pending.\n"
"            <br/>\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Your order will be confirmed once the payment is confirmed.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Once confirmed,\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"                will remain to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            has been confirmed.\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"                <br/>\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">$ 10.00</span>\n"
"                remains to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Thank you for your trust!\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Your order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse; white-space: nowrap;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Products</span></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Quantity</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            Tax Excl.\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Tax Incl.\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-set=\"line_subtotal\" t-value=\"                     line.price_subtotal                     if object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'                     else line.price_total                 \"/>\n"
"            <t t-set=\"current_subtotal\" t-value=\"current_subtotal + line_subtotal\"/>\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and (                     line.display_type in ['line_section', 'line_note']                     or line.product_type == 'combo'                 )\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section' or line.product_type == 'combo'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Taking care of Trees Course</span>\n"
"                                <t t-set=\"current_section\" t-value=\"line\"/>\n"
"                                <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold; white-space: nowrap;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-if=\"current_section and (                     line_last                     or object.order_line[line_index+1].display_type == 'line_section'                     or object.order_line[line_index+1].product_type == 'combo'                     or (                         line.combo_item_id                         and not object.order_line[line_index+1].combo_item_id                     )                 ) and not line.is_downpayment\">\n"
"                <t t-set=\"current_section\" t-value=\"None\"/>\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 100%\" align=\"right\">\n"
"                            <span style=\"font-weight: bold;\">Subtotal:</span>\n"
"                            <span t-out=\"format_amount(current_subtotal, object.currency_id) or ''\">$ 10.00</span>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Delivery:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Taxes:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Total:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Bill to:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Payment Method:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <span style=\"font-weight:bold;\">Ship to:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Shipping Method:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Shipping Description:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"/>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_cancellation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"/>\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">user</t>,\n"
"        <br/><br/>\n"
"        Please be advised that your\n"
"        <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        has been cancelled. Therefore, you should not be charged further for this order.\n"
"        If any refund is necessary, this will be executed at best convenience.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""
"<i class=\"fa fa-comment\"/> Επικοινωνήστε μαζί μας για να λάβετε νέα "
"προσφορά."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> Ανάδραση"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Authorized"
msgstr "<i class=\"fa fa-fw fa-check\"/> Εξουσιοδοτημένο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check\"/> Πληρωμένο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Reversed"
msgstr "<i class=\"fa fa-fw fa-check\"/> Δεσμευμένο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Ληγμένο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Waiting Payment"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Αναμονή πληρωμής"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"
msgstr "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/> Ακυρώθηκε"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                    Locked"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                    Κλειδωμένο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print me-1\"/>View Details"
msgstr "<i class=\"fa fa-print me-1\"/>Προβολή λεπτομερειών"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Απόρριψη"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-usd me-1\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-usd me-1\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/> "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small class=\"text-muted\">Your contact</small>"
msgstr "<small class=\"text-muted\">Η επαφή σας</small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">Το πλεονέκτημα σας</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Παραγγελία Πώλησεων #</span>\n"
"                            <span class=\"d-block d-md-none\">Αναφ.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning me-1\" title=\"This "
"product is archived\" invisible=\"state not in ['draft', 'sent'] or not "
"is_product_archived\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning me-1\" title=\"This "
"product is archived\" invisible=\"state not in ['draft', 'sent'] or not "
"is_product_archived\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"flex-grow-1\">/ Month</span>"
msgstr "<span class=\"flex-grow-1\">/ Μήνα</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span class=\"mx-3\" invisible=\"not require_payment\">of</span>"
msgstr "<span class=\"mx-3\" invisible=\"not require_payment\">από</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Πωλήθηκε</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                                By paying,\n"
"                                            </span>"
msgstr ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                                Με την πληρωμή,\n"
"                                            </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"1\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"
msgstr ""
"<span invisible=\"1\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span> "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\">% </span>"
msgstr ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\">% </span>"

#. module: sale
#: model_terms:web_tour.tour,rainbow_man_message:sale.sale_tour
msgid ""
"<span><b>Congratulations</b>, your first quotation is sent!<br>Check your email to validate the quote.\n"
"        </span>"
msgstr ""
"<span><b>Συγχαρητήρια</b>, η πρώτη σας προσφορά στάλθηκε!<br>Ελέγξτε το email σας για να επικυρώσετε την προσφορά.\n"
"        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>Ποσό</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid ""
"<span>Are you sure you want to cancel this order? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Draft invoices for this order will be cancelled. <br/>\n"
"                        </span>"
msgstr ""
"<span>Είστε σίγουρος ότι θέλετε να ακυρώσετε αυτή την παραγγελία; <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Τα προσχέδια τιμολογίων για αυτήν την παραγγελία θα ακυρωθούν. <br/>\n"
"                        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>Εκπτ.%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_kanban
msgid "<span>Sales visibility</span>"
msgstr "<span>Εμφάνιση Πωλήσεων</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Φόροι</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: This quote contains archived product(s)</span>"
msgstr ""
"<span>Προειδοποίηση: Αυτή η προσφορά  περιέχει αρχειοθετημένα "
"προϊόντα</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: this order might be a duplicate of</span>"
msgstr ""
"<span>Προειδοποίηση: αυτή η παραγγελία μπορεί να είναι διπλοεγγραφή "
"της</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong class=\"d-block mt-3\">Shipping Address</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr " <strong class=\"mr16\">Μερικό σύνολο</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration</strong>"
msgstr "<strong>Λήξη</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Σχόλιο Φορολογικής Θέσης:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson</strong>"
msgstr "<strong>Πωλητής</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Υπογραφή</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>Σας Ευχαριστώ!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>Αυτή η προσφορά έληξε!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been cancelled.</strong>"
msgstr "<strong>Αυτή η προσφορά έχει ακυρωθεί.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference</strong>"
msgstr "<strong>Η αναφορά σας</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "Μια επιβεβαιωμένη παραγγελία πώλησης απαιτεί ημερομηνία επιβεβαίωσης."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "A line on these orders missing a product, you cannot confirm it."
msgstr ""
"Σε μια γραμμή σε αυτές τις παραγγελίες λείπει ένα προϊόν, δε μπορείτε να το "
"επιβεβαιώσετε."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A note, whose content usually applies to the section or product above."
msgstr ""
"Μια σημείωση, η οποία συνήθως σχετίζεται με την ενότητα ή το προϊόν που "
"αναφέρεται παραπάνω."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"A sale order line's combo item must be among its linked line's available "
"combo items."
msgstr ""
"Το σύνθετο είδος μιας παραγγελιογραμμής πώλησης πρέπει να συγκαταλέγεται στα"
" διαθέσιμα σύνθετα είδη της συνδεδεμένης γραμμής."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "A sale order line's product must match its combo item's product."
msgstr ""
"Το προϊόν μιας παραγγελιογραμμής πώλησης πρέπει να ταιριάζει με το προϊόν "
"του σύνθετου είδους."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A section title"
msgstr "Τίτλος ενότητας"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for "
"invoicing,according to their invoicing policy (based on ordered or delivered"
" quantity)."
msgstr ""
"Ένα τυπικό τιμολόγιο εκδίδεται με όλες τις γραμμές παραγγελίας έτοιμες για "
"τιμολόγηση, σύμφωνα με την τιμολογιακή πολιτική τους (βάσει της ποσότητας "
"που παραγγέλθηκε ή παραδόθηκε)"

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "Ορισμός προειδοποίησης σε είδος ή πελάτη (Πώληση)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Δυνατότητα επιλογής ενός τύπου συσκευασίας στις παραγγελίες πωλήσεων και "
"εξαναγκασμός μιας ποσότητας η οποία να είναι πολλαπλάσιο του πλήθος τεμαχίων"
" ανά συσκευασία."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Pay Quotation"
msgstr "Αποδοχή & Πληρωμή Προσφοράς"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Sign Quotation"
msgstr "Αποδοχή & Υπογραφή Προσφοράς"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "Αποδοχή &amp; Πληρωμή"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "Αποδοχή &amp; Υπογραφή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "Προειδοποίηση πρόσβασης"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Πρότυπο γραφήματος λογαριασμού"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Αριθμός Λογαριασμού"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr "Εγγραφή Δεδουλευμένων Εσόδων"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
#: model:ir.ui.menu,name:sale.sale_menu_config_activities
msgid "Activities"
msgstr "Δραστηριότητες"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Δραστηριότητα Εξαίρεσης Διακόσμησης"

#. module: sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_plan
msgid "Activity Plans"
msgstr "Προγράμματα Δραστηριοτήτων"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Κατάσταση Δραστηριότητας"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Εικονίδιο Τύπου Δραστηριότητας"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "Τύποι Δραστηριότητας"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                (e.g. \"Delivery scheduling\", \"Order Payment Follow-up\", ...)"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Add"
msgstr "Προσθήκη"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "Προσθήκη σημείωσης"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "Προσθήκη ενός προϊόντος"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "Προσθήκη μιας ενότητας"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add note"
msgstr "Προσθήκη σημείωσης"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Add one"
msgstr "Προσθέστε ένα"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Add optional products"
msgstr "Προσθήκη προαιρετικών προϊόντων"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add product"
msgstr "Προσθήκη προϊόντος"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add section"
msgstr "Προσθήκη ενότητας"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "Προσθέστε πολλές μεταβλητές σε μια παραγγελία από ένα πλέγμα"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "Επιτρέπεται η αποστολή προτιμολογίων σε πελάτες"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "Επιτρέπεται η αποστολή προτιμολογίων"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__amount_paid
msgid "Already Paid"
msgstr "Ήδη Πληρωμένο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_invoiced
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_invoiced
msgid "Already invoiced"
msgstr "Ήδη Τιμολογημένο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "Amazon Sync"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_amount
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Amount"
msgstr "Ποσό"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Ποσό προ έκπτωσης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "Ποσό των Προσφορών για Τιμολόγηση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "Amount to invoice"
msgstr "Ποσό σε τιμολόγιο"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"            ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Αναλυτική Κατανομή"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "Αναλυτικά Από Έξοδα"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Γραμμή Αναλυτικής"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Εφαρμογές Αναλυτικού Πλάνου"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Αναλυτική Ακρίβεια"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Αναλυτικές γραμμές"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Apply"
msgstr "Εφαρμογή"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Εφαρμόστε μη αυτόματες εκπτώσεις σε παραγγελιογραμμές πώλησης ή εμφανίστε "
"εκπτώσεις υπολογισμένες από τιμοκαταλόγους (επιλογή ενεργοποίησης στις "
"ρυθμίσεις τιμοκαταλόγου)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the"
msgstr "Είστε σίγουροι ότι θέλετε να ακυρώσετε το"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the selected item?"
msgstr "Είστε βέβαιοι ότι θέλετε να ακυρώσετε το επιλεγμένο είδος?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Είστε σίγουροι ότι θέλετε να ακυρώσετε την εξουσιοδοτημένη συναλλαγή; Αυτή η"
" ενέργεια δεν μπορεί να αναιρεθεί."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"            to sell extra hours when all ordered hours have been consumed."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "Στο κόστος"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Αριθμός Συνημμένων"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Τιμές Χαρακτηριστικών"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Ιδιότητες"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__author_id
msgid "Author"
msgstr "Συντάκτης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Εξουσιοδοτημένες Συναλλαγές"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "Αυτόματο Τιμολόγιο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Average"
msgstr "Μέσος όρος"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Bacon Burger"
msgstr "Μπέικον Μπέργκερ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Όνομα Τράπεζας"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "Βασισμένο στο Αναγνωριστικό Πελάτη"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "Βασισμένο στην Αναφορά Εγγράφου"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Beat competitors with stunning quotations!"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "Μήνυμα Αποκλεισμού"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body_has_template_value
msgid "Body content is the same as the template"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Boost sales with online payments or signatures, upsells, and a great "
"customer portal."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Build your first quotation right here!"
msgstr "Δημσιουργήστε την πρώτη σας προσφορά ακριβώς εδώ!"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying a <u>down payment</u> of"
msgstr "Με την πληρωμή μιας <u>προκαταβολής</u> του"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying,"
msgstr "Με την πληρωμή,"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By signing, you confirm acceptance on behalf of"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "Εκστρατεία"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__can_edit_body
msgid "Can Edit Body"
msgstr "Μπορεί να Επεξεργαστεί το Σώμα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "Μπορεί να Επεξεργαστεί το Είδος"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model:ir.actions.act_window,name:sale.action_mass_cancel_orders
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Ακύρωση"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Cancel %s"
msgstr "Ακύρωση %s"

#. module: sale
#: model:ir.model,name:sale.model_sale_mass_cancel_orders
msgid "Cancel multiple quotations"
msgstr "Ακύρωση πολλαπλών προσφορών"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Cancel quotations"
msgstr "Ακύρωση προσφορών"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "Ακυρώθηκε"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Cannot create an invoice. No items are available to invoice.\n"
"\n"
"To resolve this issue, please ensure that:\n"
"   • The products have been delivered before attempting to invoice them.\n"
"   • The invoicing policy of the product is configured correctly.\n"
"\n"
"If you want to invoice based on ordered quantities instead:\n"
"   • For consumable or storable products, open the product, go to the 'General Information' tab and change the 'Invoicing Policy' from 'Delivered Quantities' to 'Ordered Quantities'.\n"
"   • For services (and other products), change the 'Invoicing Policy' to 'Prepaid/Fixed Price'.\n"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "Καταγραφή Συναλλαγής"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Catalog"
msgstr "Κατάλογος"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "Κατηγορία"

#. module: sale
#: model:product.template,name:sale.product_product_1_product_template
msgid "Chair floor protection"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Changing the company of an existing quotation might need some manual "
"adjustments in the details of the lines. You might consider updating the "
"prices."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Check a sample. It's clean!"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Click here to add some products or services to your quotation."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/xml/sales_team_progress_bar_template.xml:0
msgid "Click to define an invoicing target"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Κλείσιμο"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_combos
msgid "Combo Choices"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__combo_item_id
msgid "Combo Item"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_commission
msgid "Commissions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_provider__so_reference_type
msgid "Communication"
msgstr "Επικοινωνία"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Communication history"
msgstr "Ιστορικό επικοινωνίας"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Εταιρία"

#. module: sale
#: model:ir.model,name:sale.model_base_document_layout
msgid "Company Document Layout"
msgstr "Διάταξη Εταιρικού Εγγράφου"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Υπολογίστε τα έξοδα αποστολής και αποστολή με DHL"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Υπολογίστε τα έξοδα αποστολής και αποστολή με FedEx"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Υπολογίστε τα έξοδα αποστολής και αποστολή με UPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Υπολογίστε τα έξοδα αποστολής και αποστολή με USPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Υπολογίστε τα έξοδα αποστολής και αποστολή με bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Υπολογίστε τα έξοδα αποστολής στις παραγγελίες"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.js:0
msgid "Configure your product"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "Επιβεβαίωση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__confirmation_message
msgid "Confirmation Message"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__consolidated_billing
msgid "Consolidated Billing"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_partner
msgid "Contact"
msgstr "Επαφή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body
msgid "Contents"
msgstr "Περιεχόμενα"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Μετατροπή ανάμεσα σε Μονάδες Μέτρησης μπορεί να πραγματοποιηθεί μόνο αν "
"ανήκουν στην ίδια κατηγορία. Η μετατροπή θα γίνει βάσει των αναλογιών."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__country_code
msgid "Country code"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_loyalty
msgid "Coupons & Loyalty"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Draft"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr "Δημιουργία Τιμολογίου"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Create Invoices"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid "Create a Sale Order Activity Plan"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "Δημιουργία τιμολογίου πελάτη"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "Δημιουργήστε ένα νέο προϊόν"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a new quotation, the first step of a new sale!"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoice(s)"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__service_tracking
msgid "Create on Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__consolidated_billing
msgid ""
"Create one invoice for all orders related to same customer and same "
"invoicing address"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_report__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "Νόμισμα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Ισοτιμία"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
msgid "Custom"
msgstr "Προσωποποιημένο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Πελάτης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "Διεύθυνση URL Πύλης Πελατών"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Αναφορά πελάτη"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__state_id
msgid "Customer State"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_zip
msgid "Customer ZIP"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "Customer needs to pay at least %(amount)s to confirm the order."
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_customer
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Πελάτες"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Ημερομηνία"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Ημερομηνία:"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,help:sale.field_res_config_settings__quotation_validity_days
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Days between quotation proposal and expiration. 0 days means automatic "
"expiration is disabled"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__company_price_include
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_price_include
msgid "Default Sales Price Include"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__company_price_include
#: model:ir.model.fields,help:sale.field_sale_order_line__company_price_include
msgid ""
"Default on whether the sales price used on the product and invoices with "
"this Company includes its taxes."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Default period during which the quote is valid and can still be accepted by "
"the customer. The default can be changed per order or template."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__sale_discount_product_id
msgid "Default product used for discounts"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "Παραδόθηκαν"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Delivered Quantity: %s"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "Ποσότητες που Παραδόθηκαν"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Διεύθυνση Παράδοσης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "Ημερομηνία Παράδοσης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Μέθοδοι Παράδοσης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivery Quantity"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""

#. module: sale
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Κατάθεση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Περιγραφή"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "Εκπτ.%"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Discard"
msgstr "Απόρριψη"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/wizard/sale_order_discount.py:0
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount"
msgstr "Έκπτωση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%- On products with the following taxes %(taxes)s"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Έκπτωση (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "Ποσό Έκπτωσης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_discount_product_id
msgid "Discount Product"
msgstr "Έκπτωση Είδους"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_type
msgid "Discount Type"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order_discount
msgid "Discount Wizard"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Έκπτωση στις γραμμές"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount- On products with the following taxes %(taxes)s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount:"
msgstr "Έκπτωση:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_discount_per_so_line
msgid "Discounts"
msgstr "Eκπτώσεις"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_draft_invoice_warning
msgid "Display Draft Invoice Warning"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_invoice_amount_warning
msgid "Display Invoice Amount Warning"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "Τύπος Οθόνης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Documents"
msgstr "Έγγραφα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Τομέας"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment"
msgstr "Προκαταβολή"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (Cancelled)"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (ref: %(reference)s on %(date)s)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment: %(date)s (Draft)"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payments"
msgstr "Προκαταβολές Πληρωμών"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "Προκαταβολή (σταθερό ποσό)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "Προκαταβολή (ποσοστό)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Down payment <br/>"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment invoice"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment of %s%%"
msgstr "Προκαταβολή του %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"Οι προκαταβολές πραγματοποιούνται κατά τη δημιουργία τιμολογίων από μια "
"παραγγελία πώλησης. Δεν αντιγράφονται όταν αντιγράφεται μια εντολή πώλησης."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_category__property_account_downpayment_categ_id
msgid "Downpayment Account"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Draft Invoices"
msgstr "Πρόχειρα Τιμολόγια"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Duplicated Documents"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__duplicated_order_ids
msgid "Duplicated Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_product_field.js:0
msgid "Edit Configuration"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "Email"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Οδηγός Σύνταξης Email"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__invoice_mail_template_id
msgid "Email sent to the customer once the invoice is available."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_template_attribute_line/product_template_attribute_line.js:0
msgid "Enter a customized value"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Error importing attachment '%(file_name)s' as order (decoder=%(decoder)s)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Αναμενόμενη Ημερομηνία"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Expense"
msgstr "Έξοδα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Expiration Date:"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Εκτεταμένα Φίλτρα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Extra line with %s"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Φορολογική Θέση"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__amount
msgid "Fixed Amount"
msgstr "Σταθερό Ποσό"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Follow, view or pay your orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Γραμματοσειρά awesome  π.χ. fa-tasks"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Από αυτήν την αναφορά, μπορείτε να έχετε μια επισκόπηση του ποσού που "
"τιμολογήθηκε στους πελάτες. Το εργαλείο της εύρεσης μπορεί επίσης να "
"χρησιμοποιηθεί για να προσωποποιήσετε τις αναφορές των τιμολογίων και έτσι "
"να ταιριάζει η ανάλυση με τις ανάγκες σας."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Full amount <br/>"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Fully Invoiced"
msgstr "Πλήρως Τιμολογημένο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Μελλοντικές Δραστηριότητες"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Δημιουργία Συνδέσμου Πληρωμής Πώλησης"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "Δημιουργία Συνδέσμου Πληρωμής"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Generated Orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "Λάβετε προειδοποιήσεις στις παραγγελίες ειδών ή πελατών"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__so_discount
msgid "Global Discount"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Go ahead and send the quotation."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Μικτό βάρος"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_active_pricelist
msgid "Has Active Pricelist"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_archived_products
msgid "Has Archived Products"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__has_confirmed_order
msgid "Has Confirmed Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_fpos
msgid "Has Fiscal Position Changed"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "Έχει Μήνυμα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__hidden
msgid "Hidden"
msgstr "Κρυφό"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "Κωδικός"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "Εικονίδιο"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Εικονίδιο που υποδεικνύει μια δραστηριότητα εξαίρεσης."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Αν επιλεγεί, κάποια μηνύματα έχουν σφάλμα παράδοσης."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__journal_id
msgid ""
"If set, the SO will invoice in this journal; otherwise the sales journal "
"with the lowest sequence is used."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"Εάν η πώληση είναι κλειδωμένη, δεν μπορείτε να την τροποποιήσετε. Ωστόσο, θα"
" μπορείτε να τιμολογήσετε ή να παραδώσετε τα προϊόντα."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Immediate"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Import Template for Products"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_view_search_catalog
msgid "In the Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "Περιεχ. ΦΠΑ)"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Invalid discount amount"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid signature data."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move.py:0
msgid "Invoice %s paid"
msgstr "Τιμολόγιο %s εξοφλημένο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Διεύθυνση Τιμολόγησης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Επιβεβαιωμένο Τιμολόγιο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Δημιουργήθηκε το Τιμολόγιο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Γραμμές Τιμολογίου"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Τιμολόγηση Παραγγελιών"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_report__line_invoice_status
msgid "Invoice Status"
msgstr "Κατάσταση τιμολογίου"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Έσοδα από τιμολόγια για τον τρέχον μήνα. Αυτό είναι το ποσό του καναλιού "
"πωλήσεων που έχουν τιμολογηθεί αυτόν τον μήνα. Χρησιμοποιείται για τον "
"υπολογισμό του λόγου εξέλιξης των εσόδων σε σχέση με τον στόχο ο οποίος έχει"
" ορισθεί στην προβολή kanban."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "Τιμολόγηση ότι έχει παραδοθεί"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "Τιμολόγηση ότι έχει παραγγελθεί"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "Τιμολογημένα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_invoiced
msgid "Invoiced Amount"
msgstr "Αξία Τιμολογίου"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Τιμολογημένη Ποσότητα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced_posted
msgid "Invoiced Quantity (posted)"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Invoiced Quantity: %s"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Τιμολογημένα αυτόν τον μήνα"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Τιμολόγια"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Ανάλυση Τιμολογίων "

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Στατιστικά Τιμολογίων"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Τιμολόγηση"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address"
msgstr "Διεύθυνση Τιμολόγησης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__journal_id
msgid "Invoicing Journal"
msgstr "Ημερολόγιο Τιμολόγησης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Πολιτική Τιμολόγησης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "Στόχος Τιμολόγησης"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__is_downpayment
msgid "Is Downpayment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is Expired"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_product_archived
msgid "Is Product Archived"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "Είναι μια προκαταβολή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Issued Date"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"Απαγορεύεται η τροποποίηση των ακολούθων πεδίων σε μια κλειδωμένη παραγγελία:\n"
"%s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "Εγγραφή Ημερολογίου"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "Στοιχείο Ημερολογίου"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__lang
msgid "Language"
msgstr "Γλώσσα"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Last Invoices"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Καθυστερημένες Δραστηριότητες"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Χρόνος Παράδοσης"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""
"Επιτρέψτε τους πελάτες σας να συνδεθούν για να δουν τα δικά τους έγγραφα."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Let’s create a beautiful quotation in a few clicks ."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Συνδεδεμένη Γραμμή Εντολής"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_ids
msgid "Linked Order Lines"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_virtual_id
msgid "Linked Virtual"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Lock"
msgstr "Κλείδωμα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__locked
msgid "Locked"
msgstr "Κλειδώθηκε"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__locked
msgid "Locked orders cannot be modified."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__template_id
msgid "Mail Template"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Make your quote attractive by adding header pages, product descriptions and "
"footer pages to your quote."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Promotions, Coupons, Loyalty cards, Gift cards & eWallet"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Sales & teams targets and commissions"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "Χειροκίνητα"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "Μη αυτόματη πληρωμή"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "Χειροκίνητη ρύθμιση ποσοτήτων στην παραγγελία"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Ορισμός ποσοτήτων χειροκίνητα στην παραγγελία: Τιμολόγηση βασισμένη στην χειροκίνητη ποσότητα, χωρίς τη δημιουργία αναλυτικού λογαριασμού.\n"
"Φύλλο Χρόνου Εργασίας σε συμβόλαιο: Τιμολόγηση βασισμένη στις ώρες από το σχετικό φύλλο εργασίας.\n"
"Δημιουργία μιας εργασίας και παρακολούθηση ωρών: Δημιουργήστε μια εργασία στην επικύρωση της παραγγελίας και παρακολουθήστε τις ώρες εργασίας."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Περιθώρια"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "Marketing"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "Μέσο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Message"
msgstr "Μήνυμα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "Σφάλμα παράδοσης μηνύματος"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Μήνυμα για Παραγγελία"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Μήνυμα για Γραμμή Παραγγελίας"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Μέθοδος"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Mitchell Admin"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Η Προθεσμία της Δραστηριότητάς Μου"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Οι παραγγελίες μου"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "Οι Προσφορές μου"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Οι Γραμμές των δικών μου Παραγγελιών"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "New"
msgstr "Νέα"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Νέα Προσφορά"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Επόμενο Γεγονός Δραστηριότητας στο Ημερολόγιο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Επόμενη Προθεσμία Δραστηριότητας"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Σύνοψη Επόμενης Δραστηριότητας"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "Επόμενος Τύπος Δραστηριότητας"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "Όχι"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "Χωρίς μήνυμα"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "No attachment was provided"
msgstr "Δεν παρασχέθηκε κανένα συνημμένο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "No further requirements for this payment"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "Να μην επεξεργάζονται πλέον παραγγελίες αφού επιβεβαιωθούν"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "Σημείωση"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__no
msgid "Nothing to Invoice"
msgstr "Τίποτα προς τιμολόγηση"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Number"
msgstr "Αριθμός"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Ο αριθμός των ημερών μεταξύ της επιβεβαίωσης της παραγγελίας και της "
"αποστολής των προϊόντων στον πελάτη"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "Αριθμός σφαλμάτων"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Αριθμός μηνυμάτων που απαιτούν ενέργεια"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Αριθμός μηνυμάτων με σφάλμα παράδοσης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "Πλήθος των Προσφορών για Τιμολόγηση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "Πλήθος των Πωλήσεων για Τιμολόγηση"

#. module: sale
#: model:product.template,description_sale:sale.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__sol_discount
msgid "On All Order Lines"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__sale_order
msgid "On confirmed order"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__quotation
msgid "On quote"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"Once a sales order is confirmed, you can't remove one of its lines (we need to track if something gets invoiced or delivered).\n"
"                Set the quantity to 0 instead."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
msgid "Online Payment"
msgstr "Ηλεκτρονική Πληρωμή"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_menu
msgid "Online Payments"
msgstr "Online Πληρωμές"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
msgid "Online Signature"
msgstr "Ηλεκτρονική Υπογραφή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online signature"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_invoiced
msgid "Only confirmed down payments are considered."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Only draft orders can be marked as sent directly."
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
msgid "Operation not supported"
msgstr "Η λειτουργία δεν υποστηρίζεται."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option for: %s"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option: %s"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "Προαιρετικά Είδη"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""
"Προαιρετικά είδη που προτείνονται κάθε φορά που ο πελάτης κάνει κλικ στο "
"*Προσθήκη στο καλάθι* (στρατηγική διασταυρούμενων πωλήσεων, π.χ. για "
"υπολογιστές: εγγύηση, λογισμικό κ.λπ.)."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_cancel__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_report__order_reference
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Παραγγελία"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Παραγγελία #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Date"
msgstr "Ημερ. Παραγγελίας"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Order Date:"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Order Date: Last 365 Days"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__invoice_status
msgid "Order Invoice Status"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Γραμμές Παραγγελίας"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Αναφορά Εντολής"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Κατάσταση Εντολής"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Order signed by %s"
msgstr "Η παραγγελία υπογράφηκε από %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "Παραγγελία προς Τιμολόγηση"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "Παραγγελθέντα"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Ταξινομήσεις"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "Παραγγελίες προς Τιμολόγηση"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Παραγγελίες για Επιπλέον Πωλήσεις"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Oscar Morgan"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "Λοιπές Πληροφορίες"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "PDF Quote"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "PDF Quote builder"
msgstr ""

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "Προτιμολόγιο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "Συσκευασίες"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Ποσότητα Συσκευασίας"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_credit_warning
msgid "Partner Credit Warning"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "Εξόφληση Τώρα"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment provider"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment"
msgstr "Πληρωμή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Μέθοδος Πληρωμής"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_method_menu
msgid "Payment Methods"
msgstr "Μέθοδοι Πληρωμής"

#. module: sale
#: model:ir.model,name:sale.model_payment_provider
msgid "Payment Provider"
msgstr "Πάροχος Πληρωμών"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_provider_menu
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Providers"
msgstr "Πάροχοι Πληρωμών"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Όροι Πληρωμής"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_token_menu
msgid "Payment Tokens"
msgstr "Διακριτικά Πληρωμής"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Συναλλαγή Πληρωμής"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_transaction_menu
msgid "Payment Transactions"
msgstr "Συναλλαγές Πληρωμής"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_paid
msgid "Payment Transactions Amount"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_percentage
msgid "Percentage"
msgstr "Ποσοστό"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "Διεύθυνση URL πρόσβασης στην Πύλη"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_res_config_settings__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_sale_order__prepayment_percent
msgid "Prepayment percentage"
msgstr "Ποσοστό προκαταβολής"

#. module: sale
#. odoo-python
#: code:addons/sale/models/res_company.py:0
#: code:addons/sale/models/sale_order.py:0
msgid "Prepayment percentage must be a valid percentage."
msgstr "Το ποσοστό προκαταβολής πρέπει να είναι έγκυρο ποσοστό."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Preview"
msgstr "Προεπισκόπηση"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
msgid "Price"
msgstr "ΤΙΜΗ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Μείωση Τιμής χωρίς ΦΠΑ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax incl"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Τιμοκατάλογος"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__pricelist_item_id
msgid "Pricelist Item"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Τιμοκατάλογοι"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Τιμολόγηση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "Προτιμολόγιο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Pro-Forma Invoice #"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Προσωρινά Τιμολόγια"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Είδος"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "Κατάλογος Ειδών"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__product_catalog_product_is_in_sale_order
msgid "Product Catalog Product Is In Sale Order"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_categories
msgid "Product Categories"
msgstr "Κατηγορίες Είδους"

#. module: sale
#: model:ir.model,name:sale.model_product_category
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Κατηγορία Είδους"

#. module: sale
#: model:ir.model,name:sale.model_product_document
msgid "Product Document"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
#: code:addons/sale/static/src/js/product_card/product_card.xml:0
msgid "Product Image"
msgstr "Εικόνα Είδους"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "Συσκευασίες Προϊόντων"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_tags
msgid "Product Tags"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "Πρότυπο Είδους "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Τύπος Είδους"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Variant"
msgstr "Μεταβλητή Είδους"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Παραλλαγές του Είδους"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed according to pricelist %s."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product taxes have been recomputed according to fiscal position %s."
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.menu_reporting_product
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "Είδη"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Promotions, Loyalty & Gift Card"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "ΤΕΜ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Παραδοθείσα Ποσότητα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Τιμολογημένη Ποσότητα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "Παραγγελθείσα Ποσότητα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Ποσότητα προς Τιμολόγηση"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "Ποσότητες για τιμολόγηση από παραγγελίες πώλησης"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Quantity"
msgstr "Ποσότητα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "Quantity To Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "Ποσότητα:"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
msgid "Quotation"
msgstr "Προσφορά"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Προσφορά #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "Ημερομηνία Προσφοράς"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "Η Προσφορά Εστάλη"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/res_config_settings.py:0
msgid "Quotation Validity is required and must be greater or equal to 0."
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_viewed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_viewed
msgid "Quotation Viewed"
msgstr ""

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Η Προσφορά Επιβεβαιώθηκε"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Η Προσφορά Εστάλη"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Quotation viewed by customer %s"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotations"
msgstr "Προσφορές"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations & Orders"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Ανάλυση Προσφορών"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Προσφορές και Πωλήσεις"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Quotations to review"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__rating_ids
msgid "Ratings"
msgstr "Αξιολογήσεις"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Costs"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__recipient_ids
msgid "Recipients"
msgstr "Αποδέκτες"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all taxes based on this fiscal position"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Remove"
msgstr "Αφαίρεση"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Remove one"
msgstr "Αφαίρεση ενός"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__render_model
msgid "Rendering Model"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "Αναφορές"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid "Request a online payment from the customer to confirm the order."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid "Request a online signature from the customer to confirm the order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request a payment to confirm orders, in full (100%) or partial. The default "
"can be changed per order or template."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request customers to sign quotations to validate orders. The default can be "
"changed per order or template."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Requested date is too soon."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "Υπεύθυνος Χρήστης"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid "Revenue Target for the current month (untaxed total of paid invoices)."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "Έσοδα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Σφάλμα παράδοσης SMS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "SO0000"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Sale"
msgstr "Πώληση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Sale Information"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__sale_order_id
#: model:ir.model.fields.selection,name:sale.selection__account_analytic_applicability__business_domain__sale_order
msgid "Sale Order"
msgstr "Παραγγελία"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_move__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "Πλήθος Παραγγελιών Πώλησης"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_plan_action_sale_order
msgid "Sale Order Plans"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Sale Orders"
msgstr "Παραγγελία Πώλησης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_orders_count
msgid "Sale Orders Count"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_provider_onboarding_wizard
msgid "Sale Payment provider onboarding wizard"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_order_ids
msgid "Sale orders to cancel"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_reporting_sales
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_form
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "Πωλήσεις"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Τιμολόγιο Πώλησης Είσπραξης Προκαταβολής"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Ανάλυση Πωλήσεων"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_customers
msgid "Sales Analysis By Customers"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_products
msgid "Sales Analysis By Products"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_salesperson
msgid "Sales Analysis By Salespersons"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Αναφορά Ανάλυσης Πωλήσεων"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__order_reference__sale_order
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Order"
msgstr "Παραγγελία"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Ακύρωση Παραγγελίας Πωλήσεων"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Η Παραγγελία Επιβεβαιώθηκε"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Γραμμή Παραγγελίας"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Γραμμές Παραγγελίας"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Γραμμές Παραγγελίας έτοιμες για να τιμολογηθούν"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Γραμμές Παραγγελιών σχετιζόμενες στις δικές μου Παραγγελίες"

#. module: sale
#. odoo-python
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
msgid "Sales Order(s)"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Παραγγελίες"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Ομάδα Πώλησης"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Ομάδες Πωλήσεων"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "Τιμή πώλησης"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_cancellation
msgid "Sales: Order Cancellation"
msgstr ""

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales: Order Confirmation"
msgstr ""

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_payment_executed
msgid "Sales: Payment Done"
msgstr ""

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales: Send Quotation"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid "Sales: Untaxed Total"
msgstr "Πωλήσεις: Αφορολόγητο Σύνολο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Πωλητής"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_salespeople
msgid "Salespersons"
msgstr "Πωλητές"

#. module: sale
#: model:ir.model,name:sale.model_mail_scheduled_message
msgid "Scheduled Message"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Αναζήτηση Παραγγελίας"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Search a customer name, or create one on the fly."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "Τομέας"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "Διακριτικό Ασφαλείας"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Select a product, or create a new one on the fly."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__selected_combo_items
msgid "Selected Combo Items"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Εάν επιλέξετε την \"Προειδοποίηση\" θα ειδοποιήσει το χρήστη με το μήνυμα.\n"
"Εάν επιλέξετε το \"Μήνυμα αποκλεισμού\" η διαδικασία θα διακοπεί και θα εμφανιστεί ένα μήνυμα. Γράψτε το μήνυμα στο επόμενο πεδίο."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Πώληση και αγορά προϊόντων σε διαφορετικές μονάδες μέτρησης"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "Πουλήστε προϊόντα με πολλαπλάσια μονάδα # ανά πακέτο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "Στείλε Προτιμολόγιο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr ""

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_send_mail
msgid "Send an email"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Send and cancel"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Αποστολή με Email"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Accounting tab)."
msgstr ""

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_cancellation
msgid "Sent automatically to customers when you cancel an order"
msgstr ""

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_confirmation
msgid "Sent to customers on order confirmation"
msgstr ""

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_payment_executed
msgid ""
"Sent to customers when a payment is received but doesn't immediately confirm"
" their order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Ορίστε πολλαπλές τιμές ανά είδος, αυτοματοποιημένες εκπτώσεις, κλπ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Ορισμός σε Προσφορά"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "Κοινοποίηση"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Shipping"
msgstr "Αποστολή"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Shipping Address"
msgstr "Διεύθυνση Παράδοσης"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Εμφάνιση όλων των εγγραφών όπου η ημερομηνία επόμενης δράσης είναι πριν από "
"σήμερα"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "Εμφάνιση περιθωρίων στις παραγγελίες"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Sign & Pay Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "Υπογραφή"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Signature is missing."
msgstr "Δεν υπάρχει υπογραφή."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "Εξαντλήθηκε"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"Some confirmed orders are selected. Their related documents might be\n"
"                        affected by the cancellation."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Some orders are not in a state requiring confirmation."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "Πηγή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Έγγραφο Πηγή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "Συγκεκριμένο email"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Κατάσταση"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Κατάσταση βασισμένη σε δραστηριότητες\n"
"Καθυστερημένη: Η ημερομηνία λήξης έχει ήδη περάσει\n"
"Σήμερα: Η ημερομηνία δραστηριότητας είναι σήμερα\n"
"Προγραμματισμένες: Μελλοντικές δραστηριότητες."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__subject
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Subject"
msgstr "Θέμα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Μερικό σύνολο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Quantity"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__amount_paid
msgid ""
"Sum of transactions made in through the online payment form that are in the "
"state 'done' or 'authorized' and linked to this order."
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_ir_config_parameter
msgid "System Parameter"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "Ετικέτες"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax 15%"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Μέθοδος Στρογγυλοποίησης Υπολογισμού Φόρου"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_country_id
msgid "Tax Country"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Excl."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax ID"
msgstr "Α.Φ.Μ."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Incl."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Tax Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals
msgid "Tax Totals"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Μέθοδος στρογγυλοποίησης υπολογισμού φόρου"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__tax_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Φόροι"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_discount__tax_ids
msgid "Taxes to add on the discount line."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"cancelling them or archiving the team instead."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__technical_price_unit
msgid "Technical Price Unit"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""
"Πείτε μας γιατί απορρίψατε αυτή την προσφορά, αυτό θα μας βοηθήσει να "
"βελτιώσουμε τις υπηρεσίες μας."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Terms & Conditions: %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Όροι &amp; Προϋποθέσεις"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Όροι και προϋποθέσεις"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "Όροι και προϋποθέσεις..."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is cancelled. You cannot register"
" an expense on a cancelled Sales Order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is currently locked. You cannot "
"register an expense on a locked Sales Order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced must be validated before "
"registering expenses."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "Ο κωδικός ασφαλείας πρόσβασης είναι άκυρος."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "The amount to invoice = Sale Order Total - Confirmed Down Payments."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The company is required, please select one before making any other changes "
"to the sale order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The delivery date is sooner than the expected date. You may be unable to "
"honor the delivery date."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid ""
"The following products cannot be restricted to the company %(company)s because they have already been used in quotations or sales orders in another company:\n"
"%(used_products)s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment provider.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment provider.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""
"Το περιθώριο υπολογίζεται ως το άθροισμα των τιμών πώλησης των ειδών μείον "
"το κόστος που καθορίζεται στην φόρμα λεπτομέρειες."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "The new invoice will deduct draft invoices linked to this sale order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The number of selected combo items must match the number of available combo "
"choices."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "The order is not in a state requiring customer payment."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The order is not in a state requiring customer signature."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "The ordered quantity has been updated."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "The payment should also be transmitted with love"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__prepayment_percent
msgid ""
"The percentage of the amount needed that must be paid by the customer to "
"confirm the order."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,help:sale.field_res_config_settings__prepayment_percent
msgid "The percentage of the amount needed to be paid to confirm quotations."
msgstr "Το ποσοστό του ποσού που απαιτείται για την επιβεβαίωση προσφορών."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "The product (%(product)s) has incompatible values: %(value_list)s"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The provided parameters are invalid."
msgstr "Οι παρεχόμενες παράμετροι είναι άκυρες."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "The value of the down payment amount must be positive."
msgstr "Η αξία του ποσού της προκαταβολής θα πρέπει να είναι θετική"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "Δεν υπάρχουν προς το παρόν προσφορές για το λογαριασμό σας."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no sales orders for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "There are existing"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid ""
"There does not seem to be any discount product configured for this company "
"yet. You can either use a per-line discount, or ask an administrator to "
"grant the discount the first time."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_category__property_account_downpayment_categ_id
msgid "This account will be used on Downpayment invoices."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Αυτή η προεπιλεγμένη τιμή εφαρμόζεται σε κάθε νέο είδος που δημιουργήθηκε. "
"Αυτό μπορεί να αλλάξει στη φόρμα λεπτομερειών του είδους."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "This option or combination of options is not available"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "This payment will confirm the quotation."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
#: model_terms:ir.actions.act_window,help:sale.action_order_report_customers
#: model_terms:ir.actions.act_window,help:sale.action_order_report_products
#: model_terms:ir.actions.act_window,help:sale.action_order_report_salesperson
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Αυτή η έκθεση κάνει ανάλυση στις προσφορές και στις παραγγελίες. Η Ανάλυση "
"ελέγχει τα έσοδα των πωλήσεων και τα ταξινομεί με διαφορετικές ομάδες "
"κριτηρίων (πωλητής, συνεργάτης, προϊόν, κλπ.) Χρησιμοποιείστε αυτή την "
"έκθεση για να εφαρμόσετε ανάλυση σε πωλήσεις που δεν έχουν τιμολογηθεί "
"ακόμη. Εάν θέλετε να αναλύσετε τον τζίρο σας, θα πρέπει να την "
"χρησιμοποιήσετε."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Αυτή η έκθεση κάνει ανάλυση στις προσφορές. Η ανάλυση ελέγχει τα έσοδα απο "
"τις πωλήσεις και τα ταξινομεί ανά διαφορετικά ομαδικά κριτήρια (πωλητής, "
"εταίρος, προϊόν, κλπ). Χρησιμοποιεί την έκθεση για να κάνει ανάλυση για "
"πωλήσεις που δεν έχουν τιμολογηθεί ακόμη. Αν θέλετε να αναλύσετε τον κύκλο "
"εργασιών, θα πρέπει να χρησιμοποιήσετε την έκθεση Ανάλυση Τιμολογίου στην "
"εφαρμογή της Λογιστικής"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Αυτή η έκθεση κάνει ανάλυση τις παραγγελίες.  Η ανάλυση ελέγχει τα έσοδα απο"
" τις πωλήσεις και τα ταξινομεί ανά διαφορετικά ομαδικά κριτήρια (πωλητής, "
"εταίρος, προϊόν, κλπ). Χρησιμοποιεί την έκθεση για να κάνει ανάλυση για "
"πωλήσεις που δεν έχουν τιμολογηθεί ακόμη. Αν θέλετε να αναλύσετε τον κύκλο "
"εργασιών, θα πρέπει να χρησιμοποιήσετε την έκθεση Ανάλυση Τιμολογίου στην "
"εφαρμογή της Λογιστικής"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update all taxes based on the currently selected fiscal position."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update the unit price of all products based on the new pricelist."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "Για Τιμολόγηση"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Σημερινές Δραστηριότητες"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Σύνολο"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Excluded"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Included"
msgstr "Συνολικός Φόρος που εμπεριέχεται"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.js:0
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Total: %s"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "Υπηρεσίες Παρακαλούθησης"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "Παρακολούθηση"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Συναλλαγές"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__translated_product_name
msgid "Translated Product Name"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Type a message..."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Τύπος της καταγεγραμμένης δραστηριότητας εξαίρεσης."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a customer..."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a product..."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "Καμπάνια UTM"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_to_invoice
msgid "Un-invoiced Balance"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale.field_sale_report__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Τιμή Μονάδας"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "Τιμή Μονάδας:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Μονάδα Μέτρησης"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Μονάδες Μέτρησης"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unlock"
msgstr "Ξεκλείδωμα"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Αφορολόγητο Ποσό"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Αφορολόγητο Σύνολο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "ΜΜ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Taxes"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Upsell %(order)s for customer %(customer)s"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Ευκαιρία Επιπλέον Πώλησης"

#. module: sale
#: model:mail.template,description:sale.email_template_edi_sale
msgid "Used by salespeople when they send quotations or proforma to prospects"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Ισχύει Ως"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "Επικύρωση Παραγγελίας"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Validated expenses, vendor bills, or stock pickings (set up to track costs) "
"can be invoiced to the customer at either cost or sales price."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__validity_date
msgid ""
"Validity of the order, after that you will not able to sign & pay the "
"quotation."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "View Details"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "View Quotation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__virtual_id
msgid "Virtual"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "Άκυρη συναλλαγή"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Όγκος"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/res_config_settings.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning"
msgstr "Προσοχή"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Warning for %s"
msgstr "Προειδοποίηση για %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Warning for the change of your quotation's company"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Προειδοποιήσεις για τις Πωλήσεις"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning when Selling this Product"
msgstr "Προειδοποίηση κατά την πώληση αυτού του είδους"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "You can invoice goods before they are delivered."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"            or check every order and invoice them one by one."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_provider__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot cancel a locked order. Please unlock it first."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot change the pricelist of a confirmed order !"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "You cannot modify the product of this order line."
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid ""
"You cannot set a negative number for the default quotation validity. Leave "
"empty (or 0) to disable the automatic expiration of quotations."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "Your Orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Your Reference:"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Your browser does not support iframe."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "add the price of your product."
msgstr ""

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
msgid "automatic invoicing: send ready invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "Κλείσιμο"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "ημέρες"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "for the"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "for this Sale Order."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "let's continue"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "quote."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "sale order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"selected\n"
"                    items?"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "units"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "you confirm acceptance on the behalf of"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
#: model:mail.template,subject:sale.mail_template_sale_payment_executed
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_cancellation
msgid ""
"{{ object.company_id.name }} {{ object.type_name }} Cancelled (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
