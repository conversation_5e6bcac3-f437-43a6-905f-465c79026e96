# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>fur A <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# NoaFarkash, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# yael terner, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>r, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# to<PERSON><PERSON><PERSON>, 2025
# <AUTHOR> <EMAIL>, 2025
# <AUTHOR> <EMAIL>, 2025
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "הנתונים נאספו"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "# הזמנות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "מס' שורות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "מס' הזמנות לקוח"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "%(attribute)s: %(values)s"
msgstr "%(attribute)s: %(values)s"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "%s has been created"
msgstr "%s נוצר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp; "

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'חשבונית עסקה - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'הצעת מחיר - %s' % (object.name)) or "
"'הזמנה - %s' % (object.name)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "2023-12-31"
msgstr "31-12-2023"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "27.00"
msgstr "27.00"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "31.05"
msgstr "31.05"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr "<b>שלח</b> לעצמך את הצעת המחיר ובדוק מה הלקוח יקבל."

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_payment_executed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        A payment with reference\n"
"        <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        amounting\n"
"        <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"        for your order\n"
"        <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"        <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            is pending.\n"
"            <br/>\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Your order will be confirmed once the payment is confirmed.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Once confirmed,\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"                will remain to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            has been confirmed.\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"                <br/>\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">$ 10.00</span>\n"
"                remains to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Thank you for your trust!\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Your order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse; white-space: nowrap;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Products</span></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Quantity</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            Tax Excl.\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Tax Incl.\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-set=\"line_subtotal\" t-value=\"                     line.price_subtotal                     if object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'                     else line.price_total                 \"/>\n"
"            <t t-set=\"current_subtotal\" t-value=\"current_subtotal + line_subtotal\"/>\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and (                     line.display_type in ['line_section', 'line_note']                     or line.product_type == 'combo'                 )\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section' or line.product_type == 'combo'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Taking care of Trees Course</span>\n"
"                                <t t-set=\"current_section\" t-value=\"line\"/>\n"
"                                <t t-set=\"current_subtotal\" t-value=\"0\"/>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold; white-space: nowrap;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-if=\"current_section and (                     line_last                     or object.order_line[line_index+1].display_type == 'line_section'                     or object.order_line[line_index+1].product_type == 'combo'                     or (                         line.combo_item_id                         and not object.order_line[line_index+1].combo_item_id                     )                 ) and not line.is_downpayment\">\n"
"                <t t-set=\"current_section\" t-value=\"None\"/>\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 100%\" align=\"right\">\n"
"                            <span style=\"font-weight: bold;\">Subtotal:</span>\n"
"                            <span t-out=\"format_amount(current_subtotal, object.currency_id) or ''\">$ 10.00</span>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Delivery:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Untaxed Amount:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px; white-space: nowrap;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Taxes:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Total:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Bill to:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Payment Method:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <span style=\"font-weight:bold;\">Ship to:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Shipping Method:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Shipping Description:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"/>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"        </t>\n"
"        <br/>\n"
"        <t t-set=\"documents\" t-value=\"object._get_product_documents()\"/>\n"
"        <t t-if=\"documents\">\n"
"            <br/> \n"
"            <t t-if=\"len(documents)&gt;1\">\n"
"                Here are some additional documents that may interest you:\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Here is an additional document that may interest you:\n"
"            </t>\n"
"            <ul style=\"margin-bottom: 0;\">\n"
"                <t t-foreach=\"documents\" t-as=\"document\">\n"
"                    <li style=\"font-size: 13px;\">\n"
"                        <a t-out=\"document.ir_attachment_id.name\" t-att-href=\"object.get_portal_url('/document/' + str(document.id))\" t-att-target=\"target\"/>\n"
"                    </li>\n"
"                </t>\n"
"            </ul>\n"
"        </t>\n"
"        <br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_cancellation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"/>\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">user</t>,\n"
"        <br/><br/>\n"
"        Please be advised that your\n"
"        <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        has been cancelled. Therefore, you should not be charged further for this order.\n"
"        If any refund is necessary, this will be executed at best convenience.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr "<i class=\"fa fa-comment\"/> צור קשר לקבלת הצעת מחיר חדשה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> משוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Authorized"
msgstr "<i class=\"fa fa-fw fa-check\"/> מורשה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check\"/> שולם"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Reversed"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> לא בתוקף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Waiting Payment"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> ממתין לתשלום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/> בוטל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                    Locked"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"נעול"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print me-1\"/>View Details"
msgstr "<i class=\"fa fa-print me-1\"/> צפה בפרטים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> דחה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-usd me-1\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small class=\"text-muted\">Your contact</small>"
msgstr "<small class=\"text-muted\">איש הקשר שלך</small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">המחיר שלך</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">מס' הזמנות לקוח</span>\n"
"                            <span class=\"d-block d-md-none\">מזהה</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning me-1\" title=\"This "
"product is archived\" invisible=\"state not in ['draft', 'sent'] or not "
"is_product_archived\"/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"flex-grow-1\">/ Month</span>"
msgstr "<span class=\"flex-grow-1\"> \\ חודש </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span class=\"mx-3\" invisible=\"not require_payment\">of</span>"
msgstr "<span class=\"mx-3\" invisible=\"not require_payment\"> מתוך</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">נמכר</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                                By paying,\n"
"                                            </span>"
msgstr ""
"<span id=\"o_sale_portal_use_amount_total\">\n"
"                                          ע\"י תשלום,\n"
"                                          </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"1\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\">% </span>"
msgstr ""
"<span invisible=\"advance_payment_method != 'percentage'\" "
"class=\"oe_inline\"> %</span>"

#. module: sale
#: model_terms:web_tour.tour,rainbow_man_message:sale.sale_tour
msgid ""
"<span><b>Congratulations</b>, your first quotation is sent!<br>Check your email to validate the quote.\n"
"        </span>"
msgstr ""
"<span><b>מזל טוב</b>, הצעת המחיר הראשונה שלך נשלחה! <br>בדוק את האימייל שלך כדי לאמת את הצעת המחיר.\n"
"       </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>סכום כולל</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid ""
"<span>Are you sure you want to cancel this order? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Draft invoices for this order will be cancelled. <br/>\n"
"                        </span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>% הנחה</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_kanban
msgid "<span>Sales visibility</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>מיסים</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: This quote contains archived product(s)</span>"
msgstr "<span>אזהרה: הצעת מחיר זו  מכילה מוצרים שנמצאים בארכיון </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Warning: this order might be a duplicate of</span>"
msgstr "<span> אזהרה: יתכן והזמנה זו היא שכפול של </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong class=\"d-block mt-3\">Shipping Address</strong>"
msgstr "<strong class=\"d-block mt-3\">כתובת אספקה</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">סכום ביניים</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration</strong>"
msgstr "<strong>תוקף</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>הערת סוג תנועה:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson</strong>"
msgstr "<strong>איש מכירות</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>חתימה</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>תודה לך!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>הצעה זו לא בתוקף!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been cancelled.</strong>"
msgstr "<strong> הצעת מחיר זו בוטלה.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference</strong>"
msgstr "<strong> המזהה שלך </strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "הזמנת לקוח מאושרת דורשת תאריך אישור."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "A line on these orders missing a product, you cannot confirm it."
msgstr "חסר מוצר בשורה בהזמנות אלו , אינך יכול לאשר אותן."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A note, whose content usually applies to the section or product above."
msgstr "הערה, שהתוכן שלה חל בדרך כלל על הסעיף או המוצר שלמעלה."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"A sale order line's combo item must be among its linked line's available "
"combo items."
msgstr ""
"פריט קומבו בשורת הזמנת מכירה חייב להיות אחד מפריטי הקומבו הזמינים בשורה "
"המקושרת אליו."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "A sale order line's product must match its combo item's product."
msgstr "המוצר בשורת הזמנת המכירה חייב להתאים למוצר של פריט הקומבו שלה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A section title"
msgstr "כותרת פסקה "

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for "
"invoicing,according to their invoicing policy (based on ordered or delivered"
" quantity)."
msgstr ""
"חשבונית סטנדרטית מופקת עם כל שורות ההזמנה מוכנות לחיוב, לפי מדיניות החיוב "
"שלהן (בהתבסס על כמות שהוזמנה או שסופקה)."

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "ניתן להגדיר אזהרה עבור מוצר או לקוח (מכירה)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"היכולת לבחור סוג חבילה בהזמנות לקוח ולאלץ כמות שהיא כפולה של מספר היחידות "
"בחבילה."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Pay Quotation"
msgstr "אשר ושלם את ההצעת מחיר"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Sign Quotation"
msgstr "קבל וחתום על הצעת המחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "קבל ושלם"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "קבל וחתום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "אזהרת גישה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "תבנית לוח חשבונות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "מספר חשבון"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr "הזנת הכנסה נצברת"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
#: model:ir.ui.menu,name:sale.sale_menu_config_activities
msgid "Activities"
msgstr "פעילויות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_plan
msgid "Activity Plans"
msgstr "תכניות פעילויות "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "סוגי פעילויות"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                (e.g. \"Delivery scheduling\", \"Order Payment Follow-up\", ...)"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Add"
msgstr "הוסף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "הוסף הערה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "הוסף מוצר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "הוסף סעיף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add note"
msgstr "הוסף הערה"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Add one"
msgstr "הוסף אחד"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Add optional products"
msgstr "הוסף מוצרים אופציונליים: "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add product"
msgstr "הוסף מוצר "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add section"
msgstr "הוסף סעיף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "הוסף מספר וריאנטים להזמנה מתצוגת רשת"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "מאפשר לך לשלוח חשבונית פרו-פורמה ללקוחות שלך"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "מאפשר לך לשלוח חשבונית חשבונית עסקה."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__amount_paid
msgid "Already Paid"
msgstr "כבר שולם"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_invoiced
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_invoiced
msgid "Already invoiced"
msgstr "חויב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "סנכרון לאמזון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_amount
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Amount"
msgstr "סכום כולל"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "סכום לפני הנחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "סכום הצעות מחיר לחיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "Amount to invoice"
msgstr "סכום לחיוב"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"            ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "חלוקה לחשבונות אנליטיים"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "ניתוח מהוצאות "

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "שורה אנליטית"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "יישומים לתכניות אנליטיות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "דיוק אנליטי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "שורות אנליטיות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Apply"
msgstr "החל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"החל הנחות ידניות על שורות הזמנת לקוח או הצג הנחות המחושבות ממחירונים (אפשרות"
" להפעלה בתצורת מחירון)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the"
msgstr "האם אתה בטוח שברצונך לבטל את ה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the selected item?"
msgstr "האם אתה בטוח שברצונך לבטל את הפריט שנבחר?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr "האם אתה בטוח שברצונך לבטל את העסקה המורשית? לא ניתן לבטל פעולה זו."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"            to sell extra hours when all ordered hours have been consumed."
msgstr ""
"לדוגמה, אם אתה מוכר שעות שירות בתשלום מראש, Odoo ממליץ לך \n"
"        למכור שעות נוספות כאשר כל השעות שהוזמנו כבר מומשו."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "בעלות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "ערכי תכונות"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "תכונות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__author_id
msgid "Author"
msgstr "מחבר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "עסקאות מורשות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "חשבונית אוטומטית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Average"
msgstr "ממוצע"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Bacon Burger"
msgstr "בורגר בייקון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "שם בנק"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "מבוסס על מזהה לקוח"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "מבוסס על מזהה מסמך"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Beat competitors with stunning quotations!"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "הודעת חסימה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body_has_template_value
msgid "Body content is the same as the template"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Boost sales with online payments or signatures, upsells, and a great "
"customer portal."
msgstr ""
"הגדל מכירות עם תשלומים או חתימות מקוונות, מכירות נוספות, ופלטפורמת לקוחות "
"מעולה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"הגדל את המכירות שלך באמצעות מגוון תוכניות: קופונים, מבצעים, כרטיסי מתנה "
"ותוכנית נאמנות. ניתן להגדיר תנאים ספציפיים (מוצרים, לקוחות, סכום רכישה "
"מינימלי, תקופת זמן). התגמולים יכולים להיות הנחות (באחוזים או סכום קבוע) או "
"מוצרים חינמיים."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Build your first quotation right here!"
msgstr "בנה את הצעת המחיר הראשונה שלך כאן!"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying a <u>down payment</u> of"
msgstr "על ידי <u>תשלום מקדמה </u>של"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying,"
msgstr "ע\"י תשלום,"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By signing, you confirm acceptance on behalf of"
msgstr "בחתימה, אתה מאשר קבלה בשם "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "קמפיין"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__can_edit_body
msgid "Can Edit Body"
msgstr "ניתן לערוך גוף"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "יכול לערוך מוצר"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model:ir.actions.act_window,name:sale.action_mass_cancel_orders
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "בטל"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Cancel %s"
msgstr "בטל %s"

#. module: sale
#: model:ir.model,name:sale.model_sale_mass_cancel_orders
msgid "Cancel multiple quotations"
msgstr "בטל מספר הצעות מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Cancel quotations"
msgstr "בטל הצעות מחיר"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "בוטל"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Cannot create an invoice. No items are available to invoice.\n"
"\n"
"To resolve this issue, please ensure that:\n"
"   • The products have been delivered before attempting to invoice them.\n"
"   • The invoicing policy of the product is configured correctly.\n"
"\n"
"If you want to invoice based on ordered quantities instead:\n"
"   • For consumable or storable products, open the product, go to the 'General Information' tab and change the 'Invoicing Policy' from 'Delivered Quantities' to 'Ordered Quantities'.\n"
"   • For services (and other products), change the 'Invoicing Policy' to 'Prepaid/Fixed Price'.\n"
msgstr ""
"לא ניתן ליצור חשבונית. אין פריטים זמינים לחיוב.\n"
"\n"
"כדי לפתור את הבעיה, אנא ודא ש:\n"
"• המוצרים סופקו לפני שניסית לחייב אותם.\n"
"• מדיניות החיוב של המוצר מוגדרת כראוי.\n"
"\n"
"אם ברצונך לחייב לפי כמות שהוזמנה במקום:\n"
"• עבור מוצרים  מסוג סחורה, פתח את המוצר, עבור ללשונית \"מידע כללי\" ושנה את מדיניות החיוב מ-\"כמויות שסופקו\" ל-\"כמויות שהוזמנו\".\n"
"• עבור שירותים (ומוצרים אחרים), שנה את מדיניות החיוב ל-\"תשלום מראש/מחיר קבוע\".\n"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "לכוד עסקה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Catalog"
msgstr "קטלוג"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "קטגוריה"

#. module: sale
#: model:product.template,name:sale.product_product_1_product_template
msgid "Chair floor protection"
msgstr "מדבקות לרגלי כסא"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Changing the company of an existing quotation might need some manual "
"adjustments in the details of the lines. You might consider updating the "
"prices."
msgstr ""
"שינוי החברה בהצעת מחיר קיימת עשוי לדרוש התאמות ידניות בפרטי השורות. ייתכן "
"שכדאי לשקול לעדכן את המחירים."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Check a sample. It's clean!"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Click here to add some products or services to your quotation."
msgstr "לחץ כאן כדי להוסיף מספר מוצרים או שירותים להצעה המחיר שלך."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/xml/sales_team_progress_bar_template.xml:0
msgid "Click to define an invoicing target"
msgstr "לחץ להגדרת יעד לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "סגור"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_combos
msgid "Combo Choices"
msgstr "שילוב מוצרים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__combo_item_id
msgid "Combo Item"
msgstr "מוצר קומבו"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_commission
msgid "Commissions"
msgstr "עמלות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_provider__so_reference_type
msgid "Communication"
msgstr "אסמכתה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Communication history"
msgstr "היסטוריית התקשרות"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "חברה"

#. module: sale
#: model:ir.model,name:sale.model_base_document_layout
msgid "Company Document Layout"
msgstr "עיצוב מסמך של החברה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "חשב עלויות המשלוח ושלח באמצעות  DHL"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "חשב עלויות המשלוח ושלח באמצעות Easypost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "חשב עלויות המשלוח ושלח באמצעות FedEx"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "חישוב עלויות משלוח ושלח עם Sendcloud"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "חישוב עלויות משלוח ושלח עם Shiprocket"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "חישוב עלויות משלוח ושלח עם Starshipit"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "חשב עלויות המשלוח ושלח באמצעות UPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "חשב עלויות המשלוח ושלח באמצעות USPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "חשב עלויות המשלוח ושלח באמצעות bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "חשב עלויות המשלוח בהזמנות"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "תצורה"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.js:0
msgid "Configure your product"
msgstr "הגדר את המוצר שלך"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "אשר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__confirmation_message
msgid "Confirmation Message"
msgstr "הודעת אישור"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "חיבורים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__consolidated_billing
msgid "Consolidated Billing"
msgstr "יצירת חשבונית מרכזת"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body
msgid "Contents"
msgstr "תוכן"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"המרה בין יחידות מידה יכולה להתרחש רק אם הן שייכות לאותה קטגוריה. ההמרה תיעשה"
" על בסיס היחס בניהן."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__country_code
msgid "Country code"
msgstr "קוד מדינה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_loyalty
msgid "Coupons & Loyalty"
msgstr "קופונים ותוכניות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "תאריך יצירה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Draft"
msgstr "צור טיוטה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr "צור חשבונית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Create Invoices"
msgstr "צור חשבוניות"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid "Create a Sale Order Activity Plan"
msgstr "צור תוכנית פעילויות להזמנת לקוח"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "צור חשבונית לקוח"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "צור מוצר חדש"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a new quotation, the first step of a new sale!"
msgstr "צור הצעת מחיר חדשה, השלב הראשון במכירה חדשה!"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoice(s)"
msgstr "צור חשבונית(יות)"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr "צור חשבוניות, רשום תשלומים ועקוב אחר השיחות עם הלקוחות שלך."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__service_tracking
msgid "Create on Order"
msgstr "צור בהזמנה "

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__consolidated_billing
msgid ""
"Create one invoice for all orders related to same customer and same "
"invoicing address"
msgstr "צור חשבונית אחת עבור כל ההזמנות שקשורות לאותו לקוח ואותה כתובת חיוב."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "תאריך יצירה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""
"תאריך יצירה של הזמנות טיוטה / שנשלחו,\n"
"תאריך אישור להזמנות שאושרו."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_report__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "שער מטבע"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
msgid "Custom"
msgstr "מותאם"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "ערכים מותאמים אישית"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "הוראות תשלום מותאמות אישית"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "ארץ לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "יישות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "תעשיית לקוח"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "כתובת אתר של פורטל לקוחות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "מספר הזמנת לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "חתימת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__state_id
msgid "Customer State"
msgstr "מדינה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_zip
msgid "Customer ZIP"
msgstr "ZIP "

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "Customer needs to pay at least %(amount)s to confirm the order."
msgstr "הלקוח צריך לשלם לפחות %(amount)s על מנת לאשר את ההזמנה."

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_customer
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "לקוחות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express-חיבור ל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "תאריך"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "תאריך:"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,help:sale.field_res_config_settings__quotation_validity_days
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Days between quotation proposal and expiration. 0 days means automatic "
"expiration is disabled"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "קיזוז המקדמות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity"
msgstr "תוקף ברירת מחדל להצעת מחיר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__company_price_include
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_price_include
msgid "Default Sales Price Include"
msgstr "מחיר מכירה ברירת מחדל כולל"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__company_price_include
#: model:ir.model.fields,help:sale.field_sale_order_line__company_price_include
msgid ""
"Default on whether the sales price used on the product and invoices with "
"this Company includes its taxes."
msgstr ""
"ברירת מחדל האם מחיר המכירה המשמש במוצר ובחשבוניות עם החברה הזו כולל את "
"המיסים."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Default period during which the quote is valid and can still be accepted by "
"the customer. The default can be changed per order or template."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__sale_discount_product_id
msgid "Default product used for discounts"
msgstr "מוצר ברירת מחדל המשמש להנחות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "שלח תוכן באמצעות דוא\"ל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "נמסר"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Delivered Quantity: %s"
msgstr "כמות שנשלחה %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "כמויות שנשלחו"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "כתובת לאספקה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "תאריך משלוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "שיטות משלוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivery Quantity"
msgstr "כמות שנשלחה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"תאריך משלוח אותו אתה יכול להבטיח ללקוח, מחושב ממשך הזמן המינימלי של שורות "
"ההזמנה במקרה של מוצרי שירות. במקרה של משלוח, מדיניות המשלוח של ההזמנה תובא "
"בחשבון כדי להשתמש בזמן המשלוח המינימלי או המקסימלי של שורות ההזמנה."

#. module: sale
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "פקדון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "תיאור"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "הנחה %"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Discard"
msgstr "בטל"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/wizard/sale_order_discount.py:0
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount"
msgstr "הנחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr " % הנחה"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount %(percent)s%%- On products with the following taxes %(taxes)s"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "הנחה (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "סכום הנחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_discount_product_id
msgid "Discount Product"
msgstr "מוצר הנחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_type
msgid "Discount Type"
msgstr "סוג הנחה"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_discount
msgid "Discount Wizard"
msgstr "אשף הנחות"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "הנחה על שורות"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Discount- On products with the following taxes %(taxes)s"
msgstr "הנחה – על מוצרים עם המיסים הבאים %(taxes)s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount:"
msgstr "הנחה:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_discount_per_so_line
msgid "Discounts"
msgstr "הנחות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_draft_invoice_warning
msgid "Display Draft Invoice Warning"
msgstr "הצגת אזהרת חשבונית טיוטה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_invoice_amount_warning
msgid "Display Invoice Amount Warning"
msgstr "הצגת אזהרת סכום חשבונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "סוג תצוגה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "מיפוי חשבון אנליטי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Documents"
msgstr "מסמכים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "דומיין"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment"
msgstr "מקדמה"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (Cancelled)"
msgstr "מקדמה (בוטל)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment (ref: %(reference)s on %(date)s)"
msgstr "תשלום מקדמה ( אסמכתא: %(reference)s על %(date)s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr "סכום מקדמה (קבוע)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payment: %(date)s (Draft)"
msgstr "תשלום מקדמה: %(date)s (טיוטה)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Down Payments"
msgstr "מקדמות"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "מקדמה (סכום קבוע)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "מקדמה (אחוזים)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Down payment <br/>"
msgstr " מקדמה <br/>"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment invoice"
msgstr "חשבונית מקדמה"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment of %s%%"
msgstr "מקדמה של %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"המקדמות נוצרות בעת יצירת חשבוניות מהזמנת לקוח. הן לא מועתקות בעת שכפול הזמנת"
" לקוח."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_category__property_account_downpayment_categ_id
msgid "Downpayment Account"
msgstr "חשבון תשלומי מקדמות"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Draft Invoices"
msgstr "טיוטת חשבוניות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Duplicated Documents"
msgstr "מסמכים כפולים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__duplicated_order_ids
msgid "Duplicated Order"
msgstr "הזמנה משוכפלת "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "חיבור ל Easypost"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_product_field.js:0
msgid "Edit Configuration"
msgstr "ערוך תצורה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr "חתימה אלקטרונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "דוא\"ל"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "אשף יצירת דוא\"ל"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__invoice_mail_template_id
msgid "Email sent to the customer once the invoice is available."
msgstr "האימייל נשלח ללקוח ברגע שהחשבונית זמינה."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_template_attribute_line/product_template_attribute_line.js:0
msgid "Enter a customized value"
msgstr "הכנס ערך מותאם אישית"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Error importing attachment '%(file_name)s' as order (decoder=%(decoder)s)"
msgstr ""
"שגיאה בייבוא הקובץ המצורף '%(file_name)s' כהזמנה (decoder=%(decoder)s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "תאריך צפוי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "צפוי:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Expense"
msgstr "מאשר הוצאות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "תוקף הצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Expiration Date:"
msgstr "תאריך תפוגה:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "מסננים מורחבים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "ערכים נוספים"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Extra line with %s"
msgstr "שורת נוספת עם %s "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "חיבור ל FedEx"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "סוג תנועה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""
"סוג תנועה משמש להתאמת מיסים וחשבונות עבור לקוחות מסוימים או הזמנות לקוח / "
"חשבוניות. ערך ברירת המחדל מגיע מהלקוח."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__amount
msgid "Fixed Amount"
msgstr "סכום קבוע"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Follow, view or pay your orders"
msgstr "עקוב, צפה או שלם את ההזמנות שלך"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr "ערכים אסורים בשורת הזמנת לקוח שאינה חייבת רישום."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"מתוך דוח זה תוכל לקבל סקירה כללית של הסכום בו חויב הלקוח שלך. ניתן להשתמש "
"בכלי החיפוש גם להתאמה אישית של דוחות החשבוניות וכך להתאים ניתוח זה לצרכים "
"שלך."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Full amount <br/>"
msgstr "סכום מלא <br/>"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Fully Invoiced"
msgstr "חויבה במלואה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "צור קישור לתשלום מכירות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "צור קישור לתשלום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "הפק חשבונית אוטומטית לאחר אישור התשלום המקוון"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Generated Orders"
msgstr "הזמנות שנוצרו"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "קבל אזהרות בהזמנות על מוצרים או לקוחות"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__so_discount
msgid "Global Discount"
msgstr "הנחה כללית"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Go ahead and send the quotation."
msgstr "המשך ושלח את הצעת המחיר."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "הענק הנחות בשורות הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "משקל ברוטו"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "קבץ לפי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_active_pricelist
msgid "Has Active Pricelist"
msgstr "מחירון פעיל"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_archived_products
msgid "Has Archived Products"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__has_confirmed_order
msgid "Has Confirmed Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_fpos
msgid "Has Fiscal Position Changed"
msgstr "האם המעמד הפיסקלי השתנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr "האם המחירון השתנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "יש מקדמות"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__hidden
msgid "Hidden"
msgstr "נסתר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "מזהה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__journal_id
msgid ""
"If set, the SO will invoice in this journal; otherwise the sales journal "
"with the lowest sequence is used."
msgstr ""
"אם מוגדר, הזמנת ה תחויב ביומן הזה; אחרת, ייעשה שימוש ביומן המכירות עם המספור"
" הנמוך ביותר."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"אם המכירה נעולה, אינך יכול לשנות אותה יותר. עם זאת, עדיין תוכל לבצע חיוב או "
"משלוח."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr "אם זה נכון, ניתן להשתמש באריזה להזמנות מכירה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr "אם תשנה את המחירון, רק השורות החדשות שנוספו יושפעו."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Immediate"
msgstr "מיד"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "ייבא הזמנות של אמזון וסנכרן משלוחים"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Import Template for Products"
msgstr "תבנית ייבוא למוצרים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_view_search_catalog
msgid "In the Order"
msgstr "בתוך ההזמנה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "כולל מע\"מ)"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid "Invalid discount amount"
msgstr "סכום הנחה לא חוקי"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid order."
msgstr "הזמנה לא חוקית."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid signature data."
msgstr "נתוני חתימה לא חוקיים."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move.py:0
msgid "Invoice %s paid"
msgstr "חשבונית %s שולמה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "כתובת לחשבונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr "התראת חשבונית"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "חשבונית אושרה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "כמות חשבוניות"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "חשבונית נוצרה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "תבנית דוא\"ל חשבונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "שורות חשבונית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "חשבונית הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_report__line_invoice_status
msgid "Invoice Status"
msgstr "סטטוס חשבונית"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr "חשבונית לאחר משלוח, לפי כמויות שנמסרו, לא אלו שהוזמנו."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "חשב כמויות מוזמנות מיד עם מכירת שירות זה."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"הכנסות מחשבוניות לחודש הנוכחי. זה הסכום בו חייב ערוץ המכירות החודש. הוא משמש"
" לחישוב יחס ההתקדמות של הכנסות הנוכחיות לעומת יעד ההכנסות בתצוגת הקאנבן."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "חייב מה שנשלח"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "חייב מה שהוזמן"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "חויב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_invoiced
msgid "Invoiced Amount"
msgstr "כמות שחויבה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "כמות שחויבה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced_posted
msgid "Invoiced Quantity (posted)"
msgstr "כמות שחויבה (נרשמה)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Invoiced Quantity: %s"
msgstr "כמות שחויבה: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "חויב החודש"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "חשבוניות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "ניתוח נתוני חשבוניות"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "סטטיסטיקות חשבוניות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "חיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address"
msgstr "כתובת חשבונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__journal_id
msgid "Invoicing Journal"
msgstr "יומן חשבוניות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "מדיניות חיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "יעד חיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address"
msgstr "כתובת חיוב ואספקה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__is_downpayment
msgid "Is Downpayment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__is_mail_template_editor
msgid "Is Editor"
msgstr "האם עורך"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is Expired"
msgstr "פג תוקף"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_product_archived
msgid "Is Product Archived"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "היא מקדמה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "היא הוצאה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "האם ניתן להגדיר את המוצר?"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr "נכון אם שורת הזמנת הלקוח מגיעה מהוצאה או מחשבונית ספק"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Issued Date"
msgstr "תאריך הנפקה"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"חל איסור לשנות את השדות הבאים בהזמנה נעולה:\n"
"%s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "תנועת יומן"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__lang
msgid "Language"
msgstr "שפה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Last Invoices"
msgstr "חשבוניות אחרונות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "זמן אספקה "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "אפשר ללקוחות שלך להתחבר כדי לראות את המסמכים שלהם"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Let’s create a beautiful quotation in a few clicks ."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "שורת הזמנה מקושרת"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_line_ids
msgid "Linked Order Lines"
msgstr "שורות הזמנה מקושרות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__linked_virtual_id
msgid "Linked Virtual"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Lock"
msgstr "נעל"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "נעל הזמנות מאושרות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__locked
msgid "Locked"
msgstr "נעולה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__locked
msgid "Locked orders cannot be modified."
msgstr "הזמנות נעולות לא ניתנות לשינוי. "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__template_id
msgid "Mail Template"
msgstr "תבנית דוא\"ל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Make your quote attractive by adding header pages, product descriptions and "
"footer pages to your quote."
msgstr ""
"הפוך את הצעת המחיר שלך לאטרקטיבית על ידי הוספת עמוד עליון מעוצב, תיאורי "
"מוצרים ועמוד תחתון מעוצב."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Promotions, Coupons, Loyalty cards, Gift cards & eWallet"
msgstr "נהל מבצעים, קופונים, מועדון לקוחות, כרטיסי מתנה וeWallet"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Sales & teams targets and commissions"
msgstr "נהל יעדים ועמלות של מכירות וצוותים"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "ידני"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "תשלום ידני"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "קבע כמויות ידנית בהזמנה"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"הגדר ידנית כמויות בהזמנה: חיוב המבוסס על הכמות שהוזנה ידנית, ללא יצירת חשבון אנליטי.\n"
"דיווחי שעות בחוזה: חיוב המבוסס על שעות בדיווח השעות הקשור.\n"
"צור משימה ושעות למעקב: צור משימה באימות הזמנת לקוח ועקוב אחר שעות העבודה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "שולי רווח"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr "סמן הצעת מחיר כנשלחה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "שיווק"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "אמצעי תקשורת"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Message"
msgstr "הודעה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "הודעה עבור הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "הודעה עבור שורת הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "אמצעי תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "שיטה לעדכון הכמות שנשלחה"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr "חסרים שדות חובה בשורת הזמנת מכירה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Mitchell Admin"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "ההזמנות שלי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "הצעות המחיר שלי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "שורות הזמנת לקוח שלי"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "New"
msgstr "חדש"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "הצעת מחיר חדשה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "לא"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "אין הודעה"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "No attachment was provided"
msgstr "לא נבחר קובץ מצורף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "No further requirements for this payment"
msgstr "אין דרישות נוספות עבור תשלום זה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "לא ניתן לערוך הזמנות לאחר שאושרו"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr "לא נמצאו הזמנות לחיוב"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr "לא נמצאו הזמנות ל Upsell"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "הערה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__no
msgid "Nothing to Invoice"
msgstr "אין מה לחייב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Number"
msgstr "מספר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr "מספר ימים בין אישור ההזמנה לבין שליחת המוצרים ללקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "מספר הצעות המחיר לחיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "מספר המכירות לחיוב"

#. module: sale
#: model:product.template,description_sale:sale.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr "כסאות משרדיים יכולים להזיק לרצפה שלך: הגן עליה."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__sol_discount
msgid "On All Order Lines"
msgstr "על כל שורות ההזמנה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__sale_order
msgid "On confirmed order"
msgstr "על הזמנת מאושרת"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on_sale__quotation
msgid "On quote"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"Once a sales order is confirmed, you can't remove one of its lines (we need to track if something gets invoiced or delivered).\n"
"                Set the quantity to 0 instead."
msgstr ""
"ברגע שהזמנת לקוח מאושרת, לא ניתן להסיר אחת מהשורות שלה (עלינו לעקוב אם משהו חויב או נמסר). \n"
" במקום זאת, הגדר את הכמות ל-0."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""
"לאחר אישור הצעת המחיר על ידי הלקוח, היא הופכת להזמנת לקוח.<br> תוכל ליצור "
"חשבונית ולגבות את התשלום."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"לאחר שהצעת המחיר מאושרת, היא הופכת להזמנת לקוח.<br> תוכל ליצור חשבונית "
"ולגבות את התשלום."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
msgid "Online Payment"
msgstr "תשלום מקוון"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_menu
msgid "Online Payments"
msgstr "תשלומים מקוונים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
msgid "Online Signature"
msgstr "חתימה מקוונת"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online payment"
msgstr "תשלום מקוון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online signature"
msgstr "חתימה מקוונת"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_invoiced
msgid "Only confirmed down payments are considered."
msgstr "רק תשלומי מקדמה שאושרו נלקחים בחשבון."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Only draft orders can be marked as sent directly."
msgstr "ניתן לסמן רק הזמנות במצב טיוטה כנשלחו ישירות."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr "רק ערך מותאם אישית אחד מותר לכל ערך מאפיין בשורת הזמנת לקוח."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
msgid "Operation not supported"
msgstr "הפעולה אינה נתמכת"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option for: %s"
msgstr "אפשרות עבור: %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Option: %s"
msgstr "אפשרות: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "מוצרים אופציונליים"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""
"מוצרים אופציונליים מוצעים בכל פעם שהלקוח לוחץ על *הוסף לעגלה* (אסטרטגיית "
"קרוס-סל, למשל למחשבים: אחריות, תוכנה וכו ')."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_cancel__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"שפת תרגום אופציונלית (קוד ISO) לבחירה בעת שליחת אימייל. אם לא מוגדר, הגרסה "
"האנגלית תשמש. בדרך כלל זה צריך להיות ביטוי מציין מיקום המספק את השפה "
"המתאימה, למשל. {{ object.partner_id.lang }}."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale.field_sale_report__order_reference
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "הזמנה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "מס' הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr "כמות הזמנות"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Date"
msgstr "תאריך הזמנה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Order Date:"
msgstr "תאריך הזמנה:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Order Date: Last 365 Days"
msgstr "תאריך הזמנה: 365 הימים האחרונים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__invoice_status
msgid "Order Invoice Status"
msgstr "סטטוס חשבונית הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "שורות הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "מזהה הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "סטטוס הזמנה"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr "Upsell להזמנה"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Order signed by %s"
msgstr "הזמנה נחתמה ע\"י %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "הזמנה לחיוב"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr "כמות שהוזמנה: %(old_qty)s -> %(new_qty)s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""
"כמות שהוזמנה: חייב כמויות שהוזמנו על ידי הלקוח.\n"
"כמות שנשלחה: חייב כמויות שנשלחו ללקוח."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "כמויות שהוזמנו"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "הזמנות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "הזמנות לחיוב"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "הזמנות ל Upsell"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Oscar Morgan"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "מידע נוסף"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "PDF Quote"
msgstr "הצעת מחיר ב- PDF"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "PDF Quote builder"
msgstr "בונה הצעות מחיר PDF"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "חשבונית עסקה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "אריזה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "כמות אריזה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_credit_warning
msgid "Partner Credit Warning"
msgstr "אזהרת אשראי לשותף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "שלם עכשיו"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment provider"
msgstr "שלם באמצעות ספק תשלום אחר"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr "PayPal "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment"
msgstr "תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "הוראות תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "אמצעי תשלום"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_method_menu
msgid "Payment Methods"
msgstr "אמצעי תשלום"

#. module: sale
#: model:ir.model,name:sale.model_payment_provider
msgid "Payment Provider"
msgstr "ספק תשלום"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_provider_menu
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Providers"
msgstr "חיבור לתשלומים מקוונים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr "מזהה תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "תנאי תשלום"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_token_menu
msgid "Payment Tokens"
msgstr "אסימוני תשלום"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "עסקת תשלום"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_transaction_menu
msgid "Payment Transactions"
msgstr "עסקאות תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_paid
msgid "Payment Transactions Amount"
msgstr "סכום עסקאות תשלום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr "תנאי תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_percentage
msgid "Percentage"
msgstr "אחוז"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "כתובת גישה לפורטל"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_res_config_settings__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_sale_order__prepayment_percent
msgid "Prepayment percentage"
msgstr "אחוז תשלום מראש"

#. module: sale
#. odoo-python
#: code:addons/sale/models/res_company.py:0
#: code:addons/sale/models/sale_order.py:0
msgid "Prepayment percentage must be a valid percentage."
msgstr "אחוז התשלום מראש חייב להיות אחוז תקני/"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Preview"
msgstr "תצוגה מקדימה"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
msgid "Price"
msgstr "מחיר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "הורדת מחיר מיסים לא כלולים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax incl"
msgstr "הורדת מחיר כולל מס"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "מחירון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__pricelist_item_id
msgid "Pricelist Item"
msgstr "פריט מחירון"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "מחירונים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricing"
msgstr "תמחור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "חשבונית עסקה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Pro-Forma Invoice #"
msgstr "חשבונית עסקה #"

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "חשבוניות עסקה"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "מוצר"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "ערך מותאם אישית של תכונת מוצר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "קטלוג מוצרים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__product_catalog_product_is_in_sale_order
msgid "Product Catalog Product Is In Sale Order"
msgstr "קטלוג מוצרים מוצר בתוך הזמנת לקוח"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_categories
msgid "Product Categories"
msgstr "קטגוריות מוצרים"

#. module: sale
#: model:ir.model,name:sale.model_product_category
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "קטגורית מוצר"

#. module: sale
#: model:ir.model,name:sale.model_product_document
msgid "Product Document"
msgstr "מסמך מוצר"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
#: code:addons/sale/static/src/js/product_card/product_card.xml:0
msgid "Product Image"
msgstr "תמונת מוצר"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "אריזת מוצר"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_tags
msgid "Product Tags"
msgstr "תגיות מוצר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_type
msgid "Product Type"
msgstr "סוג מוצר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr "יחידת מידה של מוצר לקריאה בלבד"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Variant"
msgstr "וריאנט מוצר"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "וריאנטים של מוצר"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed according to pricelist %s."
msgstr "מחירי המוצרים חושבו מחדש בהתאם למחירון %s."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed."
msgstr "מחירי מוצרים חושבו מחדש. "

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product taxes have been recomputed according to fiscal position %s."
msgstr "מיסי מוצרים חושבו מחדש בהתאם לסוג תנועה %s."

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.menu_reporting_product
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "מוצרים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Promotions, Loyalty & Gift Card"
msgstr "הנחות, מועדון לקוחות וכרטיסי מתנה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "כמות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "כמות שנשלחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "כמות שחויבה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "כמות שהוזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "כמות למשלוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "כמות לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "כמויות לחיוב מהזמנות לקוח"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_list/product_list.xml:0
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Quantity"
msgstr "כמות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "Quantity To Invoice"
msgstr "כמות לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "כמות:"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
msgid "Quotation"
msgstr "הצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "הצעת מחיר מס'"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr "כמות הצעות מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "תאריך הצעת מחיר"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "הצעת המחיר נשלחה"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/res_config_settings.py:0
msgid "Quotation Validity is required and must be greater or equal to 0."
msgstr "תוקף הצעת המחיר נדרש ויש להיות גדול או שווה ל-0."

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_viewed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_viewed
msgid "Quotation Viewed"
msgstr "הצעת המחיר נצפתה"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "ההצעה אושרה"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "הצעת מחיר נשלחה"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Quotation viewed by customer %s"
msgstr "הצעת המחיר נצפתה על ידי הלקוח %s"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotations"
msgstr "הצעות מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations & Orders"
msgstr "הצעות מחיר והזמנות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "ניתוח נתוני הצעות מחיר"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "הצעות מחיר ומכירות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Quotations to review"
msgstr "הצעות מחיר לבדיקה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Costs"
msgstr "חיוב מחדש של עלויות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr "מדיניות חיוב מחדש גלויה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__recipient_ids
msgid "Recipients"
msgstr "נמענים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr "המלץ בעת 'הוספת לעגלת הקניות' או הצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr "חשב מחדש את כל המחירים בהתבסס על מחירון זה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all taxes based on this fiscal position"
msgstr "חשב מחדש את כל המיסים בהתאם לסוג תנועה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request"
msgstr "הפניה למסמך שיצר את בקשת הזמנת המכירה הזו."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr "חשבונית רגילה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr "דחה הצעת מחיר זו"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "Remove"
msgstr "הסר"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/quantity_buttons/quantity_buttons.xml:0
msgid "Remove one"
msgstr "הסר אחד"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__render_model
msgid "Rendering Model"
msgstr "דגם עיבוד"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "דו\"חות"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid "Request a online payment from the customer to confirm the order."
msgstr "בקשת תשלום אונליין מהלקוח כדי לאשר את ההזמנה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid "Request a online signature from the customer to confirm the order."
msgstr "בקש מהלקוח חתימה מקוונת לאישור ההזמנה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request a payment to confirm orders, in full (100%) or partial. The default "
"can be changed per order or template."
msgstr ""
"בקש תשלום לאישור הזמנות, במלואו (100%) או חלקי. ברירת המחדל ניתנת לשינוי "
"עבור כל הזמנה או תבנית."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request customers to sign quotations to validate orders. The default can be "
"changed per order or template."
msgstr ""
"בקש מהלקוחות לחתום על הצעות מחיר לאישור הזמנות. ברירת המחדל ניתנת לשינוי "
"עבור כל הזמנה או תבנית."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Requested date is too soon."
msgstr "תאריך הדרישה קרוב מדי."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid "Revenue Target for the current month (untaxed total of paid invoices)."
msgstr "יעד הכנסות לחודש הנוכחי (סה\"כ של חשבוניות משולמות ללא מע\"מ)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "הכנסות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr "הכנסות שנוצרו מהקמפיין"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "SO0000"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Sale"
msgstr "מכירה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr "מכירות: גלוי ב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Sale Information"
msgstr "פרטי מכירה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__sale_order_id
#: model:ir.model.fields.selection,name:sale.selection__account_analytic_applicability__business_domain__sale_order
msgid "Sale Order"
msgstr "הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_move__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "כמות הזמנות לקוח"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_plan_action_sale_order
msgid "Sale Order Plans"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "אזהרות בהזמנת לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Sale Orders"
msgstr "הזמנות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_orders_count
msgid "Sale Orders Count"
msgstr "ספירת הזמנות לקוח"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_provider_onboarding_wizard
msgid "Sale Payment provider onboarding wizard"
msgstr "אשף התחברות לספק תשלומים עבור מכירות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr "אזהרות מכירה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr "אנא בחר אמצעי תשלום."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_order_ids
msgid "Sale orders to cancel"
msgstr "הזמנות לקוח לביטול"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_reporting_sales
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_form
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "מכירות"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "חשבונית מכירה מראש"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "ניתוח נתוני מכירות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_customers
msgid "Sales Analysis By Customers"
msgstr "ניתוח מכירות לפי לקוח"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_products
msgid "Sales Analysis By Products"
msgstr "ניתוח מכירות לפי מוצר"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_salesperson
msgid "Sales Analysis By Salespersons"
msgstr "ניתוח מכירות אנשי מכירות"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "דוח נתוני מכירות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr "רשומת רשת מכירות"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__order_reference__sale_order
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Order"
msgstr "הזמנת לקוח"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "הזמנת לקוח בוטלה"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "הזמנת לקוח אושרה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "פריט הזמנת לקוח"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "שורת הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "שורות הזמנת לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "שורות הזמנת לקוח מוכנות לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "שורות הזמנת לקוח הקשורות להזמנת לקוח שלי"

#. module: sale
#. odoo-python
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
msgid "Sales Order(s)"
msgstr "הזמנה(ות) לקוח"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "הזמנות לקוח"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "צוות מכירות"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "צוותי מכירות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr "אזהרות מכירה"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"פריט הזמנת מכירה אשר אליו יתוסף הזמן שהושקע על מנת להיות מחויב . הסר את פריט"
" הזמנת המכירה על מנת שדיווח שעות העבודה לא תהיה ניתנת לחיוב."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "מחיר מכירה"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_cancellation
msgid "Sales: Order Cancellation"
msgstr "מכירות: ביטול הזמנה"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales: Order Confirmation"
msgstr "מכירות: אישור הזמנה"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_payment_executed
msgid "Sales: Payment Done"
msgstr "מכירות: תשלום בוצע"

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales: Send Quotation"
msgstr "מכירות : שלח הצעות מחיר"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid "Sales: Untaxed Total"
msgstr "מכירות: סה\"כ ללא מע\"מ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "איש מכירות"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_salespeople
msgid "Salespersons"
msgstr "אנשי מכירות"

#. module: sale
#: model:ir.model,name:sale.model_mail_scheduled_message
msgid "Scheduled Message"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "חפש הזמנת לקוח"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Search a customer name, or create one on the fly."
msgstr "חפש שם של לקוח, או צור אחד באופן מיידי."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "סעיף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "שם פסקה (למשל מוצרים, שירותים)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "אסימון אבטחה"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Select a product, or create a new one on the fly."
msgstr "בחר מוצר או צור אחד חדש תוך כדי תנועה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__selected_combo_items
msgid "Selected Combo Items"
msgstr "פריטי קומבו שנבחרו"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"בחירה באפשרות \"אזהרה\" תודיע למשתמש על ידי הודעה,בחירת \"הודעת חסימה\" "
"תתריע על חריגה בהודעה ותחסום את ההתקדמות בתהליך. ההודעה צריכה להיכתב בשדה "
"הבא."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "מכור ורכוש מוצרים ביחידות מידה שונות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "מכור מוצרים לפי מס' יחידות בחבילה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr "מכור וריאנטים של מוצר באמצעות תכונות (גודל, צבע וכו')"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "שלח חשבונית עסקה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr "שלח דוא\"ל מסוים למוצר לאחר אישור החשבונית"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_send_mail
msgid "Send an email"
msgstr "שלח דוא\"ל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Send and cancel"
msgstr "שלח ובטל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "שלח בדוא\"ל"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Accounting tab)."
msgstr ""
"שליחת דוא\"ל שימושית אם אתה צריך לשתף מידע או תוכן ספציפי על מוצר (הוראות, "
"חוקים, קישורים, מדיה וכו'). צור והגדר את תבנית הדוא\"ל מתוך טופס פרטי המוצר "
"(בכרטיסיית הנהלת חשבונות)."

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_cancellation
msgid "Sent automatically to customers when you cancel an order"
msgstr "נשלח אוטומטית ללקוחות כאשר אתה מבטל הזמנה"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_confirmation
msgid "Sent to customers on order confirmation"
msgstr "נשלח ללקוחות עם אישור ההזמנה"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_payment_executed
msgid ""
"Sent to customers when a payment is received but doesn't immediately confirm"
" their order"
msgstr "נשלח ללקוחות כאשר מתקבל תשלום אך הוא אינו מאשר את ההזמנה באופן מיידי."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "רצף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "קבע מחירים מרובים למוצר, הנחות אוטומטיות וכו '."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "הגדר להצעת מחיר"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "הגדרות"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "שתף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Shipping"
msgstr "משלוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Shipping Address"
msgstr "כתובת לאספקה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "הצג שולי רווח בהזמנות"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Sign & Pay Quotation"
msgstr "חתום ושלם את ההצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr "חתום ושלם"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr "חתום באופן מקוון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "חתימה"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Signature is missing."
msgstr "חתימה חסרה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr "נחתם ע\"י"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr "נחתם ב-"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "נמכר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "נמכר ב -365 הימים האחרונים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"Some confirmed orders are selected. Their related documents might be\n"
"                        affected by the cancellation."
msgstr ""
"חלק מההזמנות שאושרו נבחרו. ייתכן שביטולן \n"
"              ישפיע על המסמכים הקשורים אליהן."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Some orders are not in a state requiring confirmation."
msgstr "חלק מההזמנות אינן במצב המחייב אישור."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "מקור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "מסמך מקור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "דוא\"ל מסוים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "סטטוס"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__subject
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Subject"
msgstr "נושא"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "סיכום ביניים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Quantity"
msgstr "סך כמות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr "סך סכומי הביניים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "סכום כולל ללא מע\"מ"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__amount_paid
msgid ""
"Sum of transactions made in through the online payment form that are in the "
"state 'done' or 'authorized' and linked to this order."
msgstr ""
"סכום העסקאות שבוצעו דרך טופס התשלום המקוון, הנמצאות במצב 'בוצע' או 'מאושר' "
"ומקושרות להזמנה זו."

#. module: sale
#: model:ir.model,name:sale.model_ir_config_parameter
msgid "System Parameter"
msgstr "פרמטר מערכת"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "תגיות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax 15%"
msgstr "מע\"מ 15%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "שיטת חישוב מס"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_country_id
msgid "Tax Country"
msgstr "מס ארץ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Excl."
msgstr "ללא מיסים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax ID"
msgstr "ח.פ / ע.מ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax Incl."
msgstr "כולל מיסים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Tax Total"
msgstr "סך המס"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals
msgid "Tax Totals"
msgstr "סיכומי מס"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "שיטת עיגול חישוב מס"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__tax_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "מיסים"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_discount__tax_ids
msgid "Taxes to add on the discount line."
msgstr "מיסים להוספה לשורת הנחה."

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"cancelling them or archiving the team instead."
msgstr ""
"לצוות %(team_name)sיש %(sale_order_count)s הזמנות לקוח פעילות. שקול לבטל "
"אותן או להכניס לארכיון את  הצוות במקום זאת."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__technical_price_unit
msgid "Technical Price Unit"
msgstr "יחידת מחיר טכנית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""
"ספר לנו מדוע אתה מסרב להצעת מחיר זו, הדבר יסייע לנו בשיפור השירותים שלנו."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr "פורמט תנאים והגבלות"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Terms & Conditions: %s"
msgstr "תנאים והגבלות: %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "תנאים והגבלות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "תנאים והגבלות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "תנאים והגבלות..."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"קוד ארץ ISO בשני תווים. \n"
"ניתן להשתמש בשדה זה לחיפוש מהיר."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is cancelled. You cannot register"
" an expense on a cancelled Sales Order."
msgstr ""
"הזמנת הלקוח %(order)s שיש לחייב מחדש בוטלה. לא ניתן לרשום הוצאה על הזמנת "
"לקוח שבוטלה."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced is currently locked. You cannot "
"register an expense on a locked Sales Order."
msgstr ""
"הזמנת לקוח %(order)s שיש לחייב מחדש נעולה כרגע. לא ניתן לרשום הוצאה על הזמנת"
" לקוח נעולה."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid ""
"The Sales Order %(order)s to be reinvoiced must be validated before "
"registering expenses."
msgstr "יש לאמת את הזמנת הלקוח %(order)s שיש לחייב מחדש לפני רישום הוצאות."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "האסימון גישה אינו חוקי."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "The amount to invoice = Sale Order Total - Confirmed Down Payments."
msgstr "הסכום לחיוב = סך כל הזמנת לקוח- מקדמות שאושרו."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The company is required, please select one before making any other changes "
"to the sale order."
msgstr "החברה נדרשת, אנא בחר אחת לפני ביצוע שינויים נוספים בהזמנת לקוח."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The delivery date is sooner than the expected date. You may be unable to "
"honor the delivery date."
msgstr "תאריך המסירה מוקדם מתאריך הצפי. ייתכן שלא תוכל לעמוד בתאריך המסירה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance."
msgstr "הסכום הקבוע לחיוב מראש."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid ""
"The following products cannot be restricted to the company %(company)s because they have already been used in quotations or sales orders in another company:\n"
"%(used_products)s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""
"לא ניתן להגביל את המוצרים הבאים לחברה %(company)s מכיוון שכבר נעשה בהם שימוש בהצעות מחיר או הזמנות לקוח בחברה אחרת:\n"
"%(used_products)s\n"
"באפשרותך להעביר אותם לארכיון וליצור אותם מחדש עם הגבלת החברה שלך או להשאיר אותם כמוצרים משותפים."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment provider.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment provider.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""
"החשבונית נוצרה אוטומטית וזמינה בפורטל הלקוח כאשר העסקה מאושרת על ידי ספק התשלום. \n"
"החשבונית מסומנת כמשולמת והתשלום נרשם ביומן התשלומים המוגדר בהגדרות ספק התשלום. \n"
"מצב זה מומלץ אם אתה מנפיק את החשבונית הסופית עם ההזמנה ולא לאחר המשלוח."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr "שולי הרווח מחושבים כסכום מחיר המכירה של המוצר פחות עלות המוצר."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "The new invoice will deduct draft invoices linked to this sale order."
msgstr "החשבונית החדשה תנכה חשבוניות טיוטה מקושרות להזמנת לקוח זו."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"The number of selected combo items must match the number of available combo "
"choices."
msgstr "מספר פריטי הקומבו שנבחרו חייב להתאים למספר אפשרויות הקומבו הזמינות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "The order is not in a state requiring customer payment."
msgstr "ההזמנה אינה במצב המחייב תשלום מלקוח."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The order is not in a state requiring customer signature."
msgstr "ההזמנה אינה במצב המחייב חתימה של לקוח."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "The ordered quantity has been updated."
msgstr "הכמות שהוזמנה עודכנה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr "תקשורת התשלומים של הזמנת לקוח זו."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "The payment should also be transmitted with love"
msgstr "התשלום צריך גם להיות מועבר באהבה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance."
msgstr "אחוז הסכום שיחויב מראש."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__prepayment_percent
msgid ""
"The percentage of the amount needed that must be paid by the customer to "
"confirm the order."
msgstr "אחוז הסכום הנדרש שצריך לשלם הלקוח כדי לאשר את ההזמנה."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,help:sale.field_res_config_settings__prepayment_percent
msgid "The percentage of the amount needed to be paid to confirm quotations."
msgstr "אחוז הסכום הנדרש שצריך לשלם כדי לאשר הצעות מחיר."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "The product (%(product)s) has incompatible values: %(value_list)s"
msgstr "המוצר (%(product)s) מכיל ערכים שאינם תואמים: %(value_list)s"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The provided parameters are invalid."
msgstr "הפרמטרים שסופקו אינם תקינים."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "The value of the down payment amount must be positive."
msgstr "הערך של סכום המקדמה חייב להיות חיובי."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "אין כרגע הצעות מחיר בחשבונך."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no sales orders for your account."
msgstr "כרגע אין הזמנות לקוח בחשבונך."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "There are existing"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
msgid ""
"There does not seem to be any discount product configured for this company "
"yet. You can either use a per-line discount, or ask an administrator to "
"grant the discount the first time."
msgstr ""
"נראה שאין עדיין מוצר הנחה שהוגדר עבור חברה זו. תוכל להשתמש בהנחה לפי שורה, "
"או לבקש ממנהל להעניק את ההנחה בפעם הראשונה."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_category__property_account_downpayment_categ_id
msgid "This account will be used on Downpayment invoices."
msgstr "חשבון זה ישמש לחשבוניות של מקדמות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"ערך ברירת מחדל זה חל על כל מוצר חדש שנוצר. ניתן לשנות זאת בטופס פרטי המוצר."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"זהו שם שעוזר לך לעקוב אחר מאמצי הקמפיין השונים שלך, למשל Fall_Drive, "
"Christmas_Special"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""
"זהו מועד האספקה ​​שהובטח ללקוח. אם הוא מוגדר, הזמנת המשלוח תתוזמן על פי "
"תאריך זה ולא על פי זמני אספקת המוצר."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "זוהי שיטת המשלוח, למשל גלויה, דוא\"ל או מודעת באנר"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "זהו מקור הקישור, למשל מנוע חיפוש, דומיין אחר או שם של רשימת דוא\"ל"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product/product.xml:0
msgid "This option or combination of options is not available"
msgstr "אפשרות זו או שילוב האפשרויות הזה אינו זמין."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
msgid "This payment will confirm the quotation."
msgstr "תשלום זה יאשר את הצעת המחיר."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""
"%(unit)sמוצר זה ארוז ב-%(pack_size).2f %(pack_name)s . עליך "
"למכור%(quantity).2f ."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
#: model_terms:ir.actions.act_window,help:sale.action_order_report_customers
#: model_terms:ir.actions.act_window,help:sale.action_order_report_products
#: model_terms:ir.actions.act_window,help:sale.action_order_report_salesperson
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"דוח זה מבצע ניתוח של הזמנות הלקוח והצעות המחיר שלך. ניתוח זה בודק את הכנסות "
"המכירות שלך וממיין אותן לפי קריטריונים קבוצתיים שונים (איש מכירות, שותף, "
"מוצר וכו'). השתמש בדוח זה לניתוח מכירות שטרם חויבו. אם אתה רוצה לנתח את "
"המחזור שלך, עליך להשתמש בדוח ניתוח חשבוניות ביישום הנהלת חשבונות."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"דוח זה מבצע ניתוח של הצעות המחיר שלך. ניתוח זה בודק את הכנסות המכירות שלך "
"וממיין אותן לפי קריטריונים קבוצתיים שונים (איש מכירות, שותף, מוצר וכו'). "
"השתמש בדוח זה לניתוח מכירות שטרם חויבו. אם אתה רוצה לנתח את המחזור שלך, עליך"
" להשתמש בדוח ניתוח חשבוניות ביישום הנהלת חשבונות."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"דוח זה מבצע ניתוח של הזמנות הלקוח שלך. ניתוח זה בודק את הכנסות המכירות שלך "
"וממיין אותן לפי קריטריונים קבוצתיים שונים (איש מכירות, שותף, מוצר וכו'). "
"השתמש בדוח זה לניתוח מכירות שטרם חויבו. אם אתה רוצה לנתח את המחזור שלך, עליך"
" להשתמש בדוח ניתוח חשבוניות ביישום הנהלת חשבונות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update all taxes based on the currently selected fiscal position."
msgstr " פעולה זו תעדכן את כל המיסים על בסיס הסוג תנועה שנבחרה כעת."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update the unit price of all products based on the new pricelist."
msgstr " פעולה זאת תעדכן את מחיר היחידה של כל המוצרים על בסיס המחירון החדש."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr "שדרוג מכירה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"לשליחת הזמנות במצב B2B, פתח איש קשר או בחר מספר כאלה בתצוגת רשימה ולחץ על "
"האפשרות 'ניהול גישה לפורטל' בתפריט הנפתח * פעולה*."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "סה\"כ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr "סה\"כ מיסים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Excluded"
msgstr "סה\"כ מע\"מ לא כלול"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Included"
msgstr "סה\"כ כולל מע\"מ"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.js:0
#: code:addons/sale/static/src/js/product_list/product_list.js:0
msgid "Total: %s"
msgstr "סה\"כ: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "שירות מעקב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "מעקב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "עסקאות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__translated_product_name
msgid "Translated Product Name"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "סוג שם"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Type a message..."
msgstr "כתבו הודעה..."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a customer..."
msgstr "הקלד לחיפוש לקוח..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a product..."
msgstr "הקלד למציאת המוצר.."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "חיבור ל UPS"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "חיבור ל USPS"

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "קמפיין UTM"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_order_line__amount_to_invoice
msgid "Un-invoiced Balance"
msgstr "יתרה לא מחויבת"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale.field_sale_report__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "מחיר יחידה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "מחיר יחידה:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "יחידות מידה"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "קטגוריות יחידות מידה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unlock"
msgstr "שחרר נעילה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "סכום ללא מע\"מ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "סכום ללא מע\"מ שחויב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "סכום ללא מע\"מ לחיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "סכום ללא מע\"מ שחויב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "סכום כולל ללא מע\"מ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "יחידת מידה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr "עדכון מחירים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Taxes"
msgstr "עדכון מיסים"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Upsell %(order)s for customer %(customer)s"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__line_invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "הזדמנות ל - Upsell"

#. module: sale
#: model:mail.template,description:sale.email_template_edi_sale
msgid "Used by salespeople when they send quotations or proforma to prospects"
msgstr ""
"נמצא בשימוש על ידי אנשי מכירות כאשר הם שולחים הצעות מחיר או טיוטות ללקוחות "
"פוטנציאליים."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "בתוקף עד"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "אשר הזמנה"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Validated expenses, vendor bills, or stock pickings (set up to track costs) "
"can be invoiced to the customer at either cost or sales price."
msgstr ""
"הוצאות מאומתות, חשבוניות ספקים או ליקוטי מלאי (המוגדרים למעקב עלויות) יכולים"
" להיות מחויבים ללקוח במחיר עלות או במחיר מכירה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__validity_date
msgid ""
"Validity of the order, after that you will not able to sign & pay the "
"quotation."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr "וריאנט מתצוגת רשת"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "View Details"
msgstr "צפה בפרטים"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "View Quotation"
msgstr "צפה בהצעת מחיר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__virtual_id
msgid "Virtual"
msgstr "וירטואלי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "עסקה מבוטלת"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "נפח"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/res_config_settings.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning"
msgstr "אזהרה"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Warning for %s"
msgstr "אזהרה עבור %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Warning for the change of your quotation's company"
msgstr "אזהרה על שינוי החברה בהצעת המחיר שלך"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "אזהרה בהזמנת הלקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Warning when Selling this Product"
msgstr "אזהרה בעת מכירת מוצר זה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "You can invoice goods before they are delivered."
msgstr "אתה יכול לחייב על סחורות לפני שהן נמסרות."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""
"אינך יכול למחוק הצעת מחיר שנשלחה או הזמנת לקוח מאושרת. תחילה עליך לבטל אותה."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"            or check every order and invoice them one by one."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_provider__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""
"באפשרותך לקבוע כאן את סוג התקשורת שיופיע בהזמנות לקוח. התקשורת תינתן ללקוח "
"כאשר יבחר את אמצעי התשלום."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot cancel a locked order. Please unlock it first."
msgstr "אין אפשרות לבטל הזמנה נעולה. אנא שחרר את הנעילה קודם."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You cannot change the pricelist of a confirmed order !"
msgstr "אין אפשרות לשנות את המחירונים של הזמנה מאושרת!"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""
"לא ניתן לשנות את סוג המוצר מכיוון שהוא כבר בשימוש בהזמנות מכירה קיימות."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"אינך יכול לשנות את סוג שורת הזמנת הלקוח. במקום זאת עליך למחוק את השורה "
"הנוכחית וליצור שורה חדשה מהסוג המתאים."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "You cannot modify the product of this order line."
msgstr "לא ניתן לשנות את המוצר בשורת הזמנה זו."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid ""
"You cannot set a negative number for the default quotation validity. Leave "
"empty (or 0) to disable the automatic expiration of quotations."
msgstr ""
"אין אפשרות להגדיר מספר שלילי עבור תוקף הצעת המחיר ברירת המחדל. השאר ריק (או "
"0) כדי לבטל את התפוגה האוטומטית של הצעות המחיר."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"עליך להגדיר מוצר לכל דבר שאתה מוכר או רוכש,\n"
"בין אם מדובר במוצר מנוהל מלאי, במוצר לא מנוהל מלאי או בשירות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "Your Orders"
msgstr "ההזמנות שלך"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Your Reference:"
msgstr "מזהה הזמנה:"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_action_helper/sale_action_helper_dialog.xml:0
msgid "Your browser does not support iframe."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr "משוב שלך..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr "ההזמנה שלך אושרה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr "ההזמנה שלך נחתמה אך עדיין יש לשלם כדי לאשר אותה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr "ההזמנה שלך נחתמה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr "ההזמנה שלך אינה במצב שידחה."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"הצעת המחיר מכילה מוצרים של חברת  %(product_company)s ואילו הצעת המחיר שייכת לחברה %(quote_company)s. \n"
"אנא שנה את החברה בהצעת המחיר או הסר את המוצרים מחברות אחרות  (%(bad_products)s)."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "add the price of your product."
msgstr "הוסף את המחיר של המוצר שלך."

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
msgid "automatic invoicing: send ready invoice"
msgstr "חשבונית אוטומטית: שלח חשבונית מוכנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "חיבור ל bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "סגור"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "ימים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "for the"
msgstr "עבור ה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "for this Sale Order."
msgstr "להזמנת לקוח זו."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "let's continue"
msgstr "בוא נמשיך"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "quote."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "sale order"
msgstr "הזמנת לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"selected\n"
"                    items?"
msgstr ""
"פריטים\n"
"                  שנבחרו?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "units"
msgstr "יחידות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "you confirm acceptance on the behalf of"
msgstr "אתה מאשר קבלה בשם"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
#: model:mail.template,subject:sale.mail_template_sale_payment_executed
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'הזמנה ממתינה' or 'הזמנה' }} (מספר {{ object.name or 'לא "
"קיים' } })"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'חשבונית עסקה' or 'הצעת מחיר') or 'הזמנה' }} (מספר "
"{{ object.name or 'לא קיים' }})"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_cancellation
msgid ""
"{{ object.company_id.name }} {{ object.type_name }} Cancelled (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
