<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="product_document_form" model="ir.ui.view">
        <field name="name">product.document.form.sale</field>
        <field name="model">product.document</field>
        <field name="inherit_id" ref="product.product_document_form"/>
        <field name="arch" type="xml">
            <sheet position="inside">
                <group name="sale" string="Sales">
                    <field name="attached_on_sale" class="oe_inline"/>
                </group>
            </sheet>
        </field>
    </record>

    <record id="product_document_kanban" model="ir.ui.view">
        <field name="name">product.document.kanban.sale</field>
        <field name="model">product.document</field>
        <field name="inherit_id" ref="product.product_document_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//main/div" position="inside">
                <div class="d-flex mt-2">
                    <span>Sales visibility</span>
                    <field name="attached_on_sale" class="ms-2" widget="selection"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="product_document_list" model="ir.ui.view">
        <field name="name">product.document.list.sale</field>
        <field name="model">product.document</field>
        <field name="inherit_id" ref="product.product_document_list"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="attached_on_sale"/>
            </field>
        </field>
    </record>

    <record id="product_document_search" model="ir.ui.view">
        <field name="name">product.document.search.sale</field>
        <field name="model">product.document</field>
        <field name="inherit_id" ref="product.product_document_search"/>
        <field name="arch" type="xml">
            <search position="inside">
                <separator/>
                <filter name="quotation"
                    string="Quotation"
                    domain="[('attached_on_sale', '=', 'quotation')]"/>
                <filter name="sale_order"
                    string="Sales Order"
                    domain="[('attached_on_sale', '=', 'sale_order')]"/>
            </search>
        </field>
    </record>

</odoo>
