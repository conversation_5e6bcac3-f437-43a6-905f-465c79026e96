# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* resource
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
#: code:addons/resource/models/resource_resource.py:0
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Folga</span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Work Resources</span>"
msgstr "<span class=\"o_stat_text\">Recursos de trabalho</span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span> hours/week</span>"
msgstr "<span> horas/semana</span>"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__active
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Ativo"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__afternoon
msgid "Afternoon"
msgstr "Tarde"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Archived"
msgstr "Arquivado"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 1-week calendar? All work entries will "
"be lost."
msgstr ""
"Tem certeza de que quer trocar para um calendário de uma semana? Todas as "
"entradas de trabalho serão perdidas."

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 2-week calendar? All work entries will "
"be lost."
msgstr ""
"Tem certeza de que quer trocar para um calendário de duas semanas? Todas as "
"entradas de trabalho serão perdidas."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Attendances can't overlap."
msgstr "As presenças não podem se sobrepor."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average Hour per Day"
msgstr "Média de horas por dia"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""
"Média de horas por dia que um recurso deve trabalhar com este calendário."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__lunch
msgid "Break"
msgstr "Intervalo"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_calendar
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__two_weeks_calendar
msgid "Calendar in 2 weeks mode"
msgstr "Calendário no modo 2 semanas"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Dias de fechamento"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Empresa"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__full_time_required_hours
msgid "Company Full Time"
msgstr "Tempo integral da empresa"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
msgid "Created on"
msgstr "Criado em"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Date"
msgstr "Data"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr "Período do dia"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "Dia da semana"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr "Horas de trabalho padrão"

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""
"Define o horário de trabalho do recurso. Se não for definido, o recurso terá"
" um horário de trabalho totalmente flexível."

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Defina horas de trabalho e cronograma que poderiam ser programados para seus"
" membros do projeto"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_type
msgid "Display Type"
msgstr "Tipo de exibição"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_days
msgid "Duration (days)"
msgstr "Duração (dias)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_hours
msgid "Duration (hours)"
msgstr "Duração (horas)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Fator de eficiência"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__email
msgid "Email"
msgstr "E-mail"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Data final"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_explanation
msgid "Explanation"
msgstr "Explicação"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"Usuário externo com acesso limitado, criado apenas com o propósito de "
"compartilhar dados."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__0
msgid "First"
msgstr "Primeiro"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "First week"
msgstr "Primeira semana"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__flexible_hours
msgid "Flexible Hours"
msgstr "Horas flexíveis"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__4
msgid "Friday"
msgstr "Sexta"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Afternoon"
msgstr "Sexta-feira à tarde"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Lunch"
msgstr "Almoço na sexta"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Morning"
msgstr "Sexta-feira de manhã"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
msgid "Fully Flexible"
msgstr "Totalmente flexível"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__sequence
msgid "Gives the sequence of this line when displaying the resource calendar."
msgstr "Fornece a sequência desta linha ao exibir o calendário de recursos."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
msgid "Global Time Off"
msgstr "Folga global"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Agrupar por"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Horas"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Hours per Week"
msgstr "Horas por semana"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__user
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Human"
msgstr "Humano"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic time off for the company. If a resource is set, "
"the time off is only for this resource"
msgstr ""
"Se estiver vazio, é uma folga genérica para a empresa. Se um recurso estiver"
" definido, o tempo livre é apenas para este recurso"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Se o campo ativo for definido como \"falso\", te permitirá esconder o "
"registro do recurso sem removê-lo."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__active
msgid ""
"If the active field is set to false, it will allow you to hide the Working "
"Time without removing it."
msgstr ""
"Se o campo ativo for definido como falso, isso permitirá que você oculte o "
"tempo de trabalho sem removê-lo."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"In a calendar with 2 weeks mode, all periods need to be in the sections."
msgstr ""
"Em um calendário com modo de 2 semanas, todos os períodos precisam estar nas"
" seções."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__material
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Material"
msgstr "Material"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__0
msgid "Monday"
msgstr "Segunda"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Afternoon"
msgstr "Segunda-feira à tarde"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Lunch"
msgstr "Almoço na segunda-feira"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Morning"
msgstr "Segunda-feira de manhã"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__morning
msgid "Morning"
msgstr "Manhã"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
msgid "Name"
msgstr "Nome"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr ""
"Número de horas a serem trabalhadas no cronograma da empresa para ser "
"considerado como tempo integral."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__other
msgid "Other"
msgstr "Outro"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Period"
msgstr "Período"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__phone
msgid "Phone"
msgstr "Telefone"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Motivo"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Nome de usuário relacionado para o recurso gerenciar seu acesso."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Resource"
msgstr "Recurso"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr "Mixin de recursos"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Time Off"
msgstr "Folga do recurso"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Informações de folga do recurso"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr "Tempo de trabalho do recurso"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Calendário de recursos"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Recursos"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Time Off"
msgstr "Folgas dos recursos"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Recursos te permitem criar e controlar recursos que estão envolvidos em uma "
"fase específica do projeto. Você pode também definir seu nível de eficiência"
" e carga de trabalho baseado nas horas de trabalho da semana."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__5
msgid "Saturday"
msgstr "Sábado"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Buscar recurso"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Time Off"
msgstr "Buscar folga no período de trabalho"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Buscar horário de trabalho"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__1
msgid "Second"
msgstr "Segundo"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "Second week"
msgstr "Segunda semana"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__display_type__line_section
msgid "Section"
msgstr "Seção"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__share
msgid "Share User"
msgstr "Compartilhar usuário"

#. module: resource
#. odoo-python
#: code:addons/resource/models/res_company.py:0
msgid "Standard 40 hours/week"
msgstr "Padrão 40 horas/semana"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Data de início"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""
"Horário de início e término do trabalho.\n"
"Um valor específico de 24:00 é interpretado como 23:59:59.999999."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Data de início"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Time Off"
msgstr "Data de início da folga"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__6
msgid "Sunday"
msgstr "Domingo"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch"
msgstr "Trocar"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 1 week calendar"
msgstr "Mudar para o calendário de 1 semana"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 2 weeks calendar"
msgstr "Mudar para o calendário de 2 semanas"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__display_type
msgid "Technical field for UX purpose."
msgstr "Campo técnico para fins de experiência do usuário."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"The current week (from %(first_day)s to %(last_day)s) corresponds to week "
"number %(number)s."
msgstr ""
"A semana atual (de %(first_day)s até %(last_day)s) corresponde ao número da "
"semana %(number)s."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_leaves.py:0
msgid "The start date of the time off must be earlier than the end date."
msgstr "A data de início da folga deve ser anterior à data de término."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Este campo é usado para definir em qual fuso horário os recursos irão "
"trabalhar."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Este campo é usado para calcular a duração esperada de uma ordem de trabalho"
" neste centro de trabalho. Por exemplo, se uma ordem de trabalho leva uma "
"hora e o fator de eficiência é 100%, a duração esperada será de uma hora. Se"
" o fator de eficiência for 200%, entretanto, a duração esperada será de 30 "
"minutos."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__3
msgid "Thursday"
msgstr "Quinta"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Afternoon"
msgstr "Quinta-feira à tarde"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Lunch"
msgstr "Almoço na quinta-feira"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Morning"
msgstr "Quinta-feira de manhã"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__leave
msgid "Time Off"
msgstr "Folga"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Time Off Detail"
msgstr "Detalhe da folga"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr "Tipo de tempo"

#. module: resource
#: model:ir.model.constraint,message:resource.constraint_resource_resource_check_time_efficiency
msgid "Time efficiency must be strictly positive"
msgstr "A eficiência de tempo deve ser estritamente positiva"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
msgid "Timezone"
msgstr "Fuso horário"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz_offset
msgid "Timezone offset"
msgstr "Deslocamento de fuso horário"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__1
msgid "Tuesday"
msgstr "Terça"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Afternoon"
msgstr "Terça-feira à tarde"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Lunch"
msgstr "Almoço na terça-feira"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Morning"
msgstr "Terça-feira de manhã"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Tipo"

#. module: resource
#: model:ir.model,name:resource.model_res_users
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Usuário"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__2
msgid "Wednesday"
msgstr "Quarta"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Afternoon"
msgstr "Quarta-feira à tarde"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Lunch"
msgstr "Almoço na quarta-feira"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Morning"
msgstr "Quarta-feira de manhã"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__week_type
msgid "Week Number"
msgstr "Número da semana"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__flexible_hours
msgid ""
"When enabled, it will allow employees to work flexibly, without relying on "
"the company's working schedule (working hours)."
msgstr ""
"Quando ativado, ele permitirá que os funcionários trabalhem de forma "
"flexível, sem depender do cronograma de trabalho da empresa (horário de "
"trabalho)."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a time off or as work time (eg: "
"formation)"
msgstr ""
"Se isso deve ser calculado como uma folga ou como tempo de trabalho (por "
"exemplo: formação)"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Detalhes do trabalho"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Trabalho de"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Trabalho até"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Jornada de trabalho"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Working Hours of %s"
msgstr "Horas de trabalho de %s"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Schedules"
msgstr "Horários de trabalho"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Time"
msgstr "Tempo de trabalho"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "You can't delete section between weeks."
msgstr "Não é possível excluir a seção entre as semanas."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "first"
msgstr "primeiro"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "other week"
msgstr "outra semana"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "second"
msgstr "segundo"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "this week"
msgstr "esta semana"
