# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_loyalty
# 
# Translators:
# Гэрэлтцог Цогтбаатар, 2024
# <PERSON>asa<PERSON><PERSON> Sharavsuren <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Bayarkhu<PERSON> Bataa, 2024
# <PERSON>, 2024
# Baskhu<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Baskhu<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid ""
"\n"
"        Technical field used to link multiple reward lines from the same reward together.\n"
"    "
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/partner_line/partner_line.js:0
msgid "%s Points"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.program,name:pos_loyalty.15_pc_on_next_order
msgid "15% on next order"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.15_pc_on_next_order_reward
msgid "15% on your order"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_order.js:0
msgid "A better global discount is already applied."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "A coupon/loyalty card must have a unique code."
msgstr "Купон/лоялти кардны код нь давхцахгүй байх ёстой."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "A reward could not be loaded"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__promo_barcode
msgid ""
"A technical field used as an alternative to the promo code. This is "
"automatically generated when the promo code is changed."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_res_partner__loyalty_card_count
#: model:ir.model.fields,field_description:pos_loyalty.field_res_users__loyalty_card_count
msgid "Active loyalty cards"
msgstr "Идэвхтэй лоялти оноо бүртгэл"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/utils/manage_giftcard_popup/manage_giftcard_popup.xml:0
msgid "Add Balance"
msgstr ""

#. module: pos_loyalty
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "All PoS"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/utils/manage_giftcard_popup/manage_giftcard_popup.xml:0
msgid "Amount"
msgstr "Дүн"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__any_product
msgid "Any Product"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid ""
"Are you sure you want to remove %s from this order?\n"
" You will still be able to claim it through the reward button."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "Available rewards"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "Balance"
msgstr "Үлдэгдэл"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__promo_barcode
msgid "Barcode"
msgstr "Зураасан код"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_barcode_rule
msgid "Barcode Rule"
msgstr "Зураасан кодын дүрэм"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_res_partner
msgid "Contact"
msgstr "Харилцах хаяг"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__coupon_id
#: model:ir.model.fields.selection,name:pos_loyalty.selection__barcode_rule__type__coupon
msgid "Coupon"
msgstr "Купон"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "Coupon Codes"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.15_pc_on_next_order
msgid "Coupon point(s)"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.xml:0
msgid "Current Balance:"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "Customer"
msgstr "Үйлчлүүлэгч"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "Customer needed"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "Deactivating reward"
msgstr ""

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_discount_loyalty_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Discount & Loyalty"
msgstr "Хөнгөлөлт & Лоялти"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.xml:0
msgid "Enter Code"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "Enter Gift Card Number"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/utils/manage_giftcard_popup/manage_giftcard_popup.xml:0
msgid "Enter amount"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "Error"
msgstr "Алдаа"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/payment_screen/payment_screen.js:0
msgid "Error validating rewards"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/utils/manage_giftcard_popup/manage_giftcard_popup.xml:0
msgid "Expiration"
msgstr "Хүчинтэй огноо"

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.loyalty_program_reward
msgid "Free Product - Simple Pen"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid ""
"Gift Card: %s\n"
"Balance: %s"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "Gift card or Discount code"
msgstr ""

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_gift_ewallet_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Gift cards & eWallet"
msgstr "Бэлгийн карт & eWallet"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__points_cost
msgid "How many point this reward cost on the coupon."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "Invalid gift card program reward. Use 1 currency per point discount."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "Invalid gift card program rule. Use 1 point per currency spent."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "Invalid gift card program. More than one reward."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "Invalid gift card program. More than one rule."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Is Reward Line"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Лоялти купон"

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.loyalty_program
msgid "Loyalty Points"
msgstr "Лоялти оноо"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_program
#: model:loyalty.program,name:pos_loyalty.loyalty_program
msgid "Loyalty Program"
msgstr "Лоялти хөтөлбөр"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Лоялти шагнал"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Лоялти дүрэм"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "No"
msgstr "Үгүй"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "No reward can be claimed with this coupon."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "No valid eWallet found"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
msgid "Onsite %s"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "Please select a product for this reward"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_order_count
msgid "PoS Order Count"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS Order Reference"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS order where this coupon was generated."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_ok
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "Point of Sale"
msgstr "Борлуулалтын цэг"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Борлуулалтын цэгийн тохиргоо"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "ПОС захиалгын мөр"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order
msgid "Point of Sale Orders"
msgstr "ПОС захиалга"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_session
msgid "Point of Sale Session"
msgstr "ПОС сэшн"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Point of Sales"
msgstr "Борлуулалтын цэг"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_order.js:0
msgid "Points"
msgstr "Оноо"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__points_cost
msgid "Points Cost"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_mail__pos_report_print_id
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "Print Report"
msgstr "Тайлан хэвлэх"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_product_product
msgid "Product Variant"
msgstr "Барааны хувилбар"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "Program: %(name)s, Reward Product: `%(reward_product)s`"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "Program: %(name)s, Rule Product: `%(rule_product)s`"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "Refund with eWallet"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid ""
"Refunding a top up or reward product for an eWallet or gift card program is "
"not allowed."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.xml:0
msgid "Reset Programs"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Restrict publishing to those shops."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_id
msgid "Reward"
msgstr "Шагнах"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid "Reward Identifier Code"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "Select program"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.xml:0
msgid "Sell physical gift card?"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "Sell/Manage physical gift card"
msgstr ""

#. module: pos_loyalty
#: model:product.template,name:pos_loyalty.simple_pen_product_template
msgid "Simple Pen"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
msgid ""
"Some coupons are invalid. The applied coupons have been updated. Please "
"check the order."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "Spent:"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__any_product
msgid "Technical field, whether all product match"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "That coupon code has already been scanned and activated."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "That promo code program has already been activated."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "That promo code program is expired."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "That promo code program is not yet valid."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "That promo code program requires a specific pricelist."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__coupon_id
msgid "The coupon used to claim that reward."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
msgid ""
"The following codes already exist in the database, perhaps they were already sold?\n"
"%s"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_mail__pos_report_print_id
msgid ""
"The report action to be executed when creating a coupon/gift card/loyalty "
"card in the PoS."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid ""
"The reward \"%s\" contain an error in its domain, your domain must be "
"compatible with the PoS client"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_id
msgid "The reward associated with this line."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_order.js:0
msgid "The reward could not be applied."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
msgid "There are not enough points for the coupon: %s."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_order.js:0
msgid "There are not enough points on the coupon to claim this reward."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_order.js:0
msgid "There are not enough products in the basket to claim this reward."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid ""
"There is no email template on the gift card program and your pos is set to "
"print them."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid ""
"There is no print report on the gift card program and your pos is set to "
"print them."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "These are the products that are valid for this rule."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "This Gift card has already been sold."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "This coupon is expired (%s)."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "This coupon is invalid (%s)."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "This coupon is not available with the current pricelist."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "This coupon is not yet valid (%s)."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid ""
"This gift card is not linked to any order. Do you really want to apply its "
"reward?"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "This is used to print the generated gift cards from PoS."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid "This programs requires a code to be applied."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
msgid ""
"To continue, make the following reward products available in Point of Sale."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_barcode_rule__type
msgid "Type"
msgstr "Төрөл"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_order.js:0
msgid "Unknown discount type"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "Unpaid gift card"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "Unpaid gift card rejected."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "Use eWallet to pay"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "Valid Product"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "Valid until:"
msgstr "Хүчинтэй хугацаа:"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "Validation Error"
msgstr "Шалгалтын зөрчил"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Whether this line is part of a reward or not."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "Won:"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "Yes"
msgstr "Тийм"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/order_summary/order_summary.js:0
msgid "You cannot set negative quantity or price to gift card or ewallet."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid ""
"You either have not created an eWallet or all your eWallets have expired."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/loyalty_program.py:0
msgid "You must set '%(mail_template)s' before setting '%(report)s'."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.xml:0
msgid "eWallet"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.xml:0
msgid "eWallet Pay"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/control_buttons/control_buttons.xml:0
msgid "eWallet Refund"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
msgid "eWallet requires a customer to be selected"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "no expiration"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_order.js:0
msgid "the gift cards"
msgstr ""
