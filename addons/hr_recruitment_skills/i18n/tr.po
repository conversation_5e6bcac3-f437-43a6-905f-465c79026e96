# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_skills
# 
# Translators:
# emre <PERSON>tem, 2024
# Halil, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_applicant
msgid "Applicant"
msgstr "Aday"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__candidate_id
msgid "Candidate"
msgstr "Aday"

#. module: hr_recruitment_skills
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_candidate_view_tree
msgid "Create Application"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_job__skill_ids
msgid "Expected Skills"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_job
msgid "Job Position"
msgstr "İş Pozisyonu"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_job.py:0
msgid "Matching Candidates"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__matching_score
msgid "Matching Score(%)"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__matching_skill_ids
msgid "Matching Skills"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__missing_skill_ids
msgid "Missing Skills"
msgstr ""

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_job.py:0
msgid "No Matching Candidates"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__level_progress
msgid "Progress"
msgstr "İlerleme"

#. module: hr_recruitment_skills
#: model:ir.model.fields,help:hr_recruitment_skills.field_hr_candidate_skill__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "Sıfır bilgiden (% 0) tam uzmanlığa (% 100) kadar ilerleme."

#. module: hr_recruitment_skills
#. odoo-javascript
#: code:addons/hr_recruitment_skills/static/src/components/search_job_applicant_menu/search_job_applicant_menu.xml:0
#: model:ir.actions.server,name:hr_recruitment_skills.action_applicant_search_applicant
msgid "Search Matching Applicants"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant__skill_ids
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__skill_ids
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__skill_id
msgid "Skill"
msgstr "Yetenek"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__skill_level_id
msgid "Skill Level"
msgstr "Yetenek Seviyesi"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__skill_type_id
msgid "Skill Type"
msgstr "Yetenek Türü"

#. module: hr_recruitment_skills
#: model:ir.ui.menu,name:hr_recruitment_skills.hr_recruitment_skill_type_menu
msgid "Skill Types"
msgstr "Yetenek Türü"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_candidate_skill
msgid "Skill level for a candidate"
msgstr ""

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant__candidate_skill_ids
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__candidate_skill_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_candidate_view_search
msgid "Skills"
msgstr "Yetenekler"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_candidate_skill.py:0
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "%(name)s yetenek ve %(type)s yetenek türü eşleşmiyor"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_candidate_skill.py:0
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr "%(level)s yetenek seviyesi yetenek türü için geçerli değil: %(type)s"

#. module: hr_recruitment_skills
#: model:ir.model.constraint,message:hr_recruitment_skills.constraint_hr_candidate_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "Aynı yetenek için iki seviyeye izin verilmez"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_job.py:0
msgid ""
"We do not have any candidates who meet the skill requirements for this job "
"position in the database at the moment."
msgstr ""
