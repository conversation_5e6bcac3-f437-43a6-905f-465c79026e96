# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_razorpay
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__all
msgid "All"
msgstr "Tümü"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__bharatqr
msgid "BHARATQR"
msgstr "BHARATQR"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/razorpay_pos_request.py:0
msgid "Cannot decode Razorpay POS response"
msgstr "Razorpay POS yanıtı çözümlenemiyor"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Cannot process transactions with negative amount."
msgstr "Negatif tutardaki işlemler işlenemiyor."

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__card
msgid "Card"
msgstr "Kart"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid ""
"Choose allow payment mode: \n"
" All/Card/UPI or QR"
msgstr ""
"İzin verilen ödeme yöntemini seçin: \n"
" All/Card/UPI or QR"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Odoo sunucusuna bağlanılamadı, lütfen internet bağlantınızı kontrol edin ve "
"tekrar deneyin."

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid ""
"Device Serial No \n"
" ex: 7000012300"
msgstr ""
"Cihaz Seri Numarası \n"
" örn: 7000012300"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid ""
"Payment has been queued. You may choose to wait for the payment to initiate "
"on terminal or proceed to cancel this transaction"
msgstr ""
"Ödeme sıraya alındı. Ödemenin terminalde başlatılmasını beklemeyi veya bu "
"işlemi iptal etmeye devam etmeyi seçebilirsiniz"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Satış Noktası Ödeme Yöntemleri"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Satış Noktası Ödemeleri"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid "Razorpay API Key"
msgstr "Razorpay API Anahtarı"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid "Razorpay Allowed Payment Modes"
msgstr "Razorpay İzin Verilen Ödeme Yöntemleri"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid "Razorpay Device Serial No"
msgstr "Razorpay Cihaz Seri Numarası"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Razorpay Error"
msgstr "Razorpay Hatası"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment cancel request expected errorCode not found in the "
"response"
msgstr ""
"Razorpay POS ödeme iptal isteği yanıtında beklenen errorCode bulunamadı"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment request expected errorCode not found in the response"
msgstr ""
"Razorpay POS ödeme isteği hata kodu bekliyordu ancak yanıt içinde bulunamadı"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment status request expected errorCode not found in the "
"response"
msgstr ""
"Razorpay POS ödeme durumu isteği yanıtında beklenen errorCode bulunamadı"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "Razorpay POS transaction canceled successfully"
msgstr "Razorpay POS işlemi başarıyla iptal edildi"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "Razorpay POS transaction failed"
msgstr "Razorpay POS işlemi başarısız oldu"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_reverse_ref_no
msgid "Razorpay Reverse Reference No."
msgstr "Razorpay Ters İşlem Referans Numarası"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Razorpay Test Mode"
msgstr "Razorpay Test Modu"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_username
msgid "Razorpay Username"
msgstr "Razorpay Kullanıcı Adı"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Reference number mismatched"
msgstr "Referans numarası uyuşmuyor"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "This Payment Terminal is only valid for INR Currency"
msgstr "Bu Ödeme Terminali yalnızca INR para birimi için geçerlidir"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Transaction failed due to inactivity"
msgstr "İşlem, eylemsizlik nedeniyle başarısız oldu"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Turn it on when in Test Mode"
msgstr "Test Modundayken bunu etkinleştirin"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__upi
msgid "UPI"
msgstr "UPI"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid ""
"Used when connecting to Razorpay: "
"https://razorpay.com/docs/payments/dashboard/account-settings/api-keys/"
msgstr ""
"Razorpay'a bağlanırken kullanılır: "
"https://razorpay.com/docs/payments/dashboard/account-settings/api-keys/"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_username
msgid ""
"Username(Device Login) \n"
" ex: **********"
msgstr ""
"Kullanıcı Adı (Cihaz Girişi)  \n"
"örn: **********"
