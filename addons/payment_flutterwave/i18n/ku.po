# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_flutterwave
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (status %s). Please "
"try again."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__code
msgid "Code"
msgstr "کۆد"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields.selection,name:payment_flutterwave.selection__payment_provider__code__flutterwave
msgid "Flutterwave"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_token__flutterwave_customer_email
msgid "Flutterwave Customer Email"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_public_key
msgid "Flutterwave Public Key"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_secret_key
msgid "Flutterwave Secret Key"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_webhook_secret
msgid "Flutterwave Webhook Secret"
msgstr ""

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_provider
msgid "Payment Provider"
msgstr "دابینکەری پارەدان"

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_transaction
msgid "Payment Transaction"
msgstr "مامەڵەی پارەدان"

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Public Key"
msgstr "کلیلی گشتی"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "Received data with missing reference."
msgstr ""

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Secret Key"
msgstr ""

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_provider.py:0
msgid ""
"The communication with the API failed. Flutterwave gave us the following "
"information: '%s'"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_token__flutterwave_customer_email
msgid "The email of the customer at the time the token was created."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_provider__flutterwave_public_key
msgid "The key solely used to identify the account with Flutterwave."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "Unknown payment status: %s"
msgstr ""

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Webhook Secret"
msgstr ""
