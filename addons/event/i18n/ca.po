# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event
# 
# Translators:
# <PERSON> Bochaca <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <car<PERSON><EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# jabiri7, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Noemi Pla, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.model.fields,field_description:event.field_res_users__event_count
msgid "# Events"
msgstr "# Esdeveniments"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_count_done
msgid "# Sent"
msgstr "# Enviat"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%(event_name)s (%(count)s seats remaining)"
msgstr "%(event_name)s (%(count)s seients restants)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%(event_name)s (Sold out)"
msgstr "%(event_name)s (S'ha venut)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "%(event_name)s - Registration #%(registration_id)s"
msgstr "%(event_name)s - Inscripció #%(registration_id)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "%(event_name)s - Registration for %(attendee_name)s"
msgstr "%(event_name)s - Inscripció per a %(attendee_name)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%(start_date)s to %(end_date)s"
msgstr "%(start_date)s fins el %(end_date)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid "%(ticket_name)s (%(count)s seats remaining)"
msgstr "%(ticket_name)s (%(count)s seients restants)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid "%(ticket_name)s (Sold out)"
msgstr "%(ticket_name)s (S'ha venut)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.js:0
msgid "'%(name)s' badge sent to printer '%(printer)s'"
msgstr ""

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_attendee_list
msgid "'Attendee List - %s' % (object.name)"
msgstr ""

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_attendee_list
msgid "'Attendee List'"
msgstr ""

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_badge
msgid ""
"'Badge - %s - %s' % ((object.event_id.name or 'Event').replace('/',''), "
"(object.name or '').replace('/',''))"
msgstr ""

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_badge
msgid "'Badge - %s' % (object.name or 'Event').replace('/','')"
msgstr ""

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_full_page_ticket
msgid ""
"'Full Page Ticket - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), (object.name or '').replace('/',''))"
msgstr ""
"'Tiquet a pàgina completa - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), (object.name or '').replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_full_page_ticket
msgid "'Full Page Ticket - %s' % (object.name or 'Event').replace('/','')"
msgstr ""
"'Ticket de pàgina complet - %s' % (object.name or 'Event').replace('/','')"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "+123456789"
msgstr "+123456789"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "- \"%(event_name)s\": Missing %(nb_too_many)i seats."
msgstr "- \"%(event_name)s\": falten  %(nb_too_many)i seients."

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid ""
"- the ticket \"%(ticket_name)s\" (%(event_name)s): Missing %(nb_too_many)i "
"seats."
msgstr ""
"- l'entrada \"%(ticket_name)s\" (%(event_name)s): Falten %(nb_too_many)i "
"seients."

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_2
msgid "10-14"
msgstr "10-14"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_formatted_event_address
msgid "1000 Brussels"
msgstr "1000 Brussel·les"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_3
msgid "15-18"
msgstr "15-18"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_4
msgid "18+"
msgstr "18+"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__four_per_sheet
msgid "4 per sheet"
msgstr ""

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_1
msgid "5-10"
msgstr "5-10"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__96x134
msgid "96x134mm (Badge Printer)"
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__96x82
msgid "96x82mm (Badge Printer)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Business Room</b> - To discuss implementation methodologies, best sales "
"practices, etc."
msgstr ""
"<b>Sala de negocis</b> - Per discutir metodologies d'implementació, millors "
"pràctiques de vendes, etc."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Technical Rooms</b> - One dedicated to advanced Odoo developers, one for "
"new developers."
msgstr ""
"<b>Sales tècniques</b> - Un dedicat als desenvolupadors avançats d'Odoo, un "
"per a nous desenvolupadors."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The Design Fair is preceded by 2 days of Training Sessions for "
"experts!</b><br> We propose 3 different training sessions, 2 days each."
msgstr ""
"<b>La Fira de Disseny està precedida per dos dies de sessions d'entrenament "
"per a experts!</b><br> Proposem 3 sessions de formació diferents, 2 dies "
"cadascuna."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The plenary sessions in the morning will be shorter</b> and we will give "
"more time for thematical meetings, conferences, workshops and tutorial "
"sessions in the afternoon."
msgstr ""
"<b>Les sessions plenàries del matí seran més curtes</b> I donarem més temps "
"per a les reunions temàtiques, conferències, tallers i sessions "
"d'aprenentatge a la tarda."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The whole event is open to all public!</b> <br>We ask a participation fee"
" of 49.50€ for the costs for the 3 days (coffee breaks, catering, drinks and"
" a surprising concert and beer party).<br> For those who don't want to "
"contribute, there is a free ticket, therefore, catering and access to "
"evening events aren't included."
msgstr ""
"<b>Tot l'esdeveniment està obert al públic!</b> <br>Demanem una quota de "
"participació de 49,50€ per a les despeses dels 3 dies (cafès, servei "
"d'àpats, begudes i un sorprenent concert i festa de la cervesa).<br> Per als"
" quals no vulguin contribuir, hi ha una entrada gratuïta, per la qual cosa "
"el servei d'àpats i l'accés als esdeveniments nocturns no estan inclosos."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<b>Workshop Room</b> - Mainly for developers."
msgstr "<b>Sala del taller</b> - Principalment per als desenvolupadors."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid ""
"<br/>\n"
"                                            <span class=\"me-1\">from</span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid ""
"<br/>\n"
"                                            <span class=\"me-1\">to</span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                        <span class=\"text-muted\">Air your tracks online through a Youtube integration</span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                        <span class=\"text-muted\">Share a quiz to your attendees once a track is over</span>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<em>If you wish to make a presentation, please send your topic proposal as "
"soon as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany "
"(dot) com. The presentations should be, for example, a presentation of a "
"community module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English.</em>"
msgstr ""
"<em>Si desea hacer una presentación, envíe su propuesta de tema lo antes "
"posible para su aprobación al Sr. Famke Jenssens en ngh (a) yourcompany "
"(dot) com. Les presentacions haurien de ser, per exemple, una presentació "
"d'un mòdul comunitari, un estudi de casos, retroalimentació de metodologia, "
"tècnica, etc. Cada presentació ha de ser en anglès.</em>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<i class=\"fa fa-building\" title=\"Attendee Company\"/>"
msgstr "<i class=\"fa fa-building\" title=\"Attendee Company\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Confirm Attendance "
"Button\" title=\"Confirm Attendance\"/>"
msgstr ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Confirm Attendance "
"Button\" title=\"Confirm Attendance\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Reset To Registered"
" Button\" title=\"Reset To Registered\"/>"
msgstr ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Reset To Registered"
" Button\" title=\"Reset To Registered\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-group mt-1 me-2 text-center\" title=\"Attendees\"/>"
msgstr "<i class=\"fa fa-group mt-1 me-2 text-center\" title=\"Attendees\"/>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i class=\"fa fa-info-circle me-2\"></i>This event and all the conferences "
"are in <b>English</b>!"
msgstr ""
"<i class=\"fa fa-info-circle me-2\"></i>Aquest esdeveniment i totes les "
"conferències són en <b>anglès</b>!"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-map-marker mt-1 me-2 text-center ps-1\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-map-marker mt-1 me-2 text-center ps-1\" title=\"Location\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<i class=\"fa fa-ticket\" title=\"Ticket type\"/>"
msgstr "<i class=\"fa fa-ticket\" title=\"Ticket type\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"oi oi-arrow-right me-2\" title=\"End date\"/>"
msgstr "<i class=\"oi oi-arrow-right me-2\" title=\"End date\"/>"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-"
"end\">SPEAKER</span>"
msgstr ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-"
"end\">PARLANT</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "<span class=\"me-1\">to</span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
msgid "<span class=\"o_event_badge_font_faded\">My Placeholder Company</span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Registration\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Inscripció\n"
"                                </span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "<span>John Doe</span>"
msgstr "<span>John Doe</span>"

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?badge_mode=1&amp;registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                        Download Badges\n"
"                    </a>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        Please find attached your badge for\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>From</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 4, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 AM</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>To</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 6, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">5:00 PM</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">See location on Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">La seva inscripció</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?badge_mode=1&amp;registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                        Descarregar credencials\n"
"                    </a>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hola, <t t-out=\"object.name or 'Guest'\"/>:<br/><br/>\n"
"                        Li adjuntem les seves credencials per a\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Afegeixi aquest esdeveniment al seu calendari</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        Ens veiem aviat,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">LaSevaEmpresa</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            L'equip de <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>Del</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">4 de maig del 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 h</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>al</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">6 de maig del 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">17:00 h</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europa/Brussel·les</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">l'Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Té preguntes sobre aquest esdeveniment?</span>\n"
"                            <div>Si us plau, contacti amb l'organitzador:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">LaSevaEmpresa</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Correu: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Telèfon: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Aconsegueixi la millor experiència mòbil.</strong>\n"
"                            <a href=\"/event\">Instal·li la nostra aplicació per a dispositius mòbils</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">Visualitzar la ubicació a Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Enviat per <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">LaSevaEmpresa</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Descobreixi <a href=\"/event\" style=\"color:#875A7B;\">tots els nostres esdeveniments</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or 'Guest'\"/>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Tickets\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\">\n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        We are excited to remind you that the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        is starting <strong t-out=\"object.event_date_range or ''\">today</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>From</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 4, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 AM</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>To</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 6, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">5:00 PM</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"/></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone or ''\"/></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Get the best mobile experience.</strong>\n"
"                        <a href=\"/event\">Install our mobile app</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address and location\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"/>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-attf-src=\"{{ event_address.static_map_url }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                <span t-else=\"\">See location on Google Maps</span>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br/>\n"
"            Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">La seva inscripció</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or 'Guest'\"/>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            Visualitzar entrades\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\">\n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hola<t t-out=\"object.name or 'Guest'\"/>:<br/><br/>\n"
"                        Ens complau recordar-li que l'esdeveniment\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        comença <strong t-out=\"object.event_date_range or ''\">avui</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Afegeixi aquest esdeveniment al seu calendari</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        Confirmem la seva inscripció. Serà un plaer poder saludar-lo a l'esdeveniment.<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">LaSevaEmpresa</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            L'equip de <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>Del</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">4 de maig del 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 h</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>al</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">6 de maig del 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">17:00 h</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><t t-out=\"object.event_id.date_tz or ''\">Europa/Brussel·les</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">l'Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Té preguntes sobre aquest esdeveniment?</span>\n"
"                            <div>Si us plau, contacti amb l'organitzador:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">LaSevaEmpresa</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Correu: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"/></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Telèfon: <t t-out=\"event_organizer.phone or ''\"/></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Aconsegueixi la millor experiència mòbil.</strong>\n"
"                        <a href=\"/event\">Instal·li la nostra aplicació per a dispositius mòbils</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address and location\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"/>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-attf-src=\"{{ event_address.static_map_url }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                <span t-else=\"\">Visualitzar la ubicació a Google Maps</span>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Enviat per <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">LaSevaEmpresa</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br/>\n"
"            Descobreixi <a href=\"/event\" style=\"color:#875A7B;\">tots els nostres esdeveniments</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Tickets\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\"> \n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        We are happy to confirm your registration to the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;font-weight:bold;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>.\n"
"                        <t t-if=\"object.partner_id and object.partner_id.name and object.partner_id.name != object.name\">\n"
"                            This ticket was registered by <t t-out=\"object.partner_id.name\"/>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div t-if=\"is_sale\">\n"
"                        <br/>\n"
"                        The order for this ticket has reference <t t-out=\"object.sale_order_id.name\"/>\n"
"                        and was placed on <t t-out=\"object.sale_order_id.date_order.date()\"/>\n"
"                        <t t-if=\"object.sale_order_line_id.price_unit\"> for an amount of\n"
"                            <t t-out=\"object.sale_order_line_id.price_unit\" t-options=\"{'widget': 'monetary', 'display_currency': object.sale_order_line_id.currency_id}\"/>\n"
"                        </t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>From</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 4, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 AM</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>To</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 6, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">5:00 PM</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">See location on Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">La seva inscripció</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            Visualitzar entrades\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\"> \n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hola<t t-out=\"object.name or 'Guest'\"/>: ⁣<br/><br/>\n"
"                        Ens complau confirmar la seva inscripció a l'esdeveniment\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;font-weight:bold;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>.\n"
"                        <t t-if=\"object.partner_id and object.partner_id.name and object.partner_id.name != object.name\">\n"
"                            Aquesta entrada fou registrada per <t t-out=\"object.partner_id.name\"/>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div t-if=\"is_sale\">\n"
"                        <br/>\n"
"                        La referència d'aquesta entrada és <t t-out=\"object.sale_order_id.name\"/>\n"
"                        i es va realitzar el <t t-out=\"object.sale_order_id.date_order.date()\"/>\n"
"                        <t t-if=\"object.sale_order_line_id.price_unit\"> per un import de\n"
"                            <t t-out=\"object.sale_order_line_id.price_unit\" t-options=\"{'widget': 'monetary', 'display_currency': object.sale_order_line_id.currency_id}\"/>\n"
"                        </t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Afegeixi aquest esdeveniment al seu calendari</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        Ens veiem aviat,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name\">LaSevaEmpresa</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                           L'equip de <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>Del</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">4 de maig del 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 h</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>al</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">6 de maig del 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">17:00 h</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europa/Brussel·les</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">l'Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Té preguntes sobre aquest esdeveniment?</span>\n"
"                            <div>Si us plau, contacti amb l'organitzador:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">LaSevaEmpresa</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Correu: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Telèfon: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Aconsegueixi la millor experiència mòbil.</strong>\n"
"                            <a href=\"/event\">Instal·li la nostra aplicació per a dispositius mòbils</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">Visualitzar la ubicació a Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Enviat per <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">LaSevaEmpresa</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Descobreixi <a href=\"/event\" style=\"color:#875A7B;\">tots els nostres esdeveniments</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__description
#: model:ir.model.fields,help:event.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "Una descripció de l'entrada que vol comunicar als seus clients."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__a4_french_fold
msgid "A4 foldable"
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__a6
msgid "A6"
msgstr "A6"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
#: model:ir.model.fields,field_description:event.field_event_registration__active
msgid "Active"
msgstr "Actiu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_ids
#: model:ir.model.fields,field_description:event.field_event_registration__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_2
msgid "Activity"
msgstr "Activitat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_state
#: model:ir.model.fields,field_description:event.field_event_registration__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Add a description..."
msgstr "Afegir una descripció..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""
"Afegeix un menú de navegació a les pàgines web del teu esdeveniment amb "
"l'horari, formularis de proposta..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Add some internal notes (to do lists, contact info, ...)"
msgstr ""
"Afegeix algunes notes internes (per fer llistes, informació de contacte, "
"...)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_search
msgid "Address"
msgstr "Adreça"

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Administrator"
msgstr "Administrador"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_exhibitor
msgid "Advanced Sponsors"
msgstr "Patrocinadors avançats"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_sub
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_sub
msgid "After each registration"
msgstr "Després de cada registre"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_event
msgid "After the event"
msgstr "Després de l'esdeveniment"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_1
msgid "Age"
msgstr "Edat"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__lang
msgid ""
"All the communication emails sent to attendees will be translated in this "
"language."
msgstr ""

#. module: event
#: model:event.event,name:event.event_6
msgid "An unpublished event"
msgstr "Un esdeveniment no publicat"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"And this time, we go fully ONLINE! Meet us in our live streams from the comfort of your house.<br>\n"
"        Special discount codes will be handed out during the various streams, make sure to be there on time."
msgstr ""
"I aquesta vegada, anem totalment EN LÍNIA! Troba'ns en les nostres transmissions en viu des de la comoditat de la teva casa.<br>\n"
"        Es repartiran codis de descompte especials durant els diferents fluxos, assegura't d'arribar a temps."

#. module: event
#: model:event.stage,name:event.event_stage_announced
msgid "Announced"
msgstr "Anunciat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question_answer__name
msgid "Answer"
msgstr "Resposta"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Answer Breakdown"
msgstr "Desglossament de la resposta"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:event.event_question_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Answers"
msgstr "Respostes"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Apply change."
msgstr "Aplica el canvi."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Archived"
msgstr "Arxivat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_launched
msgid "Are sales launched"
msgstr "S'han llançat les vendes"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"Around one hundred ballons will simultaneously take flight and turn the sky "
"into a beautiful canvas of colours."
msgstr ""
"Al voltant d'un centenar de balons simultàniament prenen el vol i "
"converteixen el cel en un bonic llenç de colors."

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "As a team, we are happy to contribute to this event."
msgstr "Com a equip, ens complau contribuir a aquest esdeveniment."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__once_per_order
msgid "Ask once per order"
msgstr "Pregunta una vegada per comanda"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"Als 13 anys, John DOE ja estava començant a desenvolupar les seves primeres "
"aplicacions empresarials per a clients. Després de dominar l'enginyeria "
"civil, va fundar TinyERP. Aquesta va ser la primera fase de OpenERP que "
"després es convertiria en Odoo, el programari empresarial de codi obert més "
"instal·lat a tot el món."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "Assistència"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__done
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "Assistit"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "Data d'assistència"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "Assistent"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "Respostes dels assistents"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_attendee_list
#: model:ir.actions.report,name:event.action_report_event_registration_attendee_list
msgid "Attendee List"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
msgid "Attendee Name"
msgstr "Nom d'assistent"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__registration_answer_choice_ids
msgid "Attendee Selection Answers"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Attendee list"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.actions.act_window,name:event.event_registration_action
#: model:ir.actions.act_window,name:event.event_registration_action_kanban
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr "Assistents"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Auto-Print"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_available
msgid "Available Seats"
msgstr "Auto confirmació"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_badge
msgid "Badge"
msgstr "Insígnia "

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_badge_96x134
msgid "Badge (96x134mm)"
msgstr ""

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_badge_96x82
msgid "Badge (96x82mm)"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_image
msgid "Badge Background"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_format
msgid "Badge Dimension"
msgstr ""

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_badge
msgid "Badge Example"
msgstr ""

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
msgid "Badges"
msgstr "Insígnies"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"Bands like Bar Fighters, Led Slippers and Link Floyd will offer you the show"
" of the century during our three day event."
msgstr ""
"Bandes com Bar Fighters, Led Slippers i Link Floyd us oferiran l'espectacle "
"del segle durant el nostre esdeveniment de tres dies."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__barcode
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "Barcode"
msgstr "Codi de barres"

#. module: event
#: model:ir.actions.client,name:event.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr "Interfície de codi de barres"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenclatura del codi de barres"

#. module: event
#: model:ir.model.constraint,message:event.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr "El codi de barres ha de ser únic"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__before_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__before_event
msgid "Before the event"
msgstr "Abans l'esdeveniment"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_blocked:event.event_stage_announced
#: model:event.stage,legend_blocked:event.event_stage_booked
#: model:event.stage,legend_blocked:event.event_stage_cancelled
#: model:event.stage,legend_blocked:event.event_stage_done
#: model:event.stage,legend_blocked:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__blocked
msgid "Blocked"
msgstr "Bloquejat"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_1
msgid ""
"Bloem brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"Bloem aporta honestedat i serietat a la indústria fustera mentre ajuda els "
"clients a tractar amb arbres, flors i fongs."

#. module: event
#: model:event.stage,name:event.event_stage_booked
msgid "Booked"
msgstr "Reservat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "Reservat per"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_booth
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Booth Management"
msgstr "Gestió de cabines"

#. module: event
#: model:event.event,subtitle:event.event_5
msgid ""
"Bring your outdoor field hockey season to the next level by taking the field"
" at this 9th annual Field Hockey tournament."
msgstr ""
"Porta la teva temporada d'hoquei sobre gespa al següent nivell sortint al "
"camp en aquest 9è torneig anual d'hoquei sobre gespa."

#. module: event
#: model:event.event,name:event.event_4
msgid "Business workshops"
msgstr "Tallers de negocis"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Campaign"
msgstr "Campanya"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel"
msgstr "Cancel·la"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Cancel Registration"
msgstr "Cancel·lar la inscripció"

#. module: event
#: model:event.stage,name:event.event_stage_cancelled
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__cancel
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Cancelled registration"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_id
msgid "Category"
msgstr "Categoria"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_sequence
msgid "Category Sequence"
msgstr "Seqüència de categories"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"Chamber Works reserves the right to cancel, re-name or re-locate the event "
"or change the dates on which it is held."
msgstr ""
"Les obres de cambra es reserven el dret de cancel·lar, reanomenar o reubicar"
" l'esdeveniment o canviar les dates en què es celebra."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_type_id
msgid ""
"Choose a template to auto-fill tickets, communications, descriptions and "
"other fields."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__color
msgid "Color"
msgstr "Color"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__color
msgid "Color Index"
msgstr "Índex de color"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_registration_report
msgid "Come back once you have registrations to overview answers."
msgstr ""

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Come see us live, we hope to meet you!"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "Comunicació"

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__mail_registration_ids
msgid "Communication related to event registrations"
msgstr "Comunicació relacionada amb els registres d'esdeveniments"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Community Chat Rooms"
msgstr "Sales de xat de la comunitat"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__company_name
#: model_terms:event.event,description:event.event_2
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Company"
msgstr "Empresa"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Company Logo"
msgstr "Logotip de l'empresa"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__company_name
msgid "Company Name"
msgstr "Nom d'empresa"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Compose Email"
msgstr "Redactar correu electrònic"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_2
msgid "Conference"
msgstr "Conferència"

#. module: event
#: model:event.event,name:event.event_2
#: model_terms:event.event,description:event.event_2
msgid "Conference for Architects"
msgstr "Conferencia per arquitectes"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr "Conferències, tallers i entrenaments s'organitzaran en 6 habitacions:"

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "Configuració"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Attendance"
msgstr "Confirmar assistència"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Attendance Button"
msgstr ""

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Confirm attendance?"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Continue"
msgstr "Continuar"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "País"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Create Booths and manage their reservations"
msgstr "Crea estands i gestiona les seves reserves"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Create an Event"
msgstr "Crea un esdeveniment"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid "Create an Event Stage"
msgstr "Crear una etapa dels esdeveniments"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Create an Event Tag Category"
msgstr "Crea una categoria d'etiqueta d'esdeveniment"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid "Create an Event Template"
msgstr "Crea una plantilla d'esdeveniment"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_question__create_uid
#: model:ir.model.fields,field_description:event.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration_answer__create_uid
#: model:ir.model.fields,field_description:event.field_event_stage__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_question__create_date
#: model:ir.model.fields,field_description:event.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration_answer__create_date
#: model:ir.model.fields,field_description:event.field_event_stage__create_date
#: model:ir.model.fields,field_description:event.field_event_tag__create_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_date
msgid "Created on"
msgstr "Creat el"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_1
msgid "Culture"
msgstr "Cultura"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Customer"
msgstr "Client/a"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Customer Email"
msgstr "Correu del client"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Date"
msgstr "Data"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_date_range
msgid "Date Range"
msgstr "Interval de dates"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__days
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__days
msgid "Days"
msgstr "Dies"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,help:event.field_event_type_ticket__seats_max
msgid ""
"Define the number of available tickets. If you have too many registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""
"Defineix el nombre de tiquets disponibles. Si teniu massa registres ja no "
"podreu vendre bitllets. Establiu 0 per ignorar aquest conjunt de regles com "
"a il·limitades."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
#: model:ir.model.fields,field_description:event.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event.field_event_type_ticket__description
msgid "Description"
msgstr "Descripció"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr "Fira de disseny de Los Angeles"

#. module: event
#: model:event.event,subtitle:event.event_4
msgid "Discover how to grow a sustainable business with our experts."
msgstr ""
"Descobreix com desenvolupar un negoci sostenible amb els nostres experts."

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Discover more"
msgstr "Descobreix més"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_meet
msgid "Discussion Rooms"
msgstr "Sales de discussió"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_question__display_name
#: model:ir.model.fields,field_description:event.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration_answer__display_name
#: model:ir.model.fields,field_description:event.field_event_stage__display_name
#: model:ir.model.fields,field_description:event.field_event_tag__display_name
#: model:ir.model.fields,field_description:event.field_event_tag_category__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Display Sponsors and Exhibitors on your event pages"
msgstr ""
"Mostra els Patrocinadors i els Exhibitors a les vostres pàgines "
"d'esdeveniment"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
msgid "Display Timezone"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "Ordre de visualització"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__done
msgid "Done"
msgstr "Fet"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""
"Durant aquesta conferència, el nostre equip us donarà un resum detallat de "
"les nostres aplicacions de negoci. Coneixereu tots els beneficis "
"d'utilitzar-les."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Edit"
msgstr "Modificar"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: model:ir.model.fields,field_description:event.field_event_registration__email
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__email
msgid "Email"
msgstr "Correu electrònic"

#. module: event
#: model:ir.model,name:event.model_mail_template
msgid "Email Templates"
msgstr "Plantilles de correu electrònic"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Enable barcode scanning"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_res_config_settings__use_event_barcode
msgid "Enable or Disable Event Barcode functionality."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "Data de finalització"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr "Data final establerta"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__pipe_end
msgid "End Stage"
msgstr "Etapa final"

#. module: event
#: model:event.stage,name:event.event_stage_done
msgid "Ended"
msgstr "Finalitzat"

#. module: event
#: model:event.event,subtitle:event.event_2
msgid "Enhance your architectural business and improve professional skills."
msgstr ""
"Potenciï el seu negoci d'arquitectura i millori les seves habilitats "
"professionals."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_id
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_question__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr "Esdeveniment"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr "Enviament automatitzat d'esdeveniments"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__event_type_id
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Event Category"
msgstr "Categoria d'esdeveniment"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_form
msgid "Event Category Tag"
msgstr "Etiqueta de categoria d'esdeveniment"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "Data final de l'esdeveniment"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Event Gamification"
msgstr "Gamificació d'esdeveniments"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "Informació de l'esdeveniment"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr "Programador de correus d'esdeveniments"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr "Programadors de correus d'esdeveniments"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/template_reference_field/field_event_mail_template_reference.js:0
msgid "Event Mail Template Reference"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr "Nom de l'esdeveniment"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr "Organització de l'esdeveniment"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_organizer_id
msgid "Event Organizer"
msgstr "Organitzador d'esdeveniments"

#. module: event
#: model:ir.model,name:event.model_event_question
msgid "Event Question"
msgstr "Pregunta d'esdeveniment"

#. module: event
#: model:ir.model,name:event.model_event_question_answer
msgid "Event Question Answer"
msgstr "Esdeveniment Pregunta Resposta"

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr "Registre esdeveniment"

#. module: event
#: model:ir.model,name:event.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "Resposta de registre d'esdeveniments"

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
msgid "Event Registrations"
msgstr "Inscripcions a esdeveniments"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_user_id
msgid "Event Responsible"
msgstr "Esdeveniment responsable"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
msgid ""
"Event Scheduler for:\n"
"  - Event: %(event_name)s (%(event_id)s)\n"
"  - Scheduled: %(date)s\n"
"  - Template: %(template_name)s (%(template_id)s)\n"
"\n"
"Failed with error:\n"
"  - %(error)s\n"
"\n"
"You receive this email because you are:\n"
"  - the organizer of the event,\n"
"  - or the responsible of the event,\n"
"  - or the last writer of the template.\n"
msgstr ""
"Programador d'esdeveniments per:\n"
"  - Esdeveniment: %(event_name)s (%(event_id)s)\n"
"- Programat: %(date)s\n"
"  - Plantilla: %(template_name)s (%(template_id)s)\n"
"\n"
"Ha fallat amb l'error:\n"
"  - %(error)s\n"
"\n"
"Vostè rep aquest correu electrònic perquè ho és:\n"
" - l'organitzador de l'esdeveniment,\n"
"  - o el responsable de l'esdeveniment,\n"
"  - o l'últim escriptor de la plantilla.\n"

#. module: event
#: model:ir.model,name:event.model_event_stage
msgid "Event Stage"
msgstr "Etapa dels esdeveniments"

#. module: event
#: model:ir.actions.act_window,name:event.event_stage_action
#: model:ir.ui.menu,name:event.event_stage_menu
msgid "Event Stages"
msgstr "Etapa dels esdeveniments"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr "Data d'inici esdeveniment"

#. module: event
#: model:ir.model,name:event.model_event_tag
msgid "Event Tag"
msgstr "Etiqueta de l'esdeveniment"

#. module: event
#: model:ir.model,name:event.model_event_tag_category
msgid "Event Tag Category"
msgstr "Categoria de l'etiqueta de l'esdeveniment"

#. module: event
#: model:ir.actions.act_window,name:event.event_tag_category_action_tree
#: model:ir.ui.menu,name:event.menu_event_category
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_tree
msgid "Event Tags Categories"
msgstr "Categories d'etiquetes d'esdeveniments"

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Template"
msgstr "Plantilla d'esdeveniment"

#. module: event
#: model:ir.model,name:event.model_event_type_ticket
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_form_from_type
msgid "Event Template Ticket"
msgstr "Tiquet de plantilla d'esdeveniment"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_tree_from_type
msgid "Event Template Tickets"
msgstr "Tiquets de plantilla d'esdeveniment"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Templates"
msgstr "Plantilles d'esdeveniment"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid ""
"Event Templates combine configurations you use often and are\n"
"                usually based on the types of events you organize (e.g. \"Workshop\",\n"
"                \"Roadshow\", \"Online Webinar\", etc)."
msgstr ""
"Les plantilles d'esdeveniment combinen configuracions que utilitzeu sovint i que són\n"
"                generalment basat en els tipus d'esdeveniments que organitzeu (p. ex., \"Workshop\",\n"
"                «Roadshow», «Online Webinar», etc)."

#. module: event
#: model:ir.model,name:event.model_event_event_ticket
#: model:ir.model.fields,field_description:event.field_event_event__event_ticket_ids
msgid "Event Ticket"
msgstr "Tiquet d'esdeveniment"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "Tipus d'esdeveniment"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_tree
msgid "Event registrations"
msgstr "Registres d'esdeveniments"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid ""
"Event stages are used to track the progress of an Event from its origin "
"until its conclusion."
msgstr ""
"Les etapes de l'esdeveniment s'utilitzen per seguir el progrés d'un "
"esdeveniment des del seu origen fins a la seva conclusió."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "Event's Ticket"
msgstr "Tiquet de l'esdeveniment"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
msgid "Event: Mail Scheduler"
msgstr "Programador de correus d'esdeveniments"

#. module: event
#: model:mail.template,name:event.event_registration_mail_template_badge
msgid "Event: Registration Badge"
msgstr "Esdeveniment: Insígnia de registre"

#. module: event
#: model:mail.template,name:event.event_subscription
msgid "Event: Registration Confirmation"
msgstr "Esdeveniment: confirmació de la inscripció"

#. module: event
#: model:mail.template,name:event.event_reminder
msgid "Event: Reminder"
msgstr "Esdeveniment: Recordatori"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "Esdeveniments"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr "Programadors de correus d'esdeveniments"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_tree
msgid "Events Stage"
msgstr "Escenari d'esdeveniments"

#. module: event
#: model:ir.model.fields,help:event.field_event_stage__pipe_end
msgid ""
"Events will automatically be moved into this stage when they are finished. "
"The event moved into this stage will automatically be set as green."
msgstr ""
"Els esdeveniments es mouran automàticament en aquesta etapa quan acabin. "
"L'esdeveniment que s'ha mogut a aquesta etapa s'establirà automàticament com"
" a verd."

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"            This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"Convidem cada any a la nostra comunitat, contactes i usuaris finals a venir a trobar-nos! És l'esdeveniment ideal per reunir i presentar noves característiques, full de ruta de versions futures, èxits del programari, tallers, sessions de formació, etc...\n"
"            Aquest esdeveniment és també una oportunitat per a mostrar els estudis, la metodologia o els avanços dels nostres contactes. Sigueu allà i mireu directament des de la font les característiques de la nova versió!"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc...."
msgstr ""
"Cada any convidem a la nostra comunitat, als nostres contactes i usuaris "
"finals al fet que vinguin i ens coneguin! És l'esdeveniment ideal per "
"reunir-se i presentar noves característiques, full de ruta de versions "
"futures, èxits del programari, tallers, sessions de formació, etc."

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Exhibition"
msgstr "Exhibició"

#. module: event
#: model:event.event,subtitle:event.event_3
msgid "Experience live music, local food and beverages."
msgstr "Experimenta música en viu, menjar local i begudes."

#. module: event
#: model:event.stage,description:event.event_stage_done
msgid ""
"Finished events. Odoo will automatically move them to this stage once their "
"end date has passed."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__fold
msgid "Folded in Kanban"
msgstr "Plegat al Kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_type_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
#: model_terms:event.event,description:event.event_7
msgid "For any additional information, please contact us at"
msgstr "Per qualsevol informació, contacta'ns a"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""
"Per a cada esdeveniment podeu definir un registre màxim de places (nombre "
"d’assistents), per sobre d’aquest número no s’accepten les inscripcions."

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_1
msgid "For only 10, you gain access to catering. Yum yum."
msgstr "Per només 10, tens accés al servei d'àpats. Yum yum."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Foster interactions between attendees by creating virtual conference rooms"
msgstr ""
"Fomentar les interaccions entre els assistents mitjançant la creació de "
"sales de conferències virtuals"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_0
msgid "Free"
msgstr "Gratuït"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_0
msgid "Free entrance, no food!"
msgstr ""

#. module: event
#: model:event.stage,description:event.event_stage_new
msgid "Freshly created"
msgstr "Acabat de crear"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid ""
"From this dashboard you can report, analyze and detect trends regarding your"
" event registrations."
msgstr ""
"Des d'aquest tauler de control es poden informar, analitzar i detectar "
"tendències relatives als registres d'esdeveniments."

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_full_page_ticket
msgid "Full Page Ticket"
msgstr "Tiquet de pàgina completa"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_full_page_ticket
msgid "Full Page Ticket Example"
msgstr "Tiquet de pàgina completa Exemple"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr "Flux funcional de les aplicacions principals;"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Future Activities"
msgstr "Activitats futures"

#. module: event
#: model:event.event.ticket,name:event.event_4_ticket_0
msgid "General Admission"
msgstr "Admissió general"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__general_question_ids
msgid "General Questions"
msgstr "Preguntes generals"

#. module: event
#: model:event.event,subtitle:event.event_0
msgid "Get Inspired • Stay Connected • Have Fun"
msgstr "Inspirar-se • Estar connectat • Divertir-se"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_state
msgid "Global communication Status"
msgstr "Estat de la comunicació global"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Google Maps"
msgstr "Google Maps"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__google_maps_static_api_key
msgid "Google Maps API key"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__google_maps_static_api_secret
msgid "Google Maps API secret"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__use_google_maps_static_api
msgid "Google Maps static API"
msgstr ""

#. module: event
#: model:event.event,name:event.event_1
msgid "Great Reno Ballon Race"
msgstr "Gran carrera de pilota de Ren"

#. module: event
#: model_terms:web_tour.tour,rainbow_man_message:event.event_tour
msgid "Great! Now all you have to do is wait for your attendees to show up!"
msgstr "Genial. Ara només has d'esperar que apareguin els teus assistents."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_done
msgid "Green Kanban Label"
msgstr "Etiqueta kanban verda"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Etiqueta kanban grisa"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "Agrupar per"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Guest #"
msgstr ""

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Happy to be Sponsor"
msgstr "Feliç de ser Patrocinador"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__has_message
#: model:ir.model.fields,field_description:event.field_event_registration__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr ""
"Després d'assistir a aquesta conferència, els participants han de ser "
"capaços de:"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Here it is, the 12th edition of our Live Musical Festival!"
msgstr ""

#. module: event
#: model:event.event,name:event.event_5
msgid "Hockey Tournament"
msgstr "Torneig d'hoquei"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Home"
msgstr "Inici"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__hours
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__hours
msgid "Hours"
msgstr "Hores"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (1)"
msgstr "Com doblegar (1)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (2)"
msgstr "Com doblegar (2)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (3)"
msgstr "Com doblegar (3)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (4)"
msgstr "Com doblegar (4)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_question__id
#: model:ir.model.fields,field_description:event.field_event_question_answer__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__id
#: model:ir.model.fields,field_description:event.field_event_stage__id
#: model:ir.model.fields,field_description:event.field_event_tag__id
#: model:ir.model.fields,field_description:event.field_event_tag_category__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__id
msgid "ID"
msgstr "ID"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/icon_selection_field/icon_selection_field.js:0
msgid "Icon Selection"
msgstr "Selecció d'icones"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: event
#: model:ir.model.fields,help:event.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"Si és cert, aquesta pregunta només es farà una vegada i el seu valor es "
"propagarà a tots els assistents.En cas contrari, se li demanarà a cada "
"assistent d'una reserva."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__start_sale_datetime
msgid ""
"If ticketing is used, contains the earliest starting sale date of tickets."
msgstr ""
"Si s'utilitza la venda d'entrades, conté la data més primerenca d'inici de "
"la venda d'entrades."

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "If you don't have this ticket, you will <b>not</b> be allowed entry!"
msgstr "Si no tens aquest bitllet, <b>no</b> se li permeti l'entrada!"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"If you don't know anything about Hockey, this is a great introduction to this wonderful sport as you will will be able to see some training process and also have some time\n"
"                to chat with experienced players and trainers once the tournament is over!"
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__now
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__now
msgid "Immediately"
msgstr "Immediatament"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Important ticket information"
msgstr "Informació important del tiquet"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_normal:event.event_stage_announced
#: model:event.stage,legend_normal:event.event_stage_booked
#: model:event.stage,legend_normal:event.event_stage_cancelled
#: model:event.stage,legend_normal:event.event_stage_done
#: model:event.stage,legend_normal:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__normal
msgid "In Progress"
msgstr "En curs"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_tz
msgid ""
"Indicates the timezone in which the event dates/times will be displayed on "
"the website."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Insert dynamic Google Maps in your email templates"
msgstr ""

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Install"
msgstr "Instal·lar "

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "Interval"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr "Introducció, CRM, gestió de vendes"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Invalid event / ticket choice"
msgstr "Esdeveniment / elecció del tiquet no vàlid"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
msgid "Invalid ticket"
msgstr "Tiquet no vàlid"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sale_available
msgid "Is Available"
msgstr "Està disponible"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_expired
msgid "Is Expired"
msgstr "Està caducat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_finished
msgid "Is Finished"
msgstr "S'ha acabat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_one_day
msgid "Is One Day"
msgstr "És un dia"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "Està en curs"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__seats_max
msgid "It will select this default maximum value when you choose this event"
msgstr ""
"Seleccionarà el valor màxim per defecte quan escolliu aquest esdeveniment"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "John DOE"
msgstr "John DOE"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "John Doe"
msgstr "John Doe"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Join us for the greatest ballon race of all times!"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid "Join us for this 24 hours Event"
msgstr "Uniu-vos a nosaltres per aquest esdeveniment de 24 hores"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Join us for this 3-day Event"
msgstr "Uniu-vos a nosaltres per aquest esdeveniment de 3 dies"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Explicació del kanban bloquejat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Explicació del kanban en procés"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state
msgid "Kanban State"
msgstr "Estat kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state_label
msgid "Kanban State Label"
msgstr "Etiqueta de l'estat de Kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_done
msgid "Kanban Valid Explanation"
msgstr "Explicació del kanban vàlid"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Key"
msgstr "Clau"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__lang
msgid "Language"
msgstr "Idioma"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Last 30 days"
msgstr "Últims 30 dies"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__last_registration_id
msgid "Last Attendee"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_question__write_uid
#: model:ir.model.fields,field_description:event.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration_answer__write_uid
#: model:ir.model.fields,field_description:event.field_event_stage__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_question__write_date
#: model:ir.model.fields,field_description:event.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration_answer__write_date
#: model:ir.model.fields,field_description:event.field_event_stage__write_date
#: model:ir.model.fields,field_description:event.field_event_tag__write_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Late Activities"
msgstr "Activitats endarrerides"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Let's create your first <b>event</b>."
msgstr "Crearem el teu primer <b>esdeveniment</b>."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_limited
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_limited
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_limited
msgid "Limit Attendees"
msgstr "Limita els assistents"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Limit Registrations"
msgstr "Limita els registres"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr "Seients limitats"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Live Broadcast"
msgstr "Difusió en viu"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_live
msgid "Live Mode"
msgstr "Mode en directe en viu"

#. module: event
#: model:event.event,name:event.event_3
msgid "Live Music Festival"
msgstr "Festival de música en viu"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_mail__template_ref__mail_template
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__template_ref__mail_template
msgid "Mail"
msgstr "Correu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr "Registre de correus electrònics"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr "Horari de correu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "Programador de correu electrònic"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "Programadors de correus electrònics"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Programació de correu a la categoria d'esdeveniments"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr "Correu enviat"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage & publish a schedule with tracks"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Mandatory"
msgstr "Obligatori"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr "Resposta obligatòria"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Mark as Attending"
msgstr "Marcar com a present"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Marketing"
msgstr "Màrqueting"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
msgid "Maximum"
msgstr "Màxim"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_max
msgid "Maximum Attendees"
msgstr "Participants màxims"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__seats_max
msgid "Maximum Registrations"
msgstr "Registres màxims"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Maximum Seats"
msgstr "Seients màxims"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_medium_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Medium"
msgstr "Mitjà"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__months
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__months
msgid "Months"
msgstr "Mesos"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_2
msgid "Music"
msgstr "Música"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__my_activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "Els meus esdeveniments"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: model:ir.model.fields,field_description:event.field_event_event_ticket__name
#: model:ir.model.fields,field_description:event.field_event_tag__name
#: model:ir.model.fields,field_description:event.field_event_tag_category__name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__name
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__name
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Name"
msgstr "Nom"

#. module: event
#: model:event.stage,name:event.event_stage_new
msgid "New"
msgstr "Nou"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_calendar_event_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_summary
#: model:ir.model.fields,field_description:event.field_event_registration__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_registration_report
msgid "No Answers yet!"
msgstr "Encara no hi ha respostes!"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
msgid "No Attendees expected yet!"
msgstr "¡Encara no s'esperen assistents!"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid "No Attendees yet!"
msgstr "¡Encara no hi ha assistents!"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenclatura"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__note
#: model:ir.model.fields,field_description:event.field_event_type__note
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Note"
msgstr "Nota"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Notes"
msgstr "Notes"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid "Nothing Scheduled yet!"
msgstr "¡Encara no hi ha res programat!"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Now that your event is ready, click here to move it to another stage."
msgstr ""
"Ara que el teu esdeveniment està llest, fes clic aquí per moure'l a una "
"altra etapa."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Attendees"
msgstr "Nombre d'assistents"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
msgid "Number of Registrations"
msgstr "Nombre de registres"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_taken
msgid "Number of Taken Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Objectives"
msgstr "Objectius"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "Odoo Community Days"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Once again we assembled the most legendary bands in Rock history."
msgstr ""
"Una vegada més vam reunir les bandes més llegendàries de la història de "
"Rock."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Once per Order"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ongoing Events"
msgstr "Esdeveniments en curs"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_1
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Online"
msgstr "En línia"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Online Event"
msgstr "Esdeveniment en línia"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Online Exhibitors"
msgstr "Exhibitors en línia"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr "Ticketing Online"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Online if not set"
msgstr "En línia si no establert"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Open date range picker.<br/>Pick a Start and End date for your event."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications reserves the right to cancel, re-name or re-locate the"
" event or change the dates on which it is held."
msgstr ""
"OpenElec Applications es reserva el dret a cancel·lar, reanomenar o reubicar"
" l'esdeveniment o canviar les dates en què es celebra."

#. module: event
#: model:event.event,name:event.event_7
msgid "OpenWood Collection Online Reveal"
msgstr "Presentació en línia de la col·lecció OpenWood"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "Operation not supported."
msgstr "Operació no suportada."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Organizer"
msgstr "Organitzador"

#. module: event
#: model:event.event,subtitle:event.event_7
msgid ""
"Our newest collection will be revealed online! Interact with us on our live "
"streams!"
msgstr ""
"La nostra nova col·lecció serà revelada en línia. Interactui amb nosaltres "
"en les nostres transmissions en directe."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_blocked
#: model:ir.model.fields,help:event.field_event_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection."
msgstr ""
"Sobreescriu el valor per defecte mostrat per a l'estat bloquejat per a la "
"selecció kanban."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_done
#: model:ir.model.fields,help:event.field_event_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection."
msgstr ""
"Sobreescriu el valor per defecte mostrat per a l'estat fet per a la selecció"
" kanban."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_normal
#: model:ir.model.fields,help:event.field_event_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection."
msgstr ""
"Sobreescriu el valor per defecte mostrat per a l'estat normal per a la "
"selecció kanban."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "Participant"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Empresa"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: model:ir.model.fields,field_description:event.field_event_registration__phone
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__phone
msgid "Phone"
msgstr "Telèfon"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Phone number"
msgstr "Número de telèfon"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid ""
"Please come <b>at least</b> 30 minutes before the beginning of the event."
msgstr ""
"Si us plau <b>com a mínim</b> 30 minuts abans del començament de "
"l'esdeveniment."

#. module: event
#. odoo-python
#: code:addons/event/models/res_config_settings.py:0
msgid "Please enter a valid base64 secret"
msgstr ""

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
msgid "Please, Scan again!"
msgstr "Si us plau, escaneja de nou!"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr "Punt de Venda (PDV), introducció a la personalització d'informes."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Print"
msgstr "Imprimir"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "Program"
msgstr "Programa"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr "La gestió de projectes, recursos humans, gestió de contractes."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__registration_properties
msgid "Properties"
msgstr "Propietats"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr "Punt de venda (PDV), introducció a la personalització de respostes."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "QR Code"
msgstr "Codi QR"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:event.event_question_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Question"
msgstr "Qüestió"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__question_type
#: model:ir.model.fields,field_description:event.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "Tipus de pregunta"

#. module: event
#. odoo-python
#: code:addons/event/models/event_question.py:0
msgid "Question cannot be linked to both an Event and an Event Type."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__question_ids
#: model:ir.model.fields,field_description:event.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Questions"
msgstr "Preguntes"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_quiz
msgid "Quiz on Tracks"
msgstr "Qüestionari en les pistes"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__rating_ids
#: model:ir.model.fields,field_description:event.field_event_registration__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_done:event.event_stage_announced
#: model:event.stage,legend_done:event.event_stage_booked
#: model:event.stage,legend_done:event.event_stage_cancelled
#: model:event.stage,legend_done:event.event_stage_done
#: model:event.stage,legend_done:event.event_stage_new
msgid "Ready for Next Stage"
msgstr "Preparat per propera etapa"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Ready to <b>organize events</b> in a few minutes? Let's get started!"
msgstr ""
"Llegit a <b>organitzar esdeveniments</b> ¿d'aquí uns minuts? ¡Comencem!"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Etiqueta vermella de Kanban"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__open
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registered"
msgstr "Registrat"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#: model:ir.model.fields,field_description:event.field_event_registration_answer__registration_id
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Registration"
msgstr "Registre"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr "Data del registre"

#. module: event
#: model:ir.actions.client,name:event.event_action_install_kiosk_pwa
#: model:ir.ui.menu,name:event.menu_event_registration_desk
#: model:res.groups,name:event.group_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Registration Desk"
msgstr "Taulell d'inscripció"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__end_sale_datetime
msgid "Registration End"
msgstr "Final del registre"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr "ID de registre"

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Registre de programador de correu electronic"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr "Registre de correus electrònics"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__registration_properties_definition
msgid "Registration Properties"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__start_sale_datetime
msgid "Registration Start"
msgstr "Inici del registre"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid "Registration for %s"
msgstr "Registre per %s"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr "Registrament de correu electrònic"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_open
msgid "Registration open"
msgstr "Registre obert"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_stats_from_event
msgid "Registration statistics"
msgstr "Estadístiques de registre"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__registration_ids
msgid "Registrations"
msgstr "Registres"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_open
msgid ""
"Registrations are open if:\n"
"- the event is not ended\n"
"- there are seats available on event\n"
"- the tickets are sellable (if ticketing is used)"
msgstr ""
"Els registres estan oberts si:\n"
"- l'esdeveniment no ha finalitzat\n"
"- hi ha seients disponibles a l'esdeveniment\n"
"- les entrades són vendibles (si s'utilitza la venda d'entrades)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_started
msgid "Registrations started"
msgstr "Registres iniciats"

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "Informes"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_reserved
msgid "Reserved Seats"
msgstr "Places reservades"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Reset To Registered"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Reset To Registered Button"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Responsible"
msgstr "Responsable"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_user_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_responsive_html_ticket
msgid "Responsive Html Full Page Ticket"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_formatted_event_address
msgid "Rue de la Paix 123"
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__running
msgid "Running"
msgstr "En procés"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Scan or Tap"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr "Programació i rutes"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Schedule Date"
msgstr "Data de la planificació"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events: handle registrations, send automated "
"confirmation emails, sell tickets, etc."
msgstr ""
"Programi i organitzi els seus esdeveniments: gestioni les inscripcions, "
"enviï correus electrònics de confirmació automàtics, vengui entrades, etc."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__scheduled
msgid "Scheduled"
msgstr "Planificat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr "Temps programat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__mail_registration_ids
msgid "Scheduler Emails"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_5
msgid "Seasoned Hockey Fans and curious people, this tournament is for you!"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Secret"
msgstr "Secret"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Select Attendee"
msgstr "Selecciona l'assistent"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Select printer..."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "Resposta seleccionada"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "Selecció"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr "Vendre tiquets en el teu lloc web"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with Point of Sale"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr "Vendre tiquets amb ordres de venda"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event.field_event_type_mail__notification_type
msgid "Send"
msgstr "Enviar"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "Enviar per correu electrònic "

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_done
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__sent
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Sent"
msgstr "Enviat"

#. module: event
#: model:mail.template,description:event.event_reminder
msgid ""
"Sent automatically to attendees if there is a reminder defined on the event"
msgstr ""
"Enviat automàticament als assistents si hi ha un recordatori definit a "
"l'esdeveniment"

#. module: event
#: model:mail.template,description:event.event_registration_mail_template_badge
msgid "Sent automatically to someone after they registered to an event"
msgstr ""
"Enviat automàticament a algú després d'haver-se registrat a un esdeveniment"

#. module: event
#: model:mail.template,description:event.event_subscription
msgid "Sent to attendees after registering to an event"
msgstr "Enviat als assistents després de registrar-se a un esdeveniment"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sequence
#: model:ir.model.fields,field_description:event.field_event_question__sequence
#: model:ir.model.fields,field_description:event.field_event_question_answer__sequence
#: model:ir.model.fields,field_description:event.field_event_stage__sequence
#: model:ir.model.fields,field_description:event.field_event_tag__sequence
#: model:ir.model.fields,field_description:event.field_event_tag_category__sequence
#: model:ir.model.fields,field_description:event.field_event_type__sequence
#: model:ir.model.fields,field_description:event.field_event_type_ticket__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "Configuració"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_4
msgid ""
"Shangai Pterocarpus Furniture brings honesty and seriousness to wood "
"industry while helping customers deal with trees, flowers and fungi."
msgstr ""
"Shangai Pterocarpus Furniture aporta honestedat i serietat a la indústria "
"fustera, mentre que ajuda els clients a tractar amb arbres, flors i fongs."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostra tots els registres en que la data de següent acció és abans d'avui"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_sold_out
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_sold_out
msgid "Sold Out"
msgstr "Esgotat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_source_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Source"
msgstr "Font"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "Preguntes específiques"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_3
#: model:event.type,name:event.event_type_2
msgid "Sport"
msgstr "Esport"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__stage_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Stage"
msgstr "Etapa"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Descripció de l'etapa i informació sobre eines"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__name
msgid "Stage Name"
msgstr "Nom de la fase"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__description
msgid "Stage description"
msgstr "Descripció de l'escenari"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_1
#: model:event.event.ticket,name:event.event_2_ticket_1
#: model:event.event.ticket,name:event.event_3_ticket_0
#: model:event.event.ticket,name:event.event_7_ticket_1
msgid "Standard"
msgstr "Estàndard "

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "Data inicial"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr "Data d'inici establerta"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__start_sale_datetime
msgid "Start sale date"
msgstr "Data d'inici de venda"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__static_map_url
#: model:ir.model.fields,field_description:event.field_res_users__static_map_url
msgid "Static Map Url"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__static_map_url_is_valid
#: model:ir.model.fields,field_description:event.field_res_users__static_map_url_is_valid
msgid "Static Map Url Is Valid"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Stats"
msgstr "Estadístiques"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Estat"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_state
#: model:ir.model.fields,help:event.field_event_registration__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Successfully registered!"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "Resposta suggerida"

#. module: event
#: model:ir.model.fields,help:event.field_event_tag__color
msgid ""
"Tag color. No color means no display in kanban or front-end, to distinguish "
"internal tags from public categorization tags."
msgstr ""
"Color de l'etiqueta. Sense color significa que no es mostra en kanban o "
"front-end, per a distingir les etiquetes internes de les etiquetes de "
"categorització pública."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__tag_ids
#: model:ir.model.fields,field_description:event.field_event_tag_category__tag_ids
#: model:ir.model.fields,field_description:event.field_event_type__tag_ids
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "Tags"
msgstr "Etiquetes"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Taken"
msgstr "Agafat"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_taken
msgid "Taken Seats"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "Tasca en curs. Feu clic per blocar o establir com s'ha fet."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"La tasca està bloquejada. Feu clic per desbloquejar o establir com s'ha fet."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
#: model:ir.model.fields,field_description:event.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_ref
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Template"
msgstr "Plantilla"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "Entrada de text"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "Text de la resposta"

#. module: event
#: model:event.event,subtitle:event.event_1
msgid ""
"The Great Reno Balloon Race is the world's largest free hot-air ballooning "
"event."
msgstr ""
"La Gran Reno Balloon Race és l'esdeveniment de globus d'aire calent més gran"
" del món."

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"The best Hockey teams of the country will compete for the national Hockey "
"trophy."
msgstr ""
"Els millors equips d'hoquei del país competiran pel trofeu nacional "
"d'hoquei."

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"The best aeronauts of the world will gather on this event to offer you the "
"most spectacular show."
msgstr ""
"Els millors aeronàutics del món es reuniran en aquest esdeveniment per "
"oferir-vos l'espectacle més espectacular."

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "The closing date cannot be earlier than the beginning date."
msgstr "La data de tancament no pot ser anterior a la d'inici"

#. module: event
#: model:event.stage,description:event.event_stage_cancelled
msgid "The event has been cancelled"
msgstr "L'esdeveniment s'ha cancel·lat"

#. module: event
#: model:event.stage,description:event.event_stage_announced
msgid "The event has been publicly announced"
msgstr "L'esdeveniment s'ha anunciat públicament"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_sold_out
msgid ""
"The event is sold out if no more seats are available on event. If ticketing "
"is used and all tickets are sold out, the event will be sold out."
msgstr ""
"L'esdeveniment està esgotat si no hi ha més seients disponibles en "
"l'esdeveniment. Si s'utilitza la venda d'entrades i s'esgoten totes, "
"l'esdeveniment estarà esgotat."

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"The finest OpenWood furnitures are coming to your house in a brand new "
"collection"
msgstr ""
"Els millors mobles d'OpenWood venen a casa teva en una col·lecció nova"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid ""
"The following tickets cannot be deleted while they have one or more registrations linked to them:\n"
"- %s"
msgstr ""
"Els següents bitllets no poden ser eliminats mentre tenen un o més registres enllaçats a ells:\n"
"- %s"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "The safety of our attendees and our aeronauts comes first!"
msgstr ""

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid ""
"The stop date cannot be earlier than the start date. Please check ticket "
"%(ticket_name)s"
msgstr ""
"La data de finalització no pot ser anterior a la data d'inici. Comproveu "
"l'entrada %(ticket_name)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_ticket.py:0
msgid "There are not enough seats available for:"
msgstr "No hi ha suficients seients disponibles."

#. module: event
#: model:ir.model.constraint,message:event.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "Ha d'haver-hi un valor suggerit o un valor de text."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This event is also an opportunity to showcase our partners' case studies, "
"methodology or developments. Be there and see directly from the source the "
"features of the version 12!"
msgstr ""
"Aquest esdeveniment també és una oportunitat per a mostrar els estudis de "
"cas, la metodologia o els desenvolupaments dels nostres contactes. Aneu aquí"
" i veieu directament des de la font les característiques de la versió 12!"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"This event is fully online and FREE, if you have paid for tickets, you should get a refund.<br>\n"
"        It will require a good Internet connection to get the best video quality."
msgstr ""
"Aquest esdeveniment està totalment en línia i és gratuït, si s'ha pagat per bitllets, s'hauria d'obtenir un reemborsament.<br>\n"
"        Necessitarà una bona connexió a Internet per a obtenir la millor qualitat de vídeo."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__ticket_instructions
#: model:ir.model.fields,help:event.field_event_type__ticket_instructions
msgid "This information will be printed on your tickets."
msgstr "Aquesta informació s'imprimirà als teus tiquets."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "This is the <b>name</b> your guests will see when registering."
msgstr ""
"Això és el <b>nom</b> els vostres convidats ho veuran en registrar-se."

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"This is the perfect place for spending a nice day with your family, we "
"guarantee you will be leaving with beautiful everlasting memories!"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"This is the perfect place for spending a nice time with your friends while "
"listening to some of the most iconic rock songs of all times!"
msgstr ""
"Aquest és el lloc perfecte per passar un bon temps amb els vostres amics "
"mentre escolteu algunes de les cançons de rock més icòniques de tots els "
"temps!"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "This operator is not supported"
msgstr "Aquest operador no és compatible"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "Aquest pas està fet. Faci clic per a bloquejar o posar en marxa."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "This ticket is for another event!"
msgstr ""

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "This ticket is not for an ongoing event"
msgstr "Aquest tiquet no és per un esdeveniment en curs"

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ticket"
msgstr "Tiquet"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__ticket_instructions
#: model:ir.model.fields,field_description:event.field_event_type__ticket_instructions
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Ticket Instructions"
msgstr "Instruccions del tiquet"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_registration__event_ticket_id
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ticket Type"
msgstr "Tipus d'Entrades"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Ticket already scanned!"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Ticket type"
msgstr ""

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid ""
"Ticket types allow you to distinguish your attendees. Let's <b>create</b> a "
"new one."
msgstr ""
"Els tipus de tiquets us permeten distingir els assistents. Anem <b>crear</b>"
" un de nou."

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
#: model:ir.model.fields,field_description:event.field_event_type__event_type_ticket_ids
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Tickets"
msgstr "Tiquets"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Tickets can be printed or scanned directly from your phone."
msgstr ""
"Els tiquets es poden imprimir o escanejar directament des del telèfon."

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_pos_event
msgid "Tickets with PoS"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
msgid "Tickets with Sale"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "Zona horària"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__title
msgid "Title"
msgstr "Títol"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Today Activities"
msgstr "Activitats d'avui"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total"
msgstr "Total"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total Attendees"
msgstr "Total d'assistents"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Total Registrations for this Event"
msgstr "Total d'inscripcions per a aquest esdeveniment"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr "Seguiments i agenda"

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Training"
msgstr "Formació"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_3
msgid ""
"Tree Dealers brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"Tree Dealers aporta honestedat i serietat al sector de la fusta al mateix "
"temps que ajuda als clients a ocupar-se dels arbres, les flors i els fongs."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "Activació"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr "Activació"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_3
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Type"
msgstr "Tipus"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__draft
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "No confirmat"

#. module: event
#: model:ir.model.fields,help:event.field_event_registration__state
msgid ""
"Unconfirmed: registrations in a pending state waiting for an action (specific case, notably with sale status)\n"
"Registered: registrations considered taken by a client\n"
"Attended: registrations for which the attendee attended the event\n"
"Cancelled: registrations cancelled manually"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid ""
"Under this technical menu you will find all scheduled communication related "
"to your events."
msgstr ""
"En aquest menú tècnic trobareu totes les comunicacions programades "
"relacionades amb els esdeveniments."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr "Entendre els diversos mòduls;"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Undo"
msgstr "Desfer"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "Unitat de venda"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr "Següents esdeveniments des d'avui"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr "Propers/En curs"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__use_barcode
msgid "Use Barcode"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__use_event_barcode
msgid "Use Event Barcode"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Use Event Tag Categories to classify and organize your event tags."
msgstr ""
"Utilitzeu les categories d'etiquetes d'esdeveniment per classificar i "
"organitzar les etiquetes d'esdeveniment."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Use the <b>breadcrumbs</b> to go back to your kanban overview."
msgstr ""
"Utilitza el<b>fil d'Ariadna</b> per tornar a la vista general del kanban."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_used
msgid "Used Seats"
msgstr "Seients usats"

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "Usuari"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_2
#: model:event.event.ticket,name:event.event_2_ticket_2
#: model:event.event.ticket,name:event.event_3_ticket_1
#: model:event.event.ticket,name:event.event_7_ticket_2
msgid "VIP"
msgstr "VIP"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "Value should be True or False (not %s)"
msgstr "El valor ha de ser cert o fals (no %s)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Venue"
msgstr "Lloc"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_inline
msgid "Venue (formatted for one line uses)"
msgstr "Lloc de venda (formatat per a utilitzar una línia)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr ""
"PERILL: Error del planificador d'esdeveniments per a l'esdeveniment: %s"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
msgid ""
"Wait until Attendees register to your Event or create their registrations "
"manually."
msgstr ""
"Espereu fins que els assistents es registrin a l'esdeveniment o creeu els "
"seus registres manualment."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr ""
"Administració de Bodega, Producció (MRP) &amp; Vendes, Importar/Exportar."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
msgid "Warning"
msgstr "Avís"

#. module: event
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid ""
"We reserve the right to cancel, re-name or re-locate the event or change the"
" dates on which it is held in case the weather fails us."
msgstr ""
"Ens reservem el dret a cancel·lar, reanomenar o reubicar l'esdeveniment o "
"canviar les dates en les quals se celebra en cas que el temps ens falli."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__website_message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__website_message_ids
#: model:ir.model.fields,help:event.field_event_registration__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__weeks
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__weeks
msgid "Weeks"
msgstr "Setmanes"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Welcome to"
msgstr "Benvingut a"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What's new?"
msgstr "Què és nou?"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_begin
#: model:ir.model.fields,help:event.field_event_registration__event_begin_date
msgid ""
"When the event is scheduled to take place (expressed in your local timezone "
"on the form view)."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Si l'esdeveniment ha començat"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__sale_available
msgid "Whether it is possible to sell these tickets"
msgstr "Si és possible vendre aquests tiquets"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__is_sold_out
msgid "Whether seats are not available for this ticket."
msgstr "Si no hi han disponibles seients per a aquesta entrada."

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_2
msgid "You are truly among the best."
msgstr "Realment estàs entre els millors."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"També podeu afegir una descripció per ajudar als vostres companys de feina a"
" entendre el significat i el propòsit d'aquesta etapa."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels in the kanban view."
msgstr ""
"Aquí podeu definir les etiquetes que es mostraran per a l'estat\n"
"                          de les etiquetes predeterminades a la vista kanban."

#. module: event
#. odoo-python
#: code:addons/event/models/event_question.py:0
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr ""
"No podeu canviar el tipus de pregunta d'una pregunta que ja té respostes!"

#. module: event
#. odoo-python
#: code:addons/event/models/event_question.py:0
msgid ""
"You cannot delete a question that has already been answered by attendees."
msgstr "No podeu suprimir una pregunta que ja han contestat els assistents."

#. module: event
#. odoo-python
#: code:addons/event/models/event_question_answer.py:0
msgid ""
"You cannot delete an answer that has already been selected by attendees."
msgstr "No podeu suprimir una resposta que ja han seleccionat els assistents."

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for {{ object.event_id.name }}"
msgstr "La vostra insígnia per {{object.event_id.name }}"

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at {{ object.event_id.name }}"
msgstr "El vostre registre a {{object.event_id.name }}"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "e.g. \"12-16 years old\""
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "e.g. \"Age Category\""
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "e.g. \"Azure Interior\""
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_question_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr "P. ex. \"Teniu restriccions dietètiques?\""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "e.g. \"Promoting\""
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "e.g. Conference for Architects"
msgstr "ex. Conferència per a Arquitectes"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. How to get to your event, door closing time, ..."
msgstr ""
"Per exemple, com arribar a l'esdeveniment, hora de tancament de la porta..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. Online Conferences"
msgstr "p. ex. Conferències en línia"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "e.g. VIP Ticket"
msgstr "p. ex. tiquet VIP"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "https://www.example.com"
msgstr ""

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "in %d days"
msgstr "en %d dies"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "next month"
msgstr "mes següent"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "next week"
msgstr "Setmana següent"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "on %(date)s"
msgstr "en %(date)s"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "or"
msgstr "o"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "<EMAIL>"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_started
msgid ""
"registrations have started if the current datetime is after the earliest "
"starting date of tickets."
msgstr ""
"les inscripcions han començat si la data actual és posterior a la data més "
"primerenca d'inici de les entrades."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_kanban_from_event
msgid "reserved"
msgstr "reservat"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "to"
msgstr "fins"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "today"
msgstr "avui"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "tomorrow"
msgstr "demà"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "{{ object.event_id.name }}: {{ object.event_date_range }}"
msgstr "{{ object.event_id.name }}: {{ object.event_date_range }}"
