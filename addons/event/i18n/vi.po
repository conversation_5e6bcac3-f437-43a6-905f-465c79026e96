# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.model.fields,field_description:event.field_res_users__event_count
msgid "# Events"
msgstr "# Sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_count_done
msgid "# Sent"
msgstr "# Đã gửi"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%(event_name)s (%(count)s seats remaining)"
msgstr "%(event_name)s (còn %(count)s chỗ)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%(event_name)s (Sold out)"
msgstr "%(event_name)s (Hết chỗ)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "%(event_name)s - Registration #%(registration_id)s"
msgstr "%(event_name)s - Đăng ký #%(registration_id)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "%(event_name)s - Registration for %(attendee_name)s"
msgstr "%(event_name)s - Đăng ký cho %(attendee_name)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%(start_date)s to %(end_date)s"
msgstr "%(start_date)s đến %(end_date)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid "%(ticket_name)s (%(count)s seats remaining)"
msgstr "%(ticket_name)s (còn %(count)s chỗ)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid "%(ticket_name)s (Sold out)"
msgstr "%(ticket_name)s (Hết chỗ)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "%s (copy)"
msgstr "%s (sao chép)"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.js:0
msgid "'%(name)s' badge sent to printer '%(printer)s'"
msgstr "Thẻ '%(name)s' đã được gửi đến máy in '%(printer)s'"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_attendee_list
msgid "'Attendee List - %s' % (object.name)"
msgstr "'Danh sách người tham dự - %s' % (object.name)"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_attendee_list
msgid "'Attendee List'"
msgstr "'Danh sách người tham dự'"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_badge
msgid ""
"'Badge - %s - %s' % ((object.event_id.name or 'Event').replace('/',''), "
"(object.name or '').replace('/',''))"
msgstr ""
"'Thẻ - %s - %s' % ((object.event_id.name or 'Sự kiện').replace('/',''), "
"(object.name or '').replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_badge
msgid "'Badge - %s' % (object.name or 'Event').replace('/','')"
msgstr "'Thẻ - %s' % (object.name or 'Sự kiện').replace('/','')"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_full_page_ticket
msgid ""
"'Full Page Ticket - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), (object.name or '').replace('/',''))"
msgstr ""
"'Vé toàn trang - %s - %s' % ((object.event_id.name or 'Sự "
"kiện').replace('/',''), (object.name or '').replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_full_page_ticket
msgid "'Full Page Ticket - %s' % (object.name or 'Event').replace('/','')"
msgstr "'Vé toàn trang - %s' % (object.name or 'Event').replace('/','')"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "+123456789"
msgstr "+123456789"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "- \"%(event_name)s\": Missing %(nb_too_many)i seats."
msgstr "- \"%(event_name)s\": Thiếu %(nb_too_many)i chỗ."

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid ""
"- the ticket \"%(ticket_name)s\" (%(event_name)s): Missing %(nb_too_many)i "
"seats."
msgstr "- vé \"%(ticket_name)s\" (%(event_name)s): Thiếu %(nb_too_many)i chỗ."

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_2
msgid "10-14"
msgstr "10-14"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_formatted_event_address
msgid "1000 Brussels"
msgstr "1000 Brussels"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_3
msgid "15-18"
msgstr "15-18"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_4
msgid "18+"
msgstr "18+"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__four_per_sheet
msgid "4 per sheet"
msgstr "4 trên mỗi trang"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_1
msgid "5-10"
msgstr "5-10"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__96x134
msgid "96x134mm (Badge Printer)"
msgstr "96x134mm (Máy in thẻ)"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__96x82
msgid "96x82mm (Badge Printer)"
msgstr "96x82mm (Máy in thẻ)"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Business Room</b> - To discuss implementation methodologies, best sales "
"practices, etc."
msgstr ""
"<b>Phòng kinh doanh</b> - Để thảo luận phương thức triển khai, cách thức bán"
" hàng hiệu quả nhất, v.v."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Technical Rooms</b> - One dedicated to advanced Odoo developers, one for "
"new developers."
msgstr ""
"<b>Phòng kỹ thuật</b> - Một phòng dành cho các nhà phát triển Odoo giàu kinh"
" nghiệm, một phòng dành cho các nhà phát triển mới. "

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The Design Fair is preceded by 2 days of Training Sessions for "
"experts!</b><br> We propose 3 different training sessions, 2 days each."
msgstr ""
"<b>Trước Hội chợ thiết kế là 2 ngày tập huấn dành cho chuyên gia!</b><br> "
"Chúng tôi đề xuất 3 buổi tập huấn khác nhau, mỗi buổi kéo dài 2 ngày."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The plenary sessions in the morning will be shorter</b> and we will give "
"more time for thematical meetings, conferences, workshops and tutorial "
"sessions in the afternoon."
msgstr ""
"<b>Các phiên họp toàn thể vào buổi sáng sẽ ngắn hơn</b> và chúng tôi sẽ dành"
" nhiều thời gian hơn cho các buổi họp theo chuyên đề, hội nghị, workshop và "
"hướng dẫn vào buổi chiều. "

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The whole event is open to all public!</b> <br>We ask a participation fee"
" of 49.50€ for the costs for the 3 days (coffee breaks, catering, drinks and"
" a surprising concert and beer party).<br> For those who don't want to "
"contribute, there is a free ticket, therefore, catering and access to "
"evening events aren't included."
msgstr ""
"<b>Sự kiện mở cửa cho công chúng!</b> <br> Phí tham dự cho 3 ngày là 49.50€ "
"(bao gồm cà phê, ăn nhẹ, đồ uống, một bữa tiệc âm nhạc và bia đặc biệt). "
"<br> Với những người tham gia không muốn đóng phí, sự kiện có cung cấp vé "
"miễn phí, tuy nhiên không bao gồm đồ ăn và sự kiện buổi tối."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<b>Workshop Room</b> - Mainly for developers."
msgstr "<b>Phòng workshop</b> - Chủ yếu cho nhà phát triển."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid ""
"<br/>\n"
"                                            <span class=\"me-1\">from</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"me-1\">từ</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid ""
"<br/>\n"
"                                            <span class=\"me-1\">to</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"me-1\">đến</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                        <span class=\"text-muted\">Air your tracks online through a Youtube integration</span>"
msgstr ""
"<br/>\n"
"                                        <span class=\"text-muted\">Phát sóng online các phần nội dung của bạn với tích hợp Youtube</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                        <span class=\"text-muted\">Share a quiz to your attendees once a track is over</span>"
msgstr ""
"<br/>\n"
"                                        <span class=\"text-muted\">Chia sẻ quiz với người tham dự khi phần nội dung kết thúc</span>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<em>If you wish to make a presentation, please send your topic proposal as "
"soon as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany "
"(dot) com. The presentations should be, for example, a presentation of a "
"community module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English.</em>"
msgstr ""
"<em>Nếu bạn muốn trình bày, vui lòng gửi đề xuất chủ đề trong thời gian sớm "
"nhất tới Mr. Famke Jenssen tại địa chỉ ngh (a) yourcompany (dot) com để được"
" duyệt. Bài phát biểu có thể là bài thuyết trình về phân hệ cộng đồng, một "
"trường hợp thành công, phản hồi về phương pháp, kỹ thuật, v.v. Các bài "
"thuyết trình bài được thực hiện bằng tiếng Anh.</em>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<i class=\"fa fa-building\" title=\"Attendee Company\"/>"
msgstr "<i class=\"fa fa-building\" title=\"Công ty của người tham dự\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Confirm Attendance "
"Button\" title=\"Confirm Attendance\"/>"
msgstr ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Nút Xác nhận tham "
"gia\" title=\"Xác nhận tham gia\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Reset To Registered"
" Button\" title=\"Reset To Registered\"/>"
msgstr ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Nút Đặt lại là Đã "
"đăng ký\" title=\"Đặt lại là Đã đăng ký\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-group mt-1 me-2 text-center\" title=\"Attendees\"/>"
msgstr "<i class=\"fa fa-group mt-1 me-2 text-center\" title=\"Người tham dự\"/>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i class=\"fa fa-info-circle me-2\"></i>This event and all the conferences "
"are in <b>English</b>!"
msgstr ""
"<i class=\"fa fa-info-circle me-2\"></i>Sự kiện này và tất cả các buổi hội "
"thảo diễn ra bằng <b>tiếng Anh</b>!"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Biểu tượng mũi tên\" "
"title=\"Mũi tên\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-map-marker mt-1 me-2 text-center ps-1\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-map-marker mt-1 me-2 text-center ps-1\" title=\"Vị trí\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<i class=\"fa fa-ticket\" title=\"Ticket type\"/>"
msgstr "<i class=\"fa fa-ticket\" title=\"Loại vé\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"oi oi-arrow-right me-2\" title=\"End date\"/>"
msgstr "<i class=\"oi oi-arrow-right me-2\" title=\"Ngày kết thúc\"/>"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-"
"end\">SPEAKER</span>"
msgstr ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-end\">DIỄN "
"GIẢ</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "<span class=\"me-1\">to</span>"
msgstr "<span class=\"me-1\">đến</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
msgid "<span class=\"o_event_badge_font_faded\">My Placeholder Company</span>"
msgstr "<span class=\"o_event_badge_font_faded\">Công ty giữ chỗ của tôi</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Registration\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Đăng ký\n"
"                                </span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "<span>John Doe</span>"
msgstr "<span>John Doe</span>"

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?badge_mode=1&amp;registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                        Download Badges\n"
"                    </a>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        Please find attached your badge for\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>From</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 4, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 AM</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>To</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 6, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">5:00 PM</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">See location on Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Đăng ký của bạn</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?badge_mode=1&amp;registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                        Tải xuống thẻ\n"
"                    </a>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Xin chào <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        Tệp đính kèm là thẻ của bạn dành cho sự kiện\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</strong>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Hãy thêm sự kiện này vào lịch của bạn</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        Hẹn sớm gặp bạn!<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Đội ngũ <t t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>Từ</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">ngày 4/5/2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">lúc 7:00 sáng</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>Đến</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">ngày 6/5/2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">lúc 5:00 chiều</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Châu Âu/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Bạn có câu hỏi về sự kiện?</span>\n"
"                            <div>Hãy liên hệ với ban tổ chức:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Email: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Điện thoại: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Để có trải nghiệm di động tốt nhất</strong>\n"
"                            <a href=\"/event\">hãy cài ứng dụng trên thiết bị di động của chúng tôi.</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">Xem vị trí trên Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Được gửi bởi <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Khám phá <a href=\"/event\" style=\"color:#875A7B;\">tất cả sự kiện của chúng tôi</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or 'Guest'\"/>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Tickets\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\">\n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        We are excited to remind you that the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        is starting <strong t-out=\"object.event_date_range or ''\">today</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>From</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 4, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 AM</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>To</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 6, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">5:00 PM</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"/></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone or ''\"/></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Get the best mobile experience.</strong>\n"
"                        <a href=\"/event\">Install our mobile app</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address and location\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"/>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-attf-src=\"{{ event_address.static_map_url }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                <span t-else=\"\">See location on Google Maps</span>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br/>\n"
"            Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Đăng ký của bạn</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or 'Guest'\"/>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            Xem vé\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\">\n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Xin chào <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        Chúng tôi rất hân hạnh gửi nhắc nhở cho bạn về sự kiện\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"font-weight:bold;color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</strong>\n"
"                        </t>\n"
"                        sắp bắt đầu vào <strong t-out=\"object.event_date_range or ''\">hôm nay</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Hãy thêm sự kiện này vào lịch của bạn</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        Chúng tôi đã xác nhận đăng ký của bạn và rất mong được gặp bạn tại sự kiện!<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Đội ngũ <t t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>Từ</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">Ngày 4/5/2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">lúc 7:00 sáng</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>To</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">Ngày 6/5/2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">lúc 5:00 chiều</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><t t-out=\"object.event_id.date_tz or ''\">Châu Âu/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Bạn có câu hỏi về sự kiện?</span>\n"
"                            <div>Hãy liên hệ với ban tổ chức:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Email: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"/></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Điện thoại: <t t-out=\"event_organizer.phone or ''\"/></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Để có trải nghiệm di động tốt nhất,</strong>\n"
"                        <a href=\"/event\">hãy cài ứng dụng trên thiết bị di động của chúng tôi.</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address and location\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"/>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-attf-src=\"{{ event_address.static_map_url }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                <span t-else=\"\">Xem vị trí trên Google Maps</span>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Được gửi bởi <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br/>\n"
"            Khám phá <a href=\"/event\" style=\"color:#875A7B;\">tất cả sự kiện của chúng tôi</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Tickets\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\"> \n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        We are happy to confirm your registration to the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;font-weight:bold;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>.\n"
"                        <t t-if=\"object.partner_id and object.partner_id.name and object.partner_id.name != object.name\">\n"
"                            This ticket was registered by <t t-out=\"object.partner_id.name\"/>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div t-if=\"is_sale\">\n"
"                        <br/>\n"
"                        The order for this ticket has reference <t t-out=\"object.sale_order_id.name\"/>\n"
"                        and was placed on <t t-out=\"object.sale_order_id.date_order.date()\"/>\n"
"                        <t t-if=\"object.sale_order_line_id.price_unit\"> for an amount of\n"
"                            <t t-out=\"object.sale_order_line_id.price_unit\" t-options=\"{'widget': 'monetary', 'display_currency': object.sale_order_line_id.currency_id}\"/>\n"
"                        </t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>From</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 4, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">7:00 AM</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>To</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">May 6, 2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">5:00 PM</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">See location on Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<t t-set=\"registration_ids\" t-value=\"object.ids if not is_sale else object._get_event_registration_ids_from_order()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Đăng ký của bạn</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"/>\n"
"                    </span>\n"
"                    <div style=\"margin-bottom: 5px;margin-top: 18px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            Xem vé\n"
"                        </a>\n"
"                    </div>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"object.barcode\"> \n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\"/>\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Xin chào <t t-out=\"object.name or 'Guest'\"/>,<br/><br/>\n"
"                        Chúng tôi rất hân hạnh xác nhận bạn đã đăng ký sự kiện\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;font-weight:bold;\" t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</strong>\n"
"                        </t>.\n"
"                        <t t-if=\"object.partner_id and object.partner_id.name and object.partner_id.name != object.name\">\n"
"                            Vé này đã được đăng ký bởi <t t-out=\"object.partner_id.name\"/>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div t-if=\"is_sale\">\n"
"                        <br/>\n"
"                        Đơn hàng mua vé này có mã tham chiếu <t t-out=\"object.sale_order_id.name\"/>\n"
"                        và được đặt vào <t t-out=\"object.sale_order_id.date_order.date()\"/>\n"
"                        <t t-if=\"object.sale_order_line_id.price_unit\"> với số tiền là\n"
"                            <t t-out=\"object.sale_order_line_id.price_unit\" t-options=\"{'widget': 'monetary', 'display_currency': object.sale_order_line_id.currency_id}\"/>\n"
"                        </t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Hãy thêm sự kiện này vào lịch của bạn</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        Hẹn sớm gặp bạn!<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Đội ngũ <t t-out=\"object.event_id.name or ''\">Ra mắt online bộ sưu tập OpenWood</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div>\n"
"                                    <strong>Từ</strong>\n"
"                                    <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">ngày 4/5/2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_begin\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">lúc 7:00 sáng</t>\n"
"                                </div>\n"
"                                <div>\n"
"                                    <strong>Đến</strong>\n"
"                                    <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}\">ngày 6/5/2021</t>\n"
"                                     - <t t-out=\"object.event_id.date_end\" t-options=\"{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}\">lúc 5:00 chiều</t>\n"
"                                </div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Châu Âu/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Bạn có câu hỏi về sự kiện này?</span>\n"
"                            <div>Hãy liên hệ với ban tổ chức:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Email: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Điện thoại: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Để có trải nghiệm di động tốt nhất,</strong>\n"
"                            <a href=\"/event\">hãy cài ứng dụng trên thiết bị di động của chúng tôi.</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"/>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                    <t t-else=\"\">Xem vị trí trên Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Được gửi bởi <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Khám phá <a href=\"/event\" style=\"color:#875A7B;\">tất cả sự kiện của chúng tôi</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__description
#: model:ir.model.fields,help:event.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "Nội dung mô tả vé mà bạn muốn truyền tải tới khách hàng."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__a4_french_fold
msgid "A4 foldable"
msgstr "Trang A4 có thể gấp lại"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__a6
msgid "A6"
msgstr "A6"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
#: model:ir.model.fields,field_description:event.field_event_registration__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_ids
#: model:ir.model.fields,field_description:event.field_event_registration__activity_ids
msgid "Activities"
msgstr "Hoạt động"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_2
msgid "Activity"
msgstr "Hoạt động"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hoạt động ngoại lệ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_state
#: model:ir.model.fields,field_description:event.field_event_registration__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Add a description..."
msgstr "Thêm mô tả..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""
"Thêm menu điều hướng vào các trang sự kiện của bạn với lịch trình, phần nội "
"dung, đề xuất nội dung,..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Add some internal notes (to do lists, contact info, ...)"
msgstr ""
"Thêm một số ghi chú nội bộ (danh sách việc cần làm, thông tin liên hệ,...)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_search
msgid "Address"
msgstr "Địa chỉ"

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Administrator"
msgstr "Quản trị viên"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_exhibitor
msgid "Advanced Sponsors"
msgstr "Nhà tài trợ cấp cao"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_sub
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_sub
msgid "After each registration"
msgstr "Sau mỗi đăng ký"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_event
msgid "After the event"
msgstr "Sau sự kiện"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_1
msgid "Age"
msgstr "Tuổi"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__lang
msgid ""
"All the communication emails sent to attendees will be translated in this "
"language."
msgstr ""
"Tất cả các email liên lạc được gửi tới người tham dự sẽ được dịch sang ngôn "
"ngữ này."

#. module: event
#: model:event.event,name:event.event_6
msgid "An unpublished event"
msgstr "Sự kiện chưa đăng"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"And this time, we go fully ONLINE! Meet us in our live streams from the comfort of your house.<br>\n"
"        Special discount codes will be handed out during the various streams, make sure to be there on time."
msgstr ""
"Lần này, chúng tôi sẽ phát hoàn toàn online! Hãy đón xem buổi live stream của chúng tôi ngay tại nhà.<br>\n"
"        Mã giảm giá đặc biệt sẽ được phát trong các buổi phát sóng khác nhau, hãy nhớ đến đúng giờ."

#. module: event
#: model:event.stage,name:event.event_stage_announced
msgid "Announced"
msgstr "Đã thông báo"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question_answer__name
msgid "Answer"
msgstr "Trả lời"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Answer Breakdown"
msgstr "Phân tích câu trả lời"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:event.event_question_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Answers"
msgstr "Câu trả lời"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Apply change."
msgstr "Áp dụng thay đổi."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_launched
msgid "Are sales launched"
msgstr "Đã bắt đầu bán"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"Around one hundred ballons will simultaneously take flight and turn the sky "
"into a beautiful canvas of colours."
msgstr ""
"Khoảng 100 quả bóng bay sẽ đồng loạt bay lên và biến bầu trời thành bức "
"tranh đầy màu sắc."

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "As a team, we are happy to contribute to this event."
msgstr ""
"Với tư cách là một đội nhóm, chúng tôi rất vui khi được đóng góp vào sự kiện"
" này."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__once_per_order
msgid "Ask once per order"
msgstr "Hỏi một lần cho mỗi đơn hàng"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"Chỉ ở tuổi 13, John DOE đã bắt đầu viết ứng dụng quản lý kinh doanh đầu tiên"
" cho khách hàng. Sau khi học xong chương trình kỹ sư dân dụng, anh thành lập"
" TinyERP. Đây chính là bước đầu tiên của OpenERP sau này trở thành Odoo, "
"phần mềm kinh doanh nguồn mở được cài đặt nhiều nhất trên thế giới.  "

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "Chấm công"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__done
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "Đã tham dự"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "Ngày đã tham dự"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "Tham dự"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "Câu trả lời của người tham dự "

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_attendee_list
#: model:ir.actions.report,name:event.action_report_event_registration_attendee_list
msgid "Attendee List"
msgstr "Danh sách người tham dự"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
msgid "Attendee Name"
msgstr "Tên Người tham dự"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__registration_answer_choice_ids
msgid "Attendee Selection Answers"
msgstr "Câu trả lời lựa chọn của người tham dự "

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Attendee list"
msgstr "Danh sách người tham dự"

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.actions.act_window,name:event.event_registration_action
#: model:ir.actions.act_window,name:event.event_registration_action_kanban
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr "Người Tham dự"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Auto-Print"
msgstr "In tự động"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_available
msgid "Available Seats"
msgstr "Số chỗ còn trống"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_badge
msgid "Badge"
msgstr "Thẻ"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_badge_96x134
msgid "Badge (96x134mm)"
msgstr "Thẻ (96x134mm)"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_badge_96x82
msgid "Badge (96x82mm)"
msgstr "Thẻ (96x82mm)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_image
msgid "Badge Background"
msgstr "Nền thẻ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_format
msgid "Badge Dimension"
msgstr "Kích thước thẻ"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_badge
msgid "Badge Example"
msgstr "Ví dụ thẻ"

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
msgid "Badges"
msgstr "Thẻ"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"Bands like Bar Fighters, Led Slippers and Link Floyd will offer you the show"
" of the century during our three day event."
msgstr ""
"Tại sự kiện 3 ngày của chúng tôi, các ban nhạc Bar Fighters, Led Slippers và"
" Link Floyd sẽ mang lại cho bạn buổi biểu diễn của thế kỷ."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__barcode
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "Barcode"
msgstr "Mã vạch"

#. module: event
#: model:ir.actions.client,name:event.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr "Giao diện mã vạch"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Danh pháp mã vạch"

#. module: event
#: model:ir.model.constraint,message:event.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr "Mã vạch phải là duy nhất"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__before_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__before_event
msgid "Before the event"
msgstr "Trước sự kiện"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_blocked:event.event_stage_announced
#: model:event.stage,legend_blocked:event.event_stage_booked
#: model:event.stage,legend_blocked:event.event_stage_cancelled
#: model:event.stage,legend_blocked:event.event_stage_done
#: model:event.stage,legend_blocked:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__blocked
msgid "Blocked"
msgstr "Đã bị chặn"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_1
msgid ""
"Bloem brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"Bloem mang lại sự trung thực và nghiêm túc tới ngành gỗ đồng thời giúp khách"
" hàng xử lý các loại cây, hoa và nấm."

#. module: event
#: model:event.stage,name:event.event_stage_booked
msgid "Booked"
msgstr "Đã đặt"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "Đã đặt bởi"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_booth
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Booth Management"
msgstr "Quản lý gian hàng"

#. module: event
#: model:event.event,subtitle:event.event_5
msgid ""
"Bring your outdoor field hockey season to the next level by taking the field"
" at this 9th annual Field Hockey tournament."
msgstr ""
"Hãy tham dự giải đấu khúc côn cầu trên sân thường niên lần thứ 9 để đưa mùa "
"giải khúc côn cầu ngoài trời của bạn lên một tầm cao."

#. module: event
#: model:event.event,name:event.event_4
msgid "Business workshops"
msgstr "Workshop kinh doanh"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Campaign"
msgstr "Chiến dịch"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel"
msgstr "Hủy"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Cancel Registration"
msgstr "Huỷ Đăng ký"

#. module: event
#: model:event.stage,name:event.event_stage_cancelled
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__cancel
msgid "Cancelled"
msgstr "Đã hủy"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Cancelled registration"
msgstr "Đăng ký đã bị hủy"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_id
msgid "Category"
msgstr "Danh mục"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_sequence
msgid "Category Sequence"
msgstr "Trình tự danh mục"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"Chamber Works reserves the right to cancel, re-name or re-locate the event "
"or change the dates on which it is held."
msgstr ""
"Chamber Works có quyền hủy bỏ, đổi tên hoặc thay đổi địa điểm của sự kiện "
"hoặc thay đổi ngày tổ chức sự kiện."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_type_id
msgid ""
"Choose a template to auto-fill tickets, communications, descriptions and "
"other fields."
msgstr ""
"Chọn một mẫu để tự động điền vé, thông điệp, mô tả và các trường khác."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__color
msgid "Color"
msgstr "Màu sắc"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__color
msgid "Color Index"
msgstr "Mã màu"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_registration_report
msgid "Come back once you have registrations to overview answers."
msgstr "Quay lại sau khi bạn đã đăng ký để xem câu trả lời tổng quan."

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Come see us live, we hope to meet you!"
msgstr "Hãy đón xem trực tiếp. Chúng tôi rất mong gặp các bạn!"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "Thông tin trao đổi"

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__mail_registration_ids
msgid "Communication related to event registrations"
msgstr "Thông tin liên quan đến đăng ký sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Community Chat Rooms"
msgstr "Phòng chat cộng đồng"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__company_name
#: model_terms:event.event,description:event.event_2
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Company"
msgstr "Công ty"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Company Logo"
msgstr "Logo công ty"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__company_name
msgid "Company Name"
msgstr "Tên công ty"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Compose Email"
msgstr "Soạn email"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_2
msgid "Conference"
msgstr "Hội thảo"

#. module: event
#: model:event.event,name:event.event_2
#: model_terms:event.event,description:event.event_2
msgid "Conference for Architects"
msgstr "Hội thảo cho kiến trúc sư"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr "Hội nghị, workshop và tập huấn sẽ được tổ chức tại 6 phòng:"

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Attendance"
msgstr "Xác nhận tham dự"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Attendance Button"
msgstr "Nút Xác nhận tham dự"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Confirm attendance?"
msgstr "Xác nhận tham dự?"

#. module: event
#: model:ir.model,name:event.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Continue"
msgstr "Tiếp tục"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "Quốc gia"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Create Booths and manage their reservations"
msgstr "Tạo gian hàng và quản lý đặt chỗ"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Create an Event"
msgstr "Tạo sự kiện"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid "Create an Event Stage"
msgstr "Tạo giai đoạn sự kiện"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Create an Event Tag Category"
msgstr "Tạo danh mục thẻ sự kiện"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid "Create an Event Template"
msgstr "Tạo mẫu sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_question__create_uid
#: model:ir.model.fields,field_description:event.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration_answer__create_uid
#: model:ir.model.fields,field_description:event.field_event_stage__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_question__create_date
#: model:ir.model.fields,field_description:event.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration_answer__create_date
#: model:ir.model.fields,field_description:event.field_event_stage__create_date
#: model:ir.model.fields,field_description:event.field_event_tag__create_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_1
msgid "Culture"
msgstr "Văn hóa"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Customer"
msgstr "Khách hàng"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Customer Email"
msgstr "Email khách hàng"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Date"
msgstr "Ngày"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_date_range
msgid "Date Range"
msgstr "Phạm vi ngày"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__days
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__days
msgid "Days"
msgstr "Ngày"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,help:event.field_event_type_ticket__seats_max
msgid ""
"Define the number of available tickets. If you have too many registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""
"Xác định số lượng vé sẽ cung cấp. Nếu bạn có quá nhiều lượt đăng ký, bạn sẽ "
"không thể tiếp tục bán vé. Đặt là 0 để bỏ qua quy tắc này và không giới hạn "
"số lượng."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
#: model:ir.model.fields,field_description:event.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event.field_event_type_ticket__description
msgid "Description"
msgstr "Mô tả"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr "Hội chợ thiết kế Los Angeles"

#. module: event
#: model:event.event,subtitle:event.event_4
msgid "Discover how to grow a sustainable business with our experts."
msgstr "Khám phá cách phát triển doanh nghiệp bền vững cùng các chuyên gia."

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Discover more"
msgstr "Khám phá thêm"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_meet
msgid "Discussion Rooms"
msgstr "Phòng thảo luận"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_question__display_name
#: model:ir.model.fields,field_description:event.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration_answer__display_name
#: model:ir.model.fields,field_description:event.field_event_stage__display_name
#: model:ir.model.fields,field_description:event.field_event_tag__display_name
#: model:ir.model.fields,field_description:event.field_event_tag_category__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Display Sponsors and Exhibitors on your event pages"
msgstr "Trưng bày Nhà tài trợ và Đơn vị triển lãm trong trang sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
msgid "Display Timezone"
msgstr "Hiển thị múi giờ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "Trình tự hiển thị"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__done
msgid "Done"
msgstr "Hoàn tất"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""
"Trong cuộc hội thảo này, chúng tôi sẽ cung cấp tổng quan chi tiết về các ứng"
" dụng doanh nghiệp. Bạn sẽ biết toàn bộ các lợi ích khi sử dụng các ứng dụng"
" này."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Edit"
msgstr "Chỉnh sửa"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: model:ir.model.fields,field_description:event.field_event_registration__email
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__email
msgid "Email"
msgstr "Email"

#. module: event
#: model:ir.model,name:event.model_mail_template
msgid "Email Templates"
msgstr "Mẫu email"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Enable barcode scanning"
msgstr "Bật quét mã vạch"

#. module: event
#: model:ir.model.fields,help:event.field_res_config_settings__use_event_barcode
msgid "Enable or Disable Event Barcode functionality."
msgstr "Bật hoặc tắt chức năng mã vạch sự kiện."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr "Ngày kết thúc "

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__pipe_end
msgid "End Stage"
msgstr "Kết thúc giai đoạn"

#. module: event
#: model:event.stage,name:event.event_stage_done
msgid "Ended"
msgstr "Kết thúc"

#. module: event
#: model:event.event,subtitle:event.event_2
msgid "Enhance your architectural business and improve professional skills."
msgstr ""
"Thúc đẩy hoạt động kinh doanh kiến trúc và nâng cao kỹ năng nghề nghiệp cho "
"bạn."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_id
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_question__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr "Sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr "Sự kiện gửi thư tự động"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__event_type_id
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Event Category"
msgstr "Nhóm Sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_form
msgid "Event Category Tag"
msgstr "Thẻ danh mục sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "Ngày Kết thúc Sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Event Gamification"
msgstr "Game hóa sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "Thông tin Sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr "Trình ấn định Gửi thư Sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr "Trình ấn định Gửi thư Sự kiện"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/template_reference_field/field_event_mail_template_reference.js:0
msgid "Event Mail Template Reference"
msgstr "Tham chiếu mẫu email sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr "Tên Sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr "Nhà tổ chức Sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_organizer_id
msgid "Event Organizer"
msgstr "Ban tổ chức sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_question
msgid "Event Question"
msgstr "Câu hỏi sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_question_answer
msgid "Event Question Answer"
msgstr "Trả lời câu hỏi sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr "Đăng ký sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "Trả lời đăng ký sự kiện "

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
msgid "Event Registrations"
msgstr "Đăng ký Sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_user_id
msgid "Event Responsible"
msgstr "Người phụ trách sự kiện"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
msgid ""
"Event Scheduler for:\n"
"  - Event: %(event_name)s (%(event_id)s)\n"
"  - Scheduled: %(date)s\n"
"  - Template: %(template_name)s (%(template_id)s)\n"
"\n"
"Failed with error:\n"
"  - %(error)s\n"
"\n"
"You receive this email because you are:\n"
"  - the organizer of the event,\n"
"  - or the responsible of the event,\n"
"  - or the last writer of the template.\n"
msgstr ""
"Trình lập lịch sự kiện cho:\n"
"- Sự kiện: %(event_name)s (%(event_id)s)\n"
"- Đã lên lịch: %(date)s\n"
"- Mẫu: %(template_name)s (%(template_id)s)\n"
"\n"
"Không thành công với lỗi:\n"
"- %(error)s\n"
"\n"
"Bạn nhận được email này vì bạn là:\n"
"- người tổ chức sự kiện,\n"
"- hoặc người chịu trách nhiệm sự kiện,\n"
"- hoặc người viết cuối cùng của mẫu.\n"

#. module: event
#: model:ir.model,name:event.model_event_stage
msgid "Event Stage"
msgstr "Giai đoạn sự kiện"

#. module: event
#: model:ir.actions.act_window,name:event.event_stage_action
#: model:ir.ui.menu,name:event.event_stage_menu
msgid "Event Stages"
msgstr "Giai đoạn sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr "Ngày Bắt đầu Sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_tag
msgid "Event Tag"
msgstr "Thẻ sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_tag_category
msgid "Event Tag Category"
msgstr "Danh mục thẻ sự kiện"

#. module: event
#: model:ir.actions.act_window,name:event.event_tag_category_action_tree
#: model:ir.ui.menu,name:event.menu_event_category
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_tree
msgid "Event Tags Categories"
msgstr "Danh mục thẻ sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Template"
msgstr "Mẫu sự kiện"

#. module: event
#: model:ir.model,name:event.model_event_type_ticket
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_form_from_type
msgid "Event Template Ticket"
msgstr "Mẫu sự kiện Vé"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_tree_from_type
msgid "Event Template Tickets"
msgstr "Mẫu sự kiện Vé"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Templates"
msgstr "Mẫu sự kiện"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid ""
"Event Templates combine configurations you use often and are\n"
"                usually based on the types of events you organize (e.g. \"Workshop\",\n"
"                \"Roadshow\", \"Online Webinar\", etc)."
msgstr ""
"Mẫu sự kiện tổng hợp các cấu hình bạn thường dùng và thường\n"
"                    dựa vào loại hình sự kiện bạn tổ chức (VD: \"Workshop\",\n"
"                    \"Roadshow\", \"Webinar\",...)."

#. module: event
#: model:ir.model,name:event.model_event_event_ticket
#: model:ir.model.fields,field_description:event.field_event_event__event_ticket_ids
msgid "Event Ticket"
msgstr "Vé Sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "Kiểu Sự kiện"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_tree
msgid "Event registrations"
msgstr "Đăng ký sự kiện"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid ""
"Event stages are used to track the progress of an Event from its origin "
"until its conclusion."
msgstr ""
"Giai đoạn của sự kiện được sử dụng để theo dõi tiến độ của một sự kiện từ "
"khi bắt đầu cho đến khi kết thúc."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "Event's Ticket"
msgstr "Vé của Sự kiện"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
msgid "Event: Mail Scheduler"
msgstr "Sự kiện: Trình lập lịch biểu"

#. module: event
#: model:mail.template,name:event.event_registration_mail_template_badge
msgid "Event: Registration Badge"
msgstr "Sự kiện: Thẻ đăng ký"

#. module: event
#: model:mail.template,name:event.event_subscription
msgid "Event: Registration Confirmation"
msgstr "Sự kiện: Xác nhận đăng ký"

#. module: event
#: model:mail.template,name:event.event_reminder
msgid "Event: Reminder"
msgstr "Sự kiện: Nhắc nhở"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "Sự kiện"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr "Trình ấn định Gửi thư Sự kiện"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_tree
msgid "Events Stage"
msgstr "Giai đoạn sự kiện"

#. module: event
#: model:ir.model.fields,help:event.field_event_stage__pipe_end
msgid ""
"Events will automatically be moved into this stage when they are finished. "
"The event moved into this stage will automatically be set as green."
msgstr ""
"Sự kiện sẽ được chuyển tự động sang giai đoạn này khi kết thúc. Sự kiện được"
" chuyển sang giai đoạn này sẽ được đặt tự động thành màu xanh."

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"            This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"Hằng năm, chúng tôi mời cộng đồng, đối tác, người dùng cuối cùng tới giao lưu với chúng tôi! Đây là sự kiện lý tưởng để gặp gỡ và giới thiệu những tính năng mới, định hướng các phiên bản trong tương lai, điểm lại thành tựu của phần mềm với các buổi workshop, tập huấn,...\n"
"            Sự kiện này cũng là cơ hội để vinh danh thành tựu, phương pháp và sự phát triển của các đối tác. Hãy tới tham dự và tận mắt chứng kiến các tính năng thú vị của phiên bản mới!"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc...."
msgstr ""
"Hằng năm, chúng tôi mời cộng đồng, đối tác và người dùng tới gặp mặt! Đây là"
" một sự kiện lý tưởng để hội tụ và trình bày các tính năng mới, kế hoạch cho"
" các phiên bản tiếp theo, thành tựu của phần mềm, workshop, tập huấn, v.v."

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Exhibition"
msgstr "Triển lãm"

#. module: event
#: model:event.event,subtitle:event.event_3
msgid "Experience live music, local food and beverages."
msgstr "Trải nghiệm nhạc sống, đồ ăn và thức uống địa phương."

#. module: event
#: model:event.stage,description:event.event_stage_done
msgid ""
"Finished events. Odoo will automatically move them to this stage once their "
"end date has passed."
msgstr ""
"Sự kiện đã kết thúc. Odoo sẽ tự động chuyển chúng đến giai đoạn này sau khi "
"qua ngày kết thúc."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__fold
msgid "Folded in Kanban"
msgstr "Thu gọn trong kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_type_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng, ví dụ: fa-tasks"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
#: model_terms:event.event,description:event.event_7
msgid "For any additional information, please contact us at"
msgstr "Để biết thêm thông tin, vui lòng liên hệ tại"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""
"Đối với từng sự kiện, bạn có thể định nghĩa số chỗ ngồi đăng ký tối đa (số "
"người tham dự), vượt quá số lượng này thì sẽ không được phép đăng ký."

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_1
msgid "For only 10, you gain access to catering. Yum yum."
msgstr "Chỉ cần 10 đô cho một bữa ăn tuyệt ngon."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Foster interactions between attendees by creating virtual conference rooms"
msgstr ""
"Thúc đẩy tương tác giữa những người tham dự bằng cách tạo phòng hội nghị ảo"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_0
msgid "Free"
msgstr "Miễn phí"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_0
msgid "Free entrance, no food!"
msgstr "Vào cửa miễn phí, không bao gồm đồ ăn!"

#. module: event
#: model:event.stage,description:event.event_stage_new
msgid "Freshly created"
msgstr "Vừa mới tạo"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid ""
"From this dashboard you can report, analyze and detect trends regarding your"
" event registrations."
msgstr ""
"Từ trang chủ này, bạn có thể báo cáo, phân tích, phát hiện xu hướng của đăng"
" ký sự kiện. "

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_full_page_ticket
msgid "Full Page Ticket"
msgstr "Vé toàn trang"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_full_page_ticket
msgid "Full Page Ticket Example"
msgstr "Ví dụ về vé toàn trang"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr "Dòng hoạt động của các ứng dụng chính;"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Future Activities"
msgstr "Hoạt động trong tương lai"

#. module: event
#: model:event.event.ticket,name:event.event_4_ticket_0
msgid "General Admission"
msgstr "Vé thường"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__general_question_ids
msgid "General Questions"
msgstr "Các Câu hỏi Tổng quát"

#. module: event
#: model:event.event,subtitle:event.event_0
msgid "Get Inspired • Stay Connected • Have Fun"
msgstr "Cảm hứng • Kết nối • Vui nhộn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_state
msgid "Global communication Status"
msgstr "Trạng thái liên lạc tổng thể"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Google Maps"
msgstr "Google Maps"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__google_maps_static_api_key
msgid "Google Maps API key"
msgstr "Khóa API của Google Maps"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__google_maps_static_api_secret
msgid "Google Maps API secret"
msgstr "Bí mật API của Google Maps"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__use_google_maps_static_api
msgid "Google Maps static API"
msgstr "Static API của Google Maps "

#. module: event
#: model:event.event,name:event.event_1
msgid "Great Reno Ballon Race"
msgstr "Cuộc đua khinh khí cầu Great Reno"

#. module: event
#: model_terms:web_tour.tour,rainbow_man_message:event.event_tour
msgid "Great! Now all you have to do is wait for your attendees to show up!"
msgstr "Tuyệt vời! Giờ bạn chỉ cần chờ mọi người tới tham dự!"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_done
msgid "Green Kanban Label"
msgstr "Nhãn kanban xanh lá"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Nhãn kanban xám"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Guest #"
msgstr "Khách #"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Happy to be Sponsor"
msgstr "Hân hạnh là nhà tài trợ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__has_message
#: model:ir.model.fields,field_description:event.field_event_registration__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr "Tham dự xong hội thảo này, người tham dự sẽ có thể:"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Here it is, the 12th edition of our Live Musical Festival!"
msgstr "Đây rồi! Lễ hội Âm nhạc Trực tiếp lần thứ 12 của chúng tôi!"

#. module: event
#: model:event.event,name:event.event_5
msgid "Hockey Tournament"
msgstr "Giải khúc côn cầu"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Home"
msgstr "Trang chủ"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__hours
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__hours
msgid "Hours"
msgstr "Giờ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (1)"
msgstr "Cách gập (1)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (2)"
msgstr "Cách gập (2)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (3)"
msgstr "Cách gập (3)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (4)"
msgstr "Cách gập (4)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_question__id
#: model:ir.model.fields,field_description:event.field_event_question_answer__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__id
#: model:ir.model.fields,field_description:event.field_event_stage__id
#: model:ir.model.fields,field_description:event.field_event_tag__id
#: model:ir.model.fields,field_description:event.field_event_tag_category__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__id
msgid "ID"
msgstr "ID"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/icon_selection_field/icon_selection_field.js:0
msgid "Icon Selection"
msgstr "Lựa chọn biểu tượng"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng cho thấy một hoạt động ngoại lệ."

#. module: event
#: model:ir.model.fields,help:event.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"Nếu Đúng, câu hỏi này sẽ chỉ được hỏi một lần và giá trị sẽ được nhập cho "
"mọi người tham dự. Nếu không, câu hỏi sẽ được hỏi cho mỗi người tham dự của "
"một nhóm đặt chỗ. "

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__start_sale_datetime
msgid ""
"If ticketing is used, contains the earliest starting sale date of tickets."
msgstr "Nếu sử dụng tính năng bán vé, cung cấp ngày bắt đầu bán vé sớm nhất."

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "If you don't have this ticket, you will <b>not</b> be allowed entry!"
msgstr "Bạn sẽ <b>không thể</b> vào cửa nếu bạn không có vé này."

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"If you don't know anything about Hockey, this is a great introduction to this wonderful sport as you will will be able to see some training process and also have some time\n"
"                to chat with experienced players and trainers once the tournament is over!"
msgstr ""
"Nếu bạn không biết chơi khúc côn cầu thì đây là chương trình giới thiệu tuyệt vời về môn thể thao thú vị này, vì bạn sẽ được xem một phần quá trình huấn luyện và\n"
"                trò chuyện với các cầu thủ và huấn luyện viên nhiều kinh nghiệm khi giải đấu kết thúc!"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__now
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__now
msgid "Immediately"
msgstr "Ngay lập tức"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Important ticket information"
msgstr "Thông tin vé quan trọng"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_normal:event.event_stage_announced
#: model:event.stage,legend_normal:event.event_stage_booked
#: model:event.stage,legend_normal:event.event_stage_cancelled
#: model:event.stage,legend_normal:event.event_stage_done
#: model:event.stage,legend_normal:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__normal
msgid "In Progress"
msgstr "Đang thực hiện"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_tz
msgid ""
"Indicates the timezone in which the event dates/times will be displayed on "
"the website."
msgstr "Chỉ ra múi giờ mà ngày/giờ sự kiện sẽ được hiển thị trên trang web."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Insert dynamic Google Maps in your email templates"
msgstr "Chèn Google Maps động vào mẫu email của bạn"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Install"
msgstr "Cài đặt"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "Khoảng thời gian"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr "Giới thiệu, CRM, Quản lý bán hàng"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
msgid "Invalid event / ticket choice"
msgstr "Lựa chọn vé/sự kiện không hợp lệ"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
msgid "Invalid ticket"
msgstr "Vé không hợp lệ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sale_available
msgid "Is Available"
msgstr "Khả dụng"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_expired
msgid "Is Expired"
msgstr "Đã hết hạn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_finished
msgid "Is Finished"
msgstr "Là hoàn thành"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_one_day
msgid "Is One Day"
msgstr "Là một ngày"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "Là đang diễn ra"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__seats_max
msgid "It will select this default maximum value when you choose this event"
msgstr "Giá trị tối đa mặc định khi bạn chọn sự kiện này"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "John DOE"
msgstr "John DOE"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "John Doe"
msgstr "John Doe"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Join us for the greatest ballon race of all times!"
msgstr "Hãy tham gia cuộc đua bóng bay lớn nhất từ trước tới nay!"

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid "Join us for this 24 hours Event"
msgstr "Hãy tham dự sự kiện 24 giờ này cùng chúng tôi"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Join us for this 3-day Event"
msgstr "Hãy tham dự sự kiện 3 ngày này với chúng tôi"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Giải thích về bị chặn ở Kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Giải thích về đang diễn ra ở Kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state
msgid "Kanban State"
msgstr "Trạng thái kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state_label
msgid "Kanban State Label"
msgstr "Nhãn trạng thái kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_done
msgid "Kanban Valid Explanation"
msgstr "Giải thích về hợp lệ ở Kanban"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Key"
msgstr "Khoá"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__lang
msgid "Language"
msgstr "Ngôn ngữ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Last 30 days"
msgstr "30 ngày qua"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__last_registration_id
msgid "Last Attendee"
msgstr "Người tham dự cuối cùng"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_question__write_uid
#: model:ir.model.fields,field_description:event.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration_answer__write_uid
#: model:ir.model.fields,field_description:event.field_event_stage__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_question__write_date
#: model:ir.model.fields,field_description:event.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration_answer__write_date
#: model:ir.model.fields,field_description:event.field_event_stage__write_date
#: model:ir.model.fields,field_description:event.field_event_tag__write_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Late Activities"
msgstr "Hoạt động chậm trễ"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Let's create your first <b>event</b>."
msgstr "Hãy tạo <b>sự kiện</b> đầu tiên."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_limited
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_limited
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_limited
msgid "Limit Attendees"
msgstr "Hạn chế số người tham dự"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Limit Registrations"
msgstr "Giới hạn đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr "Chỗ ngồi hạn chế"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Live Broadcast"
msgstr "Phát trực tiếp"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_live
msgid "Live Mode"
msgstr "Chế độ trực tiếp"

#. module: event
#: model:event.event,name:event.event_3
msgid "Live Music Festival"
msgstr "Lễ hội âm nhạc trực tiếp"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_mail__template_ref__mail_template
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__template_ref__mail_template
msgid "Mail"
msgstr "Mail"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr "Đăng ký thư"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr "Kế hoạch gửi mail"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "Kế hoạch gửi mail"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "Kế hoạch gửi mail"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Lập kế hoạch thư trên danh mục sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr "Mail đã Gửi"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage & publish a schedule with tracks"
msgstr "Quản lý & đăng lịch trình kèm theo các phần nội dung"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Mandatory"
msgstr "Bắt buộc"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr "Câu trả lời bắt buộc"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Mark as Attending"
msgstr "Đánh dấu là đang tham dự"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Marketing"
msgstr "Marketing"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
msgid "Maximum"
msgstr "Tối đa"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_max
msgid "Maximum Attendees"
msgstr "Số Người tham dự tối đa"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__seats_max
msgid "Maximum Registrations"
msgstr "Đăng ký tối đa"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Maximum Seats"
msgstr "Số chỗ tối đa"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_medium_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Medium"
msgstr "Phương tiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__months
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__months
msgid "Months"
msgstr "Tháng"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_2
msgid "Music"
msgstr "Âm nhạc"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__my_activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Thời hạn hoạt động của tôi"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "Sự kiện của tôi"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: model:ir.model.fields,field_description:event.field_event_event_ticket__name
#: model:ir.model.fields,field_description:event.field_event_tag__name
#: model:ir.model.fields,field_description:event.field_event_tag_category__name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__name
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__name
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Name"
msgstr "Tên"

#. module: event
#: model:event.stage,name:event.event_stage_new
msgid "New"
msgstr "Mới"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_calendar_event_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện trên lịch cho hoạt động tiếp theo"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Thời hạn cho hoạt động tiếp theo"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_summary
#: model:ir.model.fields,field_description:event.field_event_registration__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_registration_report
msgid "No Answers yet!"
msgstr "Chưa có câu trả lời nào!"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
msgid "No Attendees expected yet!"
msgstr "Chưa có người tham dự dự kiến!"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid "No Attendees yet!"
msgstr "Chưa có người tham dự!"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Danh pháp"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__note
#: model:ir.model.fields,field_description:event.field_event_type__note
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Note"
msgstr "Ghi chú"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Notes"
msgstr "Ghi chú"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid "Nothing Scheduled yet!"
msgstr "Chưa có lịch trình!"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Now that your event is ready, click here to move it to another stage."
msgstr ""
"Sự kiện hiện đã sẵn sàng, hãy bấm vào đây để chuyển sự kiện sang giai đoạn "
"khác."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Attendees"
msgstr "Số lượng người tham dự"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
msgid "Number of Registrations"
msgstr "Số lượng đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_taken
msgid "Number of Taken Seats"
msgstr "Số lượng chỗ đã được mua"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Objectives"
msgstr "Mục tiêu"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "Odoo Community Days"
msgstr "Odoo Community Days"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Once again we assembled the most legendary bands in Rock history."
msgstr ""
"Một lần nữa chúng tôi tập hợp những ban nhạc huyền thoại nhất trong lịch sử "
"nhạc Rock."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Once per Order"
msgstr "Một lần cho mỗi lần đặt vé"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ongoing Events"
msgstr "Sự kiện đang diễn ra"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_1
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Online"
msgstr "Online"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Online Event"
msgstr "Sự kiện online"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Online Exhibitors"
msgstr "Đơn vị triển lãm online"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr "Vé online"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Online if not set"
msgstr "Online nếu chưa thiết lập"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Open date range picker.<br/>Pick a Start and End date for your event."
msgstr ""
"Mở bộ chọn phạm vi ngày. <br/>Chọn ngày bắt đầu và kết thúc cho sự kiện của "
"bạn"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications reserves the right to cancel, re-name or re-locate the"
" event or change the dates on which it is held."
msgstr ""
"OpenElec Applications có quyền hủy bỏ, đổi tên hoặc thay đổi địa điểm của sự"
" kiện hoặc thay đổi ngày tổ chức sự kiện."

#. module: event
#: model:event.event,name:event.event_7
msgid "OpenWood Collection Online Reveal"
msgstr "Ra mắt online bộ sưu tập OpenWood"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "Operation not supported."
msgstr "Thao tác không được hỗ trợ."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Organizer"
msgstr "Nhà Tổ chức"

#. module: event
#: model:event.event,subtitle:event.event_7
msgid ""
"Our newest collection will be revealed online! Interact with us on our live "
"streams!"
msgstr ""
"Bộ sưu tập mới nhất của chúng tôi sẽ được ra mắt online! Hãy giao lưu với "
"chúng tôi trên live stream!"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_blocked
#: model:ir.model.fields,help:event.field_event_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection."
msgstr ""
"Ghi đè giá trị mặc định hiển thị cho giai đoạn bị chặn cho lựa chọn kanban."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_done
#: model:ir.model.fields,help:event.field_event_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection."
msgstr ""
"Ghi đè giá trị mặc định hiển thị cho giai đoạn hoàn tất cho lựa chọn kanban."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_normal
#: model:ir.model.fields,help:event.field_event_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection."
msgstr ""
"Ghi đè giá trị mặc định hiển thị cho giai đoạn bình thường cho lựa chọn "
"kanban."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "Người tham gia"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Đối tác"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: model:ir.model.fields,field_description:event.field_event_registration__phone
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__phone
msgid "Phone"
msgstr "Điện thoại"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Phone number"
msgstr "Số điện thoại"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid ""
"Please come <b>at least</b> 30 minutes before the beginning of the event."
msgstr "Vui lòng tới sớm <b>ít nhất</b> 30 phút trước khi sự kiện bắt đầu."

#. module: event
#. odoo-python
#: code:addons/event/models/res_config_settings.py:0
msgid "Please enter a valid base64 secret"
msgstr "Vui lòng nhập bí mật base64 hợp lệ"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
msgid "Please, Scan again!"
msgstr "Vui lòng quét lại!"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr "Máy tính tiền (PoS), Giới thiệu về tuỳ biến báo cáo."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Print"
msgstr "In"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "Program"
msgstr "Chương trình"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr "Quản lý dự án, Quản lý nhân lực, Quản lý hợp đồng."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__registration_properties
msgid "Properties"
msgstr "Thuộc tính"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr "Mua hàng, Quản lý bán hàng &amp; mua hàng, Kế toán tài chính."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "QR Code"
msgstr "Mã QR"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:event.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:event.event_question_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Question"
msgstr "Câu hỏi"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__question_type
#: model:ir.model.fields,field_description:event.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "Loại câu hỏi"

#. module: event
#. odoo-python
#: code:addons/event/models/event_question.py:0
msgid "Question cannot be linked to both an Event and an Event Type."
msgstr "Không thể liên kết câu hỏi với cả Sự kiện và Loại sự kiện."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__question_ids
#: model:ir.model.fields,field_description:event.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Questions"
msgstr "Câu hỏi"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_quiz
msgid "Quiz on Tracks"
msgstr "Quiz trong phần nội dung"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__rating_ids
#: model:ir.model.fields,field_description:event.field_event_registration__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_done:event.event_stage_announced
#: model:event.stage,legend_done:event.event_stage_booked
#: model:event.stage,legend_done:event.event_stage_cancelled
#: model:event.stage,legend_done:event.event_stage_done
#: model:event.stage,legend_done:event.event_stage_new
msgid "Ready for Next Stage"
msgstr "Sẵn sàng cho bước kế tiếp"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Ready to <b>organize events</b> in a few minutes? Let's get started!"
msgstr ""
"Bạn đã sẵn sàng <b>tổ chức sự kiện</b> chỉ trong vài phút chưa? Hãy cùng bắt"
" đầu nhé!"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Nhãn kanban đỏ"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__open
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registered"
msgstr "Đã đăng ký"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#: model:ir.model.fields,field_description:event.field_event_registration_answer__registration_id
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Registration"
msgstr "Đăng ký"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr "Ngày Mở Đăng ký"

#. module: event
#: model:ir.actions.client,name:event.event_action_install_kiosk_pwa
#: model:ir.ui.menu,name:event.menu_event_registration_desk
#: model:res.groups,name:event.group_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Registration Desk"
msgstr "Bàn đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__end_sale_datetime
msgid "Registration End"
msgstr "Kết thúc đăng ký"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr "ID đăng ký"

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Đăng ký lập lịch thư"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr "Thư đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__registration_properties_definition
msgid "Registration Properties"
msgstr "Thuộc tính đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__start_sale_datetime
msgid "Registration Start"
msgstr "Bắt đầu đăng ký"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid "Registration for %s"
msgstr "Đăng ký cho %s"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr "Thư đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_open
msgid "Registration open"
msgstr "Đang mở đăng ký"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_stats_from_event
msgid "Registration statistics"
msgstr "Thống kê đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__registration_ids
msgid "Registrations"
msgstr "Đăng ký"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_open
msgid ""
"Registrations are open if:\n"
"- the event is not ended\n"
"- there are seats available on event\n"
"- the tickets are sellable (if ticketing is used)"
msgstr ""
"Đăng ký mở nếu:\n"
"- sự kiện chưa kết thúc\n"
"- vẫn còn chỗ tham dự sự kiện\n"
"- vẫn có thể bán vé (nếu sử dụng tính năng bán vé)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_started
msgid "Registrations started"
msgstr "Đăng ký đã bắt đầu"

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "Báo cáo"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_reserved
msgid "Reserved Seats"
msgstr "Chỗ đã đặt trước"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Reset To Registered"
msgstr "Đặt lại là Đã đăng ký"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Reset To Registered Button"
msgstr "Nút Đặt lại là Đã đăng ký"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Responsible"
msgstr "Người phụ trách"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_user_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_responsive_html_ticket
msgid "Responsive Html Full Page Ticket"
msgstr "Vé thích ứng toàn trang Html"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_formatted_event_address
msgid "Rue de la Paix 123"
msgstr "Rue de la Paix 123"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__running
msgid "Running"
msgstr "Đang chạy"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Scan or Tap"
msgstr "Quét hoặc chạm"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr "Lịch trình và nội dung"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Schedule Date"
msgstr "Lên lịch"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events: handle registrations, send automated "
"confirmation emails, sell tickets, etc."
msgstr ""
"Lên lịch và tổ chức các sự kiện: quản lý đăng ký, gửi email xác nhận tự "
"động, bán vé, v.v."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__scheduled
msgid "Scheduled"
msgstr "Đã lên lịch"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr "Thời gian Ấn định"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__mail_registration_ids
msgid "Scheduler Emails"
msgstr "Email trình lập kế hoạch"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid "Seasoned Hockey Fans and curious people, this tournament is for you!"
msgstr ""
"Đây là giải đấu dành cho người hâm mộ khúc côn cầu và những ai hiếu kỳ!"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Secret"
msgstr "Mã bí mật"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Select Attendee"
msgstr "Chọn người tham dự"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Select printer..."
msgstr "Chọn máy in..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "Câu trả lời được chọn"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "Lựa chọn"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr "Bán vé trên trang web của bạn"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with Point of Sale"
msgstr "Bán vé với Máy tính tiền"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr "Bán vé với đơn đặt hàng"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event.field_event_type_mail__notification_type
msgid "Send"
msgstr "Gửi"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "Gửi qua email"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_done
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__sent
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Sent"
msgstr "Đã gửi"

#. module: event
#: model:mail.template,description:event.event_reminder
msgid ""
"Sent automatically to attendees if there is a reminder defined on the event"
msgstr ""
"Tự động gửi cho người tham dự nếu có lời nhắc được xác định trong sự kiện"

#. module: event
#: model:mail.template,description:event.event_registration_mail_template_badge
msgid "Sent automatically to someone after they registered to an event"
msgstr "Tự động gửi cho ai đó sau khi họ đăng ký tham gia sự kiện"

#. module: event
#: model:mail.template,description:event.event_subscription
msgid "Sent to attendees after registering to an event"
msgstr "Gửi cho người tham dự sau khi đăng ký một sự kiện"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sequence
#: model:ir.model.fields,field_description:event.field_event_question__sequence
#: model:ir.model.fields,field_description:event.field_event_question_answer__sequence
#: model:ir.model.fields,field_description:event.field_event_stage__sequence
#: model:ir.model.fields,field_description:event.field_event_tag__sequence
#: model:ir.model.fields,field_description:event.field_event_tag_category__sequence
#: model:ir.model.fields,field_description:event.field_event_type__sequence
#: model:ir.model.fields,field_description:event.field_event_type_ticket__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "Cài đặt"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_4
msgid ""
"Shangai Pterocarpus Furniture brings honesty and seriousness to wood "
"industry while helping customers deal with trees, flowers and fungi."
msgstr ""
"Shangai Pterocarpus Furniture mang đến sự trung thực và nghiêm túc cho ngành"
" gỗ đồng thời giúp khách hàng xử lý các loại cây, hoa và nấm."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Show all records which has next action date is before today"
msgstr "Hiển thị tất cả bản ghi có ngày xử lý tiếp theo trước ngày hôm nay"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_sold_out
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_sold_out
msgid "Sold Out"
msgstr "Bán hết"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_source_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Source"
msgstr "Nguồn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "Các Câu hỏi Đặc thù"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_3
#: model:event.type,name:event.event_type_2
msgid "Sport"
msgstr "Thể thao"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__stage_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Stage"
msgstr "Giai đoạn"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Chú thích và mô tả giai đoạn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__name
msgid "Stage Name"
msgstr "Tên giai đoạn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__description
msgid "Stage description"
msgstr "Mô tả giai đoạn"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_1
#: model:event.event.ticket,name:event.event_2_ticket_1
#: model:event.event.ticket,name:event.event_3_ticket_0
#: model:event.event.ticket,name:event.event_7_ticket_1
msgid "Standard"
msgstr "Tiêu chuẩn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr "Ngày bắt đầu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__start_sale_datetime
msgid "Start sale date"
msgstr "Ngày bắt đầu bán"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__static_map_url
#: model:ir.model.fields,field_description:event.field_res_users__static_map_url
msgid "Static Map Url"
msgstr "Url Static Map"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__static_map_url_is_valid
#: model:ir.model.fields,field_description:event.field_res_users__static_map_url_is_valid
msgid "Static Map Url Is Valid"
msgstr "Url Static Map hợp lệ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Stats"
msgstr "Thống kê"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Trạng thái"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_state
#: model:ir.model.fields,help:event.field_event_registration__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Hạn chót hạn đã qua\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Cần thực hiện trong tương lai."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Successfully registered!"
msgstr "Đã đăng ký thành công!"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "Câu trả lời được gợi ý"

#. module: event
#: model:ir.model.fields,help:event.field_event_tag__color
msgid ""
"Tag color. No color means no display in kanban or front-end, to distinguish "
"internal tags from public categorization tags."
msgstr ""
"Màu thẻ. Không màu nghĩa là không hiển thị trong kanban hoặc front-end, để "
"phân biệt thẻ nội bộ với thẻ danh mục công cộng."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__tag_ids
#: model:ir.model.fields,field_description:event.field_event_tag_category__tag_ids
#: model:ir.model.fields,field_description:event.field_event_type__tag_ids
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "Tags"
msgstr "Thẻ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Taken"
msgstr "Đã được mua"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_taken
msgid "Taken Seats"
msgstr "Chỗ đã được mua"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "Nhiệm vụ đang thực hiện. Nhấp để chặn hoặc đặt là xong. "

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr "Nhiệm vụ đã chặn. Nhấp để bỏ chặn hoặc đặt là xong. "

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
#: model:ir.model.fields,field_description:event.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_ref
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Template"
msgstr "Mẫu"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "Nhập văn bản"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "Câu trả lời dạng chữ"

#. module: event
#: model:event.event,subtitle:event.event_1
msgid ""
"The Great Reno Balloon Race is the world's largest free hot-air ballooning "
"event."
msgstr ""
"Cuộc đua khinh khí cầu Great Reno là sự kiện khinh khí cầu miễn phí lớn nhất"
" thế giới."

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"The best Hockey teams of the country will compete for the national Hockey "
"trophy."
msgstr ""
"Các đội khúc côn cầu xuất sắc nhất của cả nước sẽ tranh cúp Quốc gia khúc "
"côn cầu."

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"The best aeronauts of the world will gather on this event to offer you the "
"most spectacular show."
msgstr ""
"Các phi hành gia có tên tuổi trên thế giới sẽ hội tụ trong sự kiện này để "
"cống hiến cho bạn một màn trình diễn ngoạn mục nhất."

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "The closing date cannot be earlier than the beginning date."
msgstr "Ngày kết thúc không thể sớm hơn ngày bắt đầu."

#. module: event
#: model:event.stage,description:event.event_stage_cancelled
msgid "The event has been cancelled"
msgstr "Sự kiện đã bị hủy"

#. module: event
#: model:event.stage,description:event.event_stage_announced
msgid "The event has been publicly announced"
msgstr "Sự kiện đã được thông báo công khai"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_sold_out
msgid ""
"The event is sold out if no more seats are available on event. If ticketing "
"is used and all tickets are sold out, the event will be sold out."
msgstr ""
"Sự kiện là hết chỗ nếu không còn chỗ cho sự kiện. Nếu sử dụng tính năng bán "
"vé và tất cả vé đã bán hết, sự kiện cũng được coi là hết chỗ."

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"The finest OpenWood furnitures are coming to your house in a brand new "
"collection"
msgstr ""
"Các sản phẩm nội thất tinh xảo của OpenWood sắp tới ngôi nhà của bạn trong "
"bộ sưu tập mới nhất"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid ""
"The following tickets cannot be deleted while they have one or more registrations linked to them:\n"
"- %s"
msgstr ""
"Các vé sau không thể xóa khi được liên kết với một hoặc nhiều đăng ký:\n"
"-%s"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "The safety of our attendees and our aeronauts comes first!"
msgstr ""
"Chúng tôi đặt sự an toàn của người tham dự và các phi hành gia lên hàng đầu!"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
msgid ""
"The stop date cannot be earlier than the start date. Please check ticket "
"%(ticket_name)s"
msgstr ""
"Ngày kết thúc không thể sớm hơn ngày bắt đầu. Vui lòng kiểm tra vé "
"%(ticket_name)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_ticket.py:0
msgid "There are not enough seats available for:"
msgstr "Không còn đủ chỗ trống cho:"

#. module: event
#: model:ir.model.constraint,message:event.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "Phải có giá trị được gợi ý hoặc giá trị văn bản. "

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This event is also an opportunity to showcase our partners' case studies, "
"methodology or developments. Be there and see directly from the source the "
"features of the version 12!"
msgstr ""
"Sự kiện này cũng là cơ hội để giới thiệu các nghiên cứu điển hình, phương "
"pháp hoặc sự phát triển của các đối tác của chúng tôi. Hãy tham gia và tận "
"mắt xem các tính năng của phiên bản 12!"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"This event is fully online and FREE, if you have paid for tickets, you should get a refund.<br>\n"
"        It will require a good Internet connection to get the best video quality."
msgstr ""
"Sự kiện online và MIỄN PHÍ, nếu bạn đã thanh toán tiền vé, bạn sẽ được hoàn tiền.<br>\n"
"        Bạn nên có kết nối Internet tốt để xem video có chất lượng tốt nhất."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__ticket_instructions
#: model:ir.model.fields,help:event.field_event_type__ticket_instructions
msgid "This information will be printed on your tickets."
msgstr "Thông tin này sẽ được in trên vé."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "This is the <b>name</b> your guests will see when registering."
msgstr "Đây là <b>tên</b> mà khách sẽ thấy khi đăng ký."

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"This is the perfect place for spending a nice day with your family, we "
"guarantee you will be leaving with beautiful everlasting memories!"
msgstr ""
"Một nơi hoàn hảo để dành thời gian với gia đình. Chúng tôi bảo đảm bạn sẽ có"
" những kỷ niệm khó quên tại đây!"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"This is the perfect place for spending a nice time with your friends while "
"listening to some of the most iconic rock songs of all times!"
msgstr ""
"Một nơi hoàn hảo để dành thời gian cho bạn bè trong khi nghe các bài hát "
"rock biểu tượng của mọi thời đại!"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "This operator is not supported"
msgstr "Toán tử này không được hỗ trợ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "Bước này đã hoàn tất. Nhấp để chặn hoặc đặt là đang thực hiện."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "This ticket is for another event!"
msgstr "Vé này là vé của sự kiện khác!"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "This ticket is not for an ongoing event"
msgstr "Vé này không phải vé sự kiện đang diễn ra"

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ticket"
msgstr "Phiếu hỗ trợ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__ticket_instructions
#: model:ir.model.fields,field_description:event.field_event_type__ticket_instructions
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Ticket Instructions"
msgstr "Hướng dẫn vé"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_registration__event_ticket_id
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ticket Type"
msgstr "Loại phiếu hỗ trợ"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Ticket already scanned!"
msgstr "Vé đã được quét!"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Ticket type"
msgstr "Loại vé"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid ""
"Ticket types allow you to distinguish your attendees. Let's <b>create</b> a "
"new one."
msgstr ""
"Loại vé cho phép bạn phân biệt người tham dự. Hãy <b>tạo</b> một loại mới."

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
#: model:ir.model.fields,field_description:event.field_event_type__event_type_ticket_ids
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Tickets"
msgstr "Phiếu hỗ trợ"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Tickets can be printed or scanned directly from your phone."
msgstr "Có thể in vé hoặc quét trực tiếp từ điện thoại."

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_pos_event
msgid "Tickets with PoS"
msgstr "Vé với POS"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
msgid "Tickets with Sale"
msgstr "Vé với Bán hàng"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "Múi giờ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_question__title
msgid "Title"
msgstr "Tiêu đề"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total"
msgstr "Tổng"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total Attendees"
msgstr "Tổng người tham dự"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Total Registrations for this Event"
msgstr "Tổng lượt đăng ký cho sự kiện này"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr "Nội dung và chương trình"

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Training"
msgstr "Đào tạo"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_3
msgid ""
"Tree Dealers brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"Tree Dealers mang đến sự trung thực và nghiêm túc cho ngành gỗ đồng thời "
"giúp khách hàng xử lý các loại cây, hoa và nấm."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "Kích hoạt"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr "Kích hoạt"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_3
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Type"
msgstr "Loại"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trong bản ghi."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__draft
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "Chưa xác nhận"

#. module: event
#: model:ir.model.fields,help:event.field_event_registration__state
msgid ""
"Unconfirmed: registrations in a pending state waiting for an action (specific case, notably with sale status)\n"
"Registered: registrations considered taken by a client\n"
"Attended: registrations for which the attendee attended the event\n"
"Cancelled: registrations cancelled manually"
msgstr ""
"Chưa xác nhận: đăng ký đang ở trạng thái treo để đợi một tác vụ khác (trường hợp cụ thể, đặc biệt là trạng thái bán hàng)\n"
"Đã đăng ký: đăng ký được thực hiện bởi khách hàng\n"
"Đã tham dự: đăng ký mà người tham dự đã dự sự kiện\n"
"Đã hủy: đăng ký bị hủy thủ công"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid ""
"Under this technical menu you will find all scheduled communication related "
"to your events."
msgstr ""
"Trong menu kỹ thuật này, bạn sẽ tìm thấy tất cả trao đổi liên lạc được lên "
"kế hoạch có liên quan tới sự kiện."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr "Hiểu về các phân hệ khác nhau;"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
msgid "Undo"
msgstr "Hoàn tác"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "Đơn vị"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr "Sự kiện sắp diễn ra từ hôm nay"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr "Sắp diễn ra / Đang chạy"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__use_barcode
msgid "Use Barcode"
msgstr "Sử dụng mã vạch"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__use_event_barcode
msgid "Use Event Barcode"
msgstr "Sử dụng mã vạch sự kiện"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Use Event Tag Categories to classify and organize your event tags."
msgstr "Dùng danh mục thẻ sự kiện để phân loại và tổ chức các thẻ sự kiện."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
msgid "Use the <b>breadcrumbs</b> to go back to your kanban overview."
msgstr "Sử dụng <b>breadcrumb</b> để quay lại tổng quan kanban."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_used
msgid "Used Seats"
msgstr "Chỗ đã dùng"

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "Người dùng"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_2
#: model:event.event.ticket,name:event.event_2_ticket_2
#: model:event.event.ticket,name:event.event_3_ticket_1
#: model:event.event.ticket,name:event.event_7_ticket_2
msgid "VIP"
msgstr "VIP"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "Value should be True or False (not %s)"
msgstr "Giá trị phải là Đúng hoặc Sai (không phải %s)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Venue"
msgstr "Địa điểm "

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_inline
msgid "Venue (formatted for one line uses)"
msgstr "Địa điểm (được định dạng cho mục đích sử dụng trong một dòng)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr "CẢNH BÁO: Lỗi Trình lập lịch sự kiện cho sự kiện: %s"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
msgid ""
"Wait until Attendees register to your Event or create their registrations "
"manually."
msgstr ""
"Đợi tới lúc đăng ký Người tham dự cho sự kiện hoặc tạo đăng ký thủ công."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr "Quản lý nhà kho, Sản xuất (MRP) &amp; Bán hàng, Xuất/Nhập khẩu."

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
msgid "Warning"
msgstr "Cảnh báo"

#. module: event
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid ""
"We reserve the right to cancel, re-name or re-locate the event or change the"
" dates on which it is held in case the weather fails us."
msgstr ""
"Chúng tôi có quyền hủy bỏ, đổi tên hoặc thay đổi địa điểm của sự kiện hoặc "
"thay đổi ngày tổ chức nếu thời tiết xấu."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__website_message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__website_message_ids
#: model:ir.model.fields,help:event.field_event_registration__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__weeks
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__weeks
msgid "Weeks"
msgstr "Tuần"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "Welcome to"
msgstr "Chào mừng bạn đến"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What's new?"
msgstr "Có gì mới?"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_begin
#: model:ir.model.fields,help:event.field_event_registration__event_begin_date
msgid ""
"When the event is scheduled to take place (expressed in your local timezone "
"on the form view)."
msgstr ""
"Thời gian sự kiện diễn ra theo lịch trình (được thể hiện theo múi giờ địa "
"phương của bạn trên chế độ xem biểu mẫu)."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Liệu sự kiện đã bắt đầu"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__sale_available
msgid "Whether it is possible to sell these tickets"
msgstr "Liệu có thể bán được những vé này không"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__is_sold_out
msgid "Whether seats are not available for this ticket."
msgstr "Liệu chỗ trống không còn cho vé này."

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_2
msgid "You are truly among the best."
msgstr "Bạn quả thực là những người xuất sắc nhất"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"Bạn cũng có thể thêm mô tả để giúp đồng nghiệp hiểu ý nghĩa và mục đích của "
"giai đoạn. "

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels in the kanban view."
msgstr ""
"Tại đây bạn có thể xác định nhãn sẽ hiển thị cho trạng thái thay vì\n"
"                            nhãn mặc định trong chế độ xem kanban."

#. module: event
#. odoo-python
#: code:addons/event/models/event_question.py:0
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr "Bạn không thể thay đổi kiểu câu hỏi của câu hỏi đã có câu trả lời!"

#. module: event
#. odoo-python
#: code:addons/event/models/event_question.py:0
msgid ""
"You cannot delete a question that has already been answered by attendees."
msgstr "Bạn không thể xóa câu hỏi đã được người tham dự trả lời."

#. module: event
#. odoo-python
#: code:addons/event/models/event_question_answer.py:0
msgid ""
"You cannot delete an answer that has already been selected by attendees."
msgstr "Bạn không thể xóa câu trả lời đã được người tham dự lựa chọn."

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for {{ object.event_id.name }}"
msgstr "Thẻ của bạn cho {{ object.event_id.name }}"

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at {{ object.event_id.name }}"
msgstr "Đăng ký của bạn tại {{ object.event_id.name }}"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "e.g. \"12-16 years old\""
msgstr "VD: \"12-16 tuổi\""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "e.g. \"Age Category\""
msgstr "VD: \"Hạng mục tuổi\""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "e.g. \"Azure Interior\""
msgstr "VD: \"Azure Interior\""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_question_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr "VD: \"Bạn có hạn chế nào về chế độ ăn uống không?\""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "e.g. \"Promoting\""
msgstr "VD: \"Quảng bá\""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "e.g. Conference for Architects"
msgstr "e.g. Conference for Architects"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. How to get to your event, door closing time, ..."
msgstr "VD: Làm thế nào để đến sự kiện của bạn, thời gian đóng cửa,..."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. Online Conferences"
msgstr "VD: Hội nghị online"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "e.g. VIP Ticket"
msgstr "VD: Vé VIP"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "https://www.example.com"
msgstr "https://www.example.com"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "in %d days"
msgstr "trong %d ngày"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "next month"
msgstr "tháng tiếp theo"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "next week"
msgstr "tuần tới"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "on %(date)s"
msgstr "vào %(date)s"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
msgid "or"
msgstr "hoặc"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_started
msgid ""
"registrations have started if the current datetime is after the earliest "
"starting date of tickets."
msgstr ""
"đăng ký đã bắt đầu nếu ngày giờ hiện tại sau ngày bắt đầu sớm nhất của vé."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_kanban_from_event
msgid "reserved"
msgstr "đã đặt"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "to"
msgstr "đến"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "today"
msgstr "hôm nay"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
msgid "tomorrow"
msgstr "ngày mai"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "{{ object.event_id.name }}: {{ object.event_date_range }}"
msgstr "{{ object.event_id.name }}: {{ object.event_date_range }}"
