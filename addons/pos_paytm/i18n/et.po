# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_paytm
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Anna, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Anna, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__accept_payment
msgid "Accept Payment"
msgstr "Nõustu maksega"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__allowed_payment_modes__all
msgid "All"
msgstr "Kõik"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__allowed_payment_modes
msgid "Allowed Payment Modes"
msgstr "Lubatud makseviisid"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__accept_payment__auto
msgid "Automatically"
msgstr "Automaatselt"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__allowed_payment_modes__card
msgid "Card"
msgstr "Kaart"

#. module: pos_paytm
#: model:ir.model.fields,help:pos_paytm.field_pos_payment_method__accept_payment
msgid ""
"Choose accept payment mode: \n"
" Manually or Automatically"
msgstr ""

#. module: pos_paytm
#: model:ir.model.fields,help:pos_paytm.field_pos_payment_method__allowed_payment_modes
msgid ""
"Choose allow payment mode: \n"
" All/Card or QR"
msgstr ""

#. module: pos_paytm
#: model:ir.model.fields,help:pos_paytm.field_pos_payment_method__paytm_mid
msgid "Go to https://business.paytm.com/ and create the merchant account"
msgstr ""

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__accept_payment__manual
msgid "Manually"
msgstr "Käsitsi"

#. module: pos_paytm
#: model:ir.model.fields,help:pos_paytm.field_pos_payment_method__paytm_merchant_key
msgid ""
"Merchant/AES key \n"
" ex: B1o6Ivjy8L1@abc9"
msgstr ""

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__channel_id
msgid "PayTM Channel ID"
msgstr "PayTM kanali ID"

#. module: pos_paytm
#. odoo-javascript
#: code:addons/pos_paytm/static/src/js/payment_paytm.js:0
msgid "PayTM Error"
msgstr "PayTM veateade"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_merchant_key
msgid "PayTM Merchant API Key"
msgstr "PayTM kaupmehe API võti"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_mid
msgid "PayTM Merchant ID"
msgstr "PayTM kaupmehe ID"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_tid
msgid "PayTM Terminal ID"
msgstr "PayTM terminali ID"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_test_mode
msgid "PayTM Test Mode"
msgstr "PayTM testrežiim"

#. module: pos_paytm
#: model:ir.model,name:pos_paytm.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Kassa maksemeetodid"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__allowed_payment_modes__qr
msgid "QR"
msgstr "QR"

#. module: pos_paytm
#. odoo-javascript
#: code:addons/pos_paytm/static/src/js/payment_paytm.js:0
msgid "Reference number mismatched"
msgstr "Viitenumber ei sobitu"

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
msgid "Something went wrong with paytm request. Please try later."
msgstr ""

#. module: pos_paytm
#: model:ir.model.fields,help:pos_paytm.field_pos_payment_method__paytm_tid
msgid ""
"Terminal model or Activation code \n"
" ex: 70000123"
msgstr ""

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
msgid "This Payment Terminal is only valid for INR Currency"
msgstr ""

#. module: pos_paytm
#: model:ir.model.fields,help:pos_paytm.field_pos_payment_method__paytm_test_mode
msgid "Turn it on when in Test Mode"
msgstr "Lülita see sisse, kui oled testrežiimis"

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
msgid "makePaymentRequest expected resultCode not found in the response"
msgstr ""

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
msgid "paymentFetchRequest expected resultCode not found in the response"
msgstr ""

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
msgid "paytm transaction failure"
msgstr "PayTM tehingu ebaõnnestumine"

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
msgid "paytm transaction request declined"
msgstr "Paytm tehingu taotlus on tagasi lükatud"
