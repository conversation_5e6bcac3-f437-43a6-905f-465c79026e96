# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_mrp
# 
# Translators:
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_mrp
#: model:ir.model,name:project_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr "Stykliste"

#. module: project_mrp
#. odoo-python
#: code:addons/project_mrp/models/project_project.py:0
#: model:ir.embedded.actions,name:project_mrp.project_embedded_action_bills_of_materials
msgid "Bills of Materials"
msgstr "Styklister"

#. module: project_mrp
#. odoo-python
#: code:addons/project_mrp/models/project_project.py:0
msgid ""
"Bills of materials allow you to define the list of required raw materials "
"used to make a finished product; through a manufacturing order or a pack of "
"products."
msgstr ""

#. module: project_mrp
#: model:ir.model,name:project_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr "Produktionsordre"

#. module: project_mrp
#. odoo-python
#: code:addons/project_mrp/models/project_project.py:0
#: model:ir.embedded.actions,name:project_mrp.project_embedded_action_manufacturing_orders
msgid "Manufacturing Orders"
msgstr "Produktionsordrer"

#. module: project_mrp
#. odoo-python
#: code:addons/project_mrp/models/project_project.py:0
msgid "No bill of materials found. Let's create one."
msgstr ""

#. module: project_mrp
#: model:ir.model,name:project_mrp.model_project_project
#: model:ir.model.fields,field_description:project_mrp.field_mrp_bom__project_id
#: model:ir.model.fields,field_description:project_mrp.field_mrp_production__project_id
msgid "Project"
msgstr "Projekt"

#. module: project_mrp
#: model:ir.model,name:project_mrp.model_stock_move
msgid "Stock Move"
msgstr "Lagerflytning"

#. module: project_mrp
#: model:ir.model,name:project_mrp.model_stock_rule
msgid "Stock Rule"
msgstr "Lager regel"
