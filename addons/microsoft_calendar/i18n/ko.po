# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_calendar
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-30 18:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s 일"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s 시간"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s 분"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%s - At time of event"
msgstr "%s - 행사 시"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "(No title)"
msgstr "(제목 없음)"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__active
msgid "Active"
msgstr "활성화"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid ""
"An administrator needs to configure Outlook Synchronization before you can "
"use it!"
msgstr "사용하려면 관리자가 Outlook 동기화 설정을 해야 합니다!"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/res_users.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Microsoft Azure portal or try to stop and restart your "
"calendar synchronisation."
msgstr ""
"토큰 생성 중에 오류가 발생했습니다. 인증 코드가 잘못되었거나 이미 만료된 코드 [%s]입니다. Microsoft Azure 포털에서 "
"클라이언트 아이디 및 암호를 확인하거나 캘린더 동기화를 중지한 후 다시 시작하시기 바랍니다."

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "일정표 참석자 정보"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "행사 일정표"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Cancel"
msgstr "취소"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "고객 ID"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "고객 비밀키"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Configuration"
msgstr "설정"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Configure"
msgstr "설정"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Confirm"
msgstr "승인"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_uid
msgid "Created by"
msgstr "작성자"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_date
msgid "Created on"
msgstr "작성일자"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Odoo에서 삭제"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "양 쪽에서 삭제"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_microsoft
msgid "Delete from the current Microsoft Calendar account"
msgstr "현재 Microsoft 캘린더 계정에서 삭제"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Discard"
msgstr "취소"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__display_name
msgid "Display Name"
msgstr "표시명"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrence updates must be done "
"directly in Outlook Calendar."
msgstr "Outlook 캘린더 제한으로 인해 반복되는 일정은 Outlook 캘린더에서 직접 업데이트해야 합니다."

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrence updates must be done directly in Outlook Calendar.\n"
"If this recurrence is not shown in Outlook Calendar, you must delete it in Odoo Calendar and recreate it in Outlook Calendar."
msgstr ""
"Outlook 캘린더 제한으로 인해 반복되는 일정은 Outlook 캘린더에서 직접 업데이트해야 합니다.\n"
"반복 내용이 Outlook 캘린더에 표시되지 않으면 Odoo 캘린더에서 삭제한 후 Outlook 캘린더에서 다시 생성할 수 있습니다."

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrent events must be created "
"directly in Outlook Calendar."
msgstr "Outlook 캘린더 제한으로 인해 반복되는 일정은 Outlook 캘린더에서 직접 생성해야 합니다."

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "일정 알림 관리자"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "일정 반복 규칙"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"For a correct synchronization between Odoo and Outlook Calendar, all attendees must have an email address. However, some events do not respect this condition. As long as the events are incorrect, the calendars will not be synchronized.\n"
"Either update the events/attendees or archive these events %(details)s:\n"
"%(invalid_events)s"
msgstr ""
"Odoo와 Outlook 일정 간에 제대로 동기화하려면 모든 참석자에게 전자 메일 주소가 있어야 합니다. 일부 이벤트는 이 요구 사항을 충족하지 않아 동기화가 되지 않습니다.\n"
"이벤트/참석자를 업데이트하거나 이러한 이벤트를 %(details)s에 보관하세요:\n"
"%(invalid_events)s"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"For having a different organizer in your event, it is necessary that the "
"organizer have its Odoo Calendar synced with Outlook Calendar."
msgstr "행사에 다른 주최자를 추가하려면 주최자의 Odoo 캘린더가 Outlook 캘린더와 동기화되어 있어야 합니다."

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__id
msgid "ID"
msgstr "ID"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr "사용중인 필드를 아니오로 설정하면 일정 알람 정보를 제거하지 않고 숨길 수 있습니다."

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_res_config_settings__cal_microsoft_sync_paused
msgid "Indicates if synchronization with Outlook Calendar is paused or not."
msgstr "Outlook 캘린더와의 동기화가 일시 중지되었는지 여부를 나타냅니다."

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"It is necessary adding the proposed organizer as attendee before saving the "
"event."
msgstr "행사를 저장하기 전에 제안된 주최자를 참석자로 추가해야 합니다."

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_last_sync_date
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_last_sync_date
msgid "Last Sync Date"
msgstr "최근 동기화 날짜"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Last Sync Time"
msgstr "최근 동기화 시간"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_res_users__microsoft_last_sync_date
#: model:ir.model.fields,help:microsoft_calendar.field_res_users_settings__microsoft_last_sync_date
msgid "Last synchronization date with Outlook Calendar"
msgstr "Outlook 캘린더와 마지막으로 동기화된 날짜"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "변경하지 않고 나가기"

#. module: microsoft_calendar
#: model:ir.actions.act_window,name:microsoft_calendar.microsoft_calendar_reset_account_action
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_account_reset
msgid "Microsoft Calendar Account Reset"
msgstr "Microsoft 캘린더 계정 초기화"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_id
msgid "Microsoft Client_id"
msgstr "Microsoft 클라이언트 아이디"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_secret
msgid "Microsoft Client_key"
msgstr "Microsoft 클라이언트 키"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_calendar_sync_token
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_calendar_sync_token
msgid "Microsoft Next Sync Token"
msgstr "Microsoft 다음 동기화 토큰"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_recurrence_master_id
msgid "Microsoft Recurrence Master Id"
msgstr "Microsoft 반복 마스터 아이디"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_sync_paused
msgid "Microsoft Synchronization Paused"
msgstr "Microsoft 동기화 일시정지"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__need_sync_m
msgid "Need Sync M"
msgstr "동기화 M 필요"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Next Sync Token"
msgstr "다음 동기화 토큰"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "다음 동기화"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "Notification"
msgstr "알림"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__microsoft_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__microsoft_id
msgid "Organizer event Id"
msgstr "주최자 행사 아이디"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.xml:0
msgid "Outlook"
msgstr "아웃룩"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Outlook Calendar"
msgstr "아웃룩 달력"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_synchronization_stopped
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_synchronization_stopped
msgid "Outlook Synchronization stopped"
msgstr "아웃룩 동기화가 중지되었습니다"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Outlook limitation: in a recurrence, an event cannot be moved to or before "
"the day of the previous event, and cannot be moved to or after the day of "
"the following event."
msgstr ""
"Outlook 제한: 반복되는 경우, 행사를 이전 행사일 당일이나 이전으로 이동할 수 없으며, 다음 행사일 당일이나 이후로 이동할 수 "
"없습니다."

#. module: microsoft_calendar
#: model:ir.actions.server,name:microsoft_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Outlook: synchronization"
msgstr "아웃룩: 동기화"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr "동기화 일시 중지"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Refresh Token"
msgstr "새로 고침 토큰"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Reset Account"
msgstr "계정 초기화"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Reset Outlook Calendar Account"
msgstr "Outlook 캘린더 계정 초기화"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_sync
msgid "Synchronize a record with Microsoft Calendar"
msgstr "Outlook 캘린더와 레코드 동기화"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "기존 행사 전체 동기화"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "새로운 행사만 동기화"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid ""
"The Outlook Synchronization needs to be configured before you can use it, do"
" you want to do it now?"
msgstr "Outlook 동기화를 사용하려면 먼저 설정을 해야 합니다. 지금 설정하시겠습니까?"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "사용자가 행사 소유자인 경우에만 영향이 있습니다."

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Token Validity"
msgstr "유효 토큰"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__ms_universal_event_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__ms_universal_event_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__ms_universal_event_id
msgid "Universal event Id"
msgstr "행사 범용 아이디"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_users
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__user_id
msgid "User"
msgstr "사용자"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_users_settings
msgid "User Settings"
msgstr "사용자 설정"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "User Token"
msgstr "사용자 토큰"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "사용자의 기존 행사"
