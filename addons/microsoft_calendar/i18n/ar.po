# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_calendar
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-30 18:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s أيام "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s ساعات "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s دقائق "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%s - At time of event"
msgstr "%s - في وقت الفعالية "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "(No title)"
msgstr "(بلا عنوان) "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__active
msgid "Active"
msgstr "نشط"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid ""
"An administrator needs to configure Outlook Synchronization before you can "
"use it!"
msgstr ""
"يجب أن يقوم أحد المشرفين بتهيئة مزامنة Outlook قبل أن تتمكن من استخدامها! "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/res_users.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Microsoft Azure portal or try to stop and restart your "
"calendar synchronisation."
msgstr ""
"لقد وقع خطأ أثناء إنشاء الرمز. قد يكون رمز التفويض الخاص بك غير صالح أو "
"انتهت مدة صلاحيته [%s]. عليك التحقق من معرف العميل الخاص بك والسر في بوابة "
"Microsoft Azure لمحاولة إيقاف وإعادة تشغيل مزامنة تقويمك. "

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "بيانات الحاضر في التقويم "

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "فعالية التقويم "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Cancel"
msgstr "إلغاء"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "معرف العميل"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "سر العميل"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Configuration"
msgstr "التهيئة "

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Configure"
msgstr "تهيئة "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Confirm"
msgstr "تأكيد"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "الحذف من أودو "

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "الحذف من كليهما "

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_microsoft
msgid "Delete from the current Microsoft Calendar account"
msgstr "الحذف من حساب تقويم Microsoft الحالي "

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Discard"
msgstr "إهمال "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrence updates must be done "
"directly in Outlook Calendar."
msgstr ""
"نظراً لتقييد تقويم Outlook، يجب القيام بتحديثات التكرار مباشرة في تقويم "
"Outlook. "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrence updates must be done directly in Outlook Calendar.\n"
"If this recurrence is not shown in Outlook Calendar, you must delete it in Odoo Calendar and recreate it in Outlook Calendar."
msgstr ""
"نظراً لتقييد تقويم Outlook، يجب القيام بتحديثات التكرار مباشرة في تقويم Outlook. \n"
"إذا لم يكن هذا التكرار معروضاً في تقويم Outlook، يجب عليك حذفه في تقويم أودو وإعادة إنشائه في تقويم Outlook. "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrent events must be created "
"directly in Outlook Calendar."
msgstr ""
"نظراً لتقييد تقويم Outlook، يجب إنشاء الفعاليات المتكررة مباشرة في تقويم "
"Outlook. "

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "إدارة تنبيه الفعالية"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "قاعدة تكرار الفعالية "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"For a correct synchronization between Odoo and Outlook Calendar, all attendees must have an email address. However, some events do not respect this condition. As long as the events are incorrect, the calendars will not be synchronized.\n"
"Either update the events/attendees or archive these events %(details)s:\n"
"%(invalid_events)s"
msgstr ""
"للمزامنة بشكل صحيح بين تقويمي أودو وOutlook، يجب أن يكون لكافة الحاضرين عنوان بريد إلكتروني. ولكن، لا تمتثل بعض الءفعاليات بهذا الشرط. لن تتم مزامنة التقويم طالما أن الفعاليات غير صحيحة. \n"
"إما أن تقوم بتحديث الفعاليات/الحاضرين أو أرشفة هذه الفعاليات %(details)s: \n"
"%(invalid_events)s "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"For having a different organizer in your event, it is necessary that the "
"organizer have its Odoo Calendar synced with Outlook Calendar."
msgstr ""
"للحصول على منظم مختلف في فعاليتك، من الضروري أن يقوم المنظم بمزامنة تقويم "
"أودو الخاص به مع تقويم Outlook. "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__id
msgid "ID"
msgstr "المُعرف"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"إذا تم تعيين قيمة الحقل النشط إلى خطأ، يمكنك إخفاء معلومات التنبيه للفعالية "
"دون إزالتها. "

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_res_config_settings__cal_microsoft_sync_paused
msgid "Indicates if synchronization with Outlook Calendar is paused or not."
msgstr "يشير إلى ما إذا كانت المزامنة مع تقويم Outlook متوقفة مؤقتًا أم لا. "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"It is necessary adding the proposed organizer as attendee before saving the "
"event."
msgstr "من الضروري إضافة المنظم المقترح كحاضر قبل حفظ الفعالية. "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_last_sync_date
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_last_sync_date
msgid "Last Sync Date"
msgstr "تاريخ آخر مزامنة "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Last Sync Time"
msgstr "وقت آخر مزامنة "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_res_users__microsoft_last_sync_date
#: model:ir.model.fields,help:microsoft_calendar.field_res_users_settings__microsoft_last_sync_date
msgid "Last synchronization date with Outlook Calendar"
msgstr "تاريخ آخر مزامنة مع تقويم Outlook "

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "اتركهم كما هم "

#. module: microsoft_calendar
#: model:ir.actions.act_window,name:microsoft_calendar.microsoft_calendar_reset_account_action
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_account_reset
msgid "Microsoft Calendar Account Reset"
msgstr "إعادة ضبط حساب تقويم Microsoft  "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_id
msgid "Microsoft Client_id"
msgstr "Microsoft Client_id"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_secret
msgid "Microsoft Client_key"
msgstr "Microsoft Client_key"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_calendar_sync_token
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_calendar_sync_token
msgid "Microsoft Next Sync Token"
msgstr "رمز المزامنة التالية لـ Microsoft "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_recurrence_master_id
msgid "Microsoft Recurrence Master Id"
msgstr "المعرف الرئيسي لتكرار Microsoft "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_sync_paused
msgid "Microsoft Synchronization Paused"
msgstr "لقد توقفت مزامنة Microsoft مؤقتاً "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__need_sync_m
msgid "Need Sync M"
msgstr "بحاجة إلى مزامنة M "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Next Sync Token"
msgstr "رمز المزامنة التالي "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "المزامنة التالية"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "Notification"
msgstr "إشعار "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__microsoft_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__microsoft_id
msgid "Organizer event Id"
msgstr "معرّف فعالية المنظم "

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.xml:0
msgid "Outlook"
msgstr "Outlook"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Outlook Calendar"
msgstr "تقويم Outlook "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_synchronization_stopped
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_synchronization_stopped
msgid "Outlook Synchronization stopped"
msgstr "توقفت مزامنة Outlook "

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Outlook limitation: in a recurrence, an event cannot be moved to or before "
"the day of the previous event, and cannot be moved to or after the day of "
"the following event."
msgstr ""
"حدود Outlook: في التكرار، يمكن نقل الفعالية إلى اليوم الذي به الفعالية "
"السابقة أو قبلها، ولا يمكن نقلها إلى يوم الفعالية التالية أو بعدها. "

#. module: microsoft_calendar
#: model:ir.actions.server,name:microsoft_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Outlook: synchronization"
msgstr "Outlook: المزامنة "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr "إيقاف المزامنة مؤقتاً "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Refresh Token"
msgstr "تحديث الرمز"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Reset Account"
msgstr "إعادة ضبط الحساب "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Reset Outlook Calendar Account"
msgstr "إعادة تعيين حساب تقويم Outlook "

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_sync
msgid "Synchronize a record with Microsoft Calendar"
msgstr "قم بمزامنة سجل مع تقويم Microsoft "

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "مزامنة كافة الفعاليات الموجودة "

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "مزامنة الفعاليات الجديدة فقط "

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid ""
"The Outlook Synchronization needs to be configured before you can use it, do"
" you want to do it now?"
msgstr "يجب تهيئة مزامنة Outlook قبل الاستخدام. أترغب في فعل ذلك الآن؟ "

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "سيؤثر ذلك فقط على الفعاليات التي يمتلكها هذا المستخدم "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Token Validity"
msgstr "صلاحية الرمز "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__ms_universal_event_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__ms_universal_event_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__ms_universal_event_id
msgid "Universal event Id"
msgstr "المعرّف الموحد للفعالية "

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_users
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__user_id
msgid "User"
msgstr "المستخدم"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_users_settings
msgid "User Settings"
msgstr "إعدادات المستخدم "

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "User Token"
msgstr "رمز المستخدم "

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "فعاليات المستخدم الموجودة بالفعل "
