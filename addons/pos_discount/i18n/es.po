# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_discount
#. odoo-python
#: code:addons/pos_discount/models/pos_config.py:0
msgid ""
"A discount product is needed to use the Global Discount feature. Go to Point"
" of Sale > Configuration > Settings to set it."
msgstr ""
"Se requiere un producto de descuento para usar la función de descuento "
"global. Se configura en Punto de venta > Configuración > Ajustes."

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "Permita al cajero ofrecer descuentos en todo el pedido."

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/control_buttons/control_buttons.xml:0
#: model:product.template,name:pos_discount.product_product_consumable_product_template
msgid "Discount"
msgstr "Descuento"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr "Descuento %"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/control_buttons/control_buttons.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
msgid "Discount Percentage"
msgstr "Porcentaje de descuento"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "Producto de descuento"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid "No discount product found"
msgstr "No se ha encontrado ningún producto de descuento"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "Descuentos de pedidos"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuración del TPV"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr "Producto de descuento de TPV"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_product_product
msgid "Product Variant"
msgstr "Variante de producto"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr ""
"El porcentaje automático de descuento al hacer clic en el botón de descuento"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/control_buttons/control_buttons.js:0
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr ""
"El producto de descuento parece estar mal configurado. Asegúrese de que está"
" marcado como 'Se puede vender' y 'Disponible en el punto de venta'."

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr "El producto usado para aplicar el descuento en el recibo."
