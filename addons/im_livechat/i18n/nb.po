# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON>e <PERSON>ad, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:02+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Norwegian <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
msgid "\"%s\" is not a valid email."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "# Messages"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# økter"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "# talere"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% fornøyd"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
msgid "%s (copy)"
msgstr "%s (kopi)"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid ""
"'%(input_email)s' does not look like a valid email. Can you please try "
"again?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Show' displays the chat button on the pages.\n"
"* 'Show with notification' is 'Show' in addition to a floating text just next to the button.\n"
"* 'Open automatically' displays the button and automatically opens the conversation pane.\n"
"* 'Hide' hides the chat button on the pages.\n"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "1 is Monday, 7 is Sunday"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Back to edit mode"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "<i title=\"Remove operator\" class=\"fa fa-fw fa-lg fa-close\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "<span class=\"fw-bold\">Country: </span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Reminder: This step will only be played if no operator is "
"available.</span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before the Bot can perform more complex actions (Forward to an Operator, ...). </span>\n"
"                    <span>Use Channel Rules if you want the Bot to interact with visitors only when no operator is available.</span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before "
"the Bot can perform more complex actions (Forward to an Operator, "
"...).</span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Tip: Plan further steps for the Bot in case no operator is "
"available.</span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<span>You are currently testing</span>"
msgstr ""

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_chatbot_message__unique_mail_message_id
msgid "A mail.message can only be linked to a single chatbot message"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevet"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__active
msgid "Active"
msgstr "Aktiv"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Add the following code to the &lt;head&gt; section of your website:"
msgstr ""

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Administrator"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Alternatively, share this URL with your customers or suppliers via email:"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "An error occurred. Please try again."
msgstr ""

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_redirect
msgid "And tadaaaa here you go! 🌟"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Anonym"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__name
msgid "Answer"
msgstr "Svar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__answer_ids
msgid "Answers"
msgstr "Svar"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Archived"
msgstr "Arkivert"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Er du inne i matrisen?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__available_operator_ids
msgid "Available Operator"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg
msgid "Average Rating"
msgstr "Gjennomsnittlig rating"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg_percentage
msgid "Average Rating (%)"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Gjennomsnittlig varighet"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Gjennomsnittsmelding"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating given by the visitor"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Gjennomsnittlig tid før første respons til den besøkende"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Bad Ratings"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__operator_partner_id
msgid "Bot Operator"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "Bakgrunnsfarge på knapp"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "Tekstfarge på knapp"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Standardsvar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Channel"
msgstr "Kanal"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel_member
msgid "Channel Member"
msgstr "Kanal medlem"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Kanalnavn"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Kanalregel"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Kanalregler"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Kanaltype"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Channels"
msgstr "Kanaler"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.chatbot_script_action
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_script_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Chatbot"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_current_step_id
msgid "Chatbot Current Step"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_message
msgid "Chatbot Message"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_message_ids
msgid "Chatbot Messages"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Chatbot Name"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_answer
msgid "Chatbot Script Answer"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__script_step_id
msgid "Chatbot Step"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.chatbot_config
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Chatbots"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Choose the live chat channel you want to display on your website."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "Close"
msgstr "Lukk"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Close conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Kode"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Configure Channel"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Samtale"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
msgid "Conversation ended..."
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid "Conversation with %s"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__country_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "Country"
msgstr "Land"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__country_id
msgid "Country of the visitor of the channel"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid "Create a Chatbot"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Opprettet den"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Opprettelsesdato"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Opprettet (time)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Kundevurderinger"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "Date:"
msgstr "Dato:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Standard bakgrunnsfarge for Livechat-knapp"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Standard tekstfarge for Livechat-knapp"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Standardtekst som vises på kundestøtte-chat-knappen."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Define rules for your live support channel. You can apply an action for the "
"given URL, and per country.<br/>To identify the country, GeoIP must be "
"installed on your server, otherwise, the countries of the rule will not be "
"taken into account."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Open automatically' otherwise this parameter will "
"not be taken into account."
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Did we correctly answer your question?"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Digest"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/views/livechat_view_controller_mixin.js:0
msgid "Discuss"
msgstr "Diskuter"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__discuss_channel_id
msgid "Discussion Channel"
msgstr "Diskusjonskanal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_button.xml:0
msgid "Drag to Move"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__duration
msgid "Duration"
msgstr "Varighet"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Duration of Session (min)"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Samtalens varighet (i sekunder)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__duration
msgid "Duration of the session in hours"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "Duration:"
msgstr "Varighet:"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_email
msgid "Email"
msgstr "E-post"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
msgid "Enable the bot only if there is no operator available"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Enabled only if no operator"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Explain your note"
msgstr "Forklar notatet ditt"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "FOR OTHER WEBSITES"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "FOR WEBSITES BUILT WITH ODOO"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_invalid
msgid "First Step Invalid"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_operator
msgid "First Step Operator"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__first_step_warning
msgid "First Step Warning"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__forward_operator
msgid "Forward to Operator"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_single
msgid "Free Input"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_multi
msgid "Free Input (Multi-Line)"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Go to <strong>Website</strong> &gt; <strong>Configuration</strong> &gt; "
"<strong>Settings</strong> &gt; <strong>Email</strong> &amp; "
"<strong>Marketing.</strong>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Good Ratings"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Group By..."
msgstr "Grupper etter..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__has_message
msgid "Has Message"
msgstr "Har melding"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__has_access_livechat
msgid "Has access to Livechat"
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
msgid "Have a Question? Chat with us."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr ""

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide"
msgstr "Skjul"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "History"
msgstr "Historikk"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing
msgid ""
"Hmmm, let me check if I can find someone that could help you with that..."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
msgid "How may I help you?"
msgstr "Hva kan jeg hjelpe deg med?"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr ""

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_just_looking
msgid "I am just looking around"
msgstr ""

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_documentation
msgid "I am looking for your documentation"
msgstr ""

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_pricing
msgid "I have a pricing question"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis haket av, vil nye meldinger kreve din oppmerksomhet."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis haket av, har enkelte meldinger leveringsfeil."

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_exit
msgid "If you need anything else, feel free to get back in touch"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1920
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
msgid "Image"
msgstr "Bilde"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1024
msgid "Image 1024"
msgstr "Bilde 1024"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_128
msgid "Image 128"
msgstr "Bilde 128"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_256
msgid "Image 256"
msgstr "Bilde 256"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_512
msgid "Image 512"
msgstr "Bilde 512"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__is_forward_operator_child
msgid "Is Forward Operator Child"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Delta"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "Join %s"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Bli med i kanal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_invitation_patch.xml:0
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
msgid "Lang"
msgstr "Lang"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Siste 24 timer"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Learn how to display a live chat bubble on your website and start chatting "
"with visitors in a few easy steps."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Forlat"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "Leave %s"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Forlat kanal"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Chat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Live Chat Button"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/im_livechat/static/src/core/public_web/messaging_menu_patch.js:0
#: code:addons/im_livechat/static/src/core/web/thread_icon_patch.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
msgid "Livechat"
msgstr "Chat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Farge på Livechat-knapp"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Chattekanal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__livechat_channel_count
msgid "Livechat Channel Count"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__discuss_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Chattesamtale"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.unit_embed_suite
msgid "Livechat External Tests"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_lang_ids
msgid "Livechat Languages"
msgstr ""

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_discuss_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audience, the duration of a session, etc."
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Statistikk for kundestøtte via chat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_username
msgid "Livechat Username"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_lang_ids
msgid "Livechat languages"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__livechat_active
msgid "Livechat session is active until visitor leaves the conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__message
msgid "Message"
msgstr "Melding"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error
msgid "Message Delivery error"
msgstr "Melding ved leveringsfeil"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Messages per session"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "My Sessions"
msgstr "Mine økter"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__name
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Name"
msgstr "Navn"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
msgid "No available collaborator, please try again later."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "Ingen data ennå"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/res_partner.py:0
msgid "No history found"
msgstr "Ingen historikk funnet"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Notification text"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__chatbot_script_count
msgid "Number of Chatbot"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
msgid "Number of conversation"
msgstr "Antall samtaler"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Antall forskjellige talere"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Antall meldinger i samtalen"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antall beskjeder som trenger oppfølging"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Language"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Only If"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Open automatically"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Open automatically timer"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_operator_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Operatør"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Operatøranalyse"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Operatører"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Operatører som ikke viser noen aktivitet i Odoo på mer enn 30 minutter vil "
"anses som frakoblet."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_answer_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "Optional Link"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Alternativer"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_author_name
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_author_name
msgid "Parent Author Name"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_body
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_body
msgid "Parent Body"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Participant"
msgstr "Deltaker"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Participants"
msgstr "Deltakere"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_phone
msgid "Phone"
msgstr "Telefon"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
msgid "Please call me on: "
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
msgid "Please contact me on: "
msgstr ""

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_just_looking
msgid "Please do! If there is anything we can help with, let us know"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Drevet av"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_selection
msgid "Question"
msgstr "Spørsmål"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Rating"
msgstr "Vurdering"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Vurdering i siste tilbakemelding"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Vurdering siste bilde"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Vurdering siste verdi"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_text
msgid "Rating Text"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_count
msgid "Rating count"
msgstr "Antall vurderinger"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Ratings"
msgstr "Vurderinger"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Vurderinger for chattekanal"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "Receive a copy of this conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__redirect_link
msgid "Redirect Link"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_message_id
msgid "Related Mail Message"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Rapport"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
msgid "Reset to default colors"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/thread_actions.js:0
msgid "Restart Conversation"
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid "Restarting conversation..."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Regler"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Leveringsfeil"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
msgid "Say something"
msgstr "Si noe"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/composer_patch.js:0
msgid "Say something..."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Script"
msgstr "Skript"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Skript (eksternt)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__script_step_id
msgid "Script Step"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__script_step_ids
msgid "Script Steps"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Search history"
msgstr "Søk i historikk"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Søk etter rapport"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_commands_patch.js:0
msgid "See 15 last visited pages"
msgstr "Se 15 siste besøkte sider"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
msgid "Select an option above"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Send"
msgstr "Send"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__sequence
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Session Date"
msgstr "Øktdato"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Session Form"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Øktstatistikk"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Økter"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Økthistorikk"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Show"
msgstr "Vis"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Show this step only if all of these answers have been selected."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button_and_text
msgid "Show with notification"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__source_id
msgid "Source"
msgstr "Kilde"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Startdato for økt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
msgid "Step of type 'Question' must have answers."
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/composer_patch.js:0
#: code:addons/im_livechat/static/src/core/web/composer_patch.xml:0
msgid "Tab to next livechat"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__text
msgid "Text"
msgstr "Tekst"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Knappetekst"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Text to display on the notification"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Thank you for your feedback"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "The conversation was sent."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide', the "
"chat button will be hidden on the specified URL from the visitors located in"
" these 2 countries. This feature requires GeoIP installed on your server."
msgstr ""

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr ""

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Brukeren kan bli  med i kundestøttekanaler."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_answer__redirect_link
msgid ""
"The visitor will be redirected to this link upon clicking the option (note "
"that the script will end if the link is external to the livechat website)."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_lang_ids
msgid ""
"These languages, in addition to your main language, will be used to assign "
"you to Live Chat sessions."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "This Month"
msgstr "Denne måneden"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Denne uka"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr ""

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__title
msgid "Title"
msgstr "Tittel"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Turn on the <strong>Live Chat</strong> feature."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"URL til en statisk side hvor kunden kan diskutere med kanaloperatøren."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Unrated"
msgstr ""

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Bruker"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "Bruker innstillinger"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_script_answer_id
msgid "User's answer"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_raw_answer
msgid "User's raw answer"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
msgid "Visitor"
msgstr "Besøkende"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid "Visitor left the conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Nettside"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Chattekanaler på nettsted"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__website_message_ids
msgid "Website Messages"
msgstr "Meldinger fra nettsted"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__website_message_ids
msgid "Website communication history"
msgstr " Kommunikasjonshistorikk for nettsted"

#. module: im_livechat
#: model:chatbot.script,title:im_livechat.chatbot_script_welcome_bot
msgid "Welcome Bot"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Velkomstmelding"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_welcome
msgid "Welcome to CompanyName! 👋"
msgstr ""

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_dispatch
msgid "What are you looking for?"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr ""

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/close_confirmation.xml:0
msgid "Yes, leave conversation"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "You"
msgstr "Du"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/attachment.py:0
msgid "You are not allowed to upload attachments on this channel."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid ""
"You can create a new Chatbot with a defined script to speak to your website "
"visitors."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "You joined %s."
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "You left %s."
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/close_confirmation.xml:0
msgid "You're about to leave the conversation, proceed?"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Your chatter history is empty"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
msgid "country"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "e.g. \"Meeting Scheduler Bot\""
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "e.g. 'How can I help you?'"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "for eksempel Hei, hva kan jeg gjøre for deg?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "for eksempel DittNettsted.com"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "<EMAIL>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "sekunder"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr ""
