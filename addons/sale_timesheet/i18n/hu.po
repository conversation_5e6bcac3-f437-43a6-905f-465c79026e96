# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# <PERSON>, 2024
# krn<PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: gezza <<EMAIL>>, 2025\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No activities found. Let's start a new one!\n"
"                </p><p>\n"
"                    Track your working hours by projects every day and invoice this time to your customers.\n"
"                </p>\n"
"            "
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the analytic "
"distribution of the sale order item '%(so_line_name)s' linked to the "
"timesheet."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "- Timesheet product"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Onsite Interview"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Phone Call"
msgstr "1 telefonhívás"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 nap / év, amiből <br>6 nap választható."

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "2 open days"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "4 Days after Interview"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Daily Cost: </b>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Unit Price: </b>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"                            Define the rate at which an employee's time is billed based on their expertise, skills, or experience.\n"
"                            To bill the same service at a different rate, create separate sales order items.\n"
"                        </span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "<small><b>READ</b></small>"
msgstr "<small><b>OLVAS</b></small>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this timesheet entry has been cancelled.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Invoice</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Rögzített</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">Vevői rendelés</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Folyamat</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Válaszolási idő</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Amount Due:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "<strong>Time Remaining on SO: </strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "A full-time position <br>Attractive salary package."
msgstr "Teljes munkaidős állás <br>Vonzó bércsomag"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"A termék konfigurációjától függően a szállított mennyiség az alábbi módokon számítható:\n"
"  - Manuális: a mennyiség kézi megadása a soron\n"
"  - Analitikus kiadás: a mennyiség a könyvelésre küldött kiadások összesítéséből számítódik\n"
"  - Időnyilvántartás: a mennyiség az értékesítési rendelés soraihoz kapcsolt feladatokra rögzített órák összege\n"
"  - Készletmozgások: a mennyiség a jóváhagyott kiszedések mennyisége\n"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Achieve monthly sales objectives"
msgstr "Megvalósítani a havi értékesítési célokat"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Additional languages"
msgstr "További nyelvek"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Administrative Work"
msgstr "Adminisztrációs munka"

#. module: sale_timesheet
#: model:account.analytic.account,name:sale_timesheet.account_analytic_account_project_support
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allocated_hours
msgid "Allocated Time"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "Számlázandó összeg"

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitikus sor"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Analitikus sorok"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br><br>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Autonomy"
msgstr "Önállóság"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Bachelor Degree or Higher"
msgstr "Alapképzés vagy magasabb"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Based on Timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__allow_billable
msgid "Billable"
msgstr "Számlázható"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Billable Time"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_type
msgid "Billable Type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Billed"
msgstr "Számlázva"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_manual
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_manual
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed Manually"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_milestones
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_milestones
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Milestones"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "Számlázás"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billing_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billing Type"
msgstr ""

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "Költség"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Costs"
msgstr "Költségek"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "Számla létrehozása"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Create content that will help our users on a daily basis"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "Pénznem"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
msgid "Customer"
msgstr "Ügyfél"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "Ügyfél általi értékelések"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Customer Relationship"
msgstr "Vevői kapcsolattartás"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Discard"
msgstr "Elvetés"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Discover our products."
msgstr "Fedezze fel termékeinket!"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "Draft Invoice"
msgstr "Számlatervezet"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                    You can make a real contribution to the success of the company.\n"
"                    <br>\n"
"                    Several activities are often organized all over the year, such as weekly\n"
"                    sports sessions, team building events, monthly drink, and much more"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Eat &amp; Drink"
msgstr "Étel és ital"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_elevator_installation_product_template
msgid "Elevator Installation"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "Munkavállaló"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "Befejező dátum"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Expand your knowledge of various business industries"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Expected"
msgstr "Várható"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Experience in writing online content"
msgstr "Tapasztalat online tartalmak készítésében"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Gyümölcs, kávé és <br>ennivaló biztosítva."

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Furniture Delivery (Manual)"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Google Adwords experience"
msgstr "Google Adwords tapasztalat"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""
"Zseniális emberekből álló nagyszerű csapat barátságos és nyitott "
"környezetben"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_labour
msgid "Handyman"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Highly creative and autonomous"
msgstr "Nagyon kreatív és önálló"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_cost
msgid "Hourly Cost"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Ordered,"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Remaining)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "ID"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_interior_designer
msgid "Interior Designer"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_interior_designing_product_template
msgid "Interior Designing"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_account_move
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Invoice"
msgstr "Számla"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Invoice based on timesheets (delivered quantity)."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Invoiced"
msgstr "Számlázva"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Invoices"
msgstr "Számlák"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "Számlázás"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "Könyvelési tétel"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "Könyvelési tétel"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_milestones_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Lead the entire sales cycle"
msgstr "Felügyelet a teljes értékesítési cikluson"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Kézi mennyiség a rendelésen: számlázás a kézzel megadott mennyiség alapján analitikus számla létrehozása nélkül.\n"
"Szerződéses munkaidő: számlázás a kapcsolódó időnyilvántartásokon megadott órák alapján.\n"
"Feladat létrehozása és idő követése: vevői rendelés megerősítésekor feladat létrehozása és munkaidő rögzítése."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__margin
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Margin"
msgstr "Árrés"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Master demos of our software"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Materials"
msgstr "Anyagok"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Szállított mennyiség frissítésének módszere"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Must Have"
msgstr "kötelező"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Negotiate and contract"
msgstr "Tárgyalás és szerződéskötés"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Nice to have"
msgstr "Jó ha van"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Invoice"
msgstr "Nincs számla"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "Nincs tevékenység"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "No activities found. Let's start a new one!"
msgstr "Nincs tevékenység. Hozzon létre egyet!"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "Még nincs adat!"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Nincsenek: ostoba főnökök, használhatatlan eszközök és szigorú munkaidő"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__non_billable
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non-Billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_tree_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheete_analysis_report_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Non-billable"
msgstr "Nem számlázható"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Non-billable Time"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Not Billed"
msgstr "Nem számlázott"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
msgid "Number of hours spent multiplied by the unit price per hour/day."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Number of hours/days linked to a SOL."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Number of hours/days not linked to a SOL."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Operation not supported"
msgstr "A művelet nem támogatott"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "Rendelés hivatkozás"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_costs
msgid "Other costs"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_revenues
msgid "Other revenues"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Our Product"
msgstr "Termékeink"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Passion for software products"
msgstr "Elhivatottság szoftveres termékek iránt"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perfect written English"
msgstr "Tökéletes angol tudás"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perks"
msgstr "Jutalmak"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Personal Evolution"
msgstr "Személyes fejlődés"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "Sportoljon a kollégáival, <br>a számlát cégünk fizeti."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "Árazás"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product"
msgstr "Termék"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
msgid "Product Variant"
msgstr "Termékváltozat"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
msgid "Project"
msgstr "Projekt"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "Projekt sablon"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Qualify the customer needs"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Quotation"
msgstr "Ajánlat"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "Igazi felelősségek és kihívások egy gyorsan fejlődő vállalatnál"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Responsibilities"
msgstr "Felelősségek"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Revenues"
msgstr "Bevételek"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_roofing_product_template
msgid "Roofing"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "S0001"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Vevői előlegszámla"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order"
msgstr "Vevői rendelés"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order Item"
msgstr "Vevői rendelés tétel"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Vevői rendelés sor"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet_so_button
msgid "Sales Orders"
msgstr "Vevői rendelések"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"Vevői rendelés tétel, melyhez az eltöltött idő hozzáadásra kerül vevő "
"részére történő számlázás céljából. Távolítsa el a vevői rendelés tétel "
"kapcsolatot a munkaidő tételről ha nem szeretné kiszámlázni."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Vevői rendelés, melyhez a feladat kapcsolásra került."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "Számla keresés"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "Vevői rendelés keresés"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "Szolgáltatások értékesítése és a munkaidő kiszámlázása"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Service that will be used by default when invoicing the time spent on a "
"task. It can be modified on each task individually by selecting a specific "
"sales order item."
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Services"
msgstr "Szolgáltatások"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_engineer
msgid "Site Manager"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_solar_installation_product_template
msgid "Solar Panel Installation"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Sport Activity"
msgstr "Sporttevékenység"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "Kezdődátum"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__sale_order_state
msgid "Status"
msgstr "Státusz"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Strong analytical skills"
msgstr "Erős analitikus képességek"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr "Feladat"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Feladatok elemzése"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Technical Expertise"
msgstr "Műszaki szakértő"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_product.py:0
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be linked to a "
"company."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "The selected Sales Order should contain something to invoice."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default employee hourly wage in "
"employee's HR Settings"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Time Billing"
msgstr "Időalapú számlázás"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_report_project_task_user__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
msgid "Time Remaining on SO"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Time Spent"
msgstr "Eltöltött idő"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
msgid "Timesheet"
msgstr "Munkaidő-nyilvántartás"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheet Activities"
msgstr "Munkaidő-nyilvántartás tevékenységek"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "Munkaidő-nyilvántartás költségek"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
msgid "Timesheet Report"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order_item
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_account_move
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_sale_order
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheets"
msgstr "Időkimutatások"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed Manually)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Milestones)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Timesheets)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Fixed Price)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Non-Billable)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
msgid "Timesheets Analysis"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "Timesheets for the"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets of %s"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets revenues"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Timesheets revenues minus the costs"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheets without a sales order item are reported as"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Bill"
msgstr "Számlázandó"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Invoice"
msgstr "Számlázandó"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Total"
msgstr "Összesen"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "Szolgáltatás nyomkövetése"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Trainings"
msgstr "Képzések"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Egységár"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Valid work permit for Belgium"
msgstr "Érvényes munkavállalási engedély Belgiumban"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Value does not exist in the pricing type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
msgid "View Timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What We Offer"
msgstr "Amit ajánlunk"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What's great in the job?"
msgstr "Miért nagyszerű ez a munka?"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "You can only apply this action from a project."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot modify timesheets that are already invoiced."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__manually
msgid "billed manually"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "days remaining"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__not_billable
msgid "not billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "remaining"
msgstr "hátravan"
