# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ng
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.2alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 15:24+0000\n"
"PO-Revision-Date: 2024-01-29 15:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_a
msgid "A - TRANSACTION SUMMARY"
msgstr ""

#. module: l10n_ng
#: model:ir.model,name:l10n_ng.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ng
#: model:account.report.column,name:l10n_ng.l10n_ng_tr_balance
msgid "Amount"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_b
msgid "B - SALES/ INCOME"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c
msgid "C - VAT ON PURCHASES/EXPENSES"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_b_40
msgid "Income Received from Sales Subject to VAT "
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_90
msgid "Less Automatic/Electronic VAT Payment in Current Month"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_85
msgid "Less VAT deducted at source (by MDAs & Oil and Gas) Current Month"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_b_25
msgid "Less: Value of Goods and Services Exempted Included in Line 20"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_b_30
msgid "Less: Value of Zero Rated Goods & Services Included in line 20"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_95
msgid "Net VAT Payable/(Refundable) Current Month"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_65
msgid "Payment for Imported Goods For the Month"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_55
msgid "Payments for Domestic Purchases for Zero Rated Goods"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_50
msgid ""
"Payments for Domestic Purchases other than zero rated and exempted goods and"
" services For the Month"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_100
msgid "Previous Unrelieved VAT Credit Brought Forward"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_b_35
msgid "Sales Adjustments (Gross amount)"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_b_45
msgid "TOTAL Output Tax Collected @ 7.5%"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_70
msgid "TOTAL Purchases Subject to Input Tax"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_60
msgid "Total Domestic Purchases Subject to Input Tax"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_b_20
msgid "Total Income Received from Sale for the Month Excluding VAT"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_75
msgid "Total Input Tax Paid Line 70 @ 7.5%"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_a_5
msgid "Total No of Branches"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_a_15
msgid "Total Purchases"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_a_10
msgid "Total Sales/Income Exclusive of VAT"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_105
msgid "Total VAT Credit claimable"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_115
msgid "Unrelieved VAT Credit Carried Forward"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_110
msgid "VAT Credit Relieved"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_120
msgid "VAT Payable"
msgstr ""

#. module: l10n_ng
#: model:account.report.line,name:l10n_ng.l10n_ng_tr_c_80
msgid "VAT Payable /(Credit) for Current Month"
msgstr ""

#. module: l10n_ng
#: model:account.report,name:l10n_ng.l10n_ng_tax_report
msgid "VAT Report"
msgstr ""
