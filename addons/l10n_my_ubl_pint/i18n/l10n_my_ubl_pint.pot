# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_my_ubl_pint
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-09 02:43+0000\n"
"PO-Revision-Date: 2024-04-09 02:43+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_my_ubl_pint
#: model:ir.model,name:l10n_my_ubl_pint.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model,name:l10n_my_ubl_pint.model_base_document_layout
msgid "Company Document Layout"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model,name:l10n_my_ubl_pint.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_base_document_layout__account_fiscal_country_id
msgid "Fiscal Country"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_partner__ubl_cii_format
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_users__ubl_cii_format
msgid "Format"
msgstr ""

#. module: l10n_my_ubl_pint
#. odoo-python
#: code:addons/l10n_my_ubl_pint/models/account_edi_xml_pint_my.py:0
#, python-format
msgid ""
"If your business is registered for SST, please provide your registration number in your company details.\n"
"Otherwise, you are not allowed to charge sales or services taxes in the e-Invoice."
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model,name:l10n_my_ubl_pint.model_account_edi_xml_pint_my
msgid ""
"Malaysian implementation of Peppol International (PINT) model for Billing"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model.fields.selection,name:l10n_my_ubl_pint.selection__res_partner__ubl_cii_format__pint_my
msgid "PINT Malaysia"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_base_document_layout__sst_registration_number
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_company__sst_registration_number
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_partner__sst_registration_number
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_users__sst_registration_number
msgid "SST"
msgstr ""

#. module: l10n_my_ubl_pint
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_report_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_bold
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_boxed
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_standard
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_striped
msgid "SST:"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_base_document_layout__ttx_registration_number
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_company__ttx_registration_number
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_partner__ttx_registration_number
#: model:ir.model.fields,field_description:l10n_my_ubl_pint.field_res_users__ttx_registration_number
msgid "TTx"
msgstr ""

#. module: l10n_my_ubl_pint
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_bold
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_boxed
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_standard
#: model_terms:ir.ui.view,arch_db:l10n_my_ubl_pint.l10n_my_ubl_pint_external_layout_striped
msgid "TTx:"
msgstr ""

#. module: l10n_my_ubl_pint
#: model:ir.model.fields,help:l10n_my_ubl_pint.field_base_document_layout__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr ""
