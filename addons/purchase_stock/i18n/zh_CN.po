# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_stock
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# Wil <PERSON>do<PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "% On-Time Delivery"
msgstr "% 准时交货"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d天(s)"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_text\">On-time Rate</span>"
msgstr "<span class=\"o_stat_text\">准时率</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">采购</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_value\">%</span>"
msgstr "<span class=\"o_stat_value\">%</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "<span invisible=\"on_time_rate &gt;= 0\">No On-time Delivery Data</span>"
msgstr "<span invisible=\"on_time_rate &gt;= 0\">无准时交付数据</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "<span> days</span>"
msgstr "<span> 天</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_view_kanban_catalog_purchase_only
msgid "<span>Forecasted: </span>"
msgstr "<span>预测： </span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
msgid "<strong class=\"d-block mt-3\">Shipping address:</strong>"
msgstr "<strong class=\"d-block mt-3\">送货地址：</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong class=\"d-block mt-3\">Shipping address</strong>"
msgstr "<strong class=\"d-block mt-3\">送货地址</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>国际贸易术语解释通则:</strong>"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr "可储存产品是您管理库存的产品。"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"根据产品配置，收货数量可以通过以下机制自动计算：\n"
"  - 手动：手动设置行数量\n"
"  - 库存移动：已确认拣货后获取数量\n"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_rule__action
msgid "Action"
msgstr "操作"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__effective_date
msgid "Arrival"
msgstr "到达"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock.py:0
#: model:ir.model.fields.selection,name:purchase_stock.selection__stock_rule__action__buy
#: model:stock.route,name:purchase_stock.route_warehouse0_buy
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_stock_rule
msgid "Buy"
msgstr "购买"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_pull_id
msgid "Buy rule"
msgstr "购买规则"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "Buy to Resupply"
msgstr "购买补给"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_company
msgid "Companies"
msgstr "公司"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__effective_date
msgid "Completion date of the first receipt order."
msgstr "第一张收货单的完成日期。"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "Corresponding receipt not found."
msgstr "没有找到相应的收货。"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__created_purchase_line_ids
msgid "Created Purchase Order Lines"
msgstr "创建的采购订单明细"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_move.py:0
msgid "Currency exchange rate difference"
msgstr "汇率差额"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__product_description_variants
msgid "Custom Description"
msgstr "自定义说明"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__days_to_arrive
msgid "Days To Arrive"
msgstr "抵达前等待日数"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Days needed to confirm a PO"
msgstr "确认采购订单所需的天数"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,help:purchase_stock.field_res_config_settings__days_to_purchase
msgid "Days needed to confirm a PO, define when a PO should be validated"
msgstr "确认采购订单所需要的天数，定义采购订单何时被验证"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__days_to_purchase
msgid "Days to Purchase"
msgstr "采购前置天数"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__delay_pass
msgid "Delay Pass"
msgstr "延迟通过"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_type_id
msgid "Deliver To"
msgstr "交货到"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,help:purchase_stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr "根据所安装的模块，这将允许您定义路线产品:是否会购买，制造，补充订单，等。"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "目的位置类型"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_dest_ids
msgid "Downstream moves alt"
msgstr "下游移动alt"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "代发货地址"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "代发货"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__date
msgid "Effective Date"
msgstr "实际日期"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "Effective Date Last Year"
msgstr "去年实际日期"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__days_to_arrival
msgid "Effective Days To Arrival"
msgstr "抵达前有效天数"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "采购单出现异常:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s):"
msgstr "异常:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Forecast Report"
msgstr "预估报表"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__forecasted_issue
msgid "Forecasted Issue"
msgstr "预测的问题"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__full
msgid "Fully Received"
msgstr "完成接收"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Generate the draft vendor bill."
msgstr "生成供应商草稿账单。"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Go back to the purchase order to generate the vendor bill."
msgstr "返回采购订单生成供应商账单。"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__id
msgid "ID"
msgstr "ID"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoming_picking_count
msgid "Incoming Shipment count"
msgstr "进货计数"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Incoming Shipments"
msgstr "进货"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoterm_location
msgid "Incoterm Location"
msgstr "国际贸易术语位置"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__is_shipped
msgid "Is Shipped"
msgstr "是否要运送"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__is_installed_sale
msgid "Is the Sale Module Installed"
msgstr "销售模块是否已安装"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move
msgid "Journal Entry"
msgstr "日记账分录"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move_line
msgid "Journal Item"
msgstr "日记账项目"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__last_purchase_date
msgid "Last Purchase"
msgstr "最后一次采购"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_procurement_group__purchase_line_ids
msgid "Linked Purchase Order Lines"
msgstr "关联采购订单明细"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__location_final_id
msgid "Location from procurement"
msgstr "采购物资位置"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr "物流"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "批次/序列号"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Manual actions may be needed."
msgstr "可能需要手动动作。"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr "供应商供货提前期。当系统生成重新订购产品的采购订单时，他们将提前几天调度，以应付意外的供应商延迟。"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "最小库存规则"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Move forward expected request creation date by"
msgstr "将预期的请求创建日期提前"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Next transfer(s) impacted:"
msgstr "影响到的下一步调拨:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "No data yet"
msgstr "还没有数据耶"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_help_message_template
msgid "No receipt yet! Automate them with purchase orders."
msgstr "尚无收据！您可用采购订单，自动处理收据。"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__pending
msgid "Not Received"
msgstr "未收到"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_move.py:0
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr "Odoo不能生成 anglo saxon 条目。%s 的总估值为零。"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_view_graph
msgid "On-Time Delivery"
msgstr "准时交货"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__on_time_rate
msgid "On-Time Delivery Rate"
msgstr "准时交货率"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_on_time
msgid "On-Time Quantity"
msgstr "准时的数量"

#. module: purchase_stock
#: model:ir.actions.act_window,name:purchase_stock.action_purchase_vendor_delay_report
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "On-time Delivery"
msgstr "准时交货"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "On-time Rate"
msgstr "准时率"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__orderpoint_id
msgid "Orderpoint"
msgstr "订货点"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_users__on_time_rate
msgid ""
"Over the past x days; the number of products received on time divided by the"
" number of ordered products.x is either the System Parameter "
"purchase_stock.on_time_delivery_days or the default 365"
msgstr ""
"在过去的 X 天里，按时收到的产品数量除以订购的产品数量。其中 X "
"可以是系统参数purchase_stock.on_time_delivery_days值，也可以是默认值365。"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__purchase_order_line_ids
msgid "PO Lines"
msgstr "PO明细行"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__partial
msgid "Partially Received"
msgstr "部分接受"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_category__property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "价格差异科目"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Process all the receipt quantities."
msgstr "处理所有的收货数量。"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_procurement_group
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__group_id
msgid "Procurement Group"
msgstr "补货组"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__group_id
msgid "Procurement group that generated this line"
msgstr "生成这行的补货群组"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_template
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__product_id
msgid "Product"
msgstr "产品"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_category
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__category_id
msgid "Product Category"
msgstr "产品类别"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_replenish
msgid "Product Replenish"
msgstr "补料"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "产品补货混入程序"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__product_supplier_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.warehouse_orderpoint_search_inherit
msgid "Product Supplier"
msgstr "产品供应商"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_product
msgid "Product Variant"
msgstr "产品变体"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__propagate_cancel
msgid "Propagate cancellation"
msgstr "传导取消"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__purchase_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__purchase_line_ids
msgid "Purchase Lines"
msgstr "采购明细"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order
msgid "Purchase Order"
msgstr "采购订单"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__purchase_line_id
msgid "Purchase Order Line"
msgstr "采购订单明细"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_lot__purchase_order_ids
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__purchase_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_help_message_template
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "Purchase Orders"
msgstr "采购订单"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_report
msgid "Purchase Report"
msgstr "采购报表"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "Purchase Security Lead Time"
msgstr "采购安全前置时间"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__purchase_visibility_days
msgid "Purchase Visibility Days"
msgstr "采购可见性日"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_lot__purchase_order_count
msgid "Purchase order count"
msgstr "采购单数"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr "如果你想直接从供应商发货给客户，就填写一个地址。不然的话，留空把货物运到你的公司仓库。"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receipt"
msgstr "收货"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__receipt_status
msgid "Receipt Status"
msgstr "收货状态"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receive Products"
msgstr "接收产品"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Receive the ordered products."
msgstr "收到订购的产品。"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "接收数量方法"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_ids
msgid "Receptions"
msgstr "接收"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__receipt_status
msgid ""
"Red: Late\n"
"            Orange: To process today\n"
"            Green: On time"
msgstr ""
"红色：迟到\n"
"            橙色：今天处理\n"
"            绿色：准时"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr "请求您的供应商交付给您的客户"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/purchase_stock_forecasted/forecasted_details.xml:0
msgid "Requests for quotation"
msgstr "采购在途"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_ids
msgid "Reservation"
msgstr "预留"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "退回拣货"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__route_ids
msgid "Routes"
msgstr "路线"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Schedule request for quotations earlier to avoid delays"
msgstr "提早安排报价请求，以避免延误"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_supplierinfo_replenishment_tree_view
msgid "Set as Supplier"
msgstr "设为供应商"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__show_set_supplier_button
msgid "Show Set Supplier Button"
msgstr "显示设置供应商按钮"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_replenish__show_vendor
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenish_mixin__show_vendor
msgid "Show Vendor"
msgstr "显示供应商"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__show_supplier
msgid "Show supplier column"
msgstr "显示供应商栏"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_move
msgid "Stock Move"
msgstr "库存移动"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order_line__qty_received_method__stock_moves
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_line_view_form_inherit
msgid "Stock Moves"
msgstr "库存移动"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "库存补货报表"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_rule
msgid "Stock Rule"
msgstr "库存规则"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "库存估值层"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "库存规则报表"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "库存供应商补货信息"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "仓库库存补货选项"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__supplier_id
msgid "Supplier"
msgstr "供应商"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "供应商价格表"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_ids
msgid "Supplierinfo"
msgstr "供应商信息"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr "技术字段用于显示代发货地址"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock.py:0
msgid "The following replenishment order has been generated"
msgstr "已生成以下补货订单"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund."
msgstr "您的采购订单上的数量表明少于账单。您应该要求退款。"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"The warehouse of operation type (%(operation_type)s) is inconsistent with "
"location (%(location)s) of reordering rule (%(reordering_rule)s) for product"
" %(product)s. Change the operation type or cancel the request for quotation."
msgstr ""
"操作类型（%(operation_type)s）的仓库，与产品%(product)s的再订购规则（%(reordering_rule)s）的位置（%(location)s）不一致。请更改操作类型，或取消报价请求。"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid ""
"There is no matching vendor price to generate the purchase order for product"
" %s (no vendor defined, minimum quantity not reached, dates not valid, ...)."
" Go on the product form and complete the list of vendors."
msgstr "没有匹配的供应商价格来生成产品%s的采购订单（未定义供应商，未达到最小数量，日期无效，...）。进入产品表格并填写供应商列表。"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase_stock.field_product_template__property_account_creditor_price_difference
msgid ""
"This account is used in automated inventory valuation to record the price "
"difference between a purchase order and its related vendor bill when "
"validating this vendor bill."
msgstr "此账户用于自动库存评估，以在验证此供应商账单时记录采购订单与其相关供应商账单之间的价格差异。"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_category__property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr "这个科目用来评估采购价格和会计成本的价格差异。"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"这增加一个直销路线来应用于产品，以请求您的供应商交付给您的客户。 一旦销售订单被确认，要直销的产品将会产生报价的采购申请。这是按需流量。 "
"请求的送货地址将是客户送货地址，而不是您的仓库。"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr "这将决定进货的作业类型"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid ""
"Those dates couldn’t be modified accordingly on the receipt %s which had "
"already been validated."
msgstr "这些日期无法在已经验证过的收货%s上进行相应的修改。"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "Those dates have been updated accordingly on the receipt %s."
msgstr "这些日期已经在收货上作了相应的更新%s。"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_total
msgid "Total Quantity"
msgstr "数量总计"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__is_storable
msgid "Track Inventory"
msgstr "追踪库存"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_picking
msgid "Transfer"
msgstr "调拨"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid ""
"Unable to cancel purchase order(s): %s since they have receipts that are "
"already done."
msgstr "不能取消采购订单: %s ，因为已有完成的收货单。"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Validate the receipt of all ordered products."
msgstr "验证所有订购产品的收货。"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_replenish__supplier_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenish_mixin__supplier_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_warehouse_orderpoint_tree_editable_inherited_mrp
msgid "Vendor"
msgstr "供应商"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_vendor_delay_report
msgid "Vendor Delay Report"
msgstr "供应商延迟报表"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "Vendor Lead Time"
msgstr "供应商前置时间"

#. module: purchase_stock
#: model_terms:ir.actions.act_window,help:purchase_stock.action_purchase_vendor_delay_report
msgid "Vendor On-time Delivery analysis"
msgstr "供应商准时交货分析"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_stock_replenishment_info_stock_purchase_inherit
msgid "Vendors"
msgstr "供应商"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse_orderpoint__purchase_visibility_days
msgid "Visibility Days applied on the purchase routes."
msgstr "在采购路线上应用 \"可见日\"。"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__picking_type_id
msgid "Warehouse"
msgstr "仓库"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "当产品被购买后，他们可以被送到这个仓库"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%s</b>, <br/> a request for quotation is "
"created to fulfill the need.<br/>Note: This rule will be used in combination"
" with the rules<br/>of the reception route(s)"
msgstr "如果<b>%s</b>中需要产品，<br/>则创建报价请求，以满足需求。<br/>注：此规则将与接收路由的规则<br/>一同使用"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"You cannot decrease the ordered quantity below the received quantity.\n"
"Create a return first."
msgstr ""
"您不能减少订购数量低于接收数量。\n"
"         首先创建一个退货。"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "You must set a Vendor Location for this partner %s"
msgstr "您必须为此合作伙伴设置一个供应商的位置%s"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "天"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "of"
msgstr "的"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "ordered instead of"
msgstr "已订购，替换"
