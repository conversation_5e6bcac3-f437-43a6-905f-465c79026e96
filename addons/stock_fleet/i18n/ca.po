# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_fleet
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <papa<PERSON><PERSON>@pinatell.net>, 2024
# <PERSON><PERSON> <man<PERSON><EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Jonatan Gk, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2025
# Harcogourmet, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(display_name)s (%(load_capacity)s)"
msgstr "%(display_name)s (%(load_capacity)s)"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(volume_capacity)s %(volume_uom)s"
msgstr "%(volume_capacity)s %(volume_uom)s"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(weight_capacity)s %(weight_uom)s"
msgstr "%(weight_capacity)s %(weight_uom)s"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Dock:</strong>"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle Category:</strong>"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle:</strong>"
msgstr ""

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_pivot
msgid "Batch Transfer"
msgstr "Transferència per lots"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Batches by Route"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Calendar"
msgstr "Calendari"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "Categoria del model"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Dock Dispatching"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__dock_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Dock Location"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__driver_id
msgid "Driver"
msgstr "Conductor"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__end_date
msgid "End Date"
msgstr "Data de finalització"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_graph
msgid "Graph View"
msgstr "Vista gràfica"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_location
msgid "Inventory Locations"
msgstr "Ubicacions d'inventari"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_location__is_a_dock
msgid "Is a Dock Location"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Manage Batches"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity
msgid "Max Volume"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_volume_capacity
msgid "Max Volume (m³)"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity
msgid "Max Weight"
msgstr "Pes màxim"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Next 7 Days"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Operation Type"
msgstr "Tipus d'operació"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Own Fleet"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Scheduled Date"
msgstr "Data programada"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "Sequence"
msgstr "Seqüència"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Volume"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Weight"
msgstr "Pes de l'enviament"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Statistics"
msgstr "Estadístiques"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Third Party Carrier"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Third Party Provider"
msgstr "Proveïdor de tercers"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Today"
msgstr "Avui"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Tomorrow"
msgstr "Demà"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Volume"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Weight"
msgstr ""

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking
msgid "Transfer"
msgstr "Transferència"

#. module: stock_fleet
#: model:fleet.vehicle.tag,name:stock_fleet.vehicle_tag_transport
msgid "Transport"
msgstr "Transport"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Transport Management"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_weight_capacity
msgid "Vehcilce Payload Capacity"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle"
msgstr "Vehicle"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_category_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle Category"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Volume"
msgstr "Volum"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_volume_percentage
msgid "Volume %"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Unitat de volum de l' etiqueta de mesura"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Weight"
msgstr "Pes"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_weight_percentage
msgid "Weight %"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Unitat de pes de l'etiqueta de mesura"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking__zip
msgid "Zip"
msgstr "Codi Postal"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "semi-truck"
msgstr ""
