# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_fleet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(display_name)s (%(load_capacity)s)"
msgstr "%(display_name)s (%(load_capacity)s)"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(volume_capacity)s %(volume_uom)s"
msgstr "%(volume_capacity)s %(volume_uom)s"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(weight_capacity)s %(weight_uom)s"
msgstr "%(weight_capacity)s %(weight_uom)s"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Dock:</strong>"
msgstr "<strong>도킹:</strong>"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle Category:</strong>"
msgstr "<strong>차량 카테고리:</strong>"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle:</strong>"
msgstr "<strong>차량:</strong>"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_pivot
msgid "Batch Transfer"
msgstr "일괄 이송 작업"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Batches by Route"
msgstr "루트별 배치"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Calendar"
msgstr "캘린더"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "모델 카테고리"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Dock Dispatching"
msgstr "도크 배차"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__dock_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Dock Location"
msgstr "도크 위치"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__driver_id
msgid "Driver"
msgstr "운전자"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__end_date
msgid "End Date"
msgstr "종료일"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_graph
msgid "Graph View"
msgstr "그래프 화면"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_location
msgid "Inventory Locations"
msgstr "재고 공간"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_location__is_a_dock
msgid "Is a Dock Location"
msgstr "도크 위치"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Manage Batches"
msgstr "배치 관리"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity
msgid "Max Volume"
msgstr "최대 용량"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_volume_capacity
msgid "Max Volume (m³)"
msgstr "최대 부피 (m³)"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity
msgid "Max Weight"
msgstr "최대 중량"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Next 7 Days"
msgstr "다음 7일"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Operation Type"
msgstr "작업 유형"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Own Fleet"
msgstr "자체 차량"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Scheduled Date"
msgstr "예정일"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "Sequence"
msgstr "순서"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Volume"
msgstr "배송량"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Weight"
msgstr "선적 중량"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Statistics"
msgstr "통계"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Third Party Carrier"
msgstr "외부 운송업체"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Third Party Provider"
msgstr "제3자 제공업체"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Today"
msgstr "오늘"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Tomorrow"
msgstr "내일"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Volume"
msgstr "총 배송 물량"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Weight"
msgstr "총 배송 중량"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking
msgid "Transfer"
msgstr "전송"

#. module: stock_fleet
#: model:fleet.vehicle.tag,name:stock_fleet.vehicle_tag_transport
msgid "Transport"
msgstr "교통비"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Transport Management"
msgstr "운송 관리"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_weight_capacity
msgid "Vehcilce Payload Capacity"
msgstr "차량 탑재 용량"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle"
msgstr "차량"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_category_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle Category"
msgstr "차량 카테고리"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Volume"
msgstr "부피"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_volume_percentage
msgid "Volume %"
msgstr "부피 %"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__volume_uom_name
msgid "Volume unit of measure label"
msgstr "부피 단위 라벨"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Weight"
msgstr "무게"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_weight_percentage
msgid "Weight %"
msgstr "무게 %"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__weight_uom_name
msgid "Weight unit of measure label"
msgstr "무게 단위 꼬리표"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking__zip
msgid "Zip"
msgstr "우편번호"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "semi-truck"
msgstr "semi-truck"
