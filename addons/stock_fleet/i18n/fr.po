# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_fleet
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(display_name)s (%(load_capacity)s)"
msgstr "%(display_name)s (%(load_capacity)s)"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(volume_capacity)s %(volume_uom)s"
msgstr "%(volume_capacity)s %(volume_uom)s"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(weight_capacity)s %(weight_uom)s"
msgstr "%(weight_capacity)s %(weight_uom)s"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Dock:</strong>"
msgstr "<strong>Borne :</strong>"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle Category:</strong>"
msgstr "<strong>Catégorie du véhicule :</strong>"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle:</strong>"
msgstr "<strong>Véhicule :</strong>"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_pivot
msgid "Batch Transfer"
msgstr "Transfert par lot"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Batches by Route"
msgstr "Lots par route"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Calendar"
msgstr "Calendrier"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "Catégorie du modèle"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Dock Dispatching"
msgstr "Répartition des bornes"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__dock_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Dock Location"
msgstr "Emplacement de la borne"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__driver_id
msgid "Driver"
msgstr "Conducteur"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__end_date
msgid "End Date"
msgstr "Date de fin"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_graph
msgid "Graph View"
msgstr "Vue graphique"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_location
msgid "Inventory Locations"
msgstr "Emplacements de l'inventaire"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_location__is_a_dock
msgid "Is a Dock Location"
msgstr "Est un emplacement de borne"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Manage Batches"
msgstr "Gérer les lots"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity
msgid "Max Volume"
msgstr "Volume max"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_volume_capacity
msgid "Max Volume (m³)"
msgstr "Volume max (m³)"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity
msgid "Max Weight"
msgstr "Poids max"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Next 7 Days"
msgstr "Les 7 jours suivants"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Operation Type"
msgstr "Type d'opération"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Own Fleet"
msgstr "Propre parc automobile"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Scheduled Date"
msgstr "Date planifiée"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "Sequence"
msgstr "Séquence"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Volume"
msgstr "Volume d'expédition"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Weight"
msgstr "Poids d'expédition"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Statistics"
msgstr "Statistiques"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Third Party Carrier"
msgstr "Transporteur tiers"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Third Party Provider"
msgstr "Fournisseur tiers"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Today"
msgstr "Aujourd'hui"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Tomorrow"
msgstr "Demain"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Volume"
msgstr "Volume total de l'expédition"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Weight"
msgstr "Poids total de l'expédition"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking
msgid "Transfer"
msgstr "Transférer"

#. module: stock_fleet
#: model:fleet.vehicle.tag,name:stock_fleet.vehicle_tag_transport
msgid "Transport"
msgstr "Transport"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Transport Management"
msgstr "Gestion du transport"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_weight_capacity
msgid "Vehcilce Payload Capacity"
msgstr "Capacité de la charge utile du véhicule"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle"
msgstr "Véhicule"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_category_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle Category"
msgstr "Catégorie de véhicule"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Volume"
msgstr "Volume"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_volume_percentage
msgid "Volume %"
msgstr "Volume %"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Étiquette d'unité de mesure de volume"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Weight"
msgstr "Poids"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_weight_percentage
msgid "Weight %"
msgstr "Poids %"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Intitulé de l'unité de mesure de poids "

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking__zip
msgid "Zip"
msgstr "Code postal"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "semi-truck"
msgstr "semi-remorque"
