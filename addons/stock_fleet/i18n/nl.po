# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_fleet
# 
# Translators:
# <PERSON><PERSON>, 2024
# W<PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(display_name)s (%(load_capacity)s)"
msgstr "%(display_name)s (%(load_capacity)s)"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(volume_capacity)s %(volume_uom)s"
msgstr "%(volume_capacity)s %(volume_uom)s"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(weight_capacity)s %(weight_uom)s"
msgstr "%(weight_capacity)s %(weight_uom)s"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Dock:</strong>"
msgstr "<strong>Dok:</strong>"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle Category:</strong>"
msgstr "<strong>Voertuigcategorie:</strong>"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle:</strong>"
msgstr "<strong>Voertuig:</strong>"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_pivot
msgid "Batch Transfer"
msgstr "Batchverplaatsingen"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Batches by Route"
msgstr "Batches per route"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Calendar"
msgstr "Agenda"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "Categorie van het model"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Dock Dispatching"
msgstr "Dock Verzending"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__dock_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Dock Location"
msgstr "Locatie dok"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__driver_id
msgid "Driver"
msgstr "Bestuurder"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__end_date
msgid "End Date"
msgstr "Einddatum"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_graph
msgid "Graph View"
msgstr "Grafiek weergave"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_location
msgid "Inventory Locations"
msgstr "Voorraadlocaties"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_location__is_a_dock
msgid "Is a Dock Location"
msgstr "Is een doklocatie"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Manage Batches"
msgstr "Batches beheren"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity
msgid "Max Volume"
msgstr "Max Volume"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_volume_capacity
msgid "Max Volume (m³)"
msgstr "Max. volume (m³)"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity
msgid "Max Weight"
msgstr "Max. gewicht"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Next 7 Days"
msgstr "Volgende 7 dagen"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Operation Type"
msgstr "Bewerkingssoort"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Own Fleet"
msgstr "Eigen vloot"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Scheduled Date"
msgstr "Geplande datum"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "Sequence"
msgstr "Reeks"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Volume"
msgstr "Verzendvolume"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Weight"
msgstr "Aflevergewicht"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Statistics"
msgstr "Statistieken"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Third Party Carrier"
msgstr "Derde vervoerder"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Third Party Provider"
msgstr "Externe provider"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Today"
msgstr "Vandaag"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Tomorrow"
msgstr "Morgen"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Volume"
msgstr "Totaal Verzendvolume"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Weight"
msgstr "Totaal Verzendgewicht"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking
msgid "Transfer"
msgstr "Verplaatsing"

#. module: stock_fleet
#: model:fleet.vehicle.tag,name:stock_fleet.vehicle_tag_transport
msgid "Transport"
msgstr "Transport"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Transport Management"
msgstr "Transportmanagement"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_weight_capacity
msgid "Vehcilce Payload Capacity"
msgstr "Laadvermogen voertuig"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle"
msgstr "Voertuig"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_category_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle Category"
msgstr "Voertuigcategorie"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Volume"
msgstr "Volume"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_volume_percentage
msgid "Volume %"
msgstr "Volume %"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Volume maateenheidslabel"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Weight"
msgstr "Gewicht"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_weight_percentage
msgid "Weight %"
msgstr "Gewicht %"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Gewicht maateenheid label"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking__zip
msgid "Zip"
msgstr "Postcode"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "semi-truck"
msgstr "oplegger"
