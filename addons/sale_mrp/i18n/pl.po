# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_mrp
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Manufactured</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/><b>Wyprodukowane</b>"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Confirmed</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/><b>Potwierdzone</b>"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>In progress</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/><b>W trakcie</b>"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-times\"/> <b>Cancelled</b>"
msgstr "<i class=\"fa fa-fw fa-times\"/><b>Anulowane</b>"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Produkcja</span>"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.mrp_production_form_view_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr "<span class=\"o_stat_text\">Sprzedaż</span>"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<strong>Manufacturing Orders</strong>"
msgstr "<strong>Produkcja zamówień</strong>"

#. module: sale_mrp
#. odoo-python
#: code:addons/sale_mrp/models/mrp_bom.py:0
msgid ""
"As long as there are some sale order lines that must be delivered/invoiced and are related to these bills of materials, you can not remove them.\n"
"The error concerns these products: %s"
msgstr ""
"Dopóki istnieją jakieś linie zamówień sprzedaży, które muszą być "
"dostarczone/fakturowane i są powiązane z tymi listami materiałów, nie można "
"ich usunąć. Błąd dotyczy tych produktów: %s"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr "Zestawienie materiałowe"

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_sale_order__mrp_production_count
msgid "Count of MO generated"
msgstr "Liczba wygenerowanych MO"

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_mrp_production__sale_order_count
msgid "Count of Source SO"
msgstr "Liczba źródłowych SO"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "Date:"
msgstr "Data:"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_account_move_line
msgid "Journal Item"
msgstr "Pozycja zapisu"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr "Zamówienie produkcji"

#. module: sale_mrp
#. odoo-python
#: code:addons/sale_mrp/models/sale_order.py:0
msgid "Manufacturing Orders Generated by %s"
msgstr "Zamówienia Produkcji Stworzone przez %s"

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_sale_order__mrp_production_ids
msgid "Manufacturing orders associated with this sales order."
msgstr "Zlecenia produkcji powiązane z tym zamówieniem sprzedaży."

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_mrp_production__sale_line_id
msgid "Origin sale order line"
msgstr "Pochodzenie pozycji zamówienia sprzedaży"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Przesunięcia produktu (pozycja przesunięcia zasobów)"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_sale_order
msgid "Sales Order"
msgstr "Zamówienie sprzedaży"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pozycja zamówienia sprzedaży"

#. module: sale_mrp
#. odoo-python
#: code:addons/sale_mrp/models/mrp_production.py:0
msgid "Sources Sale Orders of %s"
msgstr "Źródła Zamówienia Sprzedaży %s"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_stock_rule
msgid "Stock Rule"
msgstr "Reguła zasobów"
