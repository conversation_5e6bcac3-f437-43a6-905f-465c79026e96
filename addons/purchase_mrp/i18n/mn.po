# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_mrp
# 
# Translators:
# Munkhbaatar Gombosuren, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.purchase_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Үйлдвэрлэл</span>"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.mrp_production_form_view_purchase
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Худалдан авалт</span>"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Орц найрлагын тойм тайлан"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr "Орц"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Орцын мөр"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
msgid "Components cost share have to be positive or equals to zero."
msgstr ""
"Орцын бүрэлдэхүүний өртөг хуваарилалт нь тэгээс их эсвэл тэгтэй тэнцүү байх "
"ёстой."

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_bom_line__cost_share
msgid "Cost Share (%)"
msgstr "Өртөг хуваарилах (%)"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_purchase_order__mrp_production_count
msgid "Count of MO Source"
msgstr "Үйлдвэрийн захиалгын сурвалжын тоо"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_production__purchase_order_count
msgid "Count of generated PO"
msgstr "Үүсгэгдсэн үйлдвэрийн захиалгын тоо"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_account_move_line
msgid "Journal Item"
msgstr "Журналын бичилт"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Үйлдвэрийн захиалгын товчоо тайлан"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr "Үйлдвэрлэлийн захиалга"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/purchase.py:0
msgid "Manufacturing Source of %s"
msgstr "%s-ын сурвалж үйлдвэрлэл"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/stock_move.py:0
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo англи-саксоны бүртгэлийн бичилт үүсгэж чадсангүй. %s-ын нийт өртөгийн "
"дүн нь тэг байна."

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order
msgid "Purchase Order"
msgstr "Худалдан авалтын захиалга"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Худалдан авалтын захиалгын мөр"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_production.py:0
msgid "Purchase Order generated from %s"
msgstr "%s-с үүсгэгдсэн худалдан авалтын захиалга"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_stock_move
msgid "Stock Move"
msgstr "Барааны хөдөлгөөн"

#. module: purchase_mrp
#: model:ir.model.fields,help:purchase_mrp.field_mrp_bom_line__cost_share
msgid ""
"The percentage of the component repartition cost when purchasing a kit.The "
"total of all components' cost have to be equal to 100."
msgstr ""
"Кит бүтээгдэхүүн худалдан авах үед орц бүрийн өртгийн хувийг заана. Бүх "
"орцын нийт хувь 100 байх ёстой."

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's component have to be 100"
msgstr ""
"Бүтээгдэхүүний жорыг (BoM) бүрдүүлэгч орцуудын өртгийн нийт хувь 100 байх "
"ёстой"
