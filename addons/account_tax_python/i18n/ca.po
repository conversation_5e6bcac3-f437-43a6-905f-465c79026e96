# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# Harcogourmet, 2024
# Noemi Pla, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Noemi Pla, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"    - Grups d'impostos: l'impost és un conjunt de sub-impostos.\n"
"    - Fix: L'import de l'impost roman igual independentment del preu.\n"
"    - Percentatge: l'import de l'impost és un % del preu:\n"
"        p.e 100 * (1 + 10%) = 110  (preu no inclòs)\n"
"        p.e 110 / (1 + 10%) = 100 (preu inclòs)\n"
"    - Percentatge amb impostos inclosos: l'import de l'impost és una fracció del preu :\n"
"        p.e 180 / (1 - 10%) = 200 (preu no inclòs)\n"
"        p.e 200 * (1 - 10%) = 180 (preu inclòs)\n"
"        "

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__formula
msgid ""
"Compute the amount of the tax.\n"
"\n"
":param base: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: A object representing the product\n"
msgstr ""
"Calcula l'import de l'impost.\n"
"\n"
":param base: float, import real sobre el qual s'aplica l'impost\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: un objecte que representa el producte\n"

#. module: account_tax_python
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
msgid "Custom Formula"
msgstr "Fórmula personalitzada"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula
msgid "Formula"
msgstr "Fórmula"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula_decoded_info
msgid "Formula Decoded Info"
msgstr "Informació de la fórmula descodificada"

#. module: account_tax_python
#. odoo-python
#: code:addons/account_tax_python/models/account_tax.py:0
msgid "Malformed formula '%(formula)s' at position %(position)s"
msgstr "Fórmula incorrecta '%(formula)s' en la posició %(position)s"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Impost"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
msgid "Tax Computation"
msgstr "Càlcul d'impostos"
