# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Група податків: Податок являє собою сукупність підподатків.\n"
"- Фіксований: сума податку залишається незмінною незалежно від ціни.\n"
"- Відсоток: Сума податку становить % від ціни:\n"
"наприклад, 100 * (1 + 10%) = 110 (без урахування ціни).\n"
"наприклад, 110 / (1 + 10%) = 100 (з урахуванням ціни).\n"
"- Відсотковий податок включено: Сума податку є часткою від ціни:\n"
"наприклад, 180 / (1 - 10%) = 200 (без урахування ціни)\n"
"наприклад, 200 * (1 - 10%) = 180 (ціна включена)."

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__formula
msgid ""
"Compute the amount of the tax.\n"
"\n"
":param base: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: A object representing the product\n"
msgstr ""
"Обчисліть суму податку.\n"
"\n"
":param base: float, фактична сума, до якої застосовується податок\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: Об’єкт, що представляє товар\n"

#. module: account_tax_python
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
msgid "Custom Formula"
msgstr "Власна формула"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula
msgid "Formula"
msgstr "Формула"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__formula_decoded_info
msgid "Formula Decoded Info"
msgstr "Розшифрована інформація формули"

#. module: account_tax_python
#. odoo-python
#: code:addons/account_tax_python/models/account_tax.py:0
msgid "Malformed formula '%(formula)s' at position %(position)s"
msgstr "Неправильна формула '%(formula)s' на позиції %(position)s"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Податок"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
msgid "Tax Computation"
msgstr "Розрахунок податку"
