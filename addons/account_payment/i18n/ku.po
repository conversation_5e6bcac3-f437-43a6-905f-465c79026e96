# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid ""
"#%(number)s - Installment of <strong>%(amount)s</strong> due on <strong "
"class=\"text-primary\">%(date)s</strong>"
msgstr ""

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "%s day(s) overdue"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>پەیوەندیکردن: </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"ئێستا پارە بدە</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/> ئێستا پارە بدە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"<span class=\"d-none d-md-inline\">ڕێگەپێدراو</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"<span class=\"d-none d-md-inline\">پارەی دا</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> پارەی دا"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> هەڵپەسێردراو"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Processing Payment"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> پرۆسێسکردنی پارەدان"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr "<span class=\"d-none d-md-inline\"> هەڵپەسێردراو</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "<strong>Full Amount</strong><br/>"
msgstr "<strong>بڕی تەواو</strong><br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>Installment</strong>\n"
"                                        <br/>"
msgstr ""
"<strong>قیست</strong>\n"
"<br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "A discount will be applied if the customer pays before %s included."
msgstr "داشکاندن دەکرێت ئەگەر کڕیار پێشتر پارە بدات %s لەخۆدەگرێت."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"A payment has already been made on this invoice, please make sure to not pay"
" twice."
msgstr ""
"پێشتر پارەیەک لەسەر ئەم فاکتورەیە دراوە، تکایە دڵنیابە کە دوو جار پارە "
"نەدەیت."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A payment transaction with reference %s already exists."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A token is required to create a new payment transaction."
msgstr ""

#. module: account_payment
#: model:onboarding.onboarding.step,button_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Activate Stripe"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Amount"
msgstr "بڕ"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "بڕی بەردەست بۆ گەڕاندنەوەی پارە"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__invoice_amount_due
msgid "Amount Due"
msgstr "بڕی پارە کە دەبێ بدرێت"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
msgid "Amount paid"
msgstr "بڕی خەرجکراو"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"ئایا دڵنیای کە دەتەوێت مامەڵە ڕێگەپێدراوەکە هەڵبوەشێنیتەوە؟ ئەم کارە "
"ناتوانرێت پووچەڵ بکرێتەوە."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "مامەڵە ڕێگەپێدراوەکان"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Close"
msgstr "داخستن"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "کۆد"

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "دروستکراوە لەلایەن..."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "دروستکراوە لە"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "دراو"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__discount_date
msgid "Discount Date"
msgstr "بەرواری داشکاندن"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__display_open_installments
msgid "Display Open Installments"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr ""
"تەواو بوو، پارەدانی ئۆنلاینەکەت بە سەرکەوتوویی پرۆسێس کراوە. سوپاس بۆ "
"داواکاریەکەت."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__epd_info
msgid "Early Payment Discount Information"
msgstr "زانیاری داشکاندنی پارەی پێشوەختە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Early Payment Discount of"
msgstr "داشکاندنی پارەی پێشوەختە لە"

#. module: account_payment
#: model:onboarding.onboarding.step,description:account_payment.onboarding_onboarding_step_payment_provider
msgid "Enable credit & debit card payments supported by Stripe."
msgstr ""

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__full_only
msgid "Full Only"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "دروستکردنی لینکی پارەدانی فرۆشتن"

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "دروستکردنی لینکی پارەدان"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__has_eligible_epd
msgid "Has Eligible Epd"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid ""
"Impossible to pay all the overdue invoices if they don't share the same "
"currency."
msgstr "مەحاڵە هەموو فاکتورە دواکەوتووەکان بدەیت ئەگەر هەمان دراویان نەبێت."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr "بە ئۆنلاین پارەدانی فاکتورە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "فاکتورە(ەکان)"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
msgid "Invoices"
msgstr "فاکتورەکان"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Invoices &amp; Bills"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "ژمارەی فاکتورەکان"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_account_payment
msgid "Invoices to pay"
msgstr "فاکتورەکان بۆ پارەدان"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "دوایین نوێکردنەوە لەلایەن..."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "دوایین نوێکردنەوە لە..."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "زۆرترین گەڕاندنەوەی پارە ڕێگەپێدراوە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_link_wizard__form_inherit_account_payment
msgid "Next Installments"
msgstr "قیستەکانی داهاتوو"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from providers allowing to capture the amount are "
"available."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from providers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""

#. module: account_payment
#: model:onboarding.onboarding.step,step_image_alt:account_payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Online Payments"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr ""

#. module: account_payment
#: model:onboarding.onboarding.step,title:account_payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "پارەدانی ئۆنلاین"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "Online payment option is not enabled in Configuration."
msgstr "بژاردەی پارەدانی ئۆنلاین لە ڕێکخستن چالاک نەکراوە."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same company."
msgstr "فاکتورە دواکەوتووەکان پێویستە هەمان کۆمپانیا هاوبەش بن."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same currency."
msgstr "فاکتورە دواکەوتووەکان دەبێت هەمان دراویان هەبێت."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same partner."
msgstr "فاکتورە دواکەوتووەکان دەبێت هەمان هاوبەشیان هەبێت."

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__partial
msgid "Partial"
msgstr "بەشێکی"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay"
msgstr "پارەدان"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Pay Invoice"
msgstr "پارەدانی فاکتورە"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr "پارەدانی فاکتورەکان بە شێوەی ئۆنلاین"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_docs_entry
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "ئێستا پارە بدە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "ئێستا پارە بدە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Pay overdue"
msgstr "پارەی دواکەوتوو بدە"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "پارەدان"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "بڕی پارەدان"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr "دابینکەری پارەدان"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr "دابینکەری پارەدان"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr ""

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "مامەڵەی پارەدان"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "مامەڵەی پارەدان"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "پارەدان"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "Please log in to pay your overdue invoices"
msgstr "تکایە بچۆرە ژوورەوە بۆ ئەوەی فاکتورە دواکەوتووەکانت بدەیت"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid "Provider"
msgstr "دابینکەر"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Reference"
msgstr "سەرچاوە"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refund"
msgstr "گەڕاندنەوە"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "بڕی گەڕاندنەوەی پارە"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "بڕی گەڕاوە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "گەڕاندنەوەکان"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr "ژمارەی گەڕاندنەوەی پارە"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "سەرچاوەی پارەدان"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "دۆخ"

#. module: account_payment
#: model:onboarding.onboarding.step,done_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "هەنگاوی تەواو بوو!"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The access token is invalid."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
msgid ""
"The payment related to the transaction with reference %(ref)s has been "
"posted: %(link)s"
msgstr ""
"ئەو پارەیەی کە پەیوەندی بە مامەڵەکەوە هەیە بە ئاماژە %(ref)s پۆست کراوە: "
"%(link)s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The provided parameters are invalid."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There are pending transactions for this invoice."
msgstr "مامەڵەی هەڵپەسێردراو هەیە بۆ ئەم فاکتورەیە."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There is no amount to be paid."
msgstr "هیچ بڕە پارەیەک نییە کە بدرێت."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "هەڵەیەک لە پرۆسێسکردنی پارەکەتدا هەبوو: فاکتورەی نادروست."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "هەڵەیەک لە پرۆسێسکردنی پارەکەتدا ڕوویدا: مامەڵەکە شکستی هێنا. <br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice cannot be paid online."
msgstr "ئەم فاکتورەیە ناتوانرێت بە شێوەی ئۆنلاین بدرێت."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice has already been paid."
msgstr "ئەم فاکتورەیە پێشتر خەرج کراوە."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice isn't posted."
msgstr "ئەم فاکتورەیە پۆست نەکراوە."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This is not an outgoing invoice."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_count
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_count
msgid "Transaction Count"
msgstr "ژمارەی مامەڵەکان"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
msgid "Transactions"
msgstr "مامەڵەکان"

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__none
msgid "Unsupported"
msgstr "پشتگیری نەکراوە"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "پووچەڵکردنەوەی مامەڵە"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked providers(s): %s"
msgstr ""
"ناتوانیت شێوازێکی پارەدان بسڕیتەوە کە بەستراوەتەوە بە دابینکەرێکەوە لە دۆخی چالاککراو یان تاقیکردنەوەدا.\n"
"دابینکەر(ەکان)ی پەیوەستکراو: %s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_provider.py:0
msgid ""
"You cannot uninstall this module as payments using this payment method "
"already exist."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due in %s day(s)"
msgstr ""

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due today"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "has been applied."
msgstr "جێبەجێ کراوە."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "overdue"
msgstr "دواکەوتووە"
