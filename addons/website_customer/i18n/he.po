# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# NoaFarkash, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to references"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid ""
"<i class=\"fa fa-info-circle me-2\"/>\n"
"                        There are no matching customers found for the selected country. Displaying results across all countries instead."
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Open map\" "
"title=\"Open map\"/>"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> הכל"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "<span class=\"small text-muted\">Implemented by</span>"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "פעיל"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/controllers/main.py:0
msgid "All Countries"
msgstr "כל הארצות"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/controllers/main.py:0
msgid "All Industries"
msgstr "כל התעשיות"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "בארכיון"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
msgid "Back to references list"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "יכול לפרסם"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "שם קטגוריה"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "מחלקה"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "סגור"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Countries Filter"
msgstr "סנן מדינות"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "צור תגית איש קשר חדשה"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL CUSTOMERS"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Enter a short description"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Country"
msgstr "סנן לפי ארץ"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Industry"
msgstr "סנן לפי סוג תעשייה"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Tags"
msgstr "סנן לפי תגיות"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,help:website_customer.field_res_users__website_tag_ids
msgid "Filter published customers on the .../customers website page"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filters"
msgstr "מסננים"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Go to customer"
msgstr "עבור ללקוח"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "מזהה"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Industry Filter"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "מפורסם"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr "נהל תגיות אנשי קשר כדי לסווג אותם טוב יותר למטרות מעקב וניתוח."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No results found for \""
msgstr "לא נמצאו תוצאות עבור \""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "Open countries dropdown"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "Open industries dropdown"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our references"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "תג שותף"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr ""
"תגיות שותפים - ניתן להשתמש בתגים אלה באתר לאיתור לקוחות לפי מגזר, או ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "לקוחות"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
msgid "References"
msgstr "מזהים"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "References Page"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "חיפוש"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr "חיפוש תגית שותף"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See all customers"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See countries filters"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See industries filters"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Show Map"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Tags Filter"
msgstr "סינון תגיות"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr "כתובת האתר המלאה לגישה למסמך דרך אתר האינטרנט."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "גלוי באתר האינטרנט הנוכחי"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "אתר אינטרנט"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "תגיות אתר אינטרנט"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "כתובת אתר אינטרנט"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "תגיות אתר אינטרנט"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "מפת העולם"
