# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_nuvei
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:38+0000\n"
"PO-Revision-Date: 2025-03-19 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "%(payment_method)s requires both a first and last name."
msgstr ""

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (%(reason)s). Please"
" try again."
msgstr ""

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__code
msgid "Code"
msgstr "Kod"

#. module: payment_nuvei
#: model_terms:ir.ui.view,arch_db:payment_nuvei.payment_provider_form
msgid "Merchant Identifier"
msgstr ""

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "No transaction found matching reference %(ref)s."
msgstr "Nie znaleziono transakcji pasującej do referencji %(ref)s."

#. module: payment_nuvei
#: model:ir.model.fields.selection,name:payment_nuvei.selection__payment_provider__code__nuvei
msgid "Nuvei"
msgstr ""

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__nuvei_merchant_identifier
msgid "Nuvei Merchant Identifier"
msgstr ""

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__nuvei_secret_key
msgid "Nuvei Secret Key"
msgstr ""

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__nuvei_site_identifier
msgid "Nuvei Site Identifier"
msgstr ""

#. module: payment_nuvei
#: model:ir.model,name:payment_nuvei.model_payment_provider
msgid "Payment Provider"
msgstr "Dostawca Płatności"

#. module: payment_nuvei
#: model:ir.model,name:payment_nuvei.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcja płatności"

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr ""

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "Received data with missing reference."
msgstr ""

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid ""
"Received invalid transaction status %(status)s and reason '%(reason)s'."
msgstr ""

#. module: payment_nuvei
#: model_terms:ir.ui.view,arch_db:payment_nuvei.payment_provider_form
msgid "Secret Key"
msgstr "Sekretny klucz"

#. module: payment_nuvei
#: model_terms:ir.ui.view,arch_db:payment_nuvei.payment_provider_form
msgid "Site Identifier"
msgstr ""

#. module: payment_nuvei
#: model:ir.model.fields,help:payment_nuvei.field_payment_provider__nuvei_merchant_identifier
msgid "The code of the merchant account to use with this provider."
msgstr ""

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "The customer left the payment page."
msgstr ""

#. module: payment_nuvei
#: model:ir.model.fields,help:payment_nuvei.field_payment_provider__nuvei_site_identifier
msgid "The site identifier code associated with the merchant account."
msgstr ""

#. module: payment_nuvei
#: model:ir.model.fields,help:payment_nuvei.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Kod techniczny tego dostawcy usług płatniczych."
