# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_loyalty_delivery
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_reward.py:0
msgid " (Max %s)"
msgstr ""

#. module: sale_loyalty_delivery
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
msgid "( Max"
msgstr ""

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_program.py:0
msgid "Automatic promotion: free shipping on orders higher than $50"
msgstr ""

#. module: sale_loyalty_delivery
#: model:ir.model.fields.selection,name:sale_loyalty_delivery.selection__loyalty_reward__reward_type__shipping
msgid "Free Shipping"
msgstr "Brezplačna dostava"

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/sale_order.py:0
msgid "Free Shipping - %s"
msgstr ""

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_reward.py:0
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_form_inherit_loyalty_delivery
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
msgid "Free shipping"
msgstr "Brezplačna dostava"

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_loyalty_program
msgid "Loyalty Program"
msgstr "Program zvestobe"

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Nagrada za zvestobo"

#. module: sale_loyalty_delivery
#: model:ir.model.fields,field_description:sale_loyalty_delivery.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr ""

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"
