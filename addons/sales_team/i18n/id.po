# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sales_team
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>New</span>"
msgstr "<span>Baru</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>Reporting</span>"
msgstr "Laporan"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>View</span>"
msgstr "<span>Lihat</span>"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction
msgid "Action Needed"
msgstr "Tindakan Diperluka"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__active
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__active
msgid "Active"
msgstr "Aktif"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "Jenis Aktivitas"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Add a Team Member"
msgstr "Tambahkan Anggota Tim"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__crm_team_member_ids
msgid ""
"Add members to automatically assign their documents to this sales team."
msgstr ""
"Tambahkan anggota untuk secara otomatis menetapkan dokumen mereka ke tim "
"sales."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team_member.py:0
msgid ""
"Adding %(user_name)s in this team will remove them from %(team_names)s. "
"Working in multiple teams? Activate the option under Configuration>Settings."
msgstr ""
"Menambahkan %(user_name)s di tim ini akan menghapus mereka dari "
"%(team_names)s. Bekerja di lebih dari satu tim? Aktifkan opsi di bawah "
"Konfigurasi>Pengaturan."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid ""
"Adding %(user_names)s in this team will remove them from %(team_names)s."
msgstr ""
"Menambahkan %(user_names)s di tim ini akan menghapus mereka dari "
"%(team_names)s."

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_manager
msgid "Administrator"
msgstr "Administrator"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_attachment_count
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Big Pretty Button :)"
msgstr "Tombol Besar yang Cantik :)"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_tag
msgid "CRM Tag"
msgstr "Tag CRM"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Cannot delete default team \"%s\""
msgstr "Tidak dapat menghapus tim default \"%s\""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__color
msgid "Color"
msgstr "Warna"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__company_id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__company_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Company"
msgstr "Perusahaan"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "Configuration"
msgstr "Konfigurasi"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor7
msgid "Consulting"
msgstr "Konsultasi"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid "Create CRM Tags"
msgstr "Buat Tag-Tag CRM"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid "Create a Sales Team"
msgstr "Buat Tim Sales"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid "Create an Activity Type"
msgstr "Buat Tipe Kegiatan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_button_name
msgid "Dashboard Button"
msgstr "Tombol Dashboard"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_data
msgid "Dashboard Graph Data"
msgstr "Data Grafik Dashboard"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid "Define a new sales team"
msgstr "Definisikan tim sales baru"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor5
msgid "Design"
msgstr "Desain"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__email
msgid "Email"
msgstr "Email"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__favorite_user_ids
msgid "Favorite Members"
msgstr "Anggota Favorit"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_favorite
msgid ""
"Favorite teams to display them in the dashboard and access them easily."
msgstr ""
"Tim favorit untuk ditampilkan di dashboard dan dengan mudah mengakses "
"mereka."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_follower_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_partner_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Mitra)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Group By..."
msgstr "Kelompokkan Menurut..."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__has_message
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__id
msgid "ID"
msgstr "ID"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,help:sales_team.field_crm_team_member__is_membership_multi
msgid ""
"If True, users may belong to several sales teams. Otherwise membership is "
"limited to a single sales team."
msgstr ""
"Jika TRUE, user dapat berada di lebih dari satu tim sales. Jika tidak, "
"membership dibatasi ke satu tim sales saja."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the Sales "
"Team without removing it."
msgstr ""
"Bila field aktif disetel sebagai false, akan memugkinkan Anda untuk "
"menyembunyikan Tim Sales tanpa menghapus tim. "

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_1920
msgid "Image"
msgstr "Gambar"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_128
msgid "Image (128)"
msgstr "Image (128)"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor4
msgid "Information"
msgstr "Informasi"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_is_follower
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_is_follower
msgid "Is Follower"
msgstr "Adalah Follower"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users__sale_team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr ""
"User utama tim sales. Digunakan terutama untuk pipeline, atau untuk "
"menetapkan tim sales di faktur atau langganan."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_company_ids
msgid "Member Company"
msgstr "Anggota Perusahaan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__member_warning
msgid "Member Warning"
msgstr "Peringatan Anggota"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Members"
msgstr "Anggota"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_warning
msgid "Membership Issue Warning"
msgstr "Peringatan Masalah Membership"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__mobile
msgid "Mobile"
msgstr "Telepon Mobile"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__is_membership_multi
msgid "Multiple Memberships Allowed"
msgstr "Lebih Dari Satu Membership Diizinkan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__name
msgid "Name"
msgstr "Nama"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Action"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Jumlah pesan yang membutuhkan tindakan"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah pesan dengan kesalahan pengiriman"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor8
msgid "Other"
msgstr "Lainnya"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__phone
msgid "Phone"
msgstr "Telepon"

#. module: sales_team
#: model:crm.team,name:sales_team.pos_sales_team
msgid "Point of Sale"
msgstr "POS"

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "Pre-Sales"
msgstr "Pra-Penjualan"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor1
msgid "Product"
msgstr "Produk"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__rating_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__rating_ids
msgid "Ratings"
msgstr "Rating"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Sales"
msgstr "Penjualan"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_tree
msgid "Sales Men"
msgstr "Salesmen"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Sales Person"
msgstr "Sales Person"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__crm_team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "Tim Penjualan"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team_member
msgid "Sales Team Member"
msgstr "Anggota Tim Sales"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_ids
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_member_ids
msgid "Sales Team Members"
msgstr "Anggota Tim Sales"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_all_ids
msgid "Sales Team Members (incl. inactive)"
msgstr "Anggota Tim Sales (termasuk yang tidak aktif)"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_config
#: model:ir.actions.act_window,name:sales_team.crm_team_action_sales
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_ids
msgid "Sales Teams"
msgstr "Penjualan Tim"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_id
msgid "Salesperson"
msgstr "Penjual"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_ids
msgid "Salespersons"
msgstr "Salesperson"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Salesteams Search"
msgstr "Cari Tim Penjualan"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Sample data"
msgstr "Data Contoh"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor3
msgid "Services"
msgstr "Layanan"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_favorite
msgid "Show on dashboard"
msgstr "Tampilkan pada dashboard"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor2
msgid "Software"
msgstr "Perangkat Lunak"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__name
msgid "Tag Name"
msgstr "Nama Tag"

#. module: sales_team
#: model:ir.model.constraint,message:sales_team.constraint_crm_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Nama tag sudah ada!"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.sales_team_crm_tag_action
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_tree
msgid "Tags"
msgstr "Label"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Details"
msgstr "Detail Tim"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Team Leader"
msgstr "Pemimpin tim"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_member_action
msgid "Team Members"
msgstr "Anggota tim"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Team Members are salespersons assigned to specific teams."
msgstr "Anggota Tim adalah salesperson yang ditugaskan ke tim-tim spesifik."

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_pipeline
msgid "Teams"
msgstr "Tim"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__color
msgid "The color of the channel"
msgstr "Warna saluran ini"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__company_id
msgid "The default company for this user."
msgstr "Perusahaan default untuk user ini."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Prepare meeting\")."
msgstr ""
"Mereka mewakili kategori-kategori berbeda untuk kegiatan yang harus Anda "
"lakukan (contohnya \"Telepon\" atau \"Siapkan meeting\")."

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor6
msgid "Training"
msgstr "Pelatihan"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_in_teams_ids
msgid ""
"UX: Give users not to add in the currently chosen team to avoid duplicates"
msgstr ""
"UX: Beritahu user untuk tidak menambahkan tim yang saat ini dipilih untuk "
"menghindari duplikat"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_company_ids
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_company_ids
msgid "UX: Limit to team company or all if no company"
msgstr "UX: Batasi ke perusahaan tim atau semua bila tidak ada perusahaan"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Undefined graph model for Sales Team: %s"
msgstr "Model grafik undefined untuk Tim Sales: %s"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid ""
"Use Sales Teams to organize your sales departments and draw up reports."
msgstr ""
"Gunakan Sales Teams untuk mengatur departemen sales Anda dan membuat "
"laporan."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid ""
"Use Sales Teams to organize your sales departments.\n"
"                Each team will work with a separate pipeline."
msgstr ""
"Gunakan Sales Teams untuk mengatur departemen sales Anda.\n"
"                Setiap tim akan bekerja dengan pipeline yang berbeda."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid ""
"Use Tags to manage and track your Opportunities (product structure, sales "
"type, ...)"
msgstr ""
"Gunakan Tag untuk mengelola dan melacak Opportunities Anda (struktur produk,"
" tipe sales, ...)"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "User"
msgstr "Pengguna"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_company_ids
msgid "User Company"
msgstr "Perusahaan User"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_in_teams_ids
msgid "User In Teams"
msgstr "User Di Tim"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users__sale_team_id
msgid "User Sales Team"
msgstr "User Tim Sales"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman_all_leads
msgid "User: All Documents"
msgstr "Pengguna: Semua Dokumen"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman
msgid "User: Own Documents Only"
msgstr "Pengguna: Hanya Dokumen Milik Sendiri"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_ids
msgid "Users assigned to this team."
msgstr "User ditetapkan ke tim ini."

#. module: sales_team
#: model:crm.team,name:sales_team.salesteam_website_sales
msgid "Website"
msgstr "Website"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid ""
"Working in multiple teams? Activate the option under Configuration>Settings."
msgstr ""
"Bekerja di lebih dari satu tim? Aktifkan opsi di bawah Konfigurasi > "
"Pengaturan."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team_member.py:0
msgid ""
"You are trying to create duplicate membership(s). We found that "
"%(duplicates)s already exist(s)."
msgstr ""
"Anda sedang mencoba untuk membuat membership duplikat. Kita menemukan bahwa "
"%(duplicates)s sudah dibuat."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "e.g. North America"
msgstr "contoh Amerika Utara"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
msgid "e.g. Services"
msgstr "contoh Layanan"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman_all_leads
msgid ""
"the user will have access to all records of everyone in the sales "
"application."
msgstr ""
"pengguna akan memiliki akses ke data semua orang di aplikasi penjualan."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman
msgid "the user will have access to his own data in the sales application."
msgstr ""
"pengguna akan memiliki akses ke datanya sendiri di aplikasi penjualan."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_manager
msgid ""
"the user will have an access to the sales configuration as well as statistic"
" reports."
msgstr ""
"pengguna akan memiliki akses ke konfigurasi penjualan serta laporan "
"statistik."
