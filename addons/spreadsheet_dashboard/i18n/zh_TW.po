# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard
# 
# Translators:
# Wil <PERSON>doo, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py:0
msgid "%s (copy)"
msgstr "%s (副本)"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__access_token
msgid "Access Token"
msgstr "存取權杖(token)"

#. module: spreadsheet_dashboard
#: model:res.groups,name:spreadsheet_dashboard.group_dashboard_manager
msgid "Admin"
msgstr "管理員"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "An error occured while loading the dashboard"
msgstr "載入 Dashboard 時發生錯誤"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.xml:0
msgid "BACK"
msgstr "返回"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.js:0
msgid "Choose a dashboard...."
msgstr "選擇儀表板佈局"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__company_id
msgid "Company"
msgstr "公司"

#. module: spreadsheet_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration
msgid "Configuration"
msgstr "配置"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_share
msgid "Copy of a shared dashboard"
msgstr "共享Dashboard副本"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_date
msgid "Created on"
msgstr "建立於"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__dashboard_ids
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__dashboard_id
msgid "Dashboard"
msgstr "Dashboard"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__dashboard_group_id
msgid "Dashboard Group"
msgstr "Dashboard組別"

#. module: spreadsheet_dashboard
#: model:ir.actions.act_window,name:spreadsheet_dashboard.spreadsheet_dashboard_action_configuration_dashboards
#: model:ir.actions.client,name:spreadsheet_dashboard.ir_actions_dashboard_action
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration_dashboards
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_root
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_list
msgid "Dashboards"
msgstr "Dashboard"

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_view_list
msgid "Data"
msgstr "資料"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__excel_export
msgid "Excel Export"
msgstr "匯出 Excel"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_finance
msgid "Finance"
msgstr "財務"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__group_ids
msgid "Group"
msgstr "組"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_group
msgid "Group of dashboards"
msgstr "儀表板組"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_hr
msgid "Human Resources"
msgstr "人力資源"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__id
msgid "ID"
msgstr "識別號"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__is_published
msgid "Is Published"
msgstr "已發佈"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "Loading..."
msgstr "載入中..."

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_logistics
msgid "Logistics"
msgstr "物流"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__main_data_model_ids
msgid "Main Data Model"
msgstr "主要數據模型"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_marketing
msgid "Marketing"
msgstr "推廣"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__name
msgid "Name"
msgstr "名稱"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "No available dashboard"
msgstr "沒有可用的儀表板"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_figure_container/mobile_figure_container.xml:0
msgid ""
"Only chart figures are displayed in small screens but this dashboard doesn't"
" contain any"
msgstr "細小螢幕只會顯示統計圖表，但目前的Dashboard檢視畫面未有任何圖表。"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__published_dashboard_ids
msgid "Published Dashboard"
msgstr "已發佈Dashboard"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_sales
msgid "Sales"
msgstr "銷售"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__sequence
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__sequence
msgid "Sequence"
msgstr "序列號"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_project
msgid "Services"
msgstr "服務"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard
msgid "Spreadsheet Dashboard"
msgstr "試算表儀表板"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_data
msgid "Spreadsheet Data"
msgstr "試算表數據"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_file_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr "試算表檔案名稱"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_binary_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr "試算表檔案"

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_form
msgid "Spreadsheets"
msgstr "試算表"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__thumbnail
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__thumbnail
msgid "Thumbnail"
msgstr "縮圖"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__full_url
msgid "URL"
msgstr "網址"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_website
msgid "Website"
msgstr "網站"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_group.py:0
msgid "You cannot delete %s as it is used in another module."
msgstr "您無法刪除 %s，因為它已在另一個模塊中使用。"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_share.py:0
msgid "You don't have access to this dashboard. "
msgstr "你無權限存取此Dashboard。"
