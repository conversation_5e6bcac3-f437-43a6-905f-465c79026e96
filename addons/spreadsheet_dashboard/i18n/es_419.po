# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: spreadsheet_dashboard
#: model:res.groups,name:spreadsheet_dashboard.group_dashboard_manager
msgid "Admin"
msgstr "Admin"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "An error occured while loading the dashboard"
msgstr "Ocurrió un error al cargar el tablero"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.xml:0
msgid "BACK"
msgstr "REGRESAR"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.js:0
msgid "Choose a dashboard...."
msgstr "Elegir un tablero..."

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__company_id
msgid "Company"
msgstr "Empresa"

#. module: spreadsheet_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_share
msgid "Copy of a shared dashboard"
msgstr "Copia de una hoja de cálculo compartida"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_date
msgid "Created on"
msgstr "Creado el"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__dashboard_ids
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__dashboard_id
msgid "Dashboard"
msgstr "Tablero"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__dashboard_group_id
msgid "Dashboard Group"
msgstr "Grupo de tablero"

#. module: spreadsheet_dashboard
#: model:ir.actions.act_window,name:spreadsheet_dashboard.spreadsheet_dashboard_action_configuration_dashboards
#: model:ir.actions.client,name:spreadsheet_dashboard.ir_actions_dashboard_action
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration_dashboards
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_root
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_list
msgid "Dashboards"
msgstr "Tableros"

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_view_list
msgid "Data"
msgstr "Datos"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__excel_export
msgid "Excel Export"
msgstr "Exportar a Excel"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_finance
msgid "Finance"
msgstr "Finanzas"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__group_ids
msgid "Group"
msgstr "Grupo"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_group
msgid "Group of dashboards"
msgstr "Grupo de tableros"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_hr
msgid "Human Resources"
msgstr "Recursos humanos"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__id
msgid "ID"
msgstr "ID"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "Loading..."
msgstr "Cargando…"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_logistics
msgid "Logistics"
msgstr "Logística"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__main_data_model_ids
msgid "Main Data Model"
msgstr "Modelo de datos principal"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_marketing
msgid "Marketing"
msgstr "Marketing"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__name
msgid "Name"
msgstr "Nombre"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "No available dashboard"
msgstr "No hay tablero disponible"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_figure_container/mobile_figure_container.xml:0
msgid ""
"Only chart figures are displayed in small screens but this dashboard doesn't"
" contain any"
msgstr ""
"Lo único que se muestra en recuadros pequeños son los gráficos, pero este "
"tablero no tiene ninguno"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__published_dashboard_ids
msgid "Published Dashboard"
msgstr "Tablero publicado"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_sales
msgid "Sales"
msgstr "Ventas"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__sequence
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_project
msgid "Services"
msgstr "Servicios"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard
msgid "Spreadsheet Dashboard"
msgstr "Tablero de Hojas de cálculo"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_data
msgid "Spreadsheet Data"
msgstr "Datos de la hoja de cálculo"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_file_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr "Nombre del archivo de la hoja de cálculo"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_binary_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr "Archivo de la hoja de cálculo "

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_form
msgid "Spreadsheets"
msgstr "Hojas de cálculo"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__thumbnail
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__thumbnail
msgid "Thumbnail"
msgstr "Miniatura"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__full_url
msgid "URL"
msgstr "URL"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_website
msgid "Website"
msgstr "Sitio web"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_group.py:0
msgid "You cannot delete %s as it is used in another module."
msgstr "No puede eliminar %s ya que se usa en otro módulo."

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_share.py:0
msgid "You don't have access to this dashboard. "
msgstr "No tiene acceso a este tablero."
