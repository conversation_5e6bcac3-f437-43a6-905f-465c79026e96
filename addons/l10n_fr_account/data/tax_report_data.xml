<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report" model="account.report">
        <field name="name">Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.fr"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="integer_rounding">HALF-UP</field>
        <field name="column_ids">
            <record id="tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
            <record id="tax_report_adjustment" model="account.report.column">
                <field name="name">Adjustment</field>
                <field name="expression_label">adjustment</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_montant_op_realisees" model="account.report.line">
                <field name="name">A. Amount of operations carried out</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_op_imposables_ht" model="account.report.line">
                        <field name="name">Taxable transactions (excl. VAT)</field>
                        <field name="children_ids">
                            <record id="tax_report_A1" model="account.report.line">
                                <field name="name">A1 - Sales, provision of services</field>
                                <field name="code">box_A1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_A1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_A1.balance_from_tags + box_A1.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_A1_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">A1</field>
                                    </record>
                                    <record id="tax_report_A1_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_A2" model="account.report.line">
                                <field name="name">A2 - Other taxable transactions</field>
                                <field name="code">box_A2</field>
                                <field name="expression_ids">
                                    <record id="tax_report_A2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_A2.balance_from_tags + box_A2.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_A2_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">A2</field>
                                    </record>
                                    <record id="tax_report_A2_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_A3" model="account.report.line">
                                <field name="name">A3 - Intra-Community purchases of services</field>
                                <field name="code">box_A3</field>
                                <field name="expression_ids">
                                    <record id="tax_report_A3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_A3.balance_from_tags + box_A3.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_A3_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">A3</field>
                                    </record>
                                    <record id="tax_report_A3_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_A4" model="account.report.line">
                                <field name="name">A4 - Imports (other than petroleum products)</field>
                                <field name="code">box_A4</field>
                                <field name="expression_ids">
                                    <record id="tax_report_A4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_A4.balance_from_tags + box_A4.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_A4_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">A4</field>
                                    </record>
                                    <record id="tax_report_A4_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_A5" model="account.report.line">
                                <field name="name">A5 - Removal from suspensive tax regime (other than petroleum products)</field>
                                <field name="code">box_A5</field>
                                <field name="expression_ids">
                                    <record id="tax_report_A5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_A5.balance_from_tags + box_A5.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_A5_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">A5</field>
                                    </record>
                                    <record id="tax_report_A5_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_B1" model="account.report.line">
                                <field name="name">B1 - Releases for consumption of petroleum products</field>
                                <field name="code">box_B1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_B1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_B1.balance_from_tags + box_B1.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_B1_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">B1</field>
                                    </record>
                                    <record id="tax_report_B1_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_B2" model="account.report.line">
                                <field name="name">B2 - Intra-Community acquisitions</field>
                                <field name="code">box_B2</field>
                                <field name="expression_ids">
                                    <record id="tax_report_B2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_B2.balance_from_tags + box_B2.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_B2_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">B2</field>
                                    </record>
                                    <record id="tax_report_B2_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_B3" model="account.report.line">
                                <field name="name">B3 - Taxable supplies of electricity, natural gas, heat or cooling in France</field>
                                <field name="code">box_B3</field>
                                <field name="expression_ids">
                                    <record id="tax_report_B3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_B3.balance_from_tags + box_B3.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_B3_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">B3</field>
                                    </record>
                                    <record id="tax_report_B3_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_B4" model="account.report.line">
                                <field name="name">B4 - Purchases of goods or services from a taxable person not established in France</field>
                                <field name="code">box_B4</field>
                                <field name="expression_ids">
                                    <record id="tax_report_B4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_B4.balance_from_tags + box_B4.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_B4_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">B4</field>
                                    </record>
                                    <record id="tax_report_B4_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_B5" model="account.report.line">
                                <field name="name">B5 - Regularisations</field>
                                <field name="code">box_B5</field>
                                <field name="expression_ids">
                                    <record id="tax_report_B5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_B5.balance_from_tags + box_B5.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_B5_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">B5</field>
                                    </record>
                                    <record id="tax_report_B5_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_op_non_imposables" model="account.report.line">
                        <field name="name">Untaxed operations</field>
                        <field name="children_ids">
                            <record id="tax_report_E1" model="account.report.line">
                                <field name="name">E1 - Exports outside the EU</field>
                                <field name="code">box_E1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_E1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_E1.balance_from_tags + box_E1.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_E1_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">E1</field>
                                    </record>
                                    <record id="tax_report_E1_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_E2" model="account.report.line">
                                <field name="name">E2 - Other non-taxable transactions</field>
                                <field name="code">box_E2</field>
                                <field name="expression_ids">
                                    <record id="tax_report_E2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_E2.balance_from_tags + box_E2.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_E2_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">E2</field>
                                    </record>
                                    <record id="tax_report_E2_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_E3" model="account.report.line">
                                <field name="name">E3 - Distance selling taxable in another Member State to non-taxable persons</field>
                                <field name="code">box_E3</field>
                                <field name="expression_ids">
                                    <record id="tax_report_E3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_E3.balance_from_tags + box_E3.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_E3_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">E3</field>
                                    </record>
                                    <record id="tax_report_E3_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_E4" model="account.report.line">
                                <field name="name">E4 - Imports (other than petroleum products)</field>
                                <field name="code">box_E4</field>
                                <field name="expression_ids">
                                    <record id="tax_report_E4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_E4.balance_from_tags + box_E4.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_E4_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">E4</field>
                                    </record>
                                    <record id="tax_report_E4_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_E5" model="account.report.line">
                                <field name="name">E5 - Removal from suspensive tax regime (other than petroleum products)</field>
                                <field name="code">box_E5</field>
                                <field name="expression_ids">
                                    <record id="tax_report_E5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_E5.balance_from_tags + box_E5.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_E5_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">E5</field>
                                    </record>
                                    <record id="tax_report_E5_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_E6" model="account.report.line">
                                <field name="name">E6 - Imports under suspensive tax arrangements (other than petroleum products)</field>
                                <field name="code">box_E6</field>
                                <field name="expression_ids">
                                    <record id="tax_report_E6_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_E6.balance_from_tags + box_E6.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_E6_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">E6</field>
                                    </record>
                                    <record id="tax_report_E6_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F1" model="account.report.line">
                                <field name="name">F1 - Intra-Community acquisitions</field>
                                <field name="code">box_F1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F1.balance_from_tags + box_F1.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F1_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F1</field>
                                    </record>
                                    <record id="tax_report_F1_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F2" model="account.report.line">
                                <field name="name">F2 - Intra-Community supplies to a taxable person</field>
                                <field name="code">box_F2</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F2.balance_from_tags + box_F2.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F2_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F2</field>
                                    </record>
                                    <record id="tax_report_F2_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F3" model="account.report.line">
                                <field name="name">F3 - Non-taxable supplies of electricity, natural gas, heat or cooling in France</field>
                                <field name="code">box_F3</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F3.balance_from_tags + box_F3.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F3_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F3</field>
                                    </record>
                                    <record id="tax_report_F3_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F4" model="account.report.line">
                                <field name="name">F4 - Releases for consumption of petroleum products</field>
                                <field name="code">box_F4</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F4.balance_from_tags + box_F4.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F4_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F4</field>
                                    </record>
                                    <record id="tax_report_F4_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F5" model="account.report.line">
                                <field name="name">F5 - Imports of petroleum products under a suspensive tax regime</field>
                                <field name="code">box_F5</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F5.balance_from_tags + box_F5.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F5_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F5</field>
                                    </record>
                                    <record id="tax_report_F5_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F6" model="account.report.line">
                                <field name="name">F6 - Franchise purchases</field>
                                <field name="code">box_F6</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F6_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F6.balance_from_tags + box_F6.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F6_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F6</field>
                                    </record>
                                    <record id="tax_report_F6_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F7" model="account.report.line">
                                <field name="name">F7 - Sales of goods or services by a taxable person not established in France</field>
                                <field name="code">box_F7</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F7_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F7.balance_from_tags + box_F7.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F7_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F7</field>
                                    </record>
                                    <record id="tax_report_F7_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F8" model="account.report.line">
                                <field name="name">F8 - Accruals</field>
                                <field name="code">box_7B</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F8_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_7B.balance_from_tags + box_7B.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F8_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">7B</field>
                                    </record>
                                    <record id="tax_report_F8_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_F9" model="account.report.line">
                                <field name="name">F9 - Internal transactions between members of a single taxable person</field>
                                <field name="code">box_F9</field>
                                <field name="expression_ids">
                                    <record id="tax_report_F9_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_F9.balance_from_tags + box_F9.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_F9_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">F9</field>
                                    </record>
                                    <record id="tax_report_F9_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_decompte_tva" model="account.report.line">
                <field name="name">B. Settlement of VAT to be paid</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_tva_brute" model="account.report.line">
                        <field name="name">Gross VAT</field>
                    </record>
                    <record id="tax_report_tva_brute_metropo" model="account.report.line">
                        <field name="name">Operations carried out in mainland France</field>
                        <field name="children_ids">
                            <record id="tax_report_08_base" model="account.report.line">
                                <field name="name">08 - Standard rate 20% (base)</field>
                                <field name="code">box_08_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_08_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_08_base.balance_from_tags + box_08_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_08_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">08_base</field>
                                    </record>
                                    <record id="tax_report_08_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_08_taxe" model="account.report.line">
                                <field name="name">08 - Standard rate 20% (tax)</field>
                                <field name="code">box_08_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_08_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_08_base.balance * 0.2</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_08_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">08_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_09_base" model="account.report.line">
                                <field name="name">09 - Reduced rate 5.5% (base)</field>
                                <field name="code">box_09_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_09_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_09_base.balance_from_tags + box_09_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_09_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">09_base</field>
                                    </record>
                                    <record id="tax_report_09_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_09_taxe" model="account.report.line">
                                <field name="name">09 - Reduced rate 5.5% (tax)</field>
                                <field name="code">box_09_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_09_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_09_base.balance * 0.055</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_09_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">09_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_9B_base" model="account.report.line">
                                <field name="name">9B - Reduced rate 10% (base)</field>
                                <field name="code">box_9B_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_9B_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_9B_base.balance_from_tags + box_9B_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_9B_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">9B_base</field>
                                    </record>
                                    <record id="tax_report_9B_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_9B_taxe" model="account.report.line">
                                <field name="name">9B - Reduced rate 10% (tax)</field>
                                <field name="code">box_9B_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_9B_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_9B_base.balance * 0.1</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_9B_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">9B_taxe</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_tva_brute_dom" model="account.report.line">
                        <field name="name">Operations carried out in the DOM</field>
                        <field name="children_ids">
                            <record id="tax_report_10_base" model="account.report.line">
                                <field name="name">10 - Standard rate 8.5% (base)</field>
                                <field name="code">box_10_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_10_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_10_base.balance_from_tags + box_10_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_10_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">10_base</field>
                                    </record>
                                    <record id="tax_report_10_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_10_taxe" model="account.report.line">
                                <field name="name">10 - Standard rate 8.5% (tax)</field>
                                <field name="code">box_10_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_10_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_10_base.balance * 0.085</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_10_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">10_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_11_base" model="account.report.line">
                                <field name="name">11 - Reduced rate 2.1% (base)</field>
                                <field name="code">box_11_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_11_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_11_base.balance_from_tags + box_11_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_11_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">11_base</field>
                                    </record>
                                    <record id="tax_report_11_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_11_taxe" model="account.report.line">
                                <field name="name">11 - Reduced rate 2.1% (tax)</field>
                                <field name="code">box_11_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_11_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_11_base.balance * 0.021</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_11_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">11_taxe</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_tva_brute_autre" model="account.report.line">
                        <field name="name">Transactions taxable at another rate (metropolitan France or DOM)</field>
                        <field name="children_ids">
                            <record id="tax_report_T1_base" model="account.report.line">
                                <field name="name">T1 - Transactions carried out in the French overseas departments and taxable at 1.75% (base)</field>
                                <field name="code">box_T1_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T1_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T1_base.balance_from_tags + box_T1_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T1_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T1_base</field>
                                    </record>
                                    <record id="tax_report_T1_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T1_taxe" model="account.report.line">
                                <field name="name">T1 - Transactions carried out in the French overseas departments and taxable at 1.75% (tax)</field>
                                <field name="code">box_T1_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T1_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T1_base.balance * 0.0175</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T1_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T1_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T2_base" model="account.report.line">
                                <field name="name">T2 - Transactions carried out in the French overseas departments and taxable at 1.05% (base)</field>
                                <field name="code">box_T2_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T2_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T2_base.balance_from_tags + box_T2_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T2_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T2_base</field>
                                    </record>
                                    <record id="tax_report_T2_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T2_taxe" model="account.report.line">
                                <field name="name">T2 - Transactions carried out in the French overseas departments and taxable at 1,05% (tax)</field>
                                <field name="code">box_T2_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T2_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T2_base.balance * 0.0105</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T2_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T2_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T3_base" model="account.report.line">
                                <field name="name">T3 - Transactions carried out in Corsica and taxable at the rate of 10% (base)</field>
                                <field name="code">box_T3_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T3_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T3_base.balance_from_tags + box_T3_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T3_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T3_base</field>
                                    </record>
                                    <record id="tax_report_T3_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T3_taxe" model="account.report.line">
                                <field name="name">T3 - Transactions carried out in Corsica and taxable at the rate of 10% (tax)</field>
                                <field name="code">box_T3_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T3_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T3_base.balance * 0.1</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T3_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T3_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T4_base" model="account.report.line">
                                <field name="name">T4 - Transactions carried out in Corsica and taxable at the rate of 2,1% (base)</field>
                                <field name="code">box_T4_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T4_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T4_base.balance_from_tags + box_T4_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T4_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T4_base</field>
                                    </record>
                                    <record id="tax_report_T4_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T4_taxe" model="account.report.line">
                                <field name="name">T4 - Transactions carried out in Corsica and taxable at the rate of 2,1% (tax)</field>
                                <field name="code">box_T4_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T4_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T4_base.balance * 0.021</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T4_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T4_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T5_base" model="account.report.line">
                                <field name="name">T5 - Transactions carried out in Corsica and taxable at the rate of 0,9% (base)</field>
                                <field name="code">box_T5_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T5_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T5_base.balance_from_tags + box_T5_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T5_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T5_base</field>
                                    </record>
                                    <record id="tax_report_T5_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T5_taxe" model="account.report.line">
                                <field name="name">T5 - Transactions carried out in Corsica and taxable at the rate of 0,9% (tax)</field>
                                <field name="code">box_T5_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T5_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T5_base.balance * 0.009</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T5_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T5_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T6_base" model="account.report.line">
                                <field name="name">T6 - Transactions carried out in mainland France at the rate of 2,1% (base)</field>
                                <field name="code">box_T6_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T6_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T6_base.balance_from_tags + box_T6_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T6_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T6_base</field>
                                    </record>
                                    <record id="tax_report_T6_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T6_taxe" model="account.report.line">
                                <field name="name">T6 - Transactions carried out in mainland France at the rate of 2,1% (tax)</field>
                                <field name="code">box_T6_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T6_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T6_base.balance * 0.021</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T6_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T6_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T7_base" model="account.report.line">
                                <field name="name">T7 - Withholding of VAT on copyright (base)</field>
                                <field name="code">box_T7_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T7_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_T7_base.balance_from_tags + box_T7_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_T7_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T7_base</field>
                                    </record>
                                    <record id="tax_report_T7_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_T7_taxe" model="account.report.line">
                                <field name="name">T7 - Withholding of VAT on copyright (tax)</field>
                                <field name="code">box_T7_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_T7_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">T7_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_13_base" model="account.report.line">
                                <field name="name">13 - Former rates (base)</field>
                                <field name="code">box_13_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_13_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_13_base.balance_from_tags + box_13_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_13_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">13_base</field>
                                    </record>
                                    <record id="tax_report_13_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_13_taxe" model="account.report.line">
                                <field name="name">13 - Former rates (tax)</field>
                                <field name="code">box_13_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_13_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">13_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_14_base" model="account.report.line">
                                <field name="name">14 - Transactions taxable at a particular rate (base)</field>
                                <field name="code">box_14_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_14_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_14_base.balance_from_tags + box_14_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_14_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">14_base</field>
                                    </record>
                                    <record id="tax_report_14_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_14_taxe" model="account.report.line">
                                <field name="name">14 - Transactions taxable at a particular rate (tax)</field>
                                <field name="code">box_14_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_14_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">14_taxe</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_tva_brute_petrolier" model="account.report.line">
                        <field name="name">Oil products</field>
                        <field name="children_ids">
                            <record id="tax_report_P1_base" model="account.report.line">
                                <field name="name">P1 - Standard rate 20% (base)</field>
                                <field name="code">box_P1_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_P1_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_P1_base.balance_from_tags + box_P1_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_P1_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">P1_base</field>
                                    </record>
                                    <record id="tax_report_P1_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_P1_taxe" model="account.report.line">
                                <field name="name">P1 - Standard rate 20% (tax)</field>
                                <field name="code">box_P1_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_P1_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_P1_base.balance * 0.2</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_P1_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">P1_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_P2_base" model="account.report.line">
                                <field name="name">P2 - Reduced rate 13% (base)</field>
                                <field name="code">box_P2_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_P2_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_P2_base.balance_from_tags + box_P2_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_P2_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">P2_base</field>
                                    </record>
                                    <record id="tax_report_P2_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_P2_taxe" model="account.report.line">
                                <field name="name">P2 - Reduced rate 13% (tax)</field>
                                <field name="code">box_P2_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_P2_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_P2_base.balance * 0.13</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_P2_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">P2_taxe</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_tva_brute_import" model="account.report.line">
                        <field name="name">Imports</field>
                        <field name="children_ids">
                            <record id="tax_report_I1_base" model="account.report.line">
                                <field name="name">I1 - Standard rate 20% (base)</field>
                                <field name="code">box_I1_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I1_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I1_base.balance_from_tags + box_I1_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I1_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I1_base</field>
                                    </record>
                                    <record id="tax_report_I1_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I1_taxe" model="account.report.line">
                                <field name="name">I1 - Standard rate 20% (tax)</field>
                                <field name="code">box_I1_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I1_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I1_base.balance * 0.2</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I1_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I1_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I2_base" model="account.report.line">
                                <field name="name">I2 - Reduced rate 10% (base)</field>
                                <field name="code">box_I2_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I2_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I2_base.balance_from_tags + box_I2_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I2_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I2_base</field>
                                    </record>
                                    <record id="tax_report_I2_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I2_taxe" model="account.report.line">
                                <field name="name">I2 - Reduced rate 10% (tax)</field>
                                <field name="code">box_I2_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I2_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I2_base.balance * 0.1</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I2_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I2_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I3_base" model="account.report.line">
                                <field name="name">I3 - Reduced rate 8.5% (base)</field>
                                <field name="code">box_I3_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I3_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I3_base.balance_from_tags + box_I3_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I3_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I3_base</field>
                                    </record>
                                    <record id="tax_report_I3_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I3_taxe" model="account.report.line">
                                <field name="name">I3 - Reduced rate 8.5% (tax)</field>
                                <field name="code">box_I3_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I3_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I3_base.balance * 0.085</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I3_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I3_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I4_base" model="account.report.line">
                                <field name="name">I4 - Reduced rate 5.5% (base)</field>
                                <field name="code">box_I4_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I4_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I4_base.balance_from_tags + box_I4_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I4_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I4_base</field>
                                    </record>
                                    <record id="tax_report_I4_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I4_taxe" model="account.report.line">
                                <field name="name">I4 - Reduced rate 5.5% (tax)</field>
                                <field name="code">box_I4_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I4_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I4_base.balance * 0.055</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I4_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I4_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I5_base" model="account.report.line">
                                <field name="name">I5 - Reduced rate 2.1% (base)</field>
                                <field name="code">box_I5_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I5_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I5_base.balance_from_tags + box_I5_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I5_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I5_base</field>
                                    </record>
                                    <record id="tax_report_I5_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I5_taxe" model="account.report.line">
                                <field name="name">I5 - Reduced rate 2.1% (tax)</field>
                                <field name="code">box_I5_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I5_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I5_base.balance * 0.021</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I5_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I5_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I6_base" model="account.report.line">
                                <field name="name">I6 - Reduced rate 1.05% (base)</field>
                                <field name="code">box_I6_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I6_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I6_base.balance_from_tags + box_I6_base.adjustment</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I6_base_balance_from_tags" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I6_base</field>
                                    </record>
                                    <record id="tax_report_I6_base_adjustment" model="account.report.expression">
                                        <field name="label">adjustment</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=0</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_I6_taxe" model="account.report.line">
                                <field name="name">I6 - Reduced rate 1.05% (tax)</field>
                                <field name="code">box_I6_taxe</field>
                                <field name="expression_ids">
                                    <record id="tax_report_I6_taxe_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_I6_base.balance * 0.0105</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                    <record id="tax_report_I6_taxe_tag_no_rounding" model="account.report.expression">
                                        <field name="label">balance_from_tags</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I6_taxe</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_15" model="account.report.line">
                                <field name="name">15 - Previously deducted VAT to be repaid</field>
                                <field name="code">box_15</field>
                                <field name="expression_ids">
                                    <record id="tax_report_15_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">15</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="tax_report_15_1" model="account.report.line">
                                        <field name="name">of which VAT on petroleum products</field>
                                        <field name="code">box_15_1</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_15_1_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">15_1</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="tax_report_15_2" model="account.report.line">
                                        <field name="name">of which VAT on imported products excluding petroleum products</field>
                                        <field name="code">box_15_2</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_15_2_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">15_2</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_5B" model="account.report.line">
                                <field name="name">5B - Amounts to be added, including advance holiday pay</field>
                                <field name="code">box_5B</field>
                                <field name="expression_ids">
                                    <record id="tax_report_5B_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">5B</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_16" model="account.report.line">
                                <field name="name">16 - Total gross VAT due</field>
                                <field name="code">box_16</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_16_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_08_taxe.balance + box_09_taxe.balance + box_9B_taxe.balance + box_10_taxe.balance + box_11_taxe.balance + box_13_taxe.balance + box_14_taxe.balance + box_T1_taxe.balance + box_T2_taxe.balance + box_T3_taxe.balance + box_T4_taxe.balance + box_T5_taxe.balance + box_T6_taxe.balance + box_T7_taxe.balance + box_P1_taxe.balance + box_P2_taxe.balance + box_I1_taxe.balance + box_I2_taxe.balance + box_I3_taxe.balance + box_I4_taxe.balance + box_I5_taxe.balance + box_I6_taxe.balance + box_15.balance + box_5B.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_17" model="account.report.line">
                                <field name="name">17 - Of which VAT on intra-Community acquisitions</field>
                                <field name="code">box_17</field>
                                <field name="expression_ids">
                                    <record id="tax_report_17_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">17</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_18" model="account.report.line">
                                <field name="name">18 - Of which VAT on transactions to Monaco</field>
                                <field name="code">box_18</field>
                                <field name="expression_ids">
                                    <record id="tax_report_18_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">18</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_tva_deductible" model="account.report.line">
                        <field name="name">Deductible VAT</field>
                        <field name="children_ids">
                            <record id="tax_report_19" model="account.report.line">
                                <field name="name">19 - Assets constituting fixed assets</field>
                                <field name="code">box_19</field>
                                <field name="expression_ids">
                                    <record id="tax_report_19_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">19</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_20" model="account.report.line">
                                <field name="name">20 - Other goods and services</field>
                                <field name="code">box_20</field>
                                <field name="expression_ids">
                                    <record id="tax_report_20_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">20</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_21" model="account.report.line">
                                <field name="name">21 - Other deductible VAT</field>
                                <field name="code">box_21</field>
                                <field name="expression_ids">
                                    <record id="tax_report_21_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">21</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_22" model="account.report.line">
                                <field name="name">22 - Carry-over of credit from line 27 of the previous return</field>
                                <field name="code">box_22</field>
                                <field name="expression_ids">
                                    <record id="tax_report_22_applied_carryover" model="account.report.expression">
                                        <field name="label">_applied_carryover_balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">most_recent</field>
                                        <field name="date_scope">previous_tax_period</field>
                                    </record>
                                    <record id="tax_report_22_tag" model="account.report.expression">
                                        <field name="label">tag</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">22</field>
                                    </record>
                                    <record id="tax_report_22_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_22.tag + box_22._applied_carryover_balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_2C" model="account.report.line">
                                <field name="name">2C - Amounts to be charged, including advance holiday pay</field>
                                <field name="code">box_2C</field>
                                <field name="expression_ids">
                                    <record id="tax_report_2C_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">2C</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_22A" model="account.report.line">
                                <field name="name">22A - Enter the single tax rate applicable for the period if different from 100%</field>
                                <field name="code">box_22A</field>
                                <field name="expression_ids">
                                    <record id="tax_report_22A_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">22A</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_23" model="account.report.line">
                                <field name="name">23 - Total deductible VAT</field>
                                <field name="code">box_23</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_23_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_19.balance + box_20.balance + box_21.balance + box_22.balance + box_2C.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_24" model="account.report.line">
                                <field name="name">24 - Of which deductible VAT on imports</field>
                                <field name="code">box_24</field>
                                <field name="expression_ids">
                                    <record id="tax_report_24_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">24</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_2E" model="account.report.line">
                                <field name="name">2E - Of which deductible VAT on petroleum products</field>
                                <field name="code">box_2E</field>
                                <field name="expression_ids">
                                    <record id="tax_report_2E_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">2E</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_credit" model="account.report.line">
                <field name="name">Credit</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_25" model="account.report.line">
                        <field name="name">25 - VAT credit</field>
                        <field name="code">box_25</field>
                        <field name="expression_ids">
                            <record id="tax_report_25_formula" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">box_23.balance - box_16.balance</field>
                                <field name="subformula">if_above(EUR(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_TD" model="account.report.line">
                        <field name="name">TD - VAT Due</field>
                        <field name="code">box_TD</field>
                        <field name="expression_ids">
                            <record id="tax_report_td_formula" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">box_16.balance - box_23.balance</field>
                                <field name="subformula">if_above(EUR(0))</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_regularisation" model="account.report.line">
                <field name="name">Regularisation of domestic consumption taxes (TIC)</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_credit_constate" model="account.report.line">
                        <field name="name">Recognised credit</field>
                        <field name="children_ids">
                            <record id="tax_report_TICFE" model="account.report.line">
                                <field name="name">TICFE</field>
                                <field name="code">box_TICFE</field>
                                <field name="expression_ids">
                                    <record id="tax_report_TICFE_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TICFE_constate</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_TICGN" model="account.report.line">
                                <field name="name">TICGN</field>
                                <field name="code">box_TICGN</field>
                                <field name="expression_ids">
                                    <record id="tax_report_TICGN_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TICGN_constate</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_TICC" model="account.report.line">
                                <field name="name">TICC</field>
                                <field name="code">box_TICC</field>
                                <field name="expression_ids">
                                    <record id="tax_report_TICC_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TICC_constate</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_TIC_total" model="account.report.line">
                                <field name="name">Total</field>
                                <field name="code">box_TIC_total</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_TIC_total_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_TICFE.balance + box_TICGN.balance + box_TICC.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_credit_impute" model="account.report.line">
                        <field name="name">Credit charged</field>
                        <field name="children_ids">
                            <record id="tax_report_X1" model="account.report.line">
                                <field name="name">X1</field>
                                <field name="code">box_X1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_X1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">X1</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_X2" model="account.report.line">
                                <field name="name">X2</field>
                                <field name="code">box_X2</field>
                                <field name="expression_ids">
                                    <record id="tax_report_X2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">X2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_X3" model="account.report.line">
                                <field name="name">X3</field>
                                <field name="code">box_X3</field>
                                <field name="expression_ids">
                                    <record id="tax_report_X3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">X3</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_X4" model="account.report.line">
                                <field name="name">X4</field>
                                <field name="code">box_X4</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_X4_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_X1.balance + box_X2.balance + box_X3.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_reliquat" model="account.report.line">
                        <field name="name">Outstanding credit</field>
                        <field name="children_ids">
                            <record id="tax_report_Y1" model="account.report.line">
                                <field name="name">Y1</field>
                                <field name="code">box_Y1</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_Y1_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_TICFE.balance - box_X1.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_Y2" model="account.report.line">
                                <field name="name">Y2</field>
                                <field name="code">box_Y2</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_Y2_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_TICGN.balance - box_X2.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_Y3" model="account.report.line">
                                <field name="name">Y3</field>
                                <field name="code">box_Y3</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_Y3_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_TICC.balance - box_X3.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_Y4" model="account.report.line">
                                <field name="name">Y4</field>
                                <field name="code">box_Y4</field>
                                <field name="aggregation_formula">box_Y1.balance + box_Y2.balance + box_Y3.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_tic_tax" model="account.report.line">
                        <field name="name">Tax due</field>
                        <field name="children_ids">
                            <record id="tax_report_Z1" model="account.report.line">
                                <field name="name">Z1</field>
                                <field name="code">box_Z1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_Z1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">Z1</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_Z2" model="account.report.line">
                                <field name="name">Z2</field>
                                <field name="code">box_Z2</field>
                                <field name="expression_ids">
                                    <record id="tax_report_Z2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">Z2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_Z3" model="account.report.line">
                                <field name="name">Z3</field>
                                <field name="code">box_Z3</field>
                                <field name="expression_ids">
                                    <record id="tax_report_Z3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">Z3</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_Z4" model="account.report.line">
                                <field name="name">Z4</field>
                                <field name="code">box_Z4</field>
                                <field name="aggregation_formula"></field>
                                <field name="expression_ids">
                                    <record id="tax_report_Z4_formula" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">box_Z1.balance + box_Z2.balance + box_Z3.balance</field>
                                        <field name="subformula">round(0)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_determination" model="account.report.line">
                <field name="name">Determining the amount to be paid and/or VAT and/or TIC credits</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_26_external" model="account.report.line">
                        <field name="name">26 - Repayment of credit requested on form n°3519 attached</field>
                        <field name="code">box_26_external</field>
                        <field name="expression_ids">
                            <record id="tax_report_26_external_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_AA" model="account.report.line">
                        <field name="name">AA - VAT credit transferred to the head company on the recapitulative return 3310-CA3G</field>
                        <field name="code">box_AA</field>
                        <field name="expression_ids">
                            <record id="tax_report_AA_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">AA</field>
                            </record>
                        </field>
                    </record>

                    <record id="tax_report_27" model="account.report.line">
                        <field name="name">27 - Credit to be carried forward</field>
                        <field name="code">box_27</field>
                        <field name="expression_ids">
                            <record id="tax_report_27_formula" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">box_27._balance_temp</field>
                                <field name="subformula">round(0)</field>
                            </record>
                            <record id="tax_report_27_formula_temp" model="account.report.expression">
                                <field name="label">_balance_temp</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">box_25.balance - box_26_external.balance - box_AA.balance</field>
                                <field name="subformula">if_above(EUR(0))</field>
                            </record>
                            <record id="tax_report_27_carryover" model="account.report.expression">
                                <field name="label">_carryover_balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">box_27.balance</field>
                                <field name="carryover_target">box_22._applied_carryover_balance</field>
                                <field name="subformula" eval="False"/>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_Y5" model="account.report.line">
                        <field name="name">Y5 - Refund of TIC balance requested (carried forward from line Y4)</field>
                        <field name="code">box_Y5</field>
                        <field name="aggregation_formula">box_Y4.balance</field>
                    </record>
                    <record id="tax_report_Y6" model="account.report.line">
                        <field name="name">Y6 - TIC credit transferred to the head company on the 3310-CA3G recapitulative return (carried forward from line Y4)</field>
                        <field name="code">box_Y6</field>
                    </record>
                    <record id="tax_report_X5" model="account.report.line">
                        <field name="name">X5 - TIC credit offset against VAT (carried forward from line X4)</field>
                        <field name="code">box_X5</field>
                        <field name="aggregation_formula">box_X4.balance</field>
                    </record>
                    <record id="tax_report_28" model="account.report.line">
                        <field name="name">28 - Net VAT due</field>
                        <field name="code">box_28</field>
                        <field name="expression_ids">
                            <record id="tax_report_28_formula" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">box_TD.balance - box_X5.balance</field>
                                <field name="subformula">if_above(EUR(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_29" model="account.report.line">
                        <field name="name">29 - Similar taxes calculated on schedule n°3310-A-SD</field>
                        <field name="code">box_29</field>
                        <field name="expression_ids">
                            <record id="tax_report_29_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">29</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_Z5" model="account.report.line">
                        <field name="name">Z5 - Total domestic consumption tax due (carried forward from line Z4)</field>
                        <field name="code">box_Z5</field>
                        <field name="aggregation_formula">box_Z4.balance</field>
                    </record>
                    <record id="tax_report_AB" model="account.report.line">
                        <field name="name">AB - Total to be paid by the head company on the recapitulative declaration 3310-CA3G</field>
                        <field name="code">box_AB</field>
                    </record>
                    <record id="tax_report_32" model="account.report.line">
                        <field name="name">32 - Total payable</field>
                        <field name="code">box_32</field>
                        <field name="aggregation_formula"></field>
                        <field name="expression_ids">
                            <record id="tax_report_32_formula" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">box_28.balance + box_29.balance + box_Z5.balance</field>
                                <field name="subformula">round(0)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
