# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fr_account
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-04 08:46+0000\n"
"PO-Revision-Date: 2024-11-04 08:46+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 10"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 11"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 12"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 13"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 14"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 15"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 16"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "# 17"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_08_base
msgid "08 - Standard rate 20% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_08_taxe
msgid "08 - Standard rate 20% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_09_base
msgid "09 - Reduced rate 5.5% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_09_taxe
msgid "09 - Reduced rate 5.5% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_10_base
msgid "10 - Standard rate 8.5% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_10_taxe
msgid "10 - Standard rate 8.5% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_11_base
msgid "11 - Reduced rate 2.1% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_11_taxe
msgid "11 - Reduced rate 2.1% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_13_base
msgid "13 - Former rates (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_13_taxe
msgid "13 - Former rates (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_14_base
msgid "14 - Transactions taxable at a particular rate (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_14_taxe
msgid "14 - Transactions taxable at a particular rate (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_15
msgid "15 - Previously deducted VAT to be repaid"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_16
msgid "16 - Total gross VAT due"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_17
msgid "17 - Of which VAT on intra-Community acquisitions"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_18
msgid "18 - Of which VAT on transactions to Monaco"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_19
msgid "19 - Assets constituting fixed assets"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_20
msgid "20 - Other goods and services"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_21
msgid "21 - Other deductible VAT"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_22
msgid "22 - Carry-over of credit from line 27 of the previous return"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_22A
msgid ""
"22A - Enter the single tax rate applicable for the period if different from "
"100%"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_23
msgid "23 - Total deductible VAT"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_24
msgid "24 - Of which deductible VAT on imports"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_25
msgid "25 - VAT credit"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_26
#: model:account.report.line,name:l10n_fr_account.tax_report_26_external
msgid "26 - Repayment of credit requested on form n°3519 attached"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_27
msgid "27 - Credit to be carried forward"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_28
msgid "28 - Net VAT due"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_29
msgid "29 - Similar taxes calculated on schedule n°3310-A-SD"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_2C
msgid "2C - Amounts to be charged, including advance holiday pay"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_2E
msgid "2E - Of which deductible VAT on petroleum products"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_32
msgid "32 - Total payable"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_5B
msgid "5B - Amounts to be added, including advance holiday pay"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_9B_base
msgid "9B - Reduced rate 10% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_9B_taxe
msgid "9B - Reduced rate 10% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.report_invoice_document
msgid ""
"<strong>Customer Address:</strong>\n"
"                    <br/>"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.report_invoice_document
msgid ""
"<strong>Operation Type:</strong>\n"
"                    <br/>"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_montant_op_realisees
msgid "A. Amount of operations carried out"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_A1
msgid "A1 - Sales, provision of services"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_A2
msgid "A2 - Other taxable transactions"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_A3
msgid "A3 - Intra-Community purchases of services"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_A4
msgid "A4 - Imports (other than petroleum products)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_A5
msgid ""
"A5 - Removal from suspensive tax regime (other than petroleum products)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_AA
msgid ""
"AA - VAT credit transferred to the head company on the recapitulative return"
" 3310-CA3G"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_AB
msgid ""
"AB - Total to be paid by the head company on the recapitulative declaration "
"3310-CA3G"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model,name:l10n_fr_account.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.column,name:l10n_fr_account.tax_report_adjustment
msgid "Adjustment"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_decompte_tva
msgid "B. Settlement of VAT to be paid"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_B1
msgid "B1 - Releases for consumption of petroleum products"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_B2
msgid "B2 - Intra-Community acquisitions"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_B3
msgid ""
"B3 - Taxable supplies of electricity, natural gas, heat or cooling in France"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_B4
msgid ""
"B4 - Purchases of goods or services from a taxable person not established in"
" France"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_B5
msgid "B5 - Regularisations"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.column,name:l10n_fr_account.tax_report_balance
msgid "Balance"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Cancel"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Column"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Comment"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "CompAuxLib"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "CompAuxNum"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model,name:l10n_fr_account.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "CompteLib"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "CompteNum"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_credit
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Credit"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_credit_impute
msgid "Credit charged"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "DateLet"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Debit"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tva_deductible
msgid "Deductible VAT"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_determination
msgid "Determining the amount to be paid and/or VAT and/or TIC credits"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_E1
msgid "E1 - Exports outside the EU"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_E2
msgid "E2 - Other non-taxable transactions"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_E3
msgid ""
"E3 - Distance selling taxable in another Member State to non-taxable persons"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_E4
msgid "E4 - Imports (other than petroleum products)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_E5
msgid ""
"E5 - Removal from suspensive tax regime (other than petroleum products)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_E6
msgid ""
"E6 - Imports under suspensive tax arrangements (other than petroleum "
"products)"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "EcritureDate"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "EcritureLet"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "EcritureLib"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "EcritureNum"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__date_to
msgid "End Date"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__exclude_zero
msgid "Exclude lines at 0"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__excluded_journal_ids
msgid "Excluded Journals"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__export_type
msgid "Export Type"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F1
msgid "F1 - Intra-Community acquisitions"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F2
msgid "F2 - Intra-Community supplies to a taxable person"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F3
msgid ""
"F3 - Non-taxable supplies of electricity, natural gas, heat or cooling in "
"France"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F4
msgid "F4 - Releases for consumption of petroleum products"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F5
msgid "F5 - Imports of petroleum products under a suspensive tax regime"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F6
msgid "F6 - Franchise purchases"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F7
msgid ""
"F7 - Sales of goods or services by a taxable person not established in "
"France"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F8
msgid "F8 - Accruals"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_F9
msgid "F9 - Internal transactions between members of a single taxable person"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "FEC File Generation"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model,name:l10n_fr_account.model_l10n_fr_fec_export_wizard
msgid "Fichier Echange Informatise"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__filename
msgid "Filename"
msgstr ""

#. module: l10n_fr_account
#: model:ir.ui.menu,name:l10n_fr_account.account_reports_fr_statements_menu
msgid "France"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Generate"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.report_invoice_document
msgid "Goods Delivery"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tva_brute
msgid "Gross VAT"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I1_base
msgid "I1 - Standard rate 20% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I1_taxe
msgid "I1 - Standard rate 20% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I2_base
msgid "I2 - Reduced rate 10% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I2_taxe
msgid "I2 - Reduced rate 10% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I3_base
msgid "I3 - Reduced rate 8.5% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I3_taxe
msgid "I3 - Reduced rate 8.5% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I4_base
msgid "I4 - Reduced rate 5.5% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I4_taxe
msgid "I4 - Reduced rate 5.5% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I5_base
msgid "I5 - Reduced rate 2.1% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I5_taxe
msgid "I5 - Reduced rate 2.1% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I6_base
msgid "I6 - Reduced rate 1.05% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_I6_taxe
msgid "I6 - Reduced rate 1.05% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Idevise"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tva_brute_import
msgid "Imports"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model,name:l10n_fr_account.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "JournalCode"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "JournalLib"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_account_bank_statement_line__l10n_fr_is_company_french
#: model:ir.model.fields,field_description:l10n_fr_account.field_account_move__l10n_fr_is_company_french
msgid "L10N Fr Is Company French"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_res_company__l10n_fr_rounding_difference_loss_account_id
msgid "L10N Fr Rounding Difference Loss Account"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_res_company__l10n_fr_rounding_difference_profit_account_id
msgid "L10N Fr Rounding Difference Profit Account"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.report_invoice_document
msgid "Mixed Operation"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Montantdevise"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields.selection,name:l10n_fr_account.selection__l10n_fr_fec_export_wizard__export_type__nonofficial
msgid "Non-official FEC report (posted and unposted entries)"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields.selection,name:l10n_fr_account.selection__l10n_fr_fec_export_wizard__export_type__official
msgid "Official FEC report (posted entries only)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tva_brute_petrolier
msgid "Oil products"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tva_brute_metropo
msgid "Operations carried out in mainland France"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tva_brute_dom
msgid "Operations carried out in the DOM"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.report_invoice_document
msgid "Option to pay tax on debits"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Options"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_reliquat
msgid "Outstanding credit"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_P1_base
msgid "P1 - Standard rate 20% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_P1_taxe
msgid "P1 - Standard rate 20% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_P2_base
msgid "P2 - Reduced rate 13% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_P2_taxe
msgid "P2 - Reduced rate 13% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "PieceDate"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "PieceRef"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_credit_constate
msgid "Recognised credit"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_regularisation
msgid "Regularisation of domestic consumption taxes (TIC)"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.report_invoice_document
msgid "SIRET:"
msgstr ""

#. module: l10n_fr_account
#: model:account.account.tag,name:l10n_fr_account.account_fr_tag_salaires
msgid "Salaries"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.report_invoice_document
msgid "Service Delivery"
msgstr ""

#. module: l10n_fr_account
#: model:account.account.tag,name:l10n_fr_account.account_fr_tag_charges_sociales
msgid "Social charges"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__date_from
msgid "Start Date"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T1_base
msgid ""
"T1 - Transactions carried out in the French overseas departments and taxable"
" at 1.75% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T1_taxe
msgid ""
"T1 - Transactions carried out in the French overseas departments and taxable"
" at 1.75% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T2_taxe
msgid ""
"T2 - Transactions carried out in the French overseas departments and taxable"
" at 1,05% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T2_base
msgid ""
"T2 - Transactions carried out in the French overseas departments and taxable"
" at 1.05% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T3_base
msgid ""
"T3 - Transactions carried out in Corsica and taxable at the rate of 10% "
"(base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T3_taxe
msgid ""
"T3 - Transactions carried out in Corsica and taxable at the rate of 10% "
"(tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T4_base
msgid ""
"T4 - Transactions carried out in Corsica and taxable at the rate of 2,1% "
"(base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T4_taxe
msgid ""
"T4 - Transactions carried out in Corsica and taxable at the rate of 2,1% "
"(tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T5_base
msgid ""
"T5 - Transactions carried out in Corsica and taxable at the rate of 0,9% "
"(base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T5_taxe
msgid ""
"T5 - Transactions carried out in Corsica and taxable at the rate of 0,9% "
"(tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T6_base
msgid ""
"T6 - Transactions carried out in mainland France at the rate of 2,1% (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T6_taxe
msgid ""
"T6 - Transactions carried out in mainland France at the rate of 2,1% (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T7_base
msgid "T7 - Withholding of VAT on copyright (base)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_T7_taxe
msgid "T7 - Withholding of VAT on copyright (tax)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_TD
msgid "TD - VAT Due"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_TICC
msgid "TICC"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_TICFE
msgid "TICFE"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_TICGN
msgid "TICGN"
msgstr ""

#. module: l10n_fr_account
#: model:account.report,name:l10n_fr_account.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tic_tax
msgid "Tax due"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_op_imposables_ht
msgid "Taxable transactions (excl. VAT)"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Technical Info"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "Technical Name"
msgstr ""

#. module: l10n_fr_account
#: model:ir.model.fields,field_description:l10n_fr_account.field_l10n_fr_fec_export_wizard__test_file
msgid "Test File"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid ""
"The encoding of this text file is UTF-8. The structure of file is CSV "
"separated by pipe '|'."
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_TIC_total
msgid "Total"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_tva_brute_autre
msgid "Transactions taxable at another rate (metropolitan France or DOM)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_op_non_imposables
msgid "Untaxed operations"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "ValidDate"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid "We use partner.id"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid ""
"When you download a FEC file, the lock date is set to the end date.\n"
"                If you want to test the FEC file generation, please tick the test file checkbox."
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_X1
msgid "X1"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_X2
msgid "X2"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_X3
msgid "X3"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_X4
msgid "X4"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_X5
msgid "X5 - TIC credit offset against VAT (carried forward from line X4)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Y1
msgid "Y1"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Y2
msgid "Y2"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Y3
msgid "Y3"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Y4
msgid "Y4"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Y5
msgid "Y5 - Refund of TIC balance requested (carried forward from line Y4)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Y6
msgid ""
"Y6 - TIC credit transferred to the head company on the 3310-CA3G "
"recapitulative return (carried forward from line Y4)"
msgstr ""

#. module: l10n_fr_account
#: model_terms:ir.ui.view,arch_db:l10n_fr_account.fec_export_wizard_view
msgid ""
"You are in test mode. The FEC file generation will not set the lock date."
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Z1
msgid "Z1"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Z2
msgid "Z2"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Z3
msgid "Z3"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Z4
msgid "Z4"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_Z5
msgid "Z5 - Total domestic consumption tax due (carried forward from line Z4)"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_15_2
msgid "of which VAT on imported products excluding petroleum products"
msgstr ""

#. module: l10n_fr_account
#: model:account.report.line,name:l10n_fr_account.tax_report_15_1
msgid "of which VAT on petroleum products"
msgstr ""
