# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
# Translators:
# <PERSON> <<EMAIL>>, 2025
# Wil O<PERSON>o, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "<span invisible=\"not show_availability\">Units</span>"
msgstr "<span invisible=\"not show_availability\">Eenheden</span>"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/product_card/product_card.js:0
msgid "All available quantity selected"
msgstr "Alle beschikbare hoeveelheden geselecteerd"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "All warehouses"
msgstr "Alle magazijnen"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__stock_notification_partner_ids
msgid "Back in stock Notifications"
msgstr "Meldingen Terug in voorraad"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Continue Selling"
msgstr "Verkoop hervatten"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__allow_out_of_stock_order
msgid "Continue selling when out-of-stock"
msgstr "Doorgaan met verkopen wanneer niet op voorraad"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Dear Customer,"
msgstr "Beste klant,"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""
"Standaard beschikbaarheidsmode ingesteld op nieuw aangemaakte "
"voorraadproducten. Dit kan worden gewijzigd op productniveau."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default visibility for custom messages."
msgstr "Standaard zichtbaarheid voor aangepaste berichten."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Get notified when back in stock"
msgstr "Ontvang een bericht wanneer er opnieuw voorraad is"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "How to display products having low quantities (on hand - reserved)"
msgstr ""
"Hoe toon je producten met lage hoeveelheden (in voorraad - gereserveerd)"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/controllers/main.py:0
msgid "Invalid Email"
msgstr "Ongeldige e-mail"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Invalid email"
msgstr "Ongeldige e-mail"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory Defaults"
msgstr "Standaard voorraadinstellingen"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Only"
msgstr "Nog"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Order Now"
msgstr "Bestel nu"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Out of Stock"
msgstr "Niet op voorraad"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/product/product.xml:0
msgid "Out of stock"
msgstr "Niet op voorraad"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Out-of-Stock"
msgstr "Niet op voorraad"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__out_of_stock_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__out_of_stock_message
msgid "Out-of-Stock Message"
msgstr "Bericht bij niet op voorraad"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product"
msgstr "Product"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_combo
msgid "Product Combo"
msgstr "Productcombinatie"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product Variant"
msgstr "Productvariant"

#. module: website_sale_stock
#: model:ir.actions.server,name:website_sale_stock.ir_cron_send_availability_email_ir_actions_server
msgid "Product: send email regarding products availability"
msgstr "Product: stuur een e-mail over de beschikbaarheid van producten"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Regards,"
msgstr "Met vriendelijke groet,"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/product_card/product_card.xml:0
msgid "Requested quantity not available"
msgstr "Gevraagde aantal niet beschikbaar"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Show Available Qty"
msgstr "Beschikbare hoeveelheid weergeven"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Show Threshold"
msgstr "Drempel tonen"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__show_availability
msgid "Show availability Qty"
msgstr "Beschikbaarheid hoeveelheid weergeven"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""
"Sommige producten zijn ondertussen onbeschikbaar en je winkelmandje is "
"bijgewerkt. Onze excuses voor het ongemak."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "The following product is now available."
msgstr "Het volgende product is nu beschikbaar."

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid "The item has not been added to your cart since it is not available."
msgstr ""
"Het product is niet aan je winkelwagen toegevoegd omdat het niet beschikbaar"
" is."

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
msgid "The product '%(product_name)s' is now available"
msgstr "Het product '%(product_name)s' is nu beschikbaar"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "This product is out of stock."
msgstr "Dit product is niet voorradig."

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Verplaatsing"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
msgid "Units"
msgstr "stuk(s)"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr "Magazijn"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "We'll notify you once the product is back in stock."
msgstr "We informeren je zodra het product weer op voorraad is."

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "Website"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website where this order has been placed, for eCommerce orders."
msgstr "Website waar deze order is geplaatst, voor E-commerce orders."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "You already added"
msgstr "Je hebt al"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "You already added all the available product in your cart."
msgstr "Je hebt de beschikbare producten al in je winkelmandje geplaatst."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "You already have %s Units in your cart."
msgstr "Je hebt al %s stuks in je winkelmandje."

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order_line.py:0
msgid ""
"You ask for %(desired_qty)s %(product_name)s but only %(new_qty)s is "
"available"
msgstr ""
"Je wenst %(desired_qty)s %(product_name)s maar er zijn er maar %(new_qty)s "
"beschikbaar"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid "You ask for %(desired_qty)s products but only %(new_qty)s is available"
msgstr ""
"Je vraagt %(desired_qty)s producten, maar er zijn er maar %(new_qty)s "
"beschikbaar"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "You ask for %(quantity1)s Units but only %(quantity2)s are available."
msgstr ""
"Je wenst %(quantity1)s stuks maar er zijn maar %(quantity2)s beschikbaar."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "in your cart."
msgstr "toegevoegd in je mandje."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "left in stock."
msgstr "op voorraad."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "only if below"
msgstr "alleen als onder de"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "<EMAIL>"
msgstr "<EMAIL>"
