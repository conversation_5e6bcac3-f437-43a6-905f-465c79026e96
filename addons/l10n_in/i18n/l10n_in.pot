# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-02 11:46+0000\n"
"PO-Revision-Date: 2025-01-02 11:46+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_account_bank_statement_line__l10n_in_journal_type
#: model:ir.model.fields,help:l10n_in.field_account_move__l10n_in_journal_type
msgid ""
"\n"
"        Select 'Sale' for customer invoices journals.\n"
"        Select 'Purchase' for vendor bills journals.\n"
"        Select 'Cash', 'Bank' or 'Credit Card' for journals that are used in customer or vendor payments.\n"
"        Select 'General' for miscellaneous operations journals.\n"
"        "
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__res_company__l10n_in_hsn_code_digit__4
msgid "4 Digits (turnover < 5 CR.)"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "4 digits, 6 digits or 8 digits"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__res_company__l10n_in_hsn_code_digit__6
msgid "6 Digits (turnover > 5 CR.)"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "6 digits or 8 digits"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__res_company__l10n_in_hsn_code_digit__8
msgid "8 Digits"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "8 digits"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "<strong>PAYMENT QR CODE</strong>"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid ""
"Activate this to start using Indian services in the production environment."
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/res_partner.py:0
msgid ""
"As per GSTN the country should be other than India, so it's recommended to"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/res_partner.py:0
msgid "As per GSTN the state should be %s, so it's recommended to"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_cess
msgid "BASE CESS"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_cess_rc
msgid "BASE CESS (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_cgst
msgid "BASE CGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_cgst_rc
msgid "BASE CGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_igst
msgid "BASE IGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_igst_rc
msgid "BASE IGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_sgst
msgid "BASE SGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_sgst_rc
msgid "BASE SGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_base_state_cess
msgid "BASE STATE CESS"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Bill of Entry Date"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Bill of Entry Number"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Buy credits"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.cess_tag_account
#: model:account.account.tag,name:l10n_in.tax_tag_cess
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "CESS"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_cess_rc
msgid "CESS (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.cgst_tag_account
#: model:account.account.tag,name:l10n_in.tax_tag_cgst
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "CGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_cgst_rc
msgid "CGST (RC)"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Cancelled"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Cancelled Credit Note"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "Check %s"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__module_l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Check GST Number Status"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.account_tag_closing_stock
msgid "Closing Stock"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid ""
"Connect to NIC (National Informatics Center) to submit e-waybill on posting."
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid ""
"Connect to NIC (National Informatics Center) to submit invoices on posting."
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__consumer
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__consumer
msgid "Consumer"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid ""
"Costs 1 credit per transaction. Free 200 credits will be available for the "
"first time."
msgstr ""

#. module: l10n_in
#. odoo-javascript
#: code:addons/l10n_in/static/src/components/hsn_autocomplete/hsn_autocomplete.js:0
msgid "Could not contact API"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_res_country_state
msgid "Country state"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__create_date
msgid "Created on"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Credit Note"
msgstr ""

#. module: l10n_in
#: model:iap.service,unit_name:l10n_in.iap_service_l10n_in_edi
msgid "Credits"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__deemed_export
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__deemed_export
msgid "Deemed Export"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_partner__display_pan_warning
#: model:ir.model.fields,field_description:l10n_in.field_res_users__display_pan_warning
msgid "Display pan warning"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Draft"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Draft Credit Note"
msgstr ""

#. module: l10n_in
#: model:res.partner.industry,full_name:l10n_in.eco_under_section_52
msgid "E-Commerce operator liable to deduct TCS under section 52"
msgstr ""

#. module: l10n_in
#: model:res.partner.industry,full_name:l10n_in.eco_under_section_9_5
msgid "E-Commerce operator liable to pay tax under section 9(5)"
msgstr ""

#. module: l10n_in
#: model:res.partner.industry,name:l10n_in.eco_under_section_52
msgid "ECO liable to deduct TCS u/s 52"
msgstr ""

#. module: l10n_in
#: model:res.partner.industry,name:l10n_in.eco_under_section_9_5
msgid "ECO liable to pay GST u/s 9(5)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_exempt
msgid "EXEMPT"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_res_company__l10n_in_edi_production_env
#: model:ir.model.fields,help:l10n_in.field_res_config_settings__l10n_in_edi_production_env
msgid "Enable the use of production credentials"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid ""
"Enable this to activate Tax Deduction Source and Tax Collection Source."
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Enable this to check the GST Number status"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "Ensure that the HSN/SAC Code consists either %s in invoice lines"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Export India"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/template_in.py:0
msgid "Export/SEZ"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_res_partner__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_res_users__l10n_in_gst_treatment
msgid "GST Treatment"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_gstin
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_gstin
msgid "GSTIN"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "GSTIN:"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid ""
"Generate Vendor Payment Order file(csv file), upload to your bank to make "
"the payments"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "Go to Company configuration"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
msgid "Group By"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_product_product__l10n_in_hsn_warning
#: model:ir.model.fields,field_description:l10n_in.field_product_template__l10n_in_hsn_warning
msgid "HSC/SAC warning"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_company__l10n_in_hsn_code_digit
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__l10n_in_hsn_code_digit
msgid "HSN Code Digit"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "HSN Summary"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/product_template.py:0
msgid "HSN code field must consist solely of digits and be %s in length."
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "HSN/SAC"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_move_line__l10n_in_hsn_code
#: model:ir.model.fields,field_description:l10n_in.field_product_product__l10n_in_hsn_code
#: model:ir.model.fields,field_description:l10n_in.field_product_template__l10n_in_hsn_code
msgid "HSN/SAC Code"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid ""
"HSN/SAC Digit Validation for GST Compliance based on your Aggregate Annual "
"Turnover (AATO)."
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "HSN/SAC Validation"
msgstr ""

#. module: l10n_in
#: model:account.cash.rounding,name:l10n_in.cash_rounding_in_half_up
msgid "Half Up"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_product_product__l10n_in_hsn_code
#: model:ir.model.fields,help:l10n_in.field_product_template__l10n_in_hsn_code
msgid "Harmonized System Nomenclature/Services Accounting Code"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_iap_account
msgid "IAP Account"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__id
msgid "ID"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.igst_tag_account
#: model:account.account.tag,name:l10n_in.tax_tag_igst
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "IGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_igst_rc
msgid "IGST (RC)"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Import India"
msgstr ""

#. module: l10n_in
#: model:ir.ui.menu,name:l10n_in.account_reports_in_statements_menu
msgid "India"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_form_view
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_tree_view
msgid "India Port Code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__module_l10n_in_edi
msgid "Indian Electronic Invoicing"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__module_l10n_in_edi_ewaybill
msgid "Indian Electronic Waybill"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_uom_uom__l10n_in_code
msgid "Indian GST UQC"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Indian Integration"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_company__l10n_in_edi_production_env
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__l10n_in_edi_production_env
msgid "Indian Production Environment"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__module_l10n_in_withholding
msgid "Indian TDS and TCS"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_port_code
msgid "Indian port code"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/template_in.py:0
msgid "Inter State"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/template_in.py:0
msgid "Intra State"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "Invalid sequence as per GST rule 46(b)"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "Journal Items(s)"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_journal_type
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_journal_type
msgid "Journal Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_company__l10n_in_gst_state_warning
#: model:ir.model.fields,field_description:l10n_in.field_res_partner__l10n_in_gst_state_warning
#: model:ir.model.fields,field_description:l10n_in.field_res_users__l10n_in_gst_state_warning
msgid "L10N In Gst State Warning"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_tax__l10n_in_tax_type
msgid "L10N In Tax Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_warning
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_warning
msgid "L10N In Warning"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/template_in.py:0
msgid "LUT - Export/SEZ"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__group_l10n_in_reseller
#: model:res.groups,name:l10n_in.group_l10n_in_reseller
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Manage Reseller(E-Commerce)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_nil_rated
msgid "NIL-RATED"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_gst_supplies
msgid "NON GST SUPPLIES"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_base_cess
msgid "NON ITC BASE CESS"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_base_cgst
msgid "NON ITC BASE CGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_base_igst
msgid "NON ITC BASE IGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_base_sgst
msgid "NON ITC BASE SGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_cess
msgid "NON ITC CESS"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_cess_rc
msgid "NON ITC CESS (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_cgst
msgid "NON ITC CGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_cgst_rc
msgid "NON ITC CGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_igst
msgid "NON ITC IGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_igst_rc
msgid "NON ITC IGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_sgst
msgid "NON ITC SGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_non_itc_sgst_rc
msgid "NON ITC SGST (RC)"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_account_bank_statement_line__l10n_in_reseller_partner_id
#: model:ir.model.fields,help:l10n_in.field_account_move__l10n_in_reseller_partner_id
msgid "Only Registered Reseller"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_base_cess
msgid "Other NON ITC BASE CESS"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_base_cgst
msgid "Other NON ITC BASE CGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_base_igst
msgid "Other NON ITC BASE IGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_base_sgst
msgid "Other NON ITC BASE SGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_cess
msgid "Other NON ITC CESS"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_cess_rc
msgid "Other NON ITC CESS (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_cgst
msgid "Other NON ITC CGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_cgst_rc
msgid "Other NON ITC CGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_igst
msgid "Other NON ITC IGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_igst_rc
msgid "Other NON ITC IGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_sgst
msgid "Other NON ITC SGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_other_non_itc_sgst_rc
msgid "Other NON ITC SGST (RC)"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__overseas
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__overseas
msgid "Overseas"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_company__l10n_in_pan
#: model:ir.model.fields,field_description:l10n_in.field_res_partner__l10n_in_pan
#: model:ir.model.fields,field_description:l10n_in.field_res_users__l10n_in_pan
msgid "PAN"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_company__l10n_in_pan_type
msgid "PAN Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_res_partner__l10n_in_pan
#: model:ir.model.fields,help:l10n_in.field_res_users__l10n_in_pan
msgid ""
"PAN enables the department to link all transactions of the person with the department.\n"
"These transactions include taxpayments, TDS/TCS credits, returns of income/wealth/gift/FBT, specified transactions, correspondence, and so on.\n"
"Thus, PAN acts as an identifier for the person with the tax department."
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_res_company__l10n_in_pan
msgid ""
"PAN enables the department to link all transactions of the person with the department.\n"
"These transactions include taxpayments, TDS/TCS credits, returns of income/wealth/gift/FBT,specified transactions, correspondence, and so on.\n"
"Thus, PAN acts as an identifier for the person with the tax department."
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_view_partner_form
msgid ""
"PAN number is not same as the 3rd to 12th characters of the GST number."
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid ""
"Partner %(partner_name)s (%(partner_id)s) GSTIN is required under GST "
"Treatment %(name)s"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_state_id
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_state_id
msgid "Place of supply"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.place_of_supply
msgid "Place of supply:"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/res_config_settings.py:0
msgid ""
"Please ensure that at least one Indian service and production environment is"
" enabled, and save the configuration to proceed with purchasing credits."
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "Please set a valid TIN Number on the Place of Supply %s"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__name
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
msgid "Port"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__code
msgid "Port Code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_shipping_port_code_id
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_shipping_port_code_id
msgid "Port code"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_product_template
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Product"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Production Environment"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.view_move_line_tree_hsn_l10n_in
msgid "Products"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Quantity"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Rate %"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__composition
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__composition
msgid "Registered Business - Composition"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__regular
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__regular
msgid "Registered Business - Regular"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_reseller_partner_id
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_reseller_partner_id
msgid "Reseller"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_tax__l10n_in_reverse_charge
msgid "Reverse charge"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.sgst_tag_account
#: model:account.account.tag,name:l10n_in.tax_tag_sgst
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "SGST"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_sgst_rc
msgid "SGST (RC)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_state_cess
msgid "STATE CESS"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/template_in.py:0
msgid ""
"SUPPLY MEANT FOR EXPORT/SUPPLY TO SEZ UNIT OR SEZ DEVELOPER FOR AUTHORISED "
"OPERATIONS ON PAYMENT OF INTEGRATED TAX."
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/template_in.py:0
msgid ""
"SUPPLY MEANT FOR EXPORT/SUPPLY TO SEZ UNIT OR SEZ DEVELOPER FOR AUTHORISED "
"OPERATIONS UNDER BOND OR LETTER OF UNDERTAKING WITHOUT PAYMENT OF INTEGRATED"
" TAX."
msgstr ""

#. module: l10n_in
#. odoo-javascript
#: code:addons/l10n_in/static/src/components/hsn_autocomplete/hsn_autocomplete.js:0
msgid "Searching..."
msgstr ""

#. module: l10n_in
#: model:iap.service,description:l10n_in.iap_service_l10n_in_edi
msgid "Send electronic document to Indian government"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_shipping_bill_date
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_shipping_bill_date
msgid "Shipping bill date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_shipping_bill_number
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_shipping_bill_number
msgid "Shipping bill number"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__special_economic_zone
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__special_economic_zone
msgid "Special Economic Zone"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__state_id
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
msgid "State"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "TDS and TCS"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_country_state__l10n_in_tin
msgid "TIN Number"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_res_country_state__l10n_in_tin
msgid "TIN number-first two digits"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Taxable Value"
msgstr ""

#. module: l10n_in
#: model:ir.model.constraint,message:l10n_in.constraint_l10n_in_port_code_code_uniq
msgid "The Port Code must be unique!"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/company.py:0
msgid "The entered PAN seems invalid. Please enter a valid PAN."
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid ""
"The invoice number should not exceed 16 characters\n"
"and must only contain '-' (hyphen) and '/' (slash) as special characters"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_account_tax__l10n_in_reverse_charge
msgid "Tick this if this tax is reverse charge. Only for Indian accounting"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__uin_holders
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__uin_holders
msgid "UIN Holders"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "UPI ID:"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_company__l10n_in_upi_id
msgid "UPI Id"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_uom_uom__l10n_in_code
msgid "Unique Quantity Code (UQC) under GST"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__unregistered
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__unregistered
msgid "Unregistered Business"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Use this if setup with Reseller(E-Commerce)."
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Vendor Bill"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Vendor Credit Note"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__module_l10n_in_enet_batch_payment
msgid "Vendor Payment"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid "View %s"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/template_in.py:0
msgid "Within %s"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/account_invoice.py:0
msgid ""
"Your company %s needs to have a correct address in order to validate this invoice.\n"
"Set the address of your company (Don't forget the State field)"
msgstr ""

#. module: l10n_in
#: model:account.account.tag,name:l10n_in.tax_tag_zero_rated
msgid "ZERO-RATED"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_tax__l10n_in_tax_type__cess
msgid "cess"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_tax__l10n_in_tax_type__cgst
msgid "cgst"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_view_partner_form
msgid "e.g. **********"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/product_template.py:0
msgid "either 4, 6 or 8"
msgstr ""

#. module: l10n_in
#. odoo-python
#: code:addons/l10n_in/models/product_template.py:0
msgid "either 6 or 8"
msgstr ""

#. module: l10n_in
#. odoo-javascript
#: code:addons/l10n_in/static/src/components/hsn_autocomplete/hsn_autocomplete.js:0
msgid "hsn description field"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_tax__l10n_in_tax_type__igst
msgid "igst"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_tax__l10n_in_tax_type__sgst
msgid "sgst"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_view_partner_form
#: model_terms:ir.ui.view,arch_db:l10n_in.view_company_form
msgid "update it"
msgstr ""