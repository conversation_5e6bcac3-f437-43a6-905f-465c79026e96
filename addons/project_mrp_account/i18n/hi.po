# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_mrp_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_mrp_account
#. odoo-python
#: code:addons/project_mrp_account/models/stock_move.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the project "
"'%(project_name)s' linked to the manufacturing order."
msgstr ""

#. module: project_mrp_account
#: model_terms:ir.ui.view,arch_db:project_mrp_account.mrp_production_form_view_inherit_project_mrp_account
msgid "<span class=\"o_stat_text\">Analytic Account</span>"
msgstr ""

#. module: project_mrp_account
#. odoo-python
#: code:addons/project_mrp_account/models/mrp_production.py:0
msgid "Analytic Accounts"
msgstr ""

#. module: project_mrp_account
#: model:product.template,name:project_mrp_account.product_product_dinning_table_product_template
msgid "Dining Table"
msgstr ""

#. module: project_mrp_account
#: model:ir.model.fields,field_description:project_mrp_account.field_mrp_production__has_analytic_account
msgid "Has Analytic Account"
msgstr ""

#. module: project_mrp_account
#: model:ir.model,name:project_mrp_account.model_mrp_production
msgid "Manufacturing Order"
msgstr "विनिर्माण आदेश"

#. module: project_mrp_account
#. odoo-python
#: code:addons/project_mrp_account/models/project_project.py:0
msgid "Manufacturing Orders"
msgstr ""

#. module: project_mrp_account
#: model:ir.model,name:project_mrp_account.model_project_project
msgid "Project"
msgstr ""

#. module: project_mrp_account
#: model:ir.model,name:project_mrp_account.model_stock_move
msgid "Stock Move"
msgstr "चाल स्टॉक"

#. module: project_mrp_account
#: model:ir.model,name:project_mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: project_mrp_account
#: model:ir.model,name:project_mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr ""
