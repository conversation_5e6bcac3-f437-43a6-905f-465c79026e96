# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_loyalty
# 
# Translators:
# emre oktem, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "<strong> - </strong>"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "<strong>Coupons - </strong>"
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "A coupon is needed for coupon programs."
msgstr "Kupon programları için bir kupon gereklidir."

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
msgid "A coupon with the same code was found."
msgstr "Aynı koda sahip bir kupon bulundu."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "All websites"
msgstr "Tüm web siteleri"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__ecommerce_ok
msgid "Available on Website"
msgstr "Web sitesinde mevcut"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/controllers/payment.py:0
msgid ""
"Cannot process payment: applied reward was changed or has expired.\n"
"Please refresh the page and try again."
msgstr ""

#. module: website_sale_loyalty
#. odoo-javascript
#: code:addons/website_sale_loyalty/static/src/js/portal_loyalty_card.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Claim"
msgstr "İddia"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Code:"
msgstr "Kod:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Costs"
msgstr "Maliyetler"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "Could not apply the promo code:"
msgstr "Promosyon kodu uygulanamadı:"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__coupon_id
msgid "Coupon"
msgstr "Kupon"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_coupon_share
msgid "Create links that apply a coupon and redirect to a specific page"
msgstr ""
"Kupon uygulayan ve belirli bir sayfaya yönlendiren bağlantılar oluşturun"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_sale_order__disabled_auto_rewards
msgid "Disabled Auto Rewards"
msgstr "Otomatik Ödüller Devre Dışı Bırakıldı"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_discount_loyalty_type_config
msgid "Discount & Loyalty"
msgstr "İndirim ve Sadakat"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discount:"
msgstr "İndirim:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discounted amount"
msgstr "İndirimli tutar"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Done"
msgstr "Yapıldı"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Expired Date:"
msgstr "Geçerlilik Tarihi:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Generate Short Link"
msgstr "Kısa Bağlantı Oluştur"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.sale_coupon_result
msgid "Gift card or discount code..."
msgstr "Hediye çeki veya indirim kodu..."

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_gift_ewallet_type_config
msgid "Gift cards & eWallet"
msgstr "Hediye kartları & e-Cüzdan"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__id
msgid "ID"
msgstr "ID"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Invalid or expired promo code."
msgstr "Geçersiz veya süresi dolmuş promosyon kodu."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_loyalty
msgid "Loyalty"
msgstr "Sadakat"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Sadakat Kuponu"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Sadakat Programı"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.res_config_settings_view_form_inherit_website_sale_loyalty
msgid "Loyalty Programs"
msgstr "Sadakat Programları"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Sadakat Kuralları"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Pay with eWallet"
msgstr "eCüzdan ile ödeme yapın"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_id
msgid "Program"
msgstr "Kampanya"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_website_id
msgid "Program Website"
msgstr "Program Websitesi"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__promo_code
msgid "Promo Code"
msgstr "Promosyon kodu"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "Provide either a coupon or a program."
msgstr "Bir kupon veya bir program sağlayın."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__redirect
msgid "Redirect"
msgstr "Yeniden yönlendir"

#. module: website_sale_loyalty
#: model:ir.model.fields,help:website_sale_loyalty.field_coupon_share__program_website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_rule__website_id
msgid "Restrict to a specific website."
msgstr "Belirli bir web sitesine sınırlayın."

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order
msgid "Sales Order"
msgstr "Satış Siparişi"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order_line
msgid "Sales Order Line"
msgstr "Satış Sipariş Satırı"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_card_view_tree_inherit_website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_tree_inherit_website_sale_loyalty
msgid "Share"
msgstr "Paylaş"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "Share %s"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__share_link
msgid "Share Link"
msgstr "Bağlantı Paylaş"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Share Loyalty Card"
msgstr "Sadakat Kartını Paylaş"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.snippet_options
msgid "Show Discount in Subtotal"
msgstr "İndirimi Ara Toplamda Göster"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/controllers/main.py:0
msgid ""
"The coupon will be automatically applied when you add something in your "
"cart."
msgstr ""
"Sepetinize bir şey eklediğinizde kupon otomatik olarak uygulanacaktır."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "The following promo code was applied on your order:"
msgstr "Siparişinize aşağıdaki promosyon kodu uygulandı:"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
msgid "The promo code must be unique."
msgstr "Promosyon kodu benzersiz olmalıdır."

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "The shared website should correspond to the website of the program."
msgstr "Paylaşılan web sitesi, programın web sitesine karşılık gelmelidir."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Use"
msgstr "Kullan"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_rule__website_id
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "Website"
msgstr "Websitesi"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid ""
"You can share this promotion with your customers.\n"
"                            It will be applied at checkout when the customer uses this link."
msgstr ""
"Bu promosyonu müşterilerinizle paylaşabilirsiniz.\n"
"Müşteri bu bağlantıyı kullandığında ödeme sırasında uygulanacaktır."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have"
msgstr "Var"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have successfully applied the following code:"
msgstr "Aşağıdaki kodu başarıyla uyguladınız:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "in your ewallet"
msgstr "e-cüzdanınızda"
