# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_fleet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_form_inherit_hr
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_text\"><PERSON><PERSON><PERSON></span>"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_view_form_inherit_hr
msgid ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Employee</span>"
msgstr ""
"<span class=\"o_stat_value\">1</span>\n"
"                        <span class=\"o_stat_text\">Karyawan</span>"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Templat rencana kegiatan"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Assignment"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_view_list
msgid "Attachments"
msgstr "Lampiran"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
msgid "Cannot remove address from employees with linked cars."
msgstr "Tidak dapat menghapus alamat dari karyawan dengan mobil yang di-link."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__employee_cars_count
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__employee_cars_count
msgid "Cars"
msgstr "Mobil-Mobil"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.hr_departure_wizard_view_form
msgid "Company Car"
msgstr "Mobil Perusahaan"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_employee_view_list
msgid "Current Driver"
msgstr "Pengemudi Saat Ini"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Departure Wizard"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_form_inherit_hr
msgid "Driver"
msgstr "pengemudi"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_contract__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_services__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_odometer__driver_employee_id
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_services_view_form_inherit_hr
msgid "Driver (Employee)"
msgstr "Pengemudi (Karyawan)"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Sejarah pengemudi untuk kendaraan"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_search_inherit_hr
msgid "Employee"
msgstr "Karyawan"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
msgid "Employee %s is not linked to a vehicle."
msgstr "Karyawan %s tidak di-link ke kendaraan."

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_name
msgid "Employee Name"
msgstr "Nama Karyawan"

#. module: hr_fleet
#: model:ir.model.fields.selection,name:hr_fleet.selection__mail_activity_plan_template__responsible_type__fleet_manager
msgid "Fleet Manager"
msgstr "Manajer Armada"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
msgid "Fleet Manager is limited to Employee plans."
msgstr "Manajer Armada terbatas hanya untuk rencana Karyawan."

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Fleet Mobility Card"
msgstr "Kartu Mobilitas Armada"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__future_driver_employee_id
msgid "Future Driver (Employee)"
msgstr "Pengemudi Berikutnya (Karyawan)"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/employee.py:0
msgid "History Employee Cars"
msgstr "Sejarah Mobil Karyawan"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__license_plate
msgid "License Plate"
msgstr "Pelat"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__mobility_card
msgid "Mobility Card"
msgstr "Kartu Mobilitas"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__attachment_number
msgid "Number of Attachments"
msgstr "Jumlah Lampiran"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Log odometer untuk kendaraan"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee_public
msgid "Public Employee"
msgstr "Karyawan Publik"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/fleet_vehicle.py:0
#: code:addons/hr_fleet/models/fleet_vehicle_log_contract.py:0
msgid "Related Employee"
msgstr "Karyawan Terkait"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__release_campany_car
msgid "Release Company Car"
msgstr "Lepas Mobil Perusahaan"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Layanan untuk kendaraan"

#. module: hr_fleet
#. odoo-python
#: code:addons/hr_fleet/models/mail_activity_plan_template.py:0
msgid "The vehicle of employee %(employee)s is not linked to a fleet manager."
msgstr "Kendaraan karyawan %(employee)s tidak terhubung ke manajer armada."

#. module: hr_fleet
#. odoo-javascript
#: code:addons/hr_fleet/static/src/views/hr_fleet_kanban/hr_fleet_kanban_controller.xml:0
msgid "Upload"
msgstr "Unggah"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_res_users
msgid "User"
msgstr "Pengguna"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle
msgid "Vehicle"
msgstr "Kendaraan"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Kontrak Kendaraan"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__car_ids
msgid "Vehicles (private)"
msgstr "Kendaraan (pribadi)"
