# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:46+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Jun<PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    マイルストンが見つかりませんでした。新しいマイルストンを作成しましょう！\n"
"                </p><p>\n"
"                    成功するために到達すべき重要な進捗ポイントを管理する\n"
"                </p>\n"
"            "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"\n"
");\n"
"\n"
"export class ProjectTaskFormController extends FormControllerWithHTMLExpander {\n"
"    setup() {\n"
"        super.setup();\n"
"        this.notifications = useService(\"notification\");\n"
"    }\n"
"\n"
"    /**\n"
"     * @override\n"
"     */\n"
"    getStaticActionMenuItems() {\n"
"        return {\n"
"            ...super.getStaticActionMenuItems(),\n"
"            openHistoryDialog: {\n"
"                sequence: 50,\n"
"                icon: \"fa fa-history\",\n"
"                description: _t(\"Version History\"),\n"
"                callback: () => this.openHistoryDialog(),\n"
"            },\n"
"        };\n"
"    }\n"
"\n"
"    get deleteConfirmationDialogProps() {\n"
"        const deleteConfirmationDialogProps = super.deleteConfirmationDialogProps;\n"
"        if (!this.model.root.data.subtask_count) {\n"
"            return deleteConfirmationDialogProps;\n"
"        }\n"
"        return {\n"
"            ...deleteConfirmationDialogProps,\n"
"            body: subTaskDeleteConfirmationMessage,\n"
"        }\n"
"    }\n"
"\n"
"    async openHistoryDialog() {\n"
"        const record = this.model.root;\n"
"        const versionedFieldName = 'description';\n"
"        const historyMetadata = record.data[\"html_field_history_metadata\"]?.[versionedFieldName];\n"
"        if (!historyMetadata) {\n"
"            this.notifications.add(\n"
"                escape(_t(\n"
"                    \"The task description lacks any past content that could be restored at the moment.\"\n"
"                ))\n"
"            );\n"
"            return;\n"
"        }\n"
"\n"
"        this.dialogService.add(\n"
"            HistoryDialog,\n"
"            {\n"
"                title: _t(\"Task Description History\"),\n"
"                noContentHelper: markup(\n"
"                    "
msgstr ""
"\n"
");\n"
"\n"
"export class ProjectTaskFormController extends FormControllerWithHTMLExpander {\n"
"    setup() {\n"
"        super.setup();\n"
"        this.notifications = useService(\"notification\");\n"
"    }\n"
"\n"
"    /**\n"
"     * @override\n"
"     */\n"
"    getStaticActionMenuItems() {\n"
"        return {\n"
"            ...super.getStaticActionMenuItems(),\n"
"            openHistoryDialog: {\n"
"                sequence: 50,\n"
"                icon: \"fa fa-history\",\n"
"                description: _t(\"Version History\"),\n"
"                callback: () => this.openHistoryDialog(),\n"
"            },\n"
"        };\n"
"    }\n"
"\n"
"    get deleteConfirmationDialogProps() {\n"
"        const deleteConfirmationDialogProps = super.deleteConfirmationDialogProps;\n"
"        if (!this.model.root.data.subtask_count) {\n"
"            return deleteConfirmationDialogProps;\n"
"        }\n"
"        return {\n"
"            ...deleteConfirmationDialogProps,\n"
"            body: subTaskDeleteConfirmationMessage,\n"
"        }\n"
"    }\n"
"\n"
"    async openHistoryDialog() {\n"
"        const record = this.model.root;\n"
"        const versionedFieldName = 'description';\n"
"        const historyMetadata = record.data[\"html_field_history_metadata\"]?.[versionedFieldName];\n"
"        if (!historyMetadata) {\n"
"            this.notifications.add(\n"
"                escape(_t(\n"
"                    \"The task description lacks any past content that could be restored at the moment.\"\n"
"                ))\n"
"            );\n"
"            return;\n"
"        }\n"
"\n"
"        this.dialogService.add(\n"
"            HistoryDialog,\n"
"            {\n"
"                title: _t(\"Task Description History\"),\n"
"                noContentHelper: markup(\n"
"                    "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "評価数"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr "タスク数"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "#{task.stage_id.name}"
msgstr "#{task.stage_id.name}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(closedCount)s sub-tasks closed out of %(totalCount)s"
msgstr "%(closedCount)s のサブタスクが終了しました。(全%(totalCount)sのうち)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s"
msgstr "%(closed_task_count)s / %(task_count)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"
msgstr "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(count1)s/%(count2)s"
msgstr "%(count1)s/%(count2)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s Dashboard"
msgstr "%(name)s ダッシュボード"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Burndown Chart"
msgstr "%(name)sのバーンダウンチャート"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Milestones"
msgstr "%(name)sのマイルストン"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Rating"
msgstr "%(name)sの評価"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Tasks Analysis"
msgstr "%(name)sのタスク分析"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "%(partner_name)s's Tasks"
msgstr "%(partner_name)sのタスク"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project_stage.py:0
#: code:addons/project/models/project_task.py:0
#: code:addons/project/models/project_task_type.py:0
msgid "%s (copy)"
msgstr "%s (コピー)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "%s closed tasks"
msgstr "%s クローズ済タスク"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(期限"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(最終プロジェクト更新)、"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "(other) tasks to which you do not have access."
msgstr "(他の)タスクにアクセスできません。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid ""
",\n"
"    <br/><br/>"
msgstr ", <br/><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- 以下に達した:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "<b>ドラッグ &amp; ドロップ</b> してタスクのステージを変更します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this "
"task won't be notified of the note you are logging unless you specifically "
"tag them)</i>. Use @ <b>mentions</b> to ping a colleague or # "
"<b>mentions</b> to reach an entire team."
msgstr ""
"<b>メモを記録</b>、社内コミュニケーション用 "
"<i>(このタスクをフォローしている人たちには、特にタグを付けない限り、あなたがログに記録しているメモは通知されません)</i>。同僚をピン付するには@"
" <b>メンション</b>を使用、またはチーム全体に知らせるには# <b>メンション</b>を使用して下さい。"

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/><br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the <strong t-out=\"object.name or ''\">Planning and budget</strong> task\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 13px;text-align:center;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br/>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey has been sent because your task has been moved to the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br/>Best regards,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                <t t-out=\"partner.name or ''\">Brandon Freeman</t>様<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                こんにちは。<br/><br/>\n"
"            </t>\n"
"            以下に関連した当社のサービスの評価アンケートにご協力をお願い致します: <strong t-out=\"object.name or ''\">計画と予算</strong>タスク\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                担当: <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 13px;text-align:center;\">\n"
"                    <strong>当社のサービスに関するご感想をお聞かせ下さい。</strong><br/>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(以下より1つを選択して下さい)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            フィードバックを頂きありがとうございました。引き続き改善に努めて参ります。\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">お客様のチケットがステージ: <b t-out=\"object.stage_id.name or ''\">進行中</b> ステージ</span> に移動したため、このお客様アンケートが送信されました。\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">このお客様満足度アンケートは <b t-out=\"object.project_id.rating_status_period or ''\">毎週</b> タスクが <b t-out=\"object.stage_id.name or ''\">進行中</b> ステージにある限り送信されます。</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br/>宜しくお願い致します。</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""
"<div>\n"
"     <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>様、<br/>\n"
"    無事にプロジェクト \"<strong t-out=\"object.name or ''\">リノベーション</strong>\"が完了いたしました。\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">このメールはお客様のプロジェクトが <b t-out=\"object.stage_id.name or ''\">完了</b>ステージに移動したため送信されました。</span>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/><br/>\n"
"    Thank you for contacting us. We appreciate your interest in our products/services.<br/>\n"
"    Our team is currently reviewing your inquiry and will respond to your email as soon as possible.<br/>\n"
"    If you have any further questions or concerns in the meantime, please do not hesitate to let us know. We are here to help.<br/><br/>\n"
"    Thank you for your patience.<br/>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>様、<br/><br/>\n"
"    当社のプロダクト/サービスにご興味をお持ち頂きありがとうございます。<br/>\n"
"    現在、当社のチームがお客様のお問合せを確認中です。なるべく早くメールにてお返事させて頂きます。<br/>\n"
"    さらにご質問やご不明な点がございましたら、ご遠慮なくお知らせ下さい。私たちがお手伝いさせて頂きます。<br/><br/>\n"
"    ご理解のほどよろしくお願い致します。<br/>\n"
"    宜しくお願い致します。\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> 非公開"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"顧客評価</b>は以下のプロジェクトでは無効化されています: <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>編集モードへ戻る"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i title=\"Private Task\" class=\"fa fa-lock\"/>"
msgstr "<i title=\"Private Task\" class=\"fa fa-lock\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">ステージ：</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Assignees</small>"
msgstr "<small class=\"text-muted\">割当先</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">顧客</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Done</span>"
msgstr "<span class=\"fw-normal\"> 完了</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Tasks</span>"
msgstr "<span class=\"fw-normal\"> タスク</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<span class=\"o_stat_text\">Blocked Tasks</span>"
msgstr "<span class=\"o_stat_text\">ブロック済タスク</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">最終評価</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Parent Task</span>"
msgstr "<span class=\"o_stat_text\">親タスク</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text\">サブタスク</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">タスク</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"text-muted o_row ps-1 pb-3\">Send a rating request:</span>"
msgstr "<span class=\"text-muted o_row ps-1 pb-3\">評価リクエストを送信:</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">A rating request will be sent as soon as the task reaches a stage on which a Rating Email Template is defined.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Rating requests will be sent as long as the task remains in a stage on which a Rating Email Template is defined.</span>\n"
"                                                </span>"
msgstr ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">タスクが評価メールテンプレートが定義された段階に達すると、すぐに評価リクエストが送信されます。</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">評価リクエストは、評価メールテンプレートが定義されているステージにタスクが残っている限り送信されます。</span>\n"
"                                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>レポーティング</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>閲覧</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>締切:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>マイルストン:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>プロジェクト:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>マイルストン</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "このエイリアスの新しいレコードを作成する時にデフォルト値を与えるためのPython辞書です。"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr "プロジェクト共有アクセスでは、協力者を複数回選択することはできません。重複を削除して再度試して下さい。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created and is not part of any project."
msgstr "新規タスクが作成されましたが、どのプロジェクトにも属していません。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created in the \"%(project_name)s\" project."
msgstr "新規タスクが \"%(project_name)s\"プロジェクトに作成されました。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr "個人ステージは、対応するユーザにしか見えないため、プロジェクトにリンクさせることはできません。"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_private_task_has_no_parent
msgid "A private task cannot have a parent."
msgstr "非公開タスクは親を持つことはできません。"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_recurring_task_has_no_parent
msgid "A subtask cannot be recurrent."
msgstr "サブタスクは定期的であることはできません。"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "同じ名前のタグがすでに存在します。"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "1つのタスクはユーザごとに1つの個人ステージしか持つことはできません。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Eメール受信対象"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__access_mode
msgid "Access Mode"
msgstr "アクセスモード"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "アクセス警告"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "アクティブ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "活動"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外活動文字装飾"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_project_task_plan
#: model:ir.ui.menu,name:project.mail_activity_plan_menu_config_project
msgid "Activity Plans"
msgstr "活動計画"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "活動タイプ"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Progress Report\", \"Stand-up Meeting\", ...)"
msgstr ""
"活動プランは、数回のクリックで活動リストを割当てるために使用されます。\n"
"                    (例 \"進捗レポート\"、\"スタンドアップミーティング\"、 ...)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Add Milestone"
msgstr "マイルストンを追加"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.js:0
msgid "Add Sub-tasks"
msgstr "サブタスクを追加"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add a sub-task"
msgstr "サブタスクを追加"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr "<b>ステージ</b> でタスクを整理するためにカラムを追加します。<i>例: 新規 - 進行中 - 完了</i>。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Add details about this task..."
msgstr "このタスクに詳細を追加します"

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "メールに表示するコンテンツを追加する"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid ""
"Add project-specific property fields on tasks to customize your project "
"management process."
msgstr "タスクにプロジェクト固有のプロパティフィールドを追加して、プロジェクト管理プロセスをカスタマイズして下さい。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add your task once it is ready."
msgstr "用意ができたらタスクを追加します"

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "管理者"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Agile Scrum"
msgstr "アジャイル・スクラム"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "エイリアス"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "エイリアス連絡先セキュリティ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain_id
msgid "Alias Domain"
msgstr "エイリアスドメイン"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias Domain Name"
msgstr "エイリアスドメイン名"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_full_name
msgid "Alias Email"
msgstr "エイリアスEメール"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "エイリアス名"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_status
msgid "Alias Status"
msgstr "エイリアスステータス"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_status
msgid "Alias status assessed on the last message received."
msgstr "最後に受信したメッセージで評価されたエイリアスステータス"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "エイリアス対象モデル"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "All"
msgstr "全て"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management_all_tasks
msgid "All Tasks"
msgstr "全てのタスク"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "全ての内部ユーザ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allocated_hours
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__allocated_hours
msgid "Allocated Time"
msgstr "割当時間"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_allocated_hours_template
msgid "Allocated Time:"
msgstr "割当時間:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Analytic"
msgstr "分析"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__auto_account_id
msgid "Analytic Account"
msgstr "分析勘定"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "分析"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr "チームがプロジェクトのタスクをどれくらいの早さで完了させているかを分析し、すべてが計画通りに進んでいるかを確認します。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr "プロジェクトの進捗状況や従業員のパフォーマンスを分析します。"

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__share_link
msgid "Anyone with this link can access the project in read mode."
msgstr "このリンクがあれば、誰でもプロジェクトを閲覧モードで閲覧できます。"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__03_approved
msgid "Approved"
msgstr "承認済"

#. module: project
#: model:project.tags,name:project.project_tags_07
msgid "Architecture"
msgstr "アーキテクチャ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid "Archive Account"
msgstr "アカウントをアーカイブ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid "Archive Accounts"
msgstr "アカウントをアーカイブ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "ステージをアーカイブする"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Archived"
msgstr "アーカイブ済"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "本当に続けますか？"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "本当にこのステージを削除しますか？"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: code:addons/project/static/src/components/subtask_one2many_field/subtask_list_renderer.js:0
msgid "Are you sure you want to delete this record?"
msgstr "本当にこのレコードを削除しますか？"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Are you sure you want to restore this version ?"
msgstr "本当にこのバージョンを復元しますか？"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "矢印"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "矢印アイコン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assembling"
msgstr "アセンブリ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Assign a responsible to your task"
msgstr "タスクに担当者を割り当てる"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assigned"
msgstr "割当済"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "担当者"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.open_view_blocked_by_list_view
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Assignees"
msgstr "担当者"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignement Date"
msgstr "割当日"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "割当日"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "割り当て日"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "リスク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Attach all documents or links to the task directly, to have all research "
"information centralized."
msgstr "全てのドキュメントやリンクを直接タスクに添付し、全ての検索情報を一元化します。"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Attachments"
msgstr "添付"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message"
msgstr "メッセージ以外の添付ファイル"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "著作者"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "定期的な活動のためのタスクを自動生成する"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_state
msgid "Automatic Kanban Status"
msgstr "自動かんばんステータス"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_state
msgid ""
"Automatically modify the state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the state to 'Approved' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'Changes Requested' (orange bullet).\n"
msgstr ""
"顧客がこのステージのフィードバックに返信すると、自動的にステータスが変更されます。\n"
"* 顧客からの良いフィードバックは、ステータスを'承認'(緑色の丸)に更新します。\n"
"* 中立または悪いフィードバックは、かんばんステータスを'変更要求'（オレンジ色の丸）に設定します。\n"

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Automatically send an email to customers when a task reaches a specific "
"stage in a project by setting this template on that stage"
msgstr ""
"このテンプレートをプロジェクトの特定のステージに設定することで、タスクがプロジェクトの特定のステージに達したときに、自動的に顧客に電子メールを送信します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Avatar"
msgstr "アバター"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Average Rating"
msgstr "平均評価"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "平均評価（％）"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
msgid "Average Rating (1-5)"
msgstr "平均評価 (1-5)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "平均評価：不満"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "平均評価：普通"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "平均評価：満足"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Backlog"
msgstr "バックログ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "残高"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Billed"
msgstr "請求済"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__dependent_ids
msgid "Block"
msgstr "ブロック"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocked"
msgstr "不許可"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "ブロック元"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked Tasks"
msgstr "ブロック済タスク"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocking"
msgstr "ブロック中"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Brainstorm"
msgstr "ブレインストーム"

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr "バグ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Burndown Chart"
msgstr "バーンダウンチャート"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_canceled
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
msgid "Cancelled"
msgstr "取消済"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__02_changes_requested
#: model:mail.message.subtype,description:project.mt_task_changes_requested
#: model:mail.message.subtype,name:project.mt_project_task_changes_requested
#: model:mail.message.subtype,name:project.mt_task_changes_requested
msgid "Changes Requested"
msgstr "変更要求済"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the "
"name of a customer, of a product, of a team, of a construction site, "
"etc.</i>"
msgstr ""
"プロジェクト用に<b>名前</b>を選択して下さい。 <i>好きなもの何でも可能です: 顧客、 プロダクト、チーム、建設現場の名前など</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr "タスクの <b>名前</b>を選択して下さい。 <i>(例: ウェブサイトデザイン、商品購入など)</i>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Choose one of the following access modes for your collaborators:"
msgstr "共同作業者のアクセスモードを次のいずれかを選択します:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Client Review"
msgstr "顧客レビュー"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Close the sub-tasks list"
msgstr "サブタスクリストを閉じる"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__closed_depend_on_count
msgid "Closed Depending on Tasks"
msgstr "タスクによりクローズ済"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed On"
msgstr "クローズ日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed Tasks"
msgstr "終了済タスク"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_report_project_task_user__is_closed
msgid "Closed state"
msgstr "クローズ済ステータス"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__closed
msgid "Closed tasks"
msgstr "クローズ済タスク"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__is_closed
msgid "Closing Stage"
msgstr "終了ステージ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__partner_id
msgid "Collaborator"
msgstr "協力者"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__collaborator_ids
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Collaborators"
msgstr "協力者"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "共有済プロジェクトの協力者"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""
"タスクが特定のステージに入ったときに評価要求を送信することで、顧客からのフィードバックを収集できます。これを行うには、対応するステージに評価メールテンプレートを定義します。\n"
"ステージ変更時の評価: 評価メールテンプレートが設定されたステージにタスクが到達すると、メールが自動的に送信されます。\n"
"定期的な評価: 評価メールテンプレートが設定されたステージにタスクがある限り、定期的にメールが自動的に送信されます。"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
msgid "Color"
msgstr "色"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt."
msgstr ""
"Eメールゲートウェイを使用して、タスクについて顧客とややり取りします。ロゴデザインをタスクに添付し、デザイナからTシャツをプリントする\n"
"作業者へ情報が流れるようにします。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Communication history"
msgstr "通信履歴"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_project_stage__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Company"
msgstr "会社"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "設定"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "ステージの設定"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "確定"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Confirmation"
msgstr "確認"

#. module: project
#: model_terms:web_tour.tour,rainbow_man_message:project.project_tour
msgid "Congratulations, you are now a master of project management."
msgstr "おめでとうございます。これであなたはプロジェクト管理の達人です。"

#. module: project
#: model:project.tags,name:project.project_tags_06
msgid "Construction"
msgstr "建設"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Consulting"
msgstr "コンサルティング"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "連絡先"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid "Convert Task"
msgstr "タスクを変換"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.actions.server,name:project.action_server_convert_to_subtask
msgid "Convert to Task/Sub-Task"
msgstr "タスク/サブタスクに変換"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Copywriting"
msgstr "コピーライティング"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Costs"
msgstr "経費"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "カバーイメージ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr " <b>活動</b>を作成して自分でToDoを設定する、またはミーティングを予定します。 "

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "作成日"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "プロジェクト作成"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid "Create a Task Activity Plan"
msgstr "タスク活動計画を作成する"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "タスクパイプラインに新しいステージを作成します"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Create a new sub-task"
msgstr "新規サブタスクを作成"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "プロジェクト作成"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr "プロジェクトを作成して、タスクを整理し、プロジェクトごとに異なるワークフローを定義します。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr "プロジェクトを作成し、タスクを整理します。プロジェクトごとに異なるワークフローを定義します。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "以下にメールを送りタスクを作成:"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr "プロジェクトのメールアドレスにメールを送信してタスクを作成します。"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "作成日"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "作成者"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "作成日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Creation Date"
msgstr "作成日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "タスクの現在のプロジェクト"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "このタスクの現在のステージ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr "現在、このドキュメントを閲覧しているすべての人が利用できます。クリックすると、社内の従業員に限定できます。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr "現在、社内の従業員に制限されています。クリックすると、このドキュメントを表示するすべての人が利用できます。"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "カスタムバウンスメッセージ"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Customer"
msgstr "顧客"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Customer Email"
msgstr "顧客Eメール"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Customer Feedback"
msgstr "顧客フィードバック"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "顧客ポータルURL"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "顧客評価"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "顧客評価ステータス"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly."
msgstr ""
"Eメールでのフィードバックを提案する顧客:Odooは自動的にタスクを作成し、\n"
"タスクについて直接やり取りすることができます。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Customers will be added to the followers of their project and tasks."
msgstr "顧客がプロジェクトとタスクのフォロワーに追加されます。"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "日次"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model:ir.embedded.actions,name:project.project_embedded_action_project_updates
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Dashboard"
msgstr "ダッシュボード"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Date"
msgstr "日付"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the state of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage/state to another."
msgstr ""
"タスクの状態が最後に変更された日付。\n"
"この情報に基づいて、滞っているタスクを特定したり、あるステージ/状態から別のステージ/状態にタスクを移動するのに通常かかる時間の統計を取ることができます。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr "プロジェクトの終了日。プロジェクトで定義された期間は、その計画を確認するときに考慮されます。"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"このタスクが最後に割り当てられた(または割り当てられていない)日付。これに基づいて、通常タスクを割り当てるのにかかる時間の統計を取ることができます。"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "日"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "期日までの日数"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Deadline"
msgstr "期日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "Dear"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "デフォルト値"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"プロジェクトで使用される手順を定義します。\n"
"                 タスクの作成、タスクまたは課題の終了まで。\n"
"                 これらの段階を使用して、\n"
"                 タスクや課題を解決します。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr "プロジェクトの作成から完了までのステップを定義します。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr "タスクの作成から完了までのステップを定義します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_project_stage_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Delete"
msgstr "削除"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Delete Milestone"
msgstr "マイルストン削除"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid "Delete Project Stage"
msgstr "プロジェクトステージ削除"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Delete Stage"
msgstr "ステージを削除"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr "マイルストンに達すると、販売オーダの項目にリンクして自動的にサービスを提供します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Delivered"
msgstr "配送済"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Dependent Tasks"
msgstr "依存タスク"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_count
msgid "Depending on Tasks"
msgstr "タスクに応じて"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model:ir.model.fields,field_description:project.field_report_project_task_user__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "説明"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr "このプロジェクトに関する詳細と背景を説明します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.tags,name:project.project_tags_08
msgid "Design"
msgstr "デザイン"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "タスクを実行する順序を決める"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Development"
msgstr "開発"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "ダイジェスト"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Digital Marketing"
msgstr "デジタルマーケティング"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "破棄"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "表示名"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Display the sub-task in your pipeline"
msgstr "パイプラインのサブタスクを表示"

#. module: project
#. odoo-python
#: code:addons/project/models/digest_digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "アクセス権がありません。ユーザのダイジェストメールにこのデータをスキップしてください"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__done
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_update__status__done
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_done
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
msgid "Done"
msgstr "完了"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Draft"
msgstr "ドラフト"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Duplicate"
msgstr "複製"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"Each user should have at least one personal stage. Create a new stage to "
"which the tasks can be transferred after the selected ones are deleted."
msgstr ""
"各ユーザは少なくとも1つの個人ステージを持つ必要があります。選択したステージを削除した後、タスクを転送できる新しいステージを作成してください。"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit
msgid "Edit"
msgstr "編集"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit_limited
msgid "Edit with limited access"
msgstr "アクセス制限付き編集"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit with limited access: collaborators can view and edit tasks they follow "
"in the Kanban view."
msgstr "アクセス制限付き編集:共同作業者は、かんばんビューで自分がフォローしているタスクを表示および編集できます。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit: collaborators can view and edit all tasks in the Kanban view. "
"Additionally, they can choose which tasks they want to follow."
msgstr "編集: 共同作業者は、かんばんビューの全てのタスクを閲覧、編集することができます。さらに、フォローするタスクを選択することもできます。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Editing"
msgstr "編集中"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_email
msgid "Email Alias"
msgstr "Eメールエイリアス"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "メールテンプレート"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr "このタスクからの受信メールのCCにあり、現在既存顧客にリンクされていないメールアドレス。"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "メールCC"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Eメールドメイン例 'example.com' in '<EMAIL>'"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "以下に送信されたEメール："

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Employees Only"
msgstr "従業員のみ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "終了日"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "終了日"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "エラー: タスクの再帰的階層を作ることはできません。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr "誰もがアイデアを提案することができ、編集者はその中から最も優れたものに以下の印をつけます:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Expected"
msgstr "見込"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "実験"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "有効期限日"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "外部"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "追加情報"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "お気に入り"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Favorite Projects"
msgstr "お気に入りプロジェクト"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Final Document"
msgstr "最終ドキュメント"

#. module: project
#: model:project.tags,name:project.project_tags_11
msgid "Finance"
msgstr "財務"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "かんばんで折り込む"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Follow"
msgstr "フォロー"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow and comments tasks of your projects"
msgstr "プロジェクトのタスクのフォローやコメントができます"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow the evolution of your projects"
msgstr "プロジェクトの進捗をフォローできます"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Followed"
msgstr "フォロー中"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr "フォロー済更新"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "無期限"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Frequency"
msgstr "頻度"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Future"
msgstr "今後の計画"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "今後の活動"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr "プロジェクトの現況について報告し、進捗を重要なステークホルダーと共有しましょう。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback and evaluate the performance of your employees"
msgstr "顧客からのフィードバックを得て、従業員のパフォーマンスを評価します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Give the sub-task a <b>name</b>"
msgstr "タスクに<b>名前</b>を付けて下さい"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Grant Portal Access"
msgstr "ポータルアクセスを付与"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
" Employees automatically get access to the tasks they are assigned to."
msgstr ""
"従業員をフォロワーとして追加することで、プロジェクトまたはタスクへのアクセスを許可します。従業員は自動的に割り当てられたタスクにアクセスできるようになります。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant portal users access to your project by adding them as followers (the "
"tasks of the project are not included). To grant access to tasks to a portal"
" user, add them as followers for these tasks."
msgstr ""
"ポータルユーザをフォロワーとして追加することで、プロジェクトへのアクセスを許可します(プロジェクトのタスクは含まれません)。ポータルユーザにタスクへのアクセスを許可するには、タスクのフォロワーとして追加します。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Group By"
msgstr "グループ化"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks."
msgstr "新しいプロジェクトのタスク内でアイデアを集め、タスクのチャットの中で話し合いましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Handoff"
msgstr "引継ぎ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "ハッピーフェイス"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "Hello"
msgstr "こんにちは"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Hide closed tasks"
msgstr "クローズ済タスクを隠す"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hide the sub-task in your pipeline"
msgstr "パイプラインのサブタスクを非表示"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "高"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "履歴"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history
msgid "History data"
msgstr "履歴データ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history_metadata
msgid "History metadata"
msgstr "履歴メタデータ"

#. module: project
#: model:project.tags,name:project.project_tags_13
msgid "Home"
msgstr "ホーム"

#. module: project
#: model:account.analytic.account,name:project.analytic_construction
#: model:project.project,name:project.project_home_construction
msgid "Home Construction"
msgstr "住宅建設"

#. module: project
#: model:project.project,name:project.project_project_4
msgid "Home Make Over"
msgstr "家のリノベーション"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "時間"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "プロジェクトの状況は？"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "エイリアスを保持する親レコードのID(例：プロジェクトは、タスク作成エイリアスを保持)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外活動を示すアイコン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Ideas"
msgstr "アイデア"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""
"有効にすると、このステージは、プロジェクトのかんばんビューで折りたたまれた状態で表示されます。折りたたまれたステージのプロジェクトは、終了されたとみなされます。"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""
"設定された場合、タスクがこのステージに達すると、評価要求が自動的に顧客にEメールで送信されます。\n"
"または、プロジェクトの設定によって、タスクがこのステージにある限り、定期的に送信されます。\n"
"この機能を使うには、'顧客評価'オプションがプロジェクトで有効になっていることを確認して下さい。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr "設定されている場合、プロジェクトがこの段階に達すると、Eメールが自動的に顧客に送信されます。"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr "設定されている場合、タスクがこの段階に達すると、Eメールが自動的に顧客に送信されます。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "設定されている場合、このコンテンツはデフォルトのメッセージではなく、許可されていないユーザーに自動的に送信されます。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__01_in_progress
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,name:project.project_stage_1
msgid "In Progress"
msgstr "進行中"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "In development"
msgstr "開発中"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
msgid "Inbox"
msgstr "受信トレイ"

#. module: project
#: model:project.tags,name:project.project_tags_09
msgid "Interior"
msgstr "インテリア"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "内部"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal Note"
msgstr "内部注釈"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"このプロジェクトに関連する内部電子メール。 "
"着信電子メールはタスクと自動的に同期されます(または、課題追跡モジュールがインストールされている場合はオプションの課題)。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal notes are only displayed to internal users."
msgstr "内部メモは内部ユーザにのみ表示されます。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid operator: %s"
msgstr "無効なオペレータ: %s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid value: %s"
msgstr "無効な値: %s"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users (private)"
msgstr "招待された内部ユーザ（プライベート）"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "招待されたポータルユーザとすべての社内ユーザ（公開）"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Invoiced"
msgstr "請求済"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Is Closed (Burn-up Chart)"
msgstr "クローズ済(バーンアップチャート)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
#: model:ir.model.fields,field_description:project.field_report_project_task_user__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Is toggle mode"
msgstr "トグルモード"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "課題バージョン"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__duration_tracking
#: model:ir.model.fields,help:project.field_project_task__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "many2oneフィールドのIDを滞在秒数にマッピングするJSON"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"タスクの作成から完了までの進捗を管理します。<br>\n"
"　　　　　リアルタイムチャットやEメールにより効率的に協働します。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"タスクの作成から完了までの進捗を管理します。<br>\n"
"　　　　　リアルタイムチャットやEメールにより効率的に協働します。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 30 Days"
msgstr "過去30日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 365 Days"
msgstr "過去365日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 7 Days"
msgstr "過去7日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "先月"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
msgid "Last Rating (1-5)"
msgstr "最終評価 (1-5)"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last Stage Update"
msgstr "最終ステージ更新日"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "最終更新日:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "遅れた活動"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Late Milestones"
msgstr "遅延マイルストン"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
msgid "Later"
msgstr "以降"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Leave a comment"
msgstr "コメントを残す"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>project</b>."
msgstr "最初の<b>プロジェクト</b>を作成しましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>stage</b>."
msgstr "最初の<b>ステージ</b>を作成しましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>task</b>."
msgstr "最初の<b>タスク</b>を作成しましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your second <b>stage</b>."
msgstr "2つ目の<b>ステージ</b>を作成しましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr "<b>かんばんビュー</b>に戻って次のタスクの概要を取得しましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's start working on your task."
msgstr "タスクに取り掛かりましょう。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "顧客が評価するまで待ちましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Live"
msgstr "有効"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "ローカルパートに基づく着信検知"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Logo Design"
msgstr "ロゴデザイン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Look for the"
msgstr "以下を探す:"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "低"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""
"かんばんビューでプロジェクトのライフサイクルを管理。新しく取得したプロジェクトを追加し、\n"
"割り当て、そして"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Manufacturing"
msgstr "製造"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "Mark as done"
msgstr "完了とする"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as incomplete"
msgstr "未完了としてマーク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as reached"
msgstr "達成済とマーク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Mark the task as <b>Cancelled</b>"
msgstr "タスクを<b>取消済</b>としてマーク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Material Sourcing"
msgstr "材料調達"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr "タスクが一定の段階に達したら評価リクエストを送信して、顧客満足度を測定できます。"

#. module: project
#: model:project.tags,name:project.project_tags_15
msgid "Meeting"
msgstr "ミーティング"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "メニュー"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "メッセージ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Milestone"
msgstr "マイルストン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Milestones"
msgstr "マイルストン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Mixing"
msgstr "Mixing"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "月"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動期限"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "My Deadline"
msgstr "期日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "お気に入り"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Projects"
msgstr "自分のプロジェクト"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_my_task
#: model:ir.ui.menu,name:project.menu_project_management_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "My Tasks"
msgstr "自分のタスク"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "自分の更新"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Name"
msgstr "名称"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the Tasks"
msgstr "タスクの名前"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr "プロジェクトのタスクを示すのに使われる名前 例:タスク、チケット、スプリントなど"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "普通の顔"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_list.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
msgid "New"
msgstr "新規"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "新機能"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
msgid "New Milestone"
msgstr "新規マイルストン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Orders"
msgstr "新規オーダ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_project_calendar/project_project_calendar_controller.js:0
msgid "New Project"
msgstr "新規プロジェクト"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Projects"
msgstr "新規プロジェクト"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Request"
msgstr "新規依頼"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
msgid "New Task"
msgstr "新規タスク"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Newest"
msgstr "最新"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Next"
msgstr "次へ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Next Activity"
msgstr "次の活動"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動概要"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "顧客なし"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "マイルストンなし"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Project"
msgstr "プロジェクトなし"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "No Subject"
msgstr "件名なし"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "活動タイプが見つかりません。作成しましょう！"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "顧客評価がまだありません"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "まだデータはありません！"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "プロジェクトは見つかりませんでした。作成しましょう！"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "ステージが見つかりません。作成しましょう！"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "タグが見つかりません。作成しましょう！"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "タスクが見つかりません。新しいタスクを作成しましょう！"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "最新情報が見つかりません。作成しましょう！"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "None"
msgstr "なし"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Not Implemented."
msgstr "実装されていません"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "メモ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "オフトラック"

#. module: project
#: model:project.tags,name:project.project_tags_10
msgid "Office"
msgstr "オフィス"

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "オフィスデザイン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Old Completed Sprint"
msgstr "以前完了したスプリント"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "保留"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "予定通り"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "毎月1回"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Only jpeg, png, bmp and tiff images are allowed as attachments."
msgstr " 添付できるのはjpeg, png, bmp および tiff画像のみです。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "おっと！何かがうまくいきませんでした。ページをリロードしてログインしてみてください。"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Open Tasks"
msgstr "未完了タスク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Open sub-tasks notebook section"
msgstr "サブタスクノートブックセクションを開く"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__open
msgid "Open tasks"
msgstr "タスクを開く"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Operation not supported"
msgstr "操作はサポートされていません"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "すべての受信メッセージに添付されるスレッド(レコード)のオプションIDです。設定した場合は新しいレコードの作成を無効にします。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Organize priorities amongst orders using the"
msgstr "以下を使用して、オーダ間の優先順位を整理する:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"パイプラインを横断してタスクをディスパッチすることで、タスクを整理できます。<br>\n"
"　　　　　リアルタイムチャットやEメールによる効率的に協働します。"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Others"
msgstr "その他"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Overdue"
msgstr "滞納"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "期限超過タスク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Page Ideas"
msgstr "ページアイディア"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "親モデル"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "親レコードスレッドID"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
msgid "Parent Task"
msgstr "親タスク"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"エイリアスを保持する親モデル。エイリアスの参照を保持しているモデルは、必ずしもalias_model_idによって与えられたモデルではありません(例：プロジェクト(parent_model)とタスク(model))"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid ""
"Partner company cannot be different from its assigned projects' company"
msgstr "取引先企業は、割当てられたプロジェクトの企業と異なることはできません。"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "Partner company cannot be different from its assigned tasks' company"
msgstr "取引先企業は、割当てられたタスクの企業と異なることはできません。"

#. module: project
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
msgid "Partner's Tasks"
msgstr "取引先のタスク"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"People invited to collaborate on the project will have portal access rights."
msgstr "プロジェクトに協力するために招待された人々には、ポータルへのアクセス権が与えられます。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks they are following, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"このプロジェクトとタスクが表示される人\n"
"\n"
"- 招待された内部ユーザ: プロジェクトをフォローすると、内部ユーザーは区別なく全てのタスクにアクセスできます。そうでない場合は、フォローしている特定のタスクにのみアクセスできます。\n"
"プロジェクト > 管理者のアクセス権を持つユーザは、フォロワーでなくても、プロジェクトとタスクにアクセスできます。\n"
"\n"
"- 全ての内部ユーザ：全ての内部ユーザは、区別なくプロジェクトとその全てのタスクにアクセスできます。\n"
"\n"
"- 招待されたポータルユーザと全ての内部ユーザ：全ての内部ユーザは、プロジェクトとその全てのタスクに区別なくアクセスできます。\n"
"プロジェクトをフォローしている場合、ポータルユーザはフォローしている特定のタスクにのみアクセスできます。\n"
"\n"
"プロジェクトが読み取り専用で共有されている場合、ポータルユーザはそのポータルにリダイレクトされます。タスクは閲覧できますが、編集はできません。\n"
"編集でプロジェクトが共有されると、ポータルユーザはタスクのかんばんビューとリストビューにリダイレクトされます。ポータルユーザはタスクの選択された数のフィールドを変更することができます。\n"
"\n"
"どのような場合でも、プロジェクトのアクセス権を持たない内部ユーザは、対応する URL が与えられていれば、タスクにアクセスすることができます(プロジェクトが非公開の場合は、フォロワーの一部となります)。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "高評価割合"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Personal Stage"
msgstr "パーソナルステージ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "個人ステージステータス"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "個人タスクステージ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Planned Date"
msgstr "予定日"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr "削除するアカウントにリンクされているプロジェクトの既存のタスクを削除してください。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Podcast and Video Production"
msgstr "ポッドキャストおよび動画制作"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"メールゲートウェイを使用したメッセージドキュメント投稿のポリシー\n"
"- 全員: 誰でも投稿可能\n"
"- 取引先: 認証済みの取引先のみ\n"
"- フォロワー: 関連ドキュメントのフォロワーか、フォローしているチャネルのメンバーのみ\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "ポータルアクセスURL"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr "ポータルユーザは、プロジェクトとそのタスクのフォロワーから削除されます。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Previous"
msgstr "前へ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Prioritize your tasks by marking important ones using the"
msgstr "以下を使って重要なタスクをマークし、タスクの優先順位を決めます。"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#: model:project.tags,name:project.project_tags_16
msgid "Priority"
msgstr "優先度"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr "優先: {{'Important' if task.priority == '1' else 'Normal'}}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.js:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_model.js:0
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Private"
msgstr "非公開"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Private Tasks"
msgstr "非公開タスク"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Private tasks cannot be converted into sub-tasks. Please set a project on "
"the task to gain access to this feature."
msgstr "プライベートタスクをサブタスクに変換することはできません。この機能を利用するには、タスクにプロジェクトを設定して下さい。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Profitability"
msgstr "収益性"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "進捗"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Project"
msgstr "プロジェクト"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__account_id
msgid "Project Account"
msgstr "プロジェクト会計"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "プロジェクトマネジャー"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "プロジェクトマイルストン"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "プロジェクト名"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "プロジェクト評価ステータス"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "プロジェクト共有"

#. module: project
#: model:ir.model,name:project.model_project_share_collaborator_wizard
msgid "Project Sharing Collaborator Wizard"
msgstr "プロジェクト共有協力者ウィザード"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_recurring_tasks_action
msgid "Project Sharing Recurrence"
msgstr "プロジェクト共有繰返し"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "プロジェクト共有:タスク"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "プロジェクトステージ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "プロジェクトステージ変更済"

#. module: project
#: model:ir.model,name:project.model_project_project_stage_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "プロジェクトステージ削除ウィザード"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "プロジェクトステージ"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "プロジェクトタグ"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Task Stage Delete Wizard"
msgstr "プロジェクトステージ削除ウィザード"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "プロジェクトのタスク"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "Project Title"
msgstr "プロジェクトタイトル"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "プロジェクト更新"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "プロジェクト参照可"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "プロジェクトの説明..."

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
msgid "Project shared with your collaborators."
msgstr "共同作業者と共有済のプロジェクト。"

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr "プロジェクトステータス - {{ object.name }}"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "プロジェクトのタスク"

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "プロジェクト: プロジェクト完了"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr "プロジェクト: 要求認識"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
msgid "Project: Send rating"
msgstr "プロジェクト：評価送付"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "プロジェクト: タスク評価要求"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Projects"
msgstr "プロジェクト"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr "プロジェクトには同じトピックに関するタスクが含まれ、それぞれにダッシュボードがあります。"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""
"このステージが存在するプロジェクト。複数のプロジェクトで同じようなワークフローをたどっている場合、このステージをプロジェクト間で共有することで、統合された情報を得ることができます。"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Properties"
msgstr "属性"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Public Link"
msgstr "公開リンク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Published"
msgstr "公開済"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Published on"
msgstr "以下に公開済:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Publishing"
msgstr "出版"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "四半期毎"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Quickly check the status of tasks for approvals or change requests and "
"identify those on hold until dependencies are resolved with the hourglass "
"icon."
msgstr "承認や変更要求のためのタスクのステータスをすばやくチェックし、依存関係が解決されるまで保留されているタスクを砂時計アイコンで確認できます。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "評価"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "評価 (/5)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_pivot
msgid "Rating (1-5)"
msgstr "評価 (1-5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "評価平均テキスト"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "メールテンプレート"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "評価頻度"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "評価前回のフィードバック"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "評価前回の画像"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "評価前回の数値"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "評価満足度"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "評価テキスト"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "評価数"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_milestone__rating_ids
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model:ir.model.fields,field_description:project.field_project_update__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "到達済"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__read
msgid "Read"
msgstr "閲覧のみ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Read: collaborators can view tasks but cannot edit them."
msgstr "読取り:共同作業者はタスクの閲覧は可能ですが、編集はできません。"

#. module: project
#: model:ir.model.fields,help:project.field_project_share_collaborator_wizard__access_mode
msgid ""
"Read: collaborators can view tasks but cannot edit them.\n"
"Edit with limited access: collaborators can view and edit tasks they follow in the Kanban view.\n"
"Edit: collaborators can view and edit all tasks in the Kanban view. Additionally, they can choose which tasks they want to follow."
msgstr ""
"読取り: 共同作業者はタスクの閲覧は可能ですが、編集はできません。\n"
"限定アクセスでの編集:共同作業者は、かんばんビューでフォローしているタスクを閲覧および編集できます。\n"
"編集:共同作業者は、かんばんビューで全てのタスクを閲覧および編集できます。さらに、フォローするタスクを選択することもできます。"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr " {{ object.name }}の受信"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "宛先"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "レコードのスレッドID"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Recording"
msgstr "記録"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "繰返し"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "定期"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
msgid "Recurrent tasks"
msgstr "繰返しタスク"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurring Tasks"
msgstr "繰返しタスク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Refused"
msgstr "否認"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "関連ドキュメント"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "関連ドキュメントID"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "関連ドキュメントモデル"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid "Remove Collaborator"
msgstr "協力者の削除"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "Rename"
msgstr "名前の変更"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "リノベーション"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "繰返し周期"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
msgid "Repeat Unit"
msgstr "繰返し単位"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "レポーティング"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research"
msgstr "研究"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "研究開発"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research Project"
msgstr "調査プロジェクト"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Researching"
msgstr "リサーチ中"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Resources Allocation"
msgstr "リソース割当"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "担当ユーザ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Restore"
msgstr "リストア"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"Restoring will replace the current content with the selected version. Any "
"unsaved changes will be lost."
msgstr "復元すると、現在のコンテンツが選択したバージョンに置き換えられます。保存されていない変更は失われます。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Revenues"
msgstr "収益"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "SAVE"
msgstr "保存"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "サッドフェイス"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to drag images in description"
msgstr "タスクを保存して、説明で画像をドラッグできるようにします。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to paste images in description"
msgstr "タスクを保存して、説明文に画像を貼り付けられるようにします。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Schedule your activity once it is ready."
msgstr "用意ができたら活動をスケジュールします。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Script"
msgstr "スクリプト"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "プロジェクトの検索"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "更新を検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Assignees"
msgstr "担当者検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Customer"
msgstr "顧客検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Milestone"
msgstr "マイルストン検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Priority"
msgstr "優先順位内検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Project"
msgstr "プロジェクト検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Stages"
msgstr "ステージ検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Status"
msgstr "ステータス検索"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search%(left)s Tasks%(right)s"
msgstr "検索%(left)s タスク%(right)s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "セキュリティトークン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Select an assignee from the menu"
msgstr "メニューから担当者を選択します"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Send"
msgstr "送信"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "メールを送信"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__send_invitation
msgid "Send Invitation"
msgstr "招待状を送信"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "カバーイメージを設定"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
msgid "Set Status"
msgstr "ステータスをセット"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "ステージに評価メールテンプレートを設定"

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr "プロジェクトの段階を設定し、プロジェクトがその段階に達したときに顧客に知らせます。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_priority_switch_field/project_task_priority_switch_field.js:0
msgid "Set priority as %s"
msgstr "優先順位を%sとして設定"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Set state as..."
msgstr "ステータスを以下として設定:"

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr "このテンプレートをプロジェクトステージに設定し、顧客からのフィードバックを要求します。プロジェクトの'顧客評価'機能を有効にして下さい。"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "管理設定"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Share Project"
msgstr "プロジェクトを共有"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share Task"
msgstr "タスクを共有"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__show_display_in_project
msgid "Show Display In Project"
msgstr "プロジェクトのディスプレイを表示"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Show closed tasks"
msgstr "クローズ済タスクを表示"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "以下以来:"

#. module: project
#: model:project.tags,name:project.project_tags_12
msgid "Social"
msgstr "ソーシャル"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Software Development"
msgstr "ソフトウェア開発"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid ""
"Some of the selected analytic accounts are associated with a project:\n"
"%(accountList)s\n"
"\n"
"Archiving these accounts will remove the option to log timesheets for their respective projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""
"選択された分析アカウントの一部は、プロジェクトに関連付けられています:\n"
"%(accountList)s\n"
"\n"
"これらのアカウントをアーカイブすると、それぞれのプロジェクトのタイムシートを記録するオプションが削除されます。\n"
"\n"
"本当にこのまま進めますか？"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Sorry. You can't set a task as its parent task."
msgstr "タスクをその親タスクとして設定できません。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Sort your tasks by sprint using milestones, tags, or a dedicated property. "
"At the end of each sprint, just pick the remaining tasks in your list and "
"move them all at once to the next sprint by editing the milestone, tag, or "
"property."
msgstr ""
"マイルストン、タグ、または専用のプロパティを使用して、スプリントごとにタスクを並べ替えます。各スプリントの終了時に、リスト内の残りのタスクを選び、マイルストン、タグ、またはプロパティを編集して全て次のスプリントに移動します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Specifications"
msgstr "仕様設計"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Backlog"
msgstr "スプリントバックログ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Complete"
msgstr "スプリント完了"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint in Progress"
msgstr "スプリント進行中"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Stage"
msgstr "ステージ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Stage (Burndown Chart)"
msgstr "ステージ (バーンダウンチャート)"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "ステージ変更"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "ステージ所有者"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "ステージ変更"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Stage: %s"
msgstr "ステージ: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Starred Tasks"
msgstr "スター付きタスク"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "開始日"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__state
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "State"
msgstr "都道府県・州"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_stage_state_selection/project_task_stage_with_state_selection.js:0
msgid "State readonly"
msgstr "ステータス 読取専用"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_status_with_color_selection/project_status_with_color_selection_field.xml:0
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Status"
msgstr "状態"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Status Update - %(date)s"
msgstr "ステータス更新 - %(date)s"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づくステータス\n"
"遅延: 期限が既に過ぎています\n"
"今日: 活動日は今日です\n"
"予定: 将来の活動。"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__duration_tracking
#: model:ir.model.fields,field_description:project.field_project_task__duration_tracking
msgid "Status time"
msgstr "ステータス時間"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "サブタスク"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "実行日"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_allocated_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr "このタスクにリンクしている全てのサブタスク (およびそのサブタスク)に割り当てられた時間の合計。通常、このタスクの割り当て時間以下です。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "概要"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "T-shirt Printing"
msgstr "Tシャツプリント"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tags"
msgstr "タグ"

#. module: project
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task"
msgstr "タスク"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "タスク活動"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_approved
#: model:mail.message.subtype,name:project.mt_task_approved
msgid "Task Approved"
msgstr "タスク承認済"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_canceled
msgid "Task Canceled"
msgstr "タスクキャンセル済"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_canceled
msgid "Task Cancelled"
msgstr "タスクが取消されました"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Task Converted from To-Do"
msgstr "ToDoから変換されたタスク"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "タスク作成"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "タスク依存関係"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_done
#: model:mail.message.subtype,name:project.mt_task_done
msgid "Task Done"
msgstr "タスク完了"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_in_progress
#: model:mail.message.subtype,name:project.mt_project_task_in_progress
#: model:mail.message.subtype,name:project.mt_task_in_progress
msgid "Task In Progress"
msgstr "タスク進行中"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "タスクログ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "タスクプロパティ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "タスク評価"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "タスクの繰り返し"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "タスク工程"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "タスクステージ変更済"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "タスクステージ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "タスク名"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "タスク名..."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Task Transferred from Project %(source_project)s to %(destination_project)s"
msgstr "プロジェクト %(source_project)s から %(destination_project)sへ移行済のタスク"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_waiting
#: model:mail.message.subtype,name:project.mt_project_task_waiting
#: model:mail.message.subtype,name:project.mt_task_waiting
msgid "Task Waiting"
msgstr "タスク待機中"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_approved
msgid "Task approved"
msgstr "タスク承認済"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_canceled
msgid "Task cancelled"
msgstr "タスクが取消されました"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_done
msgid "Task done"
msgstr "タスク完了済"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "タスク:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task: Rating Request"
msgstr "タスク：評価依頼"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.embedded.actions,name:project.project_embedded_action_all_tasks_dashboard
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.ui.menu,name:project.menu_project_management
#: model:project.project,label_tasks:project.project_home_construction
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model:project.project,label_tasks:project.project_project_4
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tasks"
msgstr "タスク"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "タスク分析"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "タスク管理"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "タスクステージ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
msgid "Tasks in Recurrence"
msgstr "繰り返しのタスク"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Tests"
msgstr "テスト"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid "The Burndown Chart must be grouped by Date"
msgstr "バーンダウンチャートは日付でグループ化して下さい"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "現ユーザの個人ページ"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "現ユーザの個人タスクステージ"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr "ドキュメントが存在しないか、ドキュメントにアクセスする権限がありません。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The end date should be in the future"
msgstr "終了日は未来にして下さい。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "以下のマイルストンが追加されました:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "以下のマイルストンが追加されました："

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The interval should be greater than 0"
msgstr "間隔は0より大きくする必要があります。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"このエイリアスに対応した(Odooドキュメント種別)モデル。既存のレコードに返信しない任意の受信メールは、このモデルの新しいレコード(例えば、プロジェクトタスク)の作成を引き起こします"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "eメールのエイリアス.例えば<<EMAIL>>のメールを受け取りたい場合'jobs’を選択"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project and the associated partner must be linked to the same company."
msgstr "プロジェクトと関連取引先は同じ会社にリンクされている必要があります。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project's company cannot be changed if its analytic account has analytic"
" lines or if more than one project is linked to it."
msgstr "分析アカウントに分析明細がある場合、または複数のプロジェクトがリンクされている場合、プロジェクトの会社を変更することはできません。"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr "プロジェクトの開始日は終了日より前でなければいけません。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid ""
"The report should be grouped either by \"Stage\" to represent a Burndown "
"Chart or by \"Is Closed\" to represent a Burn-up chart. Without one of these"
" groupings applied, the report will not provide relevant information."
msgstr ""
"レポートは、バーンダウンチャートであれば \"ステージ \"で、バーンアップチャートであれば "
"\"クローズ済か\"でグループ化する必要があります。これらのグループ分けのいずれかが適用されていないと、レポートは関連情報を提供しません。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "The search does not support operator %(operator)s or value %(value)s."
msgstr "検索は演算子%(operator)sまたは値%(value)sをサポートしていません。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task and the associated partner must be linked to the same company."
msgstr "タスクと関連取引先は同じ会社にリンクされている必要があります。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to "
"'%(visibility)s' in order to make it accessible by the recipient(s)."
msgstr ""
"プロジェクトのプライバシーが制限されすぎているため、タスクを受信者と共有できません。プロジェクトのプライバシーを '%(visibility)s' "
"に設定して、受信者がアクセスできるようにして下さい。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "The task description was empty at the time."
msgstr "当時、タスクの説明は空欄でした。"

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
msgid ""
"The view must be grouped by date and by Stage - Burndown chart or Is Closed "
"- Burnup chart"
msgstr "日付別、ステージ別 - バーンダウンチャートまたはバーンアップチャート、にグループ化する必要があります。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "There are no comments for now."
msgstr "まだコメントがありません。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "プロジェクトはありません。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "現在、このプロジェクトに対する評価はありません。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "タスクはありません。"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "There is nothing to report."
msgstr "レポートすることはありません。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"They can edit shared project tasks and view specific documents in read mode "
"on your website. This includes leads/opportunities, quotations/sales orders,"
" purchase orders, invoices and bills, timesheets, and tickets."
msgstr ""
"共有プロジェクトのタスクを編集したり、ウェブサイト上で特定のドキュメントを閲覧モードで表示したりすることができます。これには、案件/案件情報、見積書/販売オーダ、購買オーダ、顧客請求書および仕入先請求書、タイムシート、およびチケットが含まれます。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
msgid "This Month"
msgstr "今月"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "This Week"
msgstr "今週"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid ""
"This analytic account is associated with the following projects:\n"
"%(projectList)s\n"
"\n"
"Archiving the account will remove the option to log timesheets for these projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""
"この分析アカウントは以下のプロジェクトと関連しています:\n"
"%(projectList)s\n"
"\n"
"アカウントをアーカイブすると、これらのプロジェクトのタイムシートを記録するオプションが削除されます。\n"
"\n"
"本当にこのまま進めますか？"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid ""
"This follower is currently a project collaborator. Removing them will revoke"
" their portal access to the project. Are you sure you want to proceed?"
msgstr ""
"このフォロワーは現在プロジェクトの協力者です。このフォロワーを削除すると、プロジェクトへのポータルアクセスが無効になります。本当によろしいですか？"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid ""
"This is a preview of how the project will look when it's shared with "
"customers and they have editing access."
msgstr "これは、プロジェクトが顧客と共有され、彼らが編集アクセス権を持ったときにどのように見えるかのプレビューです。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is associated with %(project_company)s, whereas the selected "
"stage belongs to %(stage_company)s. There are a couple of options to "
"consider: either remove the company designation from the project or from the"
" stage. Alternatively, you can update the company information for these "
"records to align them under the same company."
msgstr ""
"このプロジェクトは、 %(project_company)sに関連していますが、選択されたステージは、 "
"%(stage_company)sに属しています。プロジェクトまたはステージから会社の指定を削除するか、これらのレコードの会社情報を更新して、同じ会社の下に揃えることもできます。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is currently restricted to \"Invited internal users\". The "
"project's visibility will be changed to \"invited portal users and all "
"internal users (public)\" in order to make it accessible to the recipients."
msgstr ""
"このプロジェクトは現在\"招待された内部ユーザ\"に制限されています。このプロジェクトの可視性を\"招待されたポータルユーザと全ての内部ユーザ(公開)\"に変更し、受信者がアクセスできるようにします。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is not associated with any company, while the stage is "
"associated with %s. There are a couple of options to consider: either change"
" the project's company to align with the stage's company or remove the "
"company designation from the stage"
msgstr ""
"このプロジェクトはどの会社とも関連していませんが、ステージは%sと関連しています。プロジェクトの会社名をステージの会社名に合わせるか、ステージから会社名を削除するかして下さい。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "This task has sub-tasks, so it can't be private."
msgstr "このタスクにはサブタスクがあるため、プライベートにはできません。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "This task is blocked by another unfinished task"
msgstr "このタスクは、別の未完了タスクによってブロックされています。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "This task is currently blocked by"
msgstr "このタスクは現在、次によってブロックされています:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr "これにより、以下のプロジェクトのステージと、ステージが含む全てのタスクがアーカイブされます:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr "これらは、やるべきことの様々なカテゴリを表します(例:'電話する'や'Eメールを送る')"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "時間管理"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "ヒント:受信メールからタスクを作成"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_3
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid "Tip: Project-Specific Fields"
msgstr "ヒント: プロジェクト特有フィールド"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Use task states to keep track of your tasks' progression"
msgstr "ヒント:タスクステートを使ってタスクの進捗を管理"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_2
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid "Tip: Your Own Personal Kanban"
msgstr "ヒント: あなた個人のかんばん"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
msgid "Title"
msgstr "タイトル"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Bill"
msgstr "請求する"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "未処理"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Invoice"
msgstr "未請求"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "To Print"
msgstr "印刷する"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"業務を完了させるため、活動とタスクのステータスを使用します。<br>\n"
"リアルタイムチャットやEメールで効率的に共同作業を行うことができます。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid ""
"To transform a task into a sub-task, select a parent task. Alternatively, "
"leave the parent task field blank to convert a sub-task into a standalone "
"task."
msgstr ""
"タスクをサブタスクに変換するには、親タスクを選択します。または、親タスクフィールドを空白のままにして、 サブタスクを独立したタスクに変換します。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Today"
msgstr "今日"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "本日の活動"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total"
msgstr "合計"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Costs"
msgstr "原価合計"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Revenues"
msgstr "収益合計"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "タスクの顧客満足度を追跡"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr "成功するために到達すべき重要な進捗ポイントを管理する"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Track major progress points that must be reached to achieve success."
msgstr "成功するために到達すべき重要な進捗ポイントを管理する"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid ""
"Track project costs, revenues, and margin by setting the analytic account "
"associated with the project on relevant documents."
msgstr "プロジェクトに関連する分析アカウントを関連文書に設定することで、プロジェクトの原価、収益、マージンを追跡します。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "プロジェクトの進捗を管理"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "プロジェクトやタスクの時間消費を追跡"

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr "透明タグは、プロジェクトやタスクのかんばんビューには表示されません。"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "毎月2回"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Two tasks cannot depend on each other."
msgstr "2つのタスクは互いに依存し合うことはできません。"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外活動の種類。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid "Unarchive Projects"
msgstr "プロジェクトのアーカイブ解除"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid "Unarchive Tasks"
msgstr "タスクのアーカイブ解除"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Unassigned"
msgstr "未割当"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Unfollow"
msgstr "フォロー解除"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Unknown Analytic Account"
msgstr "不明な分析勘定"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
msgid "Until"
msgstr "終了"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "更新作成済"

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr "ユーザビリティ"

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr "マイルストンを使用"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "プロジェクト評価を使用"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "繰返しタスクを使用"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "プロジェクトでステージを使用"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "依存タスクを使用"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "名前を付けてタスクを使用"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Use This For My Project"
msgstr "自分のプロジェクトに利用"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid ""
"Use personal stages to organize your tasks and create your own workflow."
msgstr "個人用ステージを使用して、タスクを整理し、独自のワークフローを作成しましょう。"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "タグを使用して、タスクをカテゴリ化します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Use the"
msgstr "以下を使う:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"main changes about this task."
msgstr ""
"チャターを使用して<b>メールを送信</b>し、顧客と効率的にやり取りしましょう。新しい人をフォロワーリストに追加して、このタスクに関する主な変更を認識してもらいましょう。"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""
"新しいタスクを設定するのにこれらのキーワードを使いましょう:\n"
"\n"
"       30h タスクに30時間を割り当てる\n"
"      #タグ タスクにタグを設定する\n"
"        @ユーザ タスクをユーザーに割り当てる \n"
"        ! タスクに高い優先順位を設定\n"
"\n"
"        正しいフォーマットと順序を使うようにしましょう。 例: 設定画面の改善 5h #feature #v16 @Mitchell !"

#. module: project
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "ユーザ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "照会"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "View Task"
msgstr "タスク照会"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "View Tasks"
msgstr "タスクを表示"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "可視性"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Visible"
msgstr "表示"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__04_waiting_normal
msgid "Waiting"
msgstr "待機中"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr "<b>プロジェクトを管理</b>するより良い方法をお探しですか？ <i>ここから始まります。</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Website Redesign"
msgstr "ウェブサイトリニューアル"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "週次"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "週"

#. module: project
#: model:project.tags,name:project.project_tags_14
msgid "Work"
msgstr "仕事"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "割当までの稼働日数"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "クローズまでの稼働日数"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "割当までの稼働時間"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "クローズまでの稼働時間"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "割当作業時間"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "クローズまでの稼働時間"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the projects contained in these stages as"
" well?"
msgstr "これらのステージに含まれるすべてのプロジェクトもアーカイブ解除しますか？"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr "これらのステージに含まれるすべてのタスクもアーカイブ解除しますか？"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Write a message..."
msgstr "メッセージを書く..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Writing"
msgstr "書き込み中"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "年次"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "年"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid ""
"You are not able to switch the company of this stage to %(company_name)s "
"since it currently includes projects associated with "
"%(project_company_name)s. Please ensure that this stage exclusively consists"
" of projects linked to %(company_name)s."
msgstr ""
"現在、このステージの会社を%(company_name)sに切り替えることはできません。このステージに%(project_company_name)sに関連するプロジェクトが含まれているためです。このステージが%(company_name)sに関連したプロジェクトのみで構成されるようにして下さい。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can change the sub-task state here!"
msgstr "ここでサブタスクのステータスを変更できます!"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You can only set a personal stage on a private task."
msgstr "個人ステージは、非公開タスクにしか設定できません。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can open sub-tasks from the kanban card!"
msgstr "かんばんカードからサブタスクを開くことができます!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You can either archive them or"
" first delete all of their projects."
msgstr "プロジェクトを含むステージは削除できません。アーカイブするか、最初に全てのプロジェクトを削除して下さい。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You should first delete all of"
" their projects."
msgstr "プロジェクトを含むステージは削除できません。最初に全てのプロジェクトを削除して下さい。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr "タスクを含むステージは削除できません。アーカイブするか、最初に全てのプロジェクトを削除して下さい。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr "タスクを含むステージは削除できません。最初に全てのタスクを削除して下さい。"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot read the following fields on tasks: %(field_list)s"
msgstr "以下のフィールドをタスクで読むことはできません:%(field_list)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot write on the following fields on tasks: %(field_list)s"
msgstr "タスクの以下のフィールドには書き込めません:%(field_list)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been assigned to %s"
msgstr "%sに割り当てられました"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "次のドキュメントに割り当てられました:"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been invited to follow %s"
msgstr "%sをフォローするよう招待されています"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "You have been invited to follow Task Document :"
msgstr "次のタスクドキュメントをフォローするよう招待されました:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"You have full control and can revoke portal access anytime. Are you ready to"
" proceed?"
msgstr "いつでもポータルへのアクセスを取り消すことができます。準備はできましたか？"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"You have unsaved changes - no worries! Odoo will automatically save it as "
"you navigate.<br/> You can discard these changes from here or manually save "
"your task.<br/>Let's save it manually."
msgstr ""
"未保存の変更があっても心配ありません！あなたがサイトを見ている間、Odooが自動的に保存します。<br/>ここから変更を破棄することも、手動でタスクを保存することもできます。<br/>手動で保存しましょう。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "You must be"
msgstr "すべきです"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Your managers decide which feedback is accepted"
msgstr "上司が、どのフィードバックを受け入れるか"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "alias"
msgstr "エイリアス"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "and"
msgstr "と"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""
"そしてどのフィードバックを\n"
"       \"否認\"カラムに移動するかを決定します。"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr "担当者"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "avatar"
msgstr "アバター"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "button."
msgstr "ボタン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "comments"
msgstr "コメント"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr "例. 月次進捗会議"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "e.g. Office Party"
msgstr "例えば オフィスパーティー"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "例: プロダクトのローンチ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr "例. 招待を送る"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "e.g. Tasks"
msgstr "例: タスク"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_tree
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "e.g. To Do"
msgstr "例: To Do"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. mycompany.com"
msgstr "例: mycompany.com"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "例: オフィスパーティー"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr "例: プロダクトのローンチ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon to organize your daily activities."
msgstr "日々の活動を整理するアイコン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"icon to see tasks waiting on other ones. Once a task is marked as complete "
"or cancelled, all of its dependencies will be unblocked."
msgstr ""
"アイコンをクリックすると、他のタスクを待っているタスクが表示されます。タスクが完了または取消済とマークされると、そのタスクの全ての依存関係が解除されます。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon."
msgstr "アイコン"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "logged in"
msgstr "ログイン"

#. module: project
#: model:ir.actions.server,name:project.action_server_view_my_task
msgid "menu view My Tasks"
msgstr "メニュービュー自分のタスク"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "on a periodic basis"
msgstr "定期的に"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "プロジェクト"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr "到達済みとマーク可能"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to indicate a request for changes or a need for discussion on a task."
msgstr "タスクの変更要求や議論の必要性を示すステータス"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to inform your colleagues that a task is approved for the next stage."
msgstr "タスクが次のステージへ検証されたことを同僚に知らせるステータス"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as cancelled."
msgstr "タスクを取消済とマークするため"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as complete."
msgstr "タスクを完了済とマークするステータス"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_name_with_subtask_count_char_field/project_task_name_with_subtask_count_char_field.xml:0
msgid "sub-tasks)"
msgstr "サブタスク)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "task"
msgstr "タスク"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "以下のマイルストン用の期日が更新されました:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "以下のマイルストン用の期日が更新されました:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""
"プロジェクトが\n"
"      次のステップへの用意ができているかを定義します。"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "to post a comment."
msgstr "コメントを投稿"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "to signalize what is the current status of your Idea."
msgstr "アイデアの現在のステータスを示します。"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "when reaching a given stage"
msgstr "一定のステージに達するとき"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr "タスクを作成します:"

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid ""
"{{ object.project_id.company_id.name or user.env.company.name }}: "
"Satisfaction Survey"
msgstr ""
"{{ object.project_id.company_id.name or user.env.company.name }}: 満足度調査"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "👤 Unassigned"
msgstr "👤 未割当"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_graph/project_task_graph_model.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "🔒 Private"
msgstr "🔒 非公開"
