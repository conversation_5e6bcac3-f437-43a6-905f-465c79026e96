# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:46+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Không tìm thấy mốc thời gian nào. Hãy tạo một mốc!\n"
"                </p><p>\n"
"                    Theo dõi các điểm tiến độ chính cần đạt được để đến với thành công.\n"
"                </p>\n"
"            "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"\n"
");\n"
"\n"
"export class ProjectTaskFormController extends FormControllerWithHTMLExpander {\n"
"    setup() {\n"
"        super.setup();\n"
"        this.notifications = useService(\"notification\");\n"
"    }\n"
"\n"
"    /**\n"
"     * @override\n"
"     */\n"
"    getStaticActionMenuItems() {\n"
"        return {\n"
"            ...super.getStaticActionMenuItems(),\n"
"            openHistoryDialog: {\n"
"                sequence: 50,\n"
"                icon: \"fa fa-history\",\n"
"                description: _t(\"Version History\"),\n"
"                callback: () => this.openHistoryDialog(),\n"
"            },\n"
"        };\n"
"    }\n"
"\n"
"    get deleteConfirmationDialogProps() {\n"
"        const deleteConfirmationDialogProps = super.deleteConfirmationDialogProps;\n"
"        if (!this.model.root.data.subtask_count) {\n"
"            return deleteConfirmationDialogProps;\n"
"        }\n"
"        return {\n"
"            ...deleteConfirmationDialogProps,\n"
"            body: subTaskDeleteConfirmationMessage,\n"
"        }\n"
"    }\n"
"\n"
"    async openHistoryDialog() {\n"
"        const record = this.model.root;\n"
"        const versionedFieldName = 'description';\n"
"        const historyMetadata = record.data[\"html_field_history_metadata\"]?.[versionedFieldName];\n"
"        if (!historyMetadata) {\n"
"            this.notifications.add(\n"
"                escape(_t(\n"
"                    \"The task description lacks any past content that could be restored at the moment.\"\n"
"                ))\n"
"            );\n"
"            return;\n"
"        }\n"
"\n"
"        this.dialogService.add(\n"
"            HistoryDialog,\n"
"            {\n"
"                title: _t(\"Task Description History\"),\n"
"                noContentHelper: markup(\n"
"                    "
msgstr ""
"\n"
");\n"
"\n"
"export class ProjectTaskFormController extends FormControllerWithHTMLExpander {\n"
"    setup() {\n"
"        super.setup();\n"
"        this.notifications = useService(\"notification\");\n"
"    }\n"
"\n"
"    /**\n"
"     * @override\n"
"     */\n"
"    getStaticActionMenuItems() {\n"
"        return {\n"
"            ...super.getStaticActionMenuItems(),\n"
"            openHistoryDialog: {\n"
"                sequence: 50,\n"
"                icon: \"fa fa-history\",\n"
"                description: _t(\"Version History\"),\n"
"                callback: () => this.openHistoryDialog(),\n"
"            },\n"
"        };\n"
"    }\n"
"\n"
"    get deleteConfirmationDialogProps() {\n"
"        const deleteConfirmationDialogProps = super.deleteConfirmationDialogProps;\n"
"        if (!this.model.root.data.subtask_count) {\n"
"            return deleteConfirmationDialogProps;\n"
"        }\n"
"        return {\n"
"            ...deleteConfirmationDialogProps,\n"
"            body: subTaskDeleteConfirmationMessage,\n"
"        }\n"
"    }\n"
"\n"
"    async openHistoryDialog() {\n"
"        const record = this.model.root;\n"
"        const versionedFieldName = 'description';\n"
"        const historyMetadata = record.data[\"html_field_history_metadata\"]?.[versionedFieldName];\n"
"        if (!historyMetadata) {\n"
"            this.notifications.add(\n"
"                escape(_t(\n"
"                    \"The task description lacks any past content that could be restored at the moment.\"\n"
"                ))\n"
"            );\n"
"            return;\n"
"        }\n"
"\n"
"        this.dialogService.add(\n"
"            HistoryDialog,\n"
"            {\n"
"                title: _t(\"Task Description History\"),\n"
"                noContentHelper: markup(\n"
"                    "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "# Đánh giá"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr "SL nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""
"#{record.milestone_count_reached.value} Mốc thời gian đã đạt trong số "
"#{record.milestone_count.value}"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "#{task.stage_id.name}"
msgstr "#{task.stage_id.name}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(closedCount)s sub-tasks closed out of %(totalCount)s"
msgstr "%(closedCount)s nhiệm vụ phụ đã đóng trong số %(totalCount)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s"
msgstr "%(closed_task_count)s/%(task_count)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"
msgstr "%(closed_task_count)s/%(task_count)s (%(closed_rate)s%%)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(count1)s/%(count2)s"
msgstr "%(count1)s/%(count2)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s Dashboard"
msgstr "Trang chủ %(name)s "

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Burndown Chart"
msgstr "Biểu đồ burn-down của %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Milestones"
msgstr "Mốc thời gian của %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Rating"
msgstr "Đánh giá của %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Tasks Analysis"
msgstr "Phân tích nhiệm vụ của %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "%(partner_name)s's Tasks"
msgstr "Nhiệm vụ của %(partner_name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project_stage.py:0
#: code:addons/project/models/project_task.py:0
#: code:addons/project/models/project_task_type.py:0
msgid "%s (copy)"
msgstr "%s (sao chép)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "%s closed tasks"
msgstr "%s nhiệm vụ đã đóng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(tới hạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(cập nhật dự án cuối cùng),"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "(other) tasks to which you do not have access."
msgstr "nhiệm vụ (khác) mà bạn không có quyền truy cập."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- đạt được vào"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "<b>Kéo &amp; thả</b> các thẻ để đổi công việc của bạn từ trạng thái."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this "
"task won't be notified of the note you are logging unless you specifically "
"tag them)</i>. Use @ <b>mentions</b> to ping a colleague or # "
"<b>mentions</b> to reach an entire team."
msgstr ""
"<b>Tạo ghi chú</b> để trao đổi nội bộ <i>(những người theo dõi nhiệm vụ này "
"sẽ không được thông báo về ghi chú bạn tạo trừ khi bạn gắn thẻ họ)</i>. Dùng"
" @ <b>đề cập</b> để thông báo cho một đồng nghiệp hoặc # <b>đề cập</b> để "
"thông báo cho cả bộ phận."

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/><br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the <strong t-out=\"object.name or ''\">Planning and budget</strong> task\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 13px;text-align:center;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br/>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey has been sent because your task has been moved to the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br/>Best regards,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Xin chào <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Xin chào,<br/><br/>\n"
"            </t>\n"
"            Vui lòng dành chút thời gian để đánh giá dịch vụ <strong t-out=\"object.name or ''\">Lập kế hoạch và ngân sách</strong>  \n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                đã được giao cho <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong> của chúng tôi.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 13px;text-align:center;\">\n"
"                    <strong>Vui lòng cho biết cảm nghĩ của bạn về dịch vụ của chúng tôi</strong><br/>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(nhấp vào một trong những biểu tượng cảm xúc này)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            Chúng tôi rất trân trọng phản hồi từ bạn, vì nhờ đó chúng tôi có thể liên tục cải thiện dịch vụ của mình.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">Bản khảo sát mức độ hài lòng này được gửi tới bạn vì nhiệm vụ của bạn đã được chuyển sang giai đoạn <b t-out=\"object.stage_id.name or ''\">Đang thực hiện</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">Bản khảo sát mức độ hài lòng này sẽ được gửi <b t-out=\"object.project_id.rating_status_period or ''\">hàng tuần</b> nếu nhiệm vụ vẫn còn trong giai đoạn <b t-out=\"object.stage_id.name or ''\">Đang thực hiện</b>.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br/>Trân trọng,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""
"<div>\n"
"    Xin chào <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    Tôi rất vui mừng được thông báo với bạn rằng chúng tôi đã hoàn thành dự án \"<strong t-out=\"object.name or ''\">Cải tiến</strong>\" thành công .\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">Bạn nhận được email này vì dự án của bạn đã được chuyển sang giai đoạn <b t-out=\"object.stage_id.name or ''\">Hoàn tất</b></span>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/><br/>\n"
"    Thank you for contacting us. We appreciate your interest in our products/services.<br/>\n"
"    Our team is currently reviewing your inquiry and will respond to your email as soon as possible.<br/>\n"
"    If you have any further questions or concerns in the meantime, please do not hesitate to let us know. We are here to help.<br/><br/>\n"
"    Thank you for your patience.<br/>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    Xin chào <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/><br/>\n"
"    Cảm ơn bạn đã liên lạc và quan tâm đến sản phẩm/dịch vụ của chúng tôi.<br/>\n"
"    Chúng tôi hiện đang xem xét trường hợp này và sẽ trả lời email của bạn sớm nhất có thể.<br/>\n"
"    Nếu bạn có thêm bất kỳ câu hỏi hoặc thắc mắc nào trong thời gian này, xin vui lòng liên hệ lại. Chúng tôi luôn sẵn sàng trợ giúp bạn.<br/><br/>\n"
"    Cảm ơn bạn đã kiên nhẫn chờ đợi.<br/>\n"
"    Trân trọng,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg < 3.66\" title=\"Hài lòng\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg < 2.33 or rating_avg >= 3.66\" title=\"Bình thường\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg >= 2.33\" title=\"Không hài lòng\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> Riêng tư"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> bị vô hiệu hóa trên (các) dự án sau: <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Trở lại chế độ chỉnh sửa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i title=\"Private Task\" class=\"fa fa-lock\"/>"
msgstr "<i title=\"Nhiệm vụ riêng tư\" class=\"fa fa-lock\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">Giai đoạn:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Assignees</small>"
msgstr "<small class=\"text-muted\">Người được phân công</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">Khách hàng</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o me-2\" title=\"Ngày\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Bí danh miền\" title=\"Bí"
" danh miền\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user me-2\" aria-label=\"Đối tác\" title=\"Đối tác\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Done</span>"
msgstr "<span class=\"fw-normal\"> Hoàn tất</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Tasks</span>"
msgstr "<span class=\"fw-normal\"> Nhiệm vụ</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<span class=\"o_stat_text\">Blocked Tasks</span>"
msgstr "<span class=\"o_stat_text\">Nhiệm vụ đã chặn</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">Đánh giá cuối cùng</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Parent Task</span>"
msgstr "<span class=\"o_stat_text\">Nhiệm vụ chính</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text\">Nhiệm vụ phụ</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">Nhiệm vụ</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"text-muted o_row ps-1 pb-3\">Send a rating request:</span>"
msgstr "<span class=\"text-muted o_row ps-1 pb-3\">Gửi yêu cầu đánh giá:</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">A rating request will be sent as soon as the task reaches a stage on which a Rating Email Template is defined.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Rating requests will be sent as long as the task remains in a stage on which a Rating Email Template is defined.</span>\n"
"                                                </span>"
msgstr ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">Yêu cầu đánh giá sẽ được gửi ngay khi nhiệm vụ đạt đến giai đoạn mà Mẫu email đánh giá được chỉ định.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Yêu cầu đánh giá sẽ được gửi nếu nhiệm vụ vẫn ở giai đoạn mà Mẫu email đánh giá được chỉ định.</span>\n"
"                                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Báo cáo</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>Xem</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Thời hạn:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>Mốc thời gian:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>Dự án:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>Mốc thời gian</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Một từ điển Python sẽ được đánh giá để cung cấp giá trị mặc định khi tạo bản"
" ghi mới cho bí danh này. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"Không thể chọn một cộng tác viên hai lần trong truy cập chia sẻ dự án. Vui "
"lòng xóa trùng lặp và thử lại."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created and is not part of any project."
msgstr "Một nhiệm vụ mới đã được tạo và không thuộc về bất kỳ dự án nào."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created in the \"%(project_name)s\" project."
msgstr "Một nhiệm vụ mới đã được tạo trong dự án \"%(project_name)s\"."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"Không thể liên kết một giai đoạn cá nhân với một dự án vì nó chỉ hiển thị "
"với người dùng tương ứng."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_private_task_has_no_parent
msgid "A private task cannot have a parent."
msgstr "Một nhiệm vụ riêng tư không thể có nhiệm vụ chính."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_recurring_task_has_no_parent
msgid "A subtask cannot be recurrent."
msgstr "Một nhiệm vụ phụ không thể lặp lại."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "Thẻ có cùng tên đã tồn tại."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "Một nhiệm vụ chỉ được có một giai đoạn cá nhân cho mỗi người dùng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Chấp nhận email từ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__access_mode
msgid "Access Mode"
msgstr "Chế độ truy cập"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "Cảnh báo truy cập"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "Hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hoạt động ngoại lệ"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_project_task_plan
#: model:ir.ui.menu,name:project.mail_activity_plan_menu_config_project
msgid "Activity Plans"
msgstr "Kế hoạch hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "Loại hoạt động"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Progress Report\", \"Stand-up Meeting\", ...)"
msgstr ""
"Kế hoạch hoạt động được sử dụng để chỉ định danh sách hoạt động chỉ trong vài cú nhấp chuột\n"
"                 (VD: \"Báo cáo tiến độ\", \"Cuộc họp ngắn\", ...)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Add Milestone"
msgstr "Thêm mốc thời gian"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.js:0
msgid "Add Sub-tasks"
msgstr "Thêm nhiệm vụ phụ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add a sub-task"
msgstr "Thêm nhiệm vụ phụ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"Thêm các cột để sắp xếp các nhiệm vụ của bạn thành <b>các giai đoạn</b> "
"<i>ví dụ. Mới - Đang tiến hành - Đã xong</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Add details about this task..."
msgstr "Thêm thông tin về nhiệm vụ này..."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "Thêm nội dung để hiển thị trong email"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid ""
"Add project-specific property fields on tasks to customize your project "
"management process."
msgstr ""
"Thêm những trường thuộc tính cụ thể theo dự án vào các nhiệm vụ để tùy chỉnh"
" quy trình quản lý dự án của bạn."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add your task once it is ready."
msgstr "Thêm nhiệm vụ của bạn khi nó đã sẵn sàng."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "Quản trị viên"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Agile Scrum"
msgstr "Agile Scrum"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "Bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "Bảo mật bí danh liên hệ "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain_id
msgid "Alias Domain"
msgstr "Miền bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias Domain Name"
msgstr "Tên miền bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_full_name
msgid "Alias Email"
msgstr "Email bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "Tên bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_status
msgid "Alias Status"
msgstr "Trạng thái bí danh"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Trạng thái bí danh được ước tính trong tin nhắn đã nhận gần đây nhất."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "Mô hình bí danh"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "All"
msgstr "Tất cả"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management_all_tasks
msgid "All Tasks"
msgstr "Tất cả nhiệm vụ"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "Tất cả người dùng nội bộ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allocated_hours
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__allocated_hours
msgid "Allocated Time"
msgstr "Thời gian được phân bổ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_allocated_hours_template
msgid "Allocated Time:"
msgstr "Thời gian đã phân bổ:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Analytic"
msgstr "Phân tích"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__auto_account_id
msgid "Analytic Account"
msgstr "Tài khoản phân tích"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "Phân tích"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr ""
"Phân tích tốc độ hoàn thành nhiệm vụ dự án và kiểm tra xem mọi thứ có tiến "
"triển theo kế hoạch hay không."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr "Phân tích tiến độ dự án và hiệu quả làm việc của nhân viên."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__share_link
msgid "Anyone with this link can access the project in read mode."
msgstr "Bất kỳ ai có liên kết này đều có thể truy cập dự án ở chế độ đọc."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__03_approved
msgid "Approved"
msgstr "Đã phê duyệt"

#. module: project
#: model:project.tags,name:project.project_tags_07
msgid "Architecture"
msgstr "Kiến trúc"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid "Archive Account"
msgstr "Lưu trữ tài khoản"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid "Archive Accounts"
msgstr "Lưu trữ tài khoản"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "Lưu trữ các giai đoạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "Bạn có chắc chắn muốn tiếp tục không?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "Bạn có chắc chắn muốn xóa các giai đoạn này không?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: code:addons/project/static/src/components/subtask_one2many_field/subtask_list_renderer.js:0
msgid "Are you sure you want to delete this record?"
msgstr "Bạn có chắc muốn xóa dữ liệu này ?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Are you sure you want to restore this version ?"
msgstr "Bạn có chắc chắn muốn khôi phục phiên bản này không?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "Mũi tên"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "Biểu tượng mũi tên"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assembling"
msgstr "Lắp ráp"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Assign a responsible to your task"
msgstr "Phân công người phụ trách nhiệm vụ của bạn"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assigned"
msgstr "Đã phân công"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "Phân công cho"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.open_view_blocked_by_list_view
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Assignees"
msgstr "Người được phân công"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignement Date"
msgstr "Ngày phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "Ngày phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "Ngày phân công"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "Rủi ro"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Attach all documents or links to the task directly, to have all research "
"information centralized."
msgstr ""
"Trực tiếp đính kèm toàn bộ tài liệu hoặc liên kết với nhiệm vụ để tập trung "
"tất cả thông tin cần nghiên cứu."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Attachments"
msgstr "Tệp đính kèm"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message"
msgstr "Tệp đính kèm không gắn với tin nhắn."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "Tác giả"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "Tự động tạo nhiệm vụ cho các hoạt động thường xuyên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_state
msgid "Automatic Kanban Status"
msgstr "Trạng thái kanban tự động"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_state
msgid ""
"Automatically modify the state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the state to 'Approved' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'Changes Requested' (orange bullet).\n"
msgstr ""
"Tự động điều chỉnh trạng thái khi khách hàng trả lời phản hồi cho giai đoạn này.\n"
"* Phản hồi tốt từ khách hàng sẽ cập nhật trạng thái thành 'Đã phê duyệt' (dấu đầu dòng màu xanh lá cây).\n"
"* Phản hồi trung lập hoặc không tốt sẽ chuyển trạng thái kanban thành 'Đã yêu cầu thay đổi' (dấu đầu dòng màu cam).\n"

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Automatically send an email to customers when a task reaches a specific "
"stage in a project by setting this template on that stage"
msgstr ""
"Tự động gửi email cho khách hàng khi nhiệm vụ đạt đến một giai đoạn cụ thể "
"trong dự án bằng cách đặt mẫu này ở giai đoạn đó"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Avatar"
msgstr "Ảnh đại diện"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Average Rating"
msgstr "Đánh giá trung bình"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Đánh giá trung bình (%)"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
msgid "Average Rating (1-5)"
msgstr "Đánh giá trung bình (1-5)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "Đánh giá trung bình: Không hài lòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "Đánh giá trung bình: Bình thường"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "Đánh giá trung bình: Hài lòng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Backlog"
msgstr "Chưa thực hiện"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "Số dư"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Billed"
msgstr "Đã tính phí"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__dependent_ids
msgid "Block"
msgstr "Chặn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocked"
msgstr "Đã bị chặn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "Bị chặn bởi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked Tasks"
msgstr "Nhiệm vụ đã chặn"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocking"
msgstr "Chặn"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Brainstorm"
msgstr "Lên ý tưởng"

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr "Lỗi"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Burndown Chart"
msgstr "Sơ đồ chi tiết"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_canceled
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
msgid "Cancelled"
msgstr "Đã hủy"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__02_changes_requested
#: model:mail.message.subtype,description:project.mt_task_changes_requested
#: model:mail.message.subtype,name:project.mt_project_task_changes_requested
#: model:mail.message.subtype,name:project.mt_task_changes_requested
msgid "Changes Requested"
msgstr "Đã yêu cầu thay đổi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the "
"name of a customer, of a product, of a team, of a construction site, "
"etc.</i>"
msgstr ""
"Chọn một <b>tên</b> cho dự án. <i>Có thể là bất kỳ tên nào bạn muốn: tên của"
" khách hàng, sản phẩm, một bộ phận, hay tên một công trường,...</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr ""
"Chọn cho nhiệm vụ một cái <b>tên</b> <i>(ví dụ. Thiết kế Website, Mua hàng "
"...)</i>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Choose one of the following access modes for your collaborators:"
msgstr "Chọn một trong các chế độ truy cập sau cho cộng tác viên của bạn:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Client Review"
msgstr "Đánh giá của khách hàng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Close the sub-tasks list"
msgstr "Đóng danh sách nhiệm vụ phụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__closed_depend_on_count
msgid "Closed Depending on Tasks"
msgstr "Đã đóng tuỳ thuộc vào Nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed On"
msgstr "Đóng vào"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed Tasks"
msgstr "Nhiệm vụ đã đóng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_report_project_task_user__is_closed
msgid "Closed state"
msgstr "Trạng thái đã đóng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__closed
msgid "Closed tasks"
msgstr "Nhiệm vụ đã đóng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__is_closed
msgid "Closing Stage"
msgstr "Giai đoạn kết thúc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__partner_id
msgid "Collaborator"
msgstr "Cộng tác viên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__collaborator_ids
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Collaborators"
msgstr "Cộng tác viên"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Cộng tác viên dự án đã chia sẻ"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""
"Thu thập phản hồi từ khách hàng của bạn bằng cách gửi cho họ yêu cầu đánh giá khi nhiệm vụ bước vào một giai đoạn nhất định. Để làm như vậy, hãy chỉ định mẫu email xếp hạng ở các giai đoạn tương ứng.\n"
"Đánh giá khi thay đổi giai đoạn: một email sẽ được gửi tự động khi nhiệm vụ đạt đến giai đoạn mà mẫu email xếp hạng được thiết lập.\n"
"Đánh giá định kỳ: một email sẽ được gửi tự động đều đặn nếu nhiệm vụ vẫn ở giai đoạn mà mẫu email xếp hạng được thiết lập."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
msgid "Color"
msgstr "Màu sắc"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt."
msgstr ""
"Trao đổi với khách hàng về nhiệm vụ thông qua cổng email. Gắn các thiết kế logo vào nhiệm vụ để thông tin được truyền từ\n"
"      nhà thiết kế đến công nhân in áo phông."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Communication history"
msgstr "Lịch sử trao đổi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_project_stage__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Company"
msgstr "Công ty"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "Cấu hình"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "Định cấu hình giai đoạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "Xác nhận"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Confirmation"
msgstr "Xác nhận"

#. module: project
#: model_terms:web_tour.tour,rainbow_man_message:project.project_tour
msgid "Congratulations, you are now a master of project management."
msgstr ""
"Xin chúc mừng! Giờ đây bạn đã trở thành một bậc thầy về quản lý dự án."

#. module: project
#: model:project.tags,name:project.project_tags_06
msgid "Construction"
msgstr "Xây dựng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Consulting"
msgstr "Tư vấn"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "Liên hệ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid "Convert Task"
msgstr "Chuyển đổi nhiệm vụ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.actions.server,name:project.action_server_convert_to_subtask
msgid "Convert to Task/Sub-Task"
msgstr "Chuyển đổi thành nhiệm vụ/nhiệm vụ phụ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Copywriting"
msgstr "Được viết bởi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Costs"
msgstr "Chi phí"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "Ảnh bìa"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr ""
"Tạo <b>hoạt động</b> để tạo việc cần làm cho mình hoặc lên lịch các cuộc "
"họp."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "Tạo ngày"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "Tạo một dự án"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid "Create a Task Activity Plan"
msgstr "Tạo một kế hoạch hoạt động nhiệm vụ"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "Tạo một giai đoạn mới trong quy trình thực hiện nhiệm vụ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Create a new sub-task"
msgstr "Tạo một nhiệm vụ phụ mới"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "Tạo dự án"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""
"Tạo dự án để sắp xếp nhiệm vụ của bạn và xác định quy trình làm việc khác "
"nhau cho từng dự án."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr ""
"Tạo dự án để tổ chức nhiệm vụ của bạn. Xác định quy trình làm việc khác nhau"
" cho từng dự án."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "Tạo công việc bằng cách gửi email tới"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr "Tạo nhiệm vụ bằng cách gửi email đến địa chỉ email dự án của bạn."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "Được tạo vào"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Creation Date"
msgstr "Ngày tạo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "Dự án hiện tại của nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "Tình trạng hiện tại của tác vụ này"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"Hiện tại tất cả mọi người có thể xem tài liệu này, nhấp để giới hạn chỉ cho "
"nhân viên nội bộ."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"Hiện tại được giới hạn chỉ cho nhân viên nội bộ, nhấp để hiển thị tài liệu "
"này cho tất cả mọi người."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Tin nhắn bị trả lại tuỳ chỉnh"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Customer"
msgstr "Khách hàng"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Customer Email"
msgstr "Email khách hàng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Customer Feedback"
msgstr "Phản hồi của khách hàng"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "URL cổng thông tin khách hàng"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "Đánh giá của khách hàng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "Trạng thái xếp hạng của khách hàng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly."
msgstr ""
"Khách hàng đề xuất phản hồi qua email; Odoo tự động tạo các nhiệm vụ và bạn có thể \n"
"            trao đổi trực tiếp trên nhiệm vụ đó."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Customers will be added to the followers of their project and tasks."
msgstr ""
"Khách hàng sẽ được thêm vào danh sách người theo dõi dự án và nhiệm vụ của "
"họ."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "Hàng ngày"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model:ir.embedded.actions,name:project.project_embedded_action_project_updates
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Dashboard"
msgstr "Trang chủ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Date"
msgstr "Ngày"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the state of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage/state to another."
msgstr ""
"Ngày mà trạng thái nhiệm vụ của bạn được điều chỉnh lần cuối.\n"
"Dựa trên thông tin này, bạn có thể xác định các nhiệm vụ đang bị đình trệ và nhận số liệu thống kê về thời gian thông thường cần để chuyển nhiệm vụ từ giai đoạn/trạng thái này sang giai đoạn/trạng thái khác."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr ""
"Ngày mà dự án này kết thúc. Khung thời gian xác định trong dự án sẽ được "
"tính đến khi xem kế hoạch dự án."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"Ngày mà nhiệm vụ này được phân công lần cuối (hoặc chưa được phân công). Dựa"
" trên thông tin này, bạn có thể nhận được số liệu thống kê về thời gian "
"thông thường cần để phân công nhiệm vụ."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "Ngày"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "Số ngày tới hạn"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Deadline"
msgstr "Thời hạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "Thân gửi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "Giá trị Mặc định"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"Định nghĩa các bước mà sẽ được sử dụng ở dự án từ lúc khởi tạo nhiệm vụ\n"
"                đến lúc đóng nhiệm vụ hoặc phát sinh. Bạn sẽ sử dụng các giai\n"
"                đoạn này để theo dõi tiến triển trong việc thực hiện nhiệm vụ\n"
"                hoặc giải quyết các phát sinh."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr ""
"Xác định các bước mà dự án của bạn trải qua từ khi tạo đến khi hoàn thành."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr ""
"Xác định các nhiệm vụ mà dự án của bạn trải qua từ khi tạo đến khi hoàn "
"thành."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_project_stage_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Delete"
msgstr "Xoá"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Delete Milestone"
msgstr "Xóa mốc"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid "Delete Project Stage"
msgstr "Xoá giai đoạn dự án"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Delete Stage"
msgstr "Xoá giai đoạn"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""
"Tự động cung cấp dịch vụ của bạn khi đạt đến một cột mốc bằng cách liên kết "
"nó với một mục đơn bán hàng."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Delivered"
msgstr "Đã giao"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Dependent Tasks"
msgstr "Nhiệm vụ phụ thuộc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_count
msgid "Depending on Tasks"
msgstr "Phụ thuộc vào Nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model:ir.model.fields,field_description:project.field_report_project_task_user__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "Mô tả"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr "Mô tả để cung cấp thêm thông tin và bối cảnh về dự án này"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.tags,name:project.project_tags_08
msgid "Design"
msgstr "Thiết kế"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "Xác định thứ tự thực hiện nhiệm vụ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Development"
msgstr "Phát triển"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "Tóm tắt"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Digital Marketing"
msgstr "Marketing số"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Display the sub-task in your pipeline"
msgstr "Hiển thị nhiệm vụ phụ trong quy trình của bạn"

#. module: project
#. odoo-python
#: code:addons/project/models/digest_digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Không có quyền truy cập, bỏ qua dữ liệu này đối với email tóm tắt của người "
"dùng"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__done
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_update__status__done
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_done
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
msgid "Done"
msgstr "Hoàn tất"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Draft"
msgstr "Nháp"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Duplicate"
msgstr "Nhân bản"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"Each user should have at least one personal stage. Create a new stage to "
"which the tasks can be transferred after the selected ones are deleted."
msgstr ""
"Mỗi người dùng nên có ít nhất một giai đoạn cá nhân. Tạo một giai đoạn mới "
"để các nhiệm vụ có thể được chuyển sang sau khi những nhiệm vụ đã chọn bị "
"xóa."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit
msgid "Edit"
msgstr "Chỉnh sửa"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit_limited
msgid "Edit with limited access"
msgstr "Chỉnh sửa với quyền truy cập hạn chế"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit with limited access: collaborators can view and edit tasks they follow "
"in the Kanban view."
msgstr ""
"Chỉnh sửa với quyền truy cập hạn chế: cộng tác viên có thể xem và chỉnh sửa "
"các nhiệm vụ mà họ theo dõi trong chế độ xem Kanban."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit: collaborators can view and edit all tasks in the Kanban view. "
"Additionally, they can choose which tasks they want to follow."
msgstr ""
"Chỉnh sửa: cộng tác viên có thể xem và chỉnh sửa tất cả nhiệm vụ trong chế "
"độ xem Kanban. Ngoài ra, họ có thể chọn nhiệm vụ mà họ muốn theo dõi."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Editing"
msgstr "Đang chỉnh sửa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_email
msgid "Email Alias"
msgstr "Bí danh email"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "Mẫu email"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr ""
"Địa chỉ email nằm trong phần CC của các email đến từ nhiệm vụ này và hiện "
"không được liên kết với khách hàng hiện tại."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Ví dụ miền email 'example.com' trong '<EMAIL>'"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "Email gửi tới"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Employees Only"
msgstr "Employees Only"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "Ngày kết thúc"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "Lỗi! Bạn không thể tạo một hệ thống phân cấp nhiệm vụ đệ quy."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr ""
"Mọi người đều có thể đề xuất ý tưởng và Người biên tập sẽ đánh dấu những ý "
"tưởng hay nhất là"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Expected"
msgstr "Dự kiến"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "Thử nghiệm"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "Ngày hết hạn"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "Bên ngoài"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "Thông tin bổ sung"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "Yêu thích"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Favorite Projects"
msgstr "Dự án yêu thích"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Final Document"
msgstr "Tài liệu cuối cùng"

#. module: project
#: model:project.tags,name:project.project_tags_11
msgid "Finance"
msgstr "Tài chính"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "Thu gọn trong kanban"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Follow"
msgstr "Theo dõi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow and comments tasks of your projects"
msgstr "Theo dõi và nhận xét nhiệm vụ của dự án của bạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow the evolution of your projects"
msgstr "Theo dõi sự phát triển của dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Followed"
msgstr "Đã theo dõi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr "Cập nhật được theo dõi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng, ví dụ: fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "Mãi mãi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Frequency"
msgstr "Tần suất"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Future"
msgstr "Tương lai"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "Hoạt động trong tương lai"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""
"Chụp ảnh trạng thái của dự án và chia sẻ tiến độ với các bên liên quan chủ "
"chốt."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback and evaluate the performance of your employees"
msgstr ""
"Nhận phản hồi của khách hàng và đánh giá hiệu quả làm việc của nhân viên"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Give the sub-task a <b>name</b>"
msgstr "Đặt <b>tên</b> cho nhiệm vụ phụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Grant Portal Access"
msgstr "Cấp quyền truy cập cổng thông tin"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
" Employees automatically get access to the tasks they are assigned to."
msgstr ""
"Cấp cho nhân viên quyền truy cập vào dự án hoặc nhiệm vụ của bạn bằng cách "
"thêm họ làm người theo dõi. Nhân viên tự động có quyền truy cập vào các "
"nhiệm vụ mà họ được phân công."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant portal users access to your project by adding them as followers (the "
"tasks of the project are not included). To grant access to tasks to a portal"
" user, add them as followers for these tasks."
msgstr ""
"Cấp quyền truy cập dự án của bạn cho người dùng cổng thông tin bằng cách "
"thêm họ làm người theo dõi (không bao gồm các nhiệm vụ của dự án). Để cấp "
"quyền truy cập nhiệm vụ cho người dùng cổng thông tin, hãy thêm họ làm người"
" theo dõi các nhiệm vụ này."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Group By"
msgstr "Nhóm theo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks."
msgstr ""
"Xử lý việc thu thập ý tưởng trong Nhiệm vụ của Dự án mới và thảo luận về "
"chúng trong hộp thoại trò chuyện của nhiệm vụ."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Handoff"
msgstr "Ra tay"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "Mặt vui vẻ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "Hello"
msgstr "Xin chào"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Hide closed tasks"
msgstr "Ẩn nhiệm vụ đã đóng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hide the sub-task in your pipeline"
msgstr "Ẩn nhiệm vụ phụ trong quy trình của bạn"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "Cao"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "Lịch sử"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history
msgid "History data"
msgstr "Dữ liệu lịch sử"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history_metadata
msgid "History metadata"
msgstr "Siêu dữ liệu lịch sử"

#. module: project
#: model:project.tags,name:project.project_tags_13
msgid "Home"
msgstr "Trang chủ"

#. module: project
#: model:account.analytic.account,name:project.analytic_construction
#: model:project.project,name:project.project_home_construction
msgid "Home Construction"
msgstr "Xây dựng nhà ở"

#. module: project
#: model:project.project,name:project.project_project_4
msgid "Home Make Over"
msgstr "Cải tạo nhà cửa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "Giờ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "Dự án đang diễn ra như thế nào?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID của bản ghi chính chứa bí danh (ví dụ: dự án chứa bí danh tạo nhiệm vụ)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng cho thấy một hoạt động ngoại lệ."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Ideas"
msgstr "Các ý tưởng"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""
"Nếu được bật, giai đoạn này sẽ được hiển thị dưới dạng thu gọn trong chế độ "
"xem kanban của dự án. Các dự án ở giai đoạn thu gọn được coi là đã đóng."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""
"Nếu thiết lập, yêu cầu đánh giá sẽ tự động được gửi qua email cho khách hàng khi nhiệm vụ đạt đến giai đoạn này.\n"
"Ngoài ra, yêu cầu đánh giá sẽ được gửi định kỳ nếu nhiệm vụ vẫn ở giai đoạn này, tùy vào cấu hình dự án của bạn.\n"
"Để sử dụng tính năng này, hãy đảm bảo rằng tùy chọn 'Đánh giá của khách hàng' được bật trên dự án của bạn."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr ""
"Nếu thiết lập, một email sẽ tự động được gửi đến khách hàng khi dự án đạt "
"đến giai đoạn này."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr ""
"Nếu thiết lập, một email sẽ tự động được gửi đến khách hàng khi nhiệm vụ đạt"
" đến giai đoạn này."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Nếu được cài đặt, nội dung này sẽ tự động được gửi đến người dùng chưa được "
"cấp quyền thay vì tin nhắn mặc định."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__01_in_progress
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,name:project.project_stage_1
msgid "In Progress"
msgstr "Đang thực hiện"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "In development"
msgstr "Đang phát triển"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
msgid "Inbox"
msgstr "Hộp thư đến"

#. module: project
#: model:project.tags,name:project.project_tags_09
msgid "Interior"
msgstr "Nội thất"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "Nội bộ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal Note"
msgstr "Ghi chú nội bộ"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"Email nội bộ liên kết với dự án này. Các email gửi đến sẽ được tự động đồng "
"bộ với các Nhiệm vụ (hoặc tuỳ chọn với Phát sinh nếu có cài đặt phân hệ Theo"
" dõi Phát sinh)."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal notes are only displayed to internal users."
msgstr "Ghi chú nội bộ chỉ được hiển thị cho người dùng nội bộ."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid operator: %s"
msgstr "Toán tử không hợp lệ: %s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid value: %s"
msgstr "Giá trị không hợp lệ: %s"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users (private)"
msgstr "Người dùng nội bộ được mời (riêng tư)"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr ""
"Người dùng cổng thông tin được mời và tất cả người dùng nội bộ (công khai)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Invoiced"
msgstr "Đã xuất hóa đơn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Is Closed (Burn-up Chart)"
msgstr "Đã đóng (Biểu đồ burn-up)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
#: model:ir.model.fields,field_description:project.field_report_project_task_user__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Is toggle mode"
msgstr "Chế độ bật tắt"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "Phiên bản Phát sinh"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__duration_tracking
#: model:ir.model.fields,help:project.field_project_task__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON ánh xạ id từ trường many2one tới số giây đã sử dụng"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Theo dõi tiến độ nhiệm vụ của bạn từ khi tạo đến khi hoàn thành.<br>\n"
"                    Cộng tác hiệu quả bằng cách trò chuyện trong thời gian thực hoặc qua email."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Theo dõi tiến độ nhiệm vụ của bạn từ khi tạo đến khi hoàn thành.<br>\n"
"                    Cộng tác hiệu quả bằng cách trò chuyện trong thời gian thực hoặc qua email."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 30 Days"
msgstr "30 ngày qua"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 365 Days"
msgstr "365 ngày qua"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 7 Days"
msgstr "7 ngày qua"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "Tháng trước"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
msgid "Last Rating (1-5)"
msgstr "Đánh giá cuối cùng (1-5)"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last Stage Update"
msgstr "Cập nhật giai đoạn lần cuối"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "Cập nhật lần cuối vào"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "Hoạt động chậm trễ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Late Milestones"
msgstr "Mốc thời gian chậm trễ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
msgid "Later"
msgstr "Để sau"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Leave a comment"
msgstr "Để lại một bình luận"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>project</b>."
msgstr "Hãy tạo <b>dự án</b> đầu tiên của bạn."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>stage</b>."
msgstr "Hãy tạo <b>giai đoạn</b> đầu tiên của bạn."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>task</b>."
msgstr "Hãy tạo <b>nhiệm vụ</b> đầu tiên của bạn."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your second <b>stage</b>."
msgstr "Hãy tạo <b>giai đoạn</b> thứ hai của bạn."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"Hãy quay lại <b>dạng xem kanban</b> để có cái nhìn tổng quan về các nhiệm vụ"
" tiếp theo."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's start working on your task."
msgstr "Hãy bắt tay vào thực hiện nhiệm vụ của bạn."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "Hãy đợi khách hàng của bạn tự biểu hiện."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Live"
msgstr "Trực tiếp"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Phát hiện gửi đến dựa trên phần cục bộ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Logo Design"
msgstr "Thiết kế logo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Look for the"
msgstr "Tìm kiếm"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "Thấp"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""
"Quản lý vòng đời dự án của bạn bằng chế độ xem Kanban. Thêm các dự án mới,\n"
"          phân công chúng và sử dụng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Manufacturing"
msgstr "Sản xuất"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "Mark as done"
msgstr "Đánh dấu xong"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as incomplete"
msgstr "Đánh dấu là chưa hoàn thành"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as reached"
msgstr "Đánh dấu là đã đạt"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Mark the task as <b>Cancelled</b>"
msgstr "Đánh dấu nhiệm vụ là <b>Đã bị huỷ</b>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Material Sourcing"
msgstr "Nguồn lực"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr ""
"Đo lường sự hài lòng của khách hàng bằng cách gửi yêu cầu đánh giá khi nhiệm"
" vụ đạt đến một giai đoạn nhất định."

#. module: project
#: model:project.tags,name:project.project_tags_15
msgid "Meeting"
msgstr "Cuộc họp"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "Tin nhắn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Milestone"
msgstr "Mốc thời gian"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Milestones"
msgstr "Mốc thời gian"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Mixing"
msgstr "Phối hợp"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "Tháng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Thời hạn hoạt động của tôi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "My Deadline"
msgstr "Thời hạn của tôi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "Danh sách yêu thích của tôi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Projects"
msgstr "Dự án của tôi"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_my_task
#: model:ir.ui.menu,name:project.menu_project_management_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "My Tasks"
msgstr "Nhiệm vụ của tôi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "Cập nhật của tôi"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Name"
msgstr "Tên"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the Tasks"
msgstr "Tên nhiệm vụ"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr ""
"Tên được sử dụng để gọi các nhiệm vụ dự án của bạn, ví dụ: nhiệm vụ, phiếu "
"hỗ trợ, chạy nước rút,..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "Mặt không biểu cảm"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_list.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
msgid "New"
msgstr "Mới"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "Tính năng mới"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
msgid "New Milestone"
msgstr "Mốc mới"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Orders"
msgstr "Đơn đặt hàng mới"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_project_calendar/project_project_calendar_controller.js:0
msgid "New Project"
msgstr "Dự án mới"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Projects"
msgstr "Dự án mới"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Request"
msgstr "Đề nghị mới"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
msgid "New Task"
msgstr "Nhiệm vụ mới"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Newest"
msgstr "Mới nhất"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Next"
msgstr "Tiếp theo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Next Activity"
msgstr "Hoạt động tiếp theo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện trên lịch cho hoạt động tiếp theo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Thời hạn cho hoạt động tiếp theo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "Không có khách hàng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "Không có mốc thời gian"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Project"
msgstr "Không có dự án"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "No Subject"
msgstr "Không tiêu đề"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "Không tìm thấy loại hoạt động nào. Hãy tạo một loại!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "Chưa có đánh giá của khách hàng"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "Chưa có dữ liệu nào!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "Không tìm thấy dự án nào. Hãy tạo một dự án!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "Không tìm thấy giai đoạn nào. Hãy tạo một giai đoạn!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "Không tìm thấy thẻ nào. Hãy tạo mới!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "Không tìm thấy nhiệm vụ nào. Hãy tạo một nhiệm vụ!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "Không tìm thấy cập nhật. Hãy tạo mới!"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "None"
msgstr "Không"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Not Implemented."
msgstr "Không được triển khai."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "Ghi chú"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "Không đúng tiến độ"

#. module: project
#: model:project.tags,name:project.project_tags_10
msgid "Office"
msgstr "Văn phòng"

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "Thiết kế văn phòng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Old Completed Sprint"
msgstr "Sprint cũ hoàn thành"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "Tạm dừng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "Đúng tiến độ"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "Mỗi tháng một lần"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Only jpeg, png, bmp and tiff images are allowed as attachments."
msgstr "Chỉ cho phép dùng hình ảnh jpeg, png, bmp và tiff làm tệp đính kèm."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "Rất tiết! Một vài thứ bị sai. Hãy thử tải lại trang và đăng nhập lại."

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Open Tasks"
msgstr "Mở nhiệm vụ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Open sub-tasks notebook section"
msgstr "Mở phần sổ ghi chép nhiệm vụ phụ"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__open
msgid "Open tasks"
msgstr "Mở nhiệm vụ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Operation not supported"
msgstr "Hoạt động không được hỗ trợ"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID tùy chọn của một luồng (bản ghi) tập hợp tất cả tin nhắn nhận được, thậm "
"chí nếu đó là tin nhắn không có phản hồi. Nếu cài đặt, điều này sẽ tắt hoàn "
"toàn việc tạo các bản ghi mới. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Organize priorities amongst orders using the"
msgstr "Sắp xếp mức độ ưu tiên giữa các đơn hàng bằng cách sử dụng"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Sắp xếp các nhiệm vụ của bạn bằng cách điều chuyển chúng trong quy trình làm việc.<br>\n"
"                    Cộng tác hiệu quả bằng cách trò chuyện trong thời gian thực hoặc qua email."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Others"
msgstr "Khác"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Overdue"
msgstr "Quá hạn"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "Nhiệm vụ quá hạn"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Page Ideas"
msgstr "Ý tưởng trang"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "Mô hình chính"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID luồng bản ghi chính"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
msgid "Parent Task"
msgstr "Nhiệm vụ chính"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Mô hình chính chứa bí danh này. Mô hình chứa tham chiếu bí danh không nhất "
"thiết phải là mô hình được đưa ra bởi alias_model_id (Ví dụ: dự án "
"(parent_model) và nhiệm vụ (model))"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid ""
"Partner company cannot be different from its assigned projects' company"
msgstr ""
"Công ty của đối tác không được khác công ty của dự án mà đối tác đó được "
"giao"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "Partner company cannot be different from its assigned tasks' company"
msgstr ""
"Công ty của đối tác không được khác công ty của nhiệm vụ mà đối tác đó được "
"giao"

#. module: project
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
msgid "Partner's Tasks"
msgstr "Nhiệm vụ của đối tác"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"People invited to collaborate on the project will have portal access rights."
msgstr ""
"Những người được mời cộng tác trong dự án sẽ có quyền truy cập cổng thông "
"tin."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks they are following, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"Những người có thể thấy được dự án này và các nhiệm vụ dự án.\n"
"\n"
"- Người dùng nội bộ được mời: khi theo dõi một dự án, người dùng nội bộ sẽ có quyền truy cập vào tất cả nhiệm vụ dự án mà không có sự phân biệt. Nếu không, họ sẽ chỉ có quyền truy cập vào các nhiệm vụ nhất định mà họ đang theo dõi.\n"
"Người dùng có cấp độ quyền truy cập quản trị viên dự án vẫn có thể truy cập dự án này và các nhiệm vụ dự án, ngay cả khi rõ ràng họ không phải là một người theo dõi.\n"
"\n"
"- Tất cả người dùng nội bộ: tất cả người dùng nội bộ đều có thể truy cập vào dự án và tất cả nhiệm vụ dự án mà không có sự phân biệt.\n"
"\n"
"- Người dùng cổng thông tin được mời và tất cả người dùng nội bộ: tất cả người dùng nội bộ có thể truy cập dự án và tất cả nhiệm vụ dự án mà không có sự phân biệt.\n"
"Khi theo dõi một dự án, người dùng cổng thông tin sẽ chỉ có quyền truy cập vào các nhiệm vụ nhất định mà họ đang theo dõi.\n"
"\n"
"Khi một dự án được chia sẻ ở chế độ chỉ đọc, người dùng cổng thông tin sẽ được chuyển hướng đến cổng thông tin của họ. Họ có thể xem nhiệm vụ mà họ đang theo dõi, nhưng không thể chỉnh sửa chúng.\n"
"Khi một dự án được chia sẻ trong chế độ chỉnh sửa, người dùng cổng thông tin sẽ được chuyển hướng đến chế độ xem kanban và chế độ xem danh sách của nhiệm vụ. Họ có thể sửa đổi một số trường đã chọn trong nhiệm vụ.\n"
"\n"
"Trong mọi trường hợp, nếu người dùng nội bộ không có quyền truy cập dự án nhưng được cung cấp URL nhiệm vụ (và họ là một người theo dõi, nếu dự án ở chế độ riêng tư), thì họ vẫn có thể truy cập nhiệm vụ tương ứng."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Phần trăm đánh giá sự vui vẻ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Personal Stage"
msgstr "Giai đoạn cá nhân"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "Trạng thái giai đoạn cá nhân"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "Trạng thái nhiệm vụ cá nhân"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Planned Date"
msgstr "Ngày theo kế hoạch"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"Vui lòng xoá các tác vụ hiện tại trong dự án được liên kết đến các tài khoản"
" mà bạn muốn xoá."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Podcast and Video Production"
msgstr "Sản xuất Podcast và Video"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Chính sách cho phép đăng tin nhắn lên tài liệu sử dụng cổng email.\n"
"- mọi người: mọi người có thể đăng\n"
"- đối tác: chỉ các đối tác đã xác thực\n"
"- người theo dõi: chỉ những người theo dõi của tài liệu liên quan hoặc thành viên của kênh đang theo dõi\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "URL truy cập cổng thông tin"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr ""
"Người dùng cổng thông tin sẽ bị xóa khỏi danh sách người theo dõi dự án và "
"nhiệm vụ dự án."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Previous"
msgstr "Trước đó"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Prioritize your tasks by marking important ones using the"
msgstr ""
"Ưu tiên các nhiệm vụ của bạn bằng cách đánh dấu những nhiệm vụ quan trọng "
"bằng cách sử dụng"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#: model:project.tags,name:project.project_tags_16
msgid "Priority"
msgstr "Mức độ ưu tiên"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr ""
"Mức độ ưu tiên: {{'Quan trọng' if task.priority == '1' else 'Bình thường'}}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.js:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_model.js:0
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Private"
msgstr "Riêng tư"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Private Tasks"
msgstr "Nhiệm vụ riêng tư"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Private tasks cannot be converted into sub-tasks. Please set a project on "
"the task to gain access to this feature."
msgstr ""
"Không thể chuyển đổi nhiệm vụ riêng tư thành nhiệm vụ phụ. Vui lòng cài đặt "
"dự án cho nhiệm vụ để có quyền truy cập vào tính năng này."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Profitability"
msgstr "Lợi nhuận"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "Tiến độ"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Project"
msgstr "Dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__account_id
msgid "Project Account"
msgstr "Tài khoản dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "Trưởng dự án"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "Mốc thời gian dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "Tên dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "Trạng thái đánh giá dự án"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "Chia sẻ dự án"

#. module: project
#: model:ir.model,name:project.model_project_share_collaborator_wizard
msgid "Project Sharing Collaborator Wizard"
msgstr "Project Sharing Collaborator Wizard"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_recurring_tasks_action
msgid "Project Sharing Recurrence"
msgstr "Chu kỳ lặp chia sẻ dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "Chia sẻ dự án: Nhiệm vụ"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "Giai đoạn dự án"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "Giai đoạn dự án đã thay đổi"

#. module: project
#: model:ir.model,name:project.model_project_project_stage_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "Công cụ xóa giai đoạn dự án"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "Giai đoạn dự án"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "Thẻ dự án"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Task Stage Delete Wizard"
msgstr "Công cụ xóa giai đoạn nhiệm vụ dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "Nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "Project Title"
msgstr "Tiêu đề dự án"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "Cập nhật dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "Chế độ hiển thị dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "Mô tả dự án..."

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
msgid "Project shared with your collaborators."
msgstr "Dự án được chia sẻ với cộng tác viên của bạn."

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr "Trạng thái dự án - {{ object.name }}"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "Nhiệm vụ của dự án"

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "Dự án: Dự án đã hoàn thành"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr "Dự án: Xác nhận yêu cầu"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
msgid "Project: Send rating"
msgstr "Dự án: Gửi đánh giá"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "Dự án: Lời mời đánh giá nhiệm vụ"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Projects"
msgstr "Dự án"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr ""
"Các dự án chứa các nhiệm vụ về cùng một chủ đề và mỗi dự án có trang chủ "
"riêng."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""
"Các dự án có giai đoạn này. Nếu bạn thực hiện cùng một quy trình làm việc "
"trong nhiều dự án, bạn có thể chia sẻ giai đoạn này giữa các dự án và nhận "
"về thông tin tổng hợp."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Properties"
msgstr "Thuộc tính"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Public Link"
msgstr "Liên kết công cộng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Published"
msgstr "Đã đăng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Published on"
msgstr "Đã đăng vào"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Publishing"
msgstr "Đang đăng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "Hàng quý"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Quickly check the status of tasks for approvals or change requests and "
"identify those on hold until dependencies are resolved with the hourglass "
"icon."
msgstr ""
"Nhanh chóng kiểm tra trạng thái nhiệm vụ để phê duyệt hoặc yêu cầu thay đổi,"
" đồng thời xác định những nhiệm vụ đang chờ xử lý cho đến khi các thành phần"
" phụ thuộc được xử lý với biểu tượng đồng hồ cát."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "Đánh giá"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "Đánh giá (/5)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_pivot
msgid "Rating (1-5)"
msgstr "Đánh giá (1-5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "Nội dung đánh giá trung bình"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "Mẫu Email đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "Tần suất đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Đánh giá phản hồi cuối cùng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "Đánh giá ảnh cuối cùng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "Đánh giá giá trị cuối cùng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Đánh giá mức độ hài lòng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "Nội dung đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "Số đánh giá"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_milestone__rating_ids
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model:ir.model.fields,field_description:project.field_project_update__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "Đã đạt"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__read
msgid "Read"
msgstr "Đọc"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Read: collaborators can view tasks but cannot edit them."
msgstr ""
"Đọc: cộng tác viên có thể xem nhiệm vụ nhưng không thể chỉnh sửa chúng."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_collaborator_wizard__access_mode
msgid ""
"Read: collaborators can view tasks but cannot edit them.\n"
"Edit with limited access: collaborators can view and edit tasks they follow in the Kanban view.\n"
"Edit: collaborators can view and edit all tasks in the Kanban view. Additionally, they can choose which tasks they want to follow."
msgstr ""
"Đọc: cộng tác viên có thể xem nhiệm vụ nhưng không thể chỉnh sửa chúng.\n"
"Chỉnh sửa với quyền truy cập hạn chế: cộng tác viên có thể xem và chỉnh sửa các nhiệm vụ mà họ theo dõi trong chế độ xem Kanban.\n"
"Chỉnh sửa: cộng tác viên có thể xem và chỉnh sửa tất cả nhiệm vụ trong chế độ xem Kanban. Ngoài ra, họ có thể chọn nhiệm vụ mà họ muốn theo dõi."

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "Tiếp nhận {{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "Người nhận"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID luồng bản ghi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Recording"
msgstr "Ghi nhận"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "Chu kỳ lặp"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "Lặp lại"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
msgid "Recurrent tasks"
msgstr "Nhiệm vụ định kỳ"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurring Tasks"
msgstr "Nhiệm vụ định kỳ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Refused"
msgstr "Bị từ chối"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "Tài liệu liên quan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "ID tài liệu liên quan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "Mô hình tài liệu liên quan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid "Remove Collaborator"
msgstr "Gỡ cộng tác viên"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "Rename"
msgstr "Đổi tên"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "Cải tạo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "Lặp lại mỗi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
msgid "Repeat Unit"
msgstr "Đơn vị tính lặp lại"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "Báo cáo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research"
msgstr "Nghiên cứu"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "Nghiên cứu & Phát triển"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research Project"
msgstr "Nghiên cứu dự án"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Researching"
msgstr "Đang nghiên cứu"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Resources Allocation"
msgstr "Phân bổ tài nguyên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Restore"
msgstr "Khôi phục"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"Restoring will replace the current content with the selected version. Any "
"unsaved changes will be lost."
msgstr ""
"Việc khôi phục sẽ thay thế nội dung hiện tại bằng phiên bản đã chọn. Mọi "
"thay đổi chưa được lưu sẽ bị mất."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Revenues"
msgstr "Doanh thu"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "SAVE"
msgstr "LƯU"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "Mặt buồn"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to drag images in description"
msgstr "Lưu nhiệm vụ để có thể kéo hình ảnh vào phần mô tả"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to paste images in description"
msgstr "Lưu nhiệm vụ để có thể dán hình ảnh vào phần mô tả"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Schedule your activity once it is ready."
msgstr "Lên kế hoạch hoạt động khi nó đã sẵn sàng."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Script"
msgstr "Kịch bản"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "Tìm kiếm dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "Tìm kiếm cập nhật"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Assignees"
msgstr "Tìm kiếm trong người được phân công"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Customer"
msgstr "Tìm trong khách hàng"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Milestone"
msgstr "Tìm kiếm trong Mốc thời gian"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Priority"
msgstr "Tìm kiếm trong Mức độ ưu tiên"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Project"
msgstr "Tìm trong dự án"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Stages"
msgstr "Tìm trong trạng thái"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Status"
msgstr "Tìm kiếm trong trạng thái"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search%(left)s Tasks%(right)s"
msgstr "Tìm kiếm%(left)s Nhiệm vụ%(right)s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "Token bảo mật"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Select an assignee from the menu"
msgstr "Chọn người được phân công từ menu"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Send"
msgstr "Gửi"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "Gửi email"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__send_invitation
msgid "Send Invitation"
msgstr "Gửi lời mời "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "Đặt ảnh bìa"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
msgid "Set Status"
msgstr "Thiết lập trạng thái"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "Cài Mẫu email xếp hạng trên các giai đoạn"

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr ""
"Thiết lập trên giai đoạn dự án để thông báo cho khách hàng khi dự án đạt đến"
" giai đoạn đó"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_priority_switch_field/project_task_priority_switch_field.js:0
msgid "Set priority as %s"
msgstr "Đặt mức độ ưu tiên là %s"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Set state as..."
msgstr "Đặt trạng thái là..."

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr ""
"Đặt mẫu này ở giai đoạn dự án để mời khách hàng của bạn gửi phản hồi. Kích "
"hoạt tính năng \"đánh giá của khách hàng\" trên dự án"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "Cài đặt"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Share Project"
msgstr "Chia sẻ dự án"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share Task"
msgstr "Chia sẻ nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__show_display_in_project
msgid "Show Display In Project"
msgstr "Hiện Hiển thị trong dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "Hiển thị tất cả bản ghi có ngày xử lý tiếp theo trước ngày hôm nay"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Show closed tasks"
msgstr "Hiện nhiệm vụ đã đóng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "Kể từ"

#. module: project
#: model:project.tags,name:project.project_tags_12
msgid "Social"
msgstr "Mạng xã hội"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Software Development"
msgstr "Phát triển phần mềm"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid ""
"Some of the selected analytic accounts are associated with a project:\n"
"%(accountList)s\n"
"\n"
"Archiving these accounts will remove the option to log timesheets for their respective projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""
"Một số tài khoản phân tích đã chọn đang liên kết với một dự án:\n"
"%(accountList)s\n"
"\n"
"Việc lưu trữ những tài khoản này sẽ loại bỏ tùy chọn ghi nhận bảng chấm công cho các dự án tương ứng của chúng.\n"
"\n"
"Bạn có chắc muốn tiếp tục không?"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Sorry. You can't set a task as its parent task."
msgstr ""
"Rất lấy làm tiếc. Bạn không thể đặt một nhiệm vụ làm nhiệm vụ chính của nó."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Sort your tasks by sprint using milestones, tags, or a dedicated property. "
"At the end of each sprint, just pick the remaining tasks in your list and "
"move them all at once to the next sprint by editing the milestone, tag, or "
"property."
msgstr ""
"Sắp xếp nhiệm vụ của bạn bằng đợt chạy nước rút thông qua việc sử dụng mốc "
"thời gian, thẻ hoặc thuộc tính riêng. Vào cuối mỗi đợt chạy nước rút, chỉ "
"cần chọn các nhiệm vụ còn lại trong danh sách của bạn và đồng thời chuyển "
"tất cả chúng sang đợt chạy nước rút tiếp theo bằng cách chỉnh sửa mốc thời "
"gian, thẻ hoặc thuộc tính."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Specifications"
msgstr "Thông số kỹ thuật"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Backlog"
msgstr "Sprint Backlog"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Complete"
msgstr "Sprint Hoàn tất"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint in Progress"
msgstr "Sprint đang thực hiện"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Stage"
msgstr "Giai đoạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Stage (Burndown Chart)"
msgstr "Giai đoạn (Biểu đồ burn-down)"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "Giai đoạn đã thay đổi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "Người sở hữu giai đoạn"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "Giai đoạn đã thay đổi"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Stage: %s"
msgstr "Giai đoạn: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Starred Tasks"
msgstr "Nhiệm vụ được gắn sao"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__state
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "State"
msgstr "Trạng thái"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_stage_state_selection/project_task_stage_with_state_selection.js:0
msgid "State readonly"
msgstr "Trạng thái chỉ đọc"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_status_with_color_selection/project_status_with_color_selection_field.xml:0
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Status"
msgstr "Trạng thái"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Status Update - %(date)s"
msgstr "Cập nhật trạng thái - %(date)s"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Hạn chót hạn đã qua\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Cần thực hiện trong tương lai."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__duration_tracking
#: model:ir.model.fields,field_description:project.field_project_task__duration_tracking
msgid "Status time"
msgstr "Thời gian của trạng thái"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "Nhiệm vụ phụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "Đã gửi vào"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_allocated_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""
"Tổng số giờ được phân bổ cho tất cả nhiệm vụ phụ (và nhiệm vụ phụ của chính "
"chúng) liên kết với nhiệm vụ này. Thường nhỏ hơn hoặc bằng số giờ được phân "
"bổ cho nhiệm vụ này."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "Tóm tắt"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "T-shirt Printing"
msgstr "In áo thun"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tags"
msgstr "Thẻ"

#. module: project
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task"
msgstr "Nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "Các hoạt động nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_approved
#: model:mail.message.subtype,name:project.mt_task_approved
msgid "Task Approved"
msgstr "Nhiệm vụ được phê duyệt"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_canceled
msgid "Task Canceled"
msgstr "Nhiệm vụ bị huỷ"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_canceled
msgid "Task Cancelled"
msgstr "Nhiệm vụ đã bị huỷ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Task Converted from To-Do"
msgstr "Nhiệm vụ được chuyển đổi từ Việc cần làm"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "Nhiệm vụ đã được tạo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "Tính phụ thuộc nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_done
#: model:mail.message.subtype,name:project.mt_task_done
msgid "Task Done"
msgstr "Nhiệm vụ hoàn tất"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_in_progress
#: model:mail.message.subtype,name:project.mt_project_task_in_progress
#: model:mail.message.subtype,name:project.mt_task_in_progress
msgid "Task In Progress"
msgstr "Nhiệm vụ đang thực hiện"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "Nhật kí công việc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "Thuộc tính nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "Đánh giá nhiệm vụ"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Chu kỳ lặp nhiệm vụ"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "Giai đoạn của nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "Giai đoạn của nhiệm vụ được thay đổi"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "Giai đoạn nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "Tiêu đề nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "Tiêu đề nhiệm vụ..."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Task Transferred from Project %(source_project)s to %(destination_project)s"
msgstr ""
"Nhiệm vụ được chuyển từ Dự án %(source_project)s sang "
"%(destination_project)s"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_waiting
#: model:mail.message.subtype,name:project.mt_project_task_waiting
#: model:mail.message.subtype,name:project.mt_task_waiting
msgid "Task Waiting"
msgstr "Nhiệm vụ đang chờ"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_approved
msgid "Task approved"
msgstr "Nhiệm vụ được phê duyệt"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_canceled
msgid "Task cancelled"
msgstr "Nhiệm vụ đã bị huỷ"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_done
msgid "Task done"
msgstr "Nhiệm vụ hoàn tất"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "Nhiệm vụ:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task: Rating Request"
msgstr "Nhiệm vụ: Yêu cầu đánh giá"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.embedded.actions,name:project.project_embedded_action_all_tasks_dashboard
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.ui.menu,name:project.menu_project_management
#: model:project.project,label_tasks:project.project_home_construction
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model:project.project,label_tasks:project.project_project_4
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tasks"
msgstr "Nhiệm vụ"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "Phân tích nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "Quản lý nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "Giai đoạn nhiệm vụ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
msgid "Tasks in Recurrence"
msgstr "Nhiệm vụ lặp lại"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Tests"
msgstr "Tests"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid "The Burndown Chart must be grouped by Date"
msgstr "Biểu đồ burn-down phải được nhóm theo Ngày"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "Giai đoạn cá nhân của người dùng hiện tại."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "Giai đoạn nhiệm vụ cá nhân của người dùng hiện tại."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr ""
"Tài liệu không tồn tại hoặc bạn không có quyền truy cập vào tài liệu đó."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The end date should be in the future"
msgstr "Ngày kết thúc phải nằm trong tương lai"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "Mốc thời gian sau đã được thêm:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "Các mốc thời gian sau đã được thêm:"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The interval should be greater than 0"
msgstr "Khoảng cách ngày phải lớn hơn 0"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Mô hình (Loại tài liệu Odoo) mà bí danh này tương tác. Mọi email đến mà "
"không phải là email trả lời một bản ghi hiện có sẽ tạo ra một bản ghi mới "
"trong mô hình này. (ví dụ: Nhiệm vụ dự án) "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Tên của bí danh email, ví dụ: 'jobs' nếu bạn muốn nhận email gửi đến địa chỉ"
" <<EMAIL>>"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project and the associated partner must be linked to the same company."
msgstr "Dự án và đối tác liên quan phải được liên kết với cùng một công ty."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project's company cannot be changed if its analytic account has analytic"
" lines or if more than one project is linked to it."
msgstr ""
"Không thể thay đổi công ty của dự án nếu tài khoản phân tích của công ty này"
" có các dòng phân tích hoặc nếu có nhiều dự án được liên kết với công ty."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr "Ngày bắt đầu dự án phải trước ngày kết thúc."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid ""
"The report should be grouped either by \"Stage\" to represent a Burndown "
"Chart or by \"Is Closed\" to represent a Burn-up chart. Without one of these"
" groupings applied, the report will not provide relevant information."
msgstr ""
"Báo cáo phải được nhóm theo \"Giai đoạn\" để trình bày Biểu đồ burn-down "
"hoặc theo \"Đã đóng\" để trình bày biểu đồ burn-up. Nếu không áp dụng một "
"trong các cách nhóm này, báo cáo sẽ không cung cấp thông tin liên quan."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "The search does not support operator %(operator)s or value %(value)s."
msgstr "Tìm kiếm không hỗ trợ toán tử %(operator)s hoặc giá trị %(value)s."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task and the associated partner must be linked to the same company."
msgstr ""
"Nhiệm vụ và đối tác liên quan phải được liên kết với cùng một công ty."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to "
"'%(visibility)s' in order to make it accessible by the recipient(s)."
msgstr ""
"Không thể chia sẻ nhiệm vụ với (các) người nhận vì quyền riêng tư của dự án "
"quá hạn chế. Đặt quyền riêng tư của dự án thành '%(visibility)s' để người "
"nhận có thể truy cập được."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "The task description was empty at the time."
msgstr "Mô tả nhiệm vụ trống vào thời điểm đó."

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
msgid ""
"The view must be grouped by date and by Stage - Burndown chart or Is Closed "
"- Burnup chart"
msgstr ""
"Chế độ xem phải được nhóm theo ngày và theo Giai đoạn - Biểu đồ burn-down "
"hoặc Đã đóng - Biểu đồ burn-up"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "There are no comments for now."
msgstr "Hiện tại không có cảm nhận."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "Không có dự án"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "Không có xếp hạng nào cho dự án này vào lúc này"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "Không có tác vụ"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "There is nothing to report."
msgstr "Không có gì để báo cáo. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"They can edit shared project tasks and view specific documents in read mode "
"on your website. This includes leads/opportunities, quotations/sales orders,"
" purchase orders, invoices and bills, timesheets, and tickets."
msgstr ""
"Họ có thể chỉnh sửa các nhiệm vụ dự án được chia sẻ và xem các tài liệu cụ "
"thể ở chế độ đọc trên trang web của bạn. Những tài liệu này bao gồm lead/cơ "
"hội, báo giá/đơn bán hàng, đơn mua hàng, hóa đơn và hóa đơn thanh toán, bảng"
" chấm công và phiếu hỗ trợ."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
msgid "This Month"
msgstr "Tháng này"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "This Week"
msgstr "Tuần này"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid ""
"This analytic account is associated with the following projects:\n"
"%(projectList)s\n"
"\n"
"Archiving the account will remove the option to log timesheets for these projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""
"Tài khoản phân tích này đang liên kết với các dự án sau:\n"
"%(projectList)s\n"
"\n"
"Việc lưu trữ tài khoản này sẽ loại bỏ tùy chọn ghi nhận bảng chấm công cho các dự án này.\n"
"\n"
"Bạn có chắc muốn tiếp tục không?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid ""
"This follower is currently a project collaborator. Removing them will revoke"
" their portal access to the project. Are you sure you want to proceed?"
msgstr ""
"Người theo dõi này hiện đang là cộng tác viên của dự án. Việc gỡ họ sẽ thu "
"hồi quyền truy cập cổng thông tin của họ vào dự án. Bạn có chắc chắn muốn "
"tiếp tục không?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid ""
"This is a preview of how the project will look when it's shared with "
"customers and they have editing access."
msgstr ""
"Đây là bản xem trước cho thấy dự án sẽ trông như thế nào khi được chia sẻ "
"với khách hàng và họ có quyền chỉnh sửa."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is associated with %(project_company)s, whereas the selected "
"stage belongs to %(stage_company)s. There are a couple of options to "
"consider: either remove the company designation from the project or from the"
" stage. Alternatively, you can update the company information for these "
"records to align them under the same company."
msgstr ""
"Dự án này được liên kết với %(project_company)s, trong khi giai đoạn đã chọn"
" thuộc về %(stage_company)s. Bạn có thể cân nhắc một số lựa chọn sau: xóa "
"công ty khỏi dự án hoặc giai đoạn. Ngoài ra, bạn có thể cập nhật thông tin "
"công ty cho các bản ghi này để chúng thuộc về cùng một công ty."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is currently restricted to \"Invited internal users\". The "
"project's visibility will be changed to \"invited portal users and all "
"internal users (public)\" in order to make it accessible to the recipients."
msgstr ""
"Dự án này hiện chỉ giới hạn cho \"Người dùng nội bộ được mời\". Chế độ hiển "
"thị của dự án sẽ được thay đổi thành \"người dùng cổng thông tin được mời và"
" tất cả người dùng nội bộ (công khai)\" để người nhận có thể truy cập được."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is not associated with any company, while the stage is "
"associated with %s. There are a couple of options to consider: either change"
" the project's company to align with the stage's company or remove the "
"company designation from the stage"
msgstr ""
"Dự án này không liên kết với bất kỳ công ty nào, trong khi giai đoạn này "
"liên kết với %s. Bạn có thể cân nhắc một số lựa chọn sau: thay đổi công ty "
"của dự án cho phù hợp với công ty của giai đoạn hoặc xóa công ty khỏi giai "
"đoạn"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "This task has sub-tasks, so it can't be private."
msgstr ""
"Nhiệm vụ này có nhiều nhiệm vụ phụ nên không thể là một nhiệm vụ riêng tư."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "This task is blocked by another unfinished task"
msgstr "Nhiệm vụ này bị chặn bởi một nhiệm vụ khác chưa hoàn thành"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "This task is currently blocked by"
msgstr "Nhiệm vụ này hiện bị chặn bởi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"Điều này sẽ lưu trữ các giai đoạn và tất cả các nhiệm vụ mà chúng chứa từ "
"các dự án sau:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Loại hành động đại diện cho những danh mục việc bạn cần làm khác nhau (ví "
"dụ: \"Gọi\" hoặc \"Gửi email\"). "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "Quản lý thời gian"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "Mẹo: Tạo nhiệm vụ từ email đến"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_3
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid "Tip: Project-Specific Fields"
msgstr "Mẹo: Các trường cụ thể của dự án"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Use task states to keep track of your tasks' progression"
msgstr "Mẹo: Sử dụng trạng thái nhiệm vụ để theo dõi tiến độ nhiệm vụ của bạn"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_2
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid "Tip: Your Own Personal Kanban"
msgstr "Mẹo: Kanban cá nhân của bạn"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
msgid "Title"
msgstr "Tiêu đề"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Bill"
msgstr "Cần tính phí"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "Việc cần làm"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Invoice"
msgstr "Cần xuất hoá đơn"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "To Print"
msgstr "In"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"Để hoàn thành công việc, hãy sử dụng các hoạt động và trạng thái trên nhiệm vụ.<br>\n"
"                Trò chuyện theo thời gian thực hoặc qua email để cộng tác hiệu quả."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid ""
"To transform a task into a sub-task, select a parent task. Alternatively, "
"leave the parent task field blank to convert a sub-task into a standalone "
"task."
msgstr ""
"Để chuyển một nhiệm vụ thành một nhiệm vụ phụ, hãy chọn một nhiệm vụ chính. "
"Ngược lại, hãy để trống trường nhiệm vụ chính để chuyển đổi một nhiệm vụ phụ"
" thành nhiệm vụ độc lập."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Today"
msgstr "Hôm nay"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total"
msgstr "Tổng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Costs"
msgstr "Tổng phí"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Revenues"
msgstr "Tổng doanh thu"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "Theo dõi mức độ hài lòng của khách hàng đối với các nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr ""
"Theo dõi các điểm tiến độ quan trọng cần đạt được để đi đến thành công"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Track major progress points that must be reached to achieve success."
msgstr ""
"Theo dõi các điểm tiến độ quan trọng cần phải đạt được để thực hiện dự án "
"thành công."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid ""
"Track project costs, revenues, and margin by setting the analytic account "
"associated with the project on relevant documents."
msgstr ""
"Theo dõi chi phí, doanh thu và lợi nhuận của dự án bằng cách thiết lập tài "
"khoản phân tích được liên kết với dự án trên các tài liệu liên quan."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "Theo dõi tiến độ của dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "Theo dõi thời gian dành cho các dự án và nhiệm vụ"

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr ""
"Thẻ trong suốt không hiển thị trong chế độ xem kanban của dự án và nhiệm vụ "
"của bạn."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "Hai lần một tháng"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Two tasks cannot depend on each other."
msgstr "Hai nhiệm vụ không thể phụ thuộc vào nhau."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trong bản ghi."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid "Unarchive Projects"
msgstr "Bỏ lưu trữ dự án"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid "Unarchive Tasks"
msgstr "Bỏ lưu trữ nhiệm vụ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Unassigned"
msgstr "Chưa phân công"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Unfollow"
msgstr "Ngừng theo dõi"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Unknown Analytic Account"
msgstr "Tài khoản phân tích không xác định"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "Tin nhắn chưa đọc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
msgid "Until"
msgstr "Cho đến"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "Đã tạo cập nhật"

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr "Khả năng sử dụng"

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr "Sử dụng mốc thời gian"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "Sử dụng đánh giá cho dự án"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "Sử dụng nhiệm vụ định kỳ"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "Dùng giai đoạn cho dự án"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "Sử dụng tính phụ thuộc của nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "Sử dụng nhiệm vụ như là"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Use This For My Project"
msgstr "Sử dụng cái này cho dự án của tôi"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid ""
"Use personal stages to organize your tasks and create your own workflow."
msgstr ""
"Sử dụng các giai đoạn cá nhân để sắp xếp các nhiệm vụ và tạo quy trình làm "
"việc của riêng bạn."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "Dùng thẻ để phân loại nhiệm vụ."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Use the"
msgstr "Sử dụng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"main changes about this task."
msgstr ""
"Dùng cửa sổ chat để <b>gửi email</b> và giao tiếp hiệu quả với khách hàng. "
"Thêm người mới vào danh sách người theo dõi để họ biết được những thay đổi "
"chính của nhiệm vụ này."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""
"Sử dụng các từ khóa này trong tiêu đề để thiết lập nhiệm vụ mới:\n"
"\n"
"         30h Phân bổ 30 giờ cho nhiệm vụ\n"
"         #thẻ Tạo thẻ cho nhiệm vụ\n"
"         @người_dùng Gán nhiệm vụ cho một người dùng\n"
"         ! Đặt nhiệm vụ ở mức độ ưu tiên cao\n"
"\n"
"Hãy đảm bảo sử dụng đúng định dạng và thứ tự. VD: Cải thiện màn hình cấu hình 5h #tính_năng #v16 @Mitchell !"

#. module: project
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "Người dùng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "Chế độ xem"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "View Task"
msgstr "Xem nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "View Tasks"
msgstr "Xem nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "Chế độ hiển thị"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Visible"
msgstr "Hiện"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__04_waiting_normal
msgid "Waiting"
msgstr "Đang chờ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""
"Muốn có cách thức tốt hơn để <b>quản lý các dự án</b>? <i>Bắt đầu từ "
"đây.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Website Redesign"
msgstr "Thiết kế lại trang web"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "Hàng tuần"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "Tuần"

#. module: project
#: model:project.tags,name:project.project_tags_14
msgid "Work"
msgstr "Công việc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "Ngày làm việc để phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "Ngày làm việc để đóng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "Giờ làm việc để phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "Giờ làm việc để đóng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "Thời gian để phân công"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "Thời gian làm việc để Đóng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the projects contained in these stages as"
" well?"
msgstr ""
"Bạn có muốn hủy lưu trữ tất cả dự án có trong các giai đoạn này không?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr ""
"Bạn có muốn hủy lưu trữ tất cả nhiệm vụ có trong các giai đoạn này không?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Write a message..."
msgstr "Viết một thông điệp..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Writing"
msgstr "Đang viết"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "Hàng năm"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "Năm"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid ""
"You are not able to switch the company of this stage to %(company_name)s "
"since it currently includes projects associated with "
"%(project_company_name)s. Please ensure that this stage exclusively consists"
" of projects linked to %(company_name)s."
msgstr ""
"Bạn không thể chuyển công ty trong giai đoạn này thành %(company_name)s vì "
"nó hiện bao gồm các dự án được liên kết với %(project_company_name)s. Vui "
"lòng đảm bảo rằng giai đoạn này chỉ bao gồm các dự án được liên kết với "
"%(company_name)s."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can change the sub-task state here!"
msgstr "Bạn có thể thay đổi trạng thái nhiệm vụ phụ tại đây!"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You can only set a personal stage on a private task."
msgstr "Bạn chỉ có thể thiết lập giai đoạn cá nhân cho một nhiệm vụ riêng tư."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can open sub-tasks from the kanban card!"
msgstr "Bạn có thể mở các nhiệm vụ phụ từ thẻ kanban!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You can either archive them or"
" first delete all of their projects."
msgstr ""
"Bạn không thể xóa các giai đoạn chứa dự án. Bạn có thể lưu trữ chúng hoặc "
"trước tiên xóa tất cả dự án của chúng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You should first delete all of"
" their projects."
msgstr ""
"Bạn không thể xóa các giai đoạn chứa dự án. Tước tiên, bạn phải xóa tất cả "
"dự án của chúng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"Bạn không thể xóa các giai đoạn chứa nhiệm vụ. Bạn có thể lưu trữ chúng hoặc"
" trước tiên xóa tất cả các tác vụ của chúng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""
"Bạn không thể xóa các giai đoạn chứa nhiệm vụ. Trước tiên, bạn nên xóa tất "
"cả các nhiệm vụ của họ."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot read the following fields on tasks: %(field_list)s"
msgstr "Bạn không thể đọc các trường sau trong nhiệm vụ: %(field_list)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot write on the following fields on tasks: %(field_list)s"
msgstr "Bạn không thể điền vào các trường sau trong nhiệm vụ: %(field_list)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been assigned to %s"
msgstr "Bạn đã được phân công cho %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "Bạn đã được phân công cho"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been invited to follow %s"
msgstr "Bạn đã được mời theo dõi %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "You have been invited to follow Task Document :"
msgstr "Bạn đã được mời theo dõi Tài liệu nhiệm vụ:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"You have full control and can revoke portal access anytime. Are you ready to"
" proceed?"
msgstr ""
"Bạn có toàn quyền kiểm soát và có thể thu hồi quyền truy cập cổng thông tin "
"bất cứ lúc nào. Bạn đã sẵn sàng để tiếp tục chưa?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"You have unsaved changes - no worries! Odoo will automatically save it as "
"you navigate.<br/> You can discard these changes from here or manually save "
"your task.<br/>Let's save it manually."
msgstr ""
"Bạn có những thay đổi chưa được lưu, nhưng đừng lo lắng! Odoo sẽ tự động lưu"
" chúng khi bạn điều hướng.<br/> Bạn có thể bỏ qua những thay đổi này từ đây "
"hoặc lưu nhiệm vụ của mình theo cách thủ công.<br/> Hãy lưu thủ công."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "You must be"
msgstr "Bạn phải"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Your managers decide which feedback is accepted"
msgstr "Quản lý của bạn quyết định phản hồi nào được chấp nhận"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "alias"
msgstr "bí danh"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "and"
msgstr "và"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""
"và phản hồi nào được\n"
"      chuyển sang cột \"Bị từ chối\"."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr "người được phân công"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "avatar"
msgstr "ảnh đại diện"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "button."
msgstr "nút."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "comments"
msgstr "bình luận"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr "VD: Nhận xét hàng tháng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "e.g. Office Party"
msgstr "vd: Tiệc văn phòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "VD: Ra mắt sản phẩm"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr "VD: Gửi lời mời"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "e.g. Tasks"
msgstr "VD: Nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_tree
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "e.g. To Do"
msgstr "VD: Việc cần làm"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. mycompany.com"
msgstr "VD: mycompany.com"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "vd: Tiệc văn phòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr "VD: Ra mắt sản phẩm"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon to organize your daily activities."
msgstr "biểu tượng để sắp xếp hoạt động hàng ngày của bạn."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"icon to see tasks waiting on other ones. Once a task is marked as complete "
"or cancelled, all of its dependencies will be unblocked."
msgstr ""
"biểu tượng để xem các nhiệm vụ đang chờ những nhiệm vụ khác. Khi một nhiệm "
"vụ được đánh dấu là hoàn thành hoặc bị hủy, tất cả thành phần phụ thuộc của "
"nhiệm vụ đó sẽ được bỏ chặn."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon."
msgstr "biểu tượng."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "logged in"
msgstr "đã đăng nhập"

#. module: project
#: model:ir.actions.server,name:project.action_server_view_my_task
msgid "menu view My Tasks"
msgstr "chế độ xem menu Nhiệm vụ của tôi"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "on a periodic basis"
msgstr "định kỳ"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "dự án."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr "sẵn sàng được đánh dấu là đã đạt được"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to indicate a request for changes or a need for discussion on a task."
msgstr ""
"trạng thái thể hiện yêu cầu thay đổi hoặc nhu cầu thảo luận về một nhiệm vụ."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to inform your colleagues that a task is approved for the next stage."
msgstr ""
"trạng thái để thông báo cho đồng nghiệp của bạn rằng một nhiệm vụ đã được "
"phê duyệt để tiếp tục giai đoạn tiếp theo."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as cancelled."
msgstr "trạng thái để đánh dấu nhiệm vụ là đã bị hủy."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as complete."
msgstr "trạng thái để đánh dấu nhiệm vụ là hoàn thành."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_name_with_subtask_count_char_field/project_task_name_with_subtask_count_char_field.xml:0
msgid "sub-tasks)"
msgstr "nhiệm vụ phụ)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "task"
msgstr "nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "thời hạn của mốc thời gian sau đã được cập nhật:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "thời hạn của các mốc thời gian sau đã được cập nhật:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""
"để xác định xem dự án đã\n"
"      sẵn sàng cho bước tiếp theo hay chưa."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "to post a comment."
msgstr "đăng một bình luận."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "to signalize what is the current status of your Idea."
msgstr "để báo hiệu trạng thái hiện tại của Ý tưởng của bạn là gì."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "when reaching a given stage"
msgstr "khi đạt đến một giai đoạn nhất định"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr "sẽ tạo ra các nhiệm vụ trong"

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid ""
"{{ object.project_id.company_id.name or user.env.company.name }}: "
"Satisfaction Survey"
msgstr ""
"{{ object.project_id.company_id.name or user.env.company.name }}: Khảo sát "
"mức độ hài lòng"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "👤 Unassigned"
msgstr "👤 Chưa được phân công"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_graph/project_task_graph_model.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "🔒 Private"
msgstr "🔒 Riêng tư"
