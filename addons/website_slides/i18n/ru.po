# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-30 18:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_engaged_count
msgid "# Active Attendees"
msgstr "# Активные участники"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Attendees"
msgstr "# Присутствующие"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Completed"
msgstr "и был завершен"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_completed_count
msgid "# Completed Attendees"
msgstr "# Завершенные участники"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Contents"
msgstr "# Завершенное содержание"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "# Enrolled Attendees"
msgstr "# Зачисленные слушатели"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_all_count
msgid "# Enrolled or Invited Attendees"
msgstr "# Записавшиеся или приглашенные участники"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_invited_count
msgid "# Invited Attendees"
msgstr "# Приглашенные участники"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Likes"
msgstr "# Лайк"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Questions"
msgstr "# Вопросы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Quizz Attempts"
msgstr "# Попытки викторины"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Total Attempts"
msgstr "# Всего попыток"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
msgid "# Total Views"
msgstr "# Всего просмотров"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Views"
msgstr "# Просмотров"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_count
msgid "# of Embeds"
msgstr "# из Встраиваемых"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "# публичные просмотры"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "# Веб-сайт просмотров"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Contents"
msgstr "% Выполнено Содержание"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr "'. Отображение результатов для '"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
msgid "(empty)"
msgstr "(пусто)"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ". This way, they will be secured."
msgstr ". Таким образом, они будут надежно защищены."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 Основные методологии"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "<b>%s</b> is requesting access to this course."
msgstr "<b>%s</b> запрашивает доступ к этому курсу."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(пусто)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>Заказать по</b>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr ""
"<b>Сохраните и опубликуйте</b> свой урок, чтобы сделать его доступным для "
"слушателей."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "<b>Save</b> your question."
msgstr "<b>Сохраните</b> свой вопрос."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>Без категории</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_enroll
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been enrolled to a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been invited to check out this course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <strong t-out=\"object.name or ''\">document</strong> with you!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px;                                 text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View <strong t-out=\"object.name or ''\">Document</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_category or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br/><br/>\n"
"                        </p><center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right\"/> Start Learning"
msgstr "<i class=\"fa fa-arrow-right\"/> Начните учиться"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> Статистика"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Уроки</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<i class=\"fa fa-check me-1\"/>Completed"
msgstr "<i class=\"fa fa-check me-1\"/>Завершено"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "<i class=\"fa fa-check\"/> Completed"
msgstr "<i class=\"fa fa-check\"/> Завершено"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>Загрузка...</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<i class=\"fa fa-clipboard\"/> Copy Embed Code"
msgstr "<i class=\"fa fa-clipboard\"/> Скопируйте код встраивания"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<i class=\"fa fa-clipboard\"/> Copy Link"
msgstr "<i class=\"fa fa-clipboard\"/> Копировать ссылку"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload me-1\"/>Add Content"
msgstr "<i class=\"fa fa-cloud-upload me-1\"/>Добавить содержание"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr "<i class=\"fa fa-comments\"/> Комментарии ("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Полный экран</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr "<i class=\"fa fa-envelope\"/> Отправить электронное письмо"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser me-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser me-1\"/>Очистить фильтры"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/> Очистить фильтры"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> Этот приватный - документ."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye me-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o me-2\" aria-label=\"article\" role=\"img\" "
"title=\"Article\"/>"
msgstr ""
"<i class=\"fa fa-file-code-o me-2\" aria-label=\"article\" role=\"img\" "
"title=\"Article\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o me-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""
"<i class=\"fa fa-file-image-o me-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""
"<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o me-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""
"<i class=\"fa fa-file-video-o me-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag me-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr "<i class=\"fa fa-flag me-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/> Викторина"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning\"/>Викторина"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o me-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o me-1\"/><span>Добавить раздел</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o me-1\"/>Add a section"
msgstr "<i class=\"fa fa-folder-o me-1\"/>Добавить раздел"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Вебсайт\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap me-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap me-1\"/>Все курсы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> Ознакомится"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/> Курс"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ms-1\">Back "
"to course</span>"
msgstr ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Вернуться к курсу</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>Course Locked</span>"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>Курс заблокирован</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-"
"block\">Добавить содержание</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus me-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus me-1\"/><span>Добавить содержание</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Добавить вопрос</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Добавить викторину</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question me-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""
"<i class=\"fa fa-question me-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Поделиться</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>\n"
"                                            Share"
msgstr ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>\n"
"                                            Поделиться"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">Поделиться</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> Поделится"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Выход из полноэкранного режима</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      Мои курсы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Courses"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Все курсы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<small class=\"text-success\">\n"
"                        Request already sent\n"
"                    </small>"
msgstr ""
"<small class=\"text-success\">\n"
"                        Запрос уже отправлен\n"
"                    </small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge text-bg-success fw-normal\"><i class=\"fa fa-"
"check\"/> Completed</span></small>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge fw-bold m-1 text-bg-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge text-bg-info badge-arrow-right fw-normal "
"m-1\">New</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-primary badge-hide fw-normal m-1\">Add "
"Quiz</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-success fw-normal "
"m-1\"><span>Preview</span></span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"badge text-bg-success fw-normal m-1\">Preview</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge text-bg-success pull-right\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"oi oi-"
"chevron-right ms-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">Следующий</span> <i class=\"oi oi-"
"chevron-right ms-2\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"fw-bold text-muted me-2\">Current rank:</span>"
msgstr "<span class=\"fw-bold text-muted me-2\">Текущее место:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"fw-normal\">Last update:</span>"
msgstr "<span class=\"fw-normal\">Последнее обновление:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<span class=\"input-group-text\">Start at Page</span>"
msgstr "<span class=\"input-group-text\">Начните со страницы</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<span class=\"ms-1\">Lessons</span>\n"
"                                <span class=\"ms-1\">·</span>"
msgstr ""
"<span class=\"ms-1\">Уроки</span>\n"
"                                <span class=\"ms-1\">-</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Слайды</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span class=\"o_stat_text\">Attendees</span>"
msgstr "<span class=\"o_stat_text\">Присутствующие</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">Курсы</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span class=\"o_stat_text\">Embed Views</span>"
msgstr "<span class=\"o_stat_text\">Встраивание просмотров</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr "<span class=\"o_wslides_arrow\">Новое содержание</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                Create a Google Project and Get a Key"
msgstr ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                Создайте проект Google и получите ключ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">Содержание курса</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr "<span class=\"text-500 mx-2\">-</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        Additional Resources\n"
"                    </span>"
msgstr ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        Дополнительные ресурсы\n"
"                    </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold col-4 col-md-3\">External sources</span>"
msgstr ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">Внешние источники</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold me-3\">Rating</span>"
msgstr "<span class=\"text-muted fw-bold me-3\">Рейтинг</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span class=\"text-muted text-truncate mw-100\" "
"title=\"Invited\">Invited</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span class=\"text-muted text-truncate mw-100\" "
"title=\"Ongoing\">Ongoing</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted text-truncate mw-100\" title=\"Total\">Total</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr ""
"<span class=\"text-muted\">Общие задачи для специалиста по "
"информатике</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr "<span class=\"text-muted\">Разделы информатики</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-inline ms-2 me-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span name=\"done_members_count_label\" class=\"text-muted text-truncate "
"mw-100\" title=\"Finished\">Finished</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"<span name=\"members_completed_count_label\" "
"class=\"o_stat_text\">Finished</span>"
msgstr ""
"<span name=\"members_completed_count_label\" "
"class=\"o_stat_text\">Готовые</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> часы</span>"

#. module: website_slides
#: model_terms:web_tour.tour,rainbow_man_message:website_slides.slides_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<b>Отличная работа!</b> Вы прошли все этапы этого тура."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr "<span>Добавить тег</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr "<span>Ответы на вопросы</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr "<span>Задать вопрос</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr "<span>Задайте правильный вопрос</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>Content only accessible to course attendees.</span>"
msgstr "<span>Содержание доступно только слушателям курса.</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr "<span>Логика</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr "<span>Математика</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr "<span>Предварительный просмотр</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr "<span>Наука</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>XP</span>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "<strong>Create a course</strong>"
msgstr "<strong>Создайте курс</strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
msgid "<strong>No Attendee Yet!</strong>"
msgstr "<strong>Пока нет ни одного участника!</strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
msgid "<strong>No Attendees Yet!</strong>"
msgstr "<strong>Посетителей пока нет!</strong>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<strong>Sharing is caring!</strong> Email(s) sent."
msgstr ""
"<strong>Делиться - значит заботиться!</strong> Отправлено по электронной "
"почте."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "Могучий лес из глубины веков"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "Фрукты"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"A good course has a structure. Pick a name for your first <b>Section</b>."
msgstr ""
"Хороший курс имеет структуру. Придумайте название для вашего первого "
"<b>Раздела</b>."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "Небольшая беседа с Гарри Горшком"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr ""
"Много красивой документации: деревья, древесина, сады. Золотая жила для "
"ссылок."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_channel_partner_uniq
msgid "A partner membership to a channel must be unique!"
msgstr "Партнерское членство в канале должно быть уникальным!"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_slide_partner_uniq
msgid "A partner membership to a slide must be unique!"
msgstr "Партнерское членство в слайде должно быть уникальным!"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_file_type
msgid "A resource of type file cannot contain a link."
msgstr "Ресурс типа файл не может содержать ссылку."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_url
msgid "A resource of type url must contain a link."
msgstr "Ресурс типа url должен содержать ссылку."

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "Лопата"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid "A slide is either filled with a url or HTML content. Not both."
msgstr "Слайд заполняется либо url, либо HTML-контентом. Не то и не другое."

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "Ложка"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "Краткое описание ноу-хау: как и что."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr ""
"Краткое изложение ноу-хау: как и что. Все основы для этого курса о "
"садоводстве."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr ""
"Краткое описание ноу-хау: каковы основные категории деревьев и как их "
"различать."

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "Стол"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "Тэг должен быть уникальным!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "Овощ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "Ключ API"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Access Granted"
msgstr "Доступ разрешен"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "Группы доступа"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Access Refused"
msgstr "Доступ отклонен"

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr "Запрос доступа"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr "Запрашиваемый доступ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "Уровни доступа"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "Accessed on"
msgstr "Доступно на"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Achievements"
msgstr "Достижения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "Активный"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__members_engaged_count
msgid "Active attendees include both 'joined' and 'ongoing' attendees."
msgstr ""
"Активные участники включают как \"присоединившихся\", так и \"продолжающих\""
" участников."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "Активность"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "Состояние активности"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "Значок типа активности"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Add"
msgstr "Добавить"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Attendees"
msgstr "Добавить участников"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "Добавить комментарий"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "Добавить контент"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "Добавить отзыв"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "Добавить секцию"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr "Добавить тэг"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
msgid "Add a section"
msgstr "Добавить раздел"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Add a tag"
msgstr "Добавить метку"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Add an answer below this one"
msgstr "Добавьте ответ ниже этого"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Add comment on this answer"
msgstr "Добавить комментарий к этому ответу"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add contacts..."
msgstr "Добавить контакты..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Add quiz"
msgstr "Добавить тест"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Add your content here..."
msgstr "Добавьте свой контент сюда..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Added On"
msgstr "Добавлен"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr "Дополнительные ресурсы для этого слайда"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr "Дополнительные ресурсы"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr "Дополнительный ресурс для конкретного слайда"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "Дополнительно"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_all_ids
msgid "All Attendees Information"
msgstr "Информация обо всех участниках"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "Все курсы"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "All progress will be lost until you rejoin this course."
msgstr ""
"Весь прогресс будет потерян до тех пор, пока вы не вернетесь на этот курс."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "All questions must be answered!"
msgstr "Все вопросы должны быть решены!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
msgid ""
"All questions must have at least one correct answer and one incorrect answer: \n"
"%s\n"
msgstr ""
"Все вопросы должны содержать как минимум один правильный и один неправильный ответ:\n"
"%s\n"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "All the courses you attend will appear here. <br/>"
msgstr "Здесь будут отображаться все курсы, которые вы посещаете. <br/>"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "Все, что вам нужно знать о создании мебели."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"Allow Attendees to like and comment your content and to submit reviews on "
"your course."
msgstr ""
"Позволяйте слушателям ставить лайки и оставлять комментарии к вашим "
"материалам, а также оставлять отзывы о вашем курсе."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr "Разрешить загрузку"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "Разрешить предварительный просмотр"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Allow Rating"
msgstr "Разрешить рейтинг"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Reviews"
msgstr "Разрешить отзывы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "Разрешить рейтинг на курсах"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr "Позволяет пользователю загрузить содержимое слайда."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "Разрешить коментарии"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Already Requested"
msgstr "Уже запрошено"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Already member"
msgstr "Уже участник"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.js:0
msgid "Amazing!"
msgstr "Потрясающе!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "А также бананы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "Ответ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Answers"
msgstr "Ответы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Появляется в"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Archive"
msgstr "Архив"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
msgid "Archive Content"
msgstr "Содержимое архива"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "Архивировано"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
msgid "Are you sure you want to archive this content?"
msgstr "Вы уверены, что хотите заархивировать это содержимое?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
msgid "Are you sure you want to delete this category?"
msgstr "Вы уверены, что хотите удалить эту категорию?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Are you sure you want to delete this question \"<strong>%s</strong>\"?"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__article
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__article
msgid "Article"
msgstr "Статья"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_article
msgid "Articles"
msgstr "Статьи"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "Вложения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "Попыток в среднем"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "Число Попыток"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_form
msgid "Attendee"
msgstr "Участник"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__member_status
msgid "Attendee Status"
msgstr "Статус участника"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.actions.act_window,name:website_slides.slide_slide_partner_action_from_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_pivot
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attendees"
msgstr "Участники"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Attendees of %s"
msgstr "Участники %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "Автоматическое зачисление групп"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
msgid "Average Rating"
msgstr "Средний рейтинг"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Average Review"
msgstr "Средний обзор"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "BUILDING BLOCKS DROPPED HERE WILL BE SHOWN ACROSS ALL LESSONS"
msgstr "БЛОКИ, ПРЕДСТАВЛЕННЫЕ ЗДЕСЬ, БУДУТ ДЕМОНСТРИРОВАТЬСЯ НА ВСЕХ УРОКАХ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
msgid "Back"
msgstr "Назад"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Back to course"
msgstr "Назад к курсу"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_default_background_image_url
msgid "Background image URL"
msgstr "URL фонового изображения"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "Значки"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "Базовый"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "Основы создания мебели"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "Основы садоводства"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Be notified when a new content is added."
msgstr "Получайте уведомления о добавлении нового контента."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Содержание тела соответствует шаблону"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "Можно комментировать"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Можно редактировать тело"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_completed
msgid "Can Mark Completed"
msgstr "Можно отметить завершенным"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "Can Mark Uncompleted"
msgstr "Можно отметить незавершенным"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "Можно опубликовать"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "Можно пересмотреть"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "Может загрузить"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "Можно голосовать"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Can not be marked as done"
msgstr "Не может быть отмечен как выполненный"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Can not be marked as not done"
msgstr "Не может быть отмечен как невыполненный"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Cancel"
msgstr "Отменить"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "Плотник"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "Броский заголовок"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "Категории"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_category
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_category
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "Категория"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "Сертификация"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "Сертификаты"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "Сертифицированные знания"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Change video privacy settings"
msgstr "Изменение настроек конфиденциальности видео"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
msgid "Channel"
msgstr "Канал"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "Каналы / Партнеры (участники)"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "Мастер приглашения каналов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_channel_template_id
msgid "Channel Share Template"
msgstr "Шаблон доли канала"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_shared
msgid "Channel Shared"
msgstr "Общий канал"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "Тип канала"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "Группы Каналов/Курсов"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "Метка Канала/Курса"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "Каналы"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "Шпаргалка"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "Check Profile"
msgstr "Проверить профиль"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Check answers"
msgstr "Проверьте ответы"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Check your answers"
msgstr "Проверьте свои ответы"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Choose a <b>File</b> on your computer."
msgstr "Выберите <b>Файл</b> на своем компьютере."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Choose a PDF"
msgstr "Выберите PDF"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Choose a layout"
msgstr "Выберите макет"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Choose an Image"
msgstr "Выберите изображение"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood!"
msgstr "Выбирайте дерево!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "Очистить фильтры"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click <b>Save</b> to create it."
msgstr "Нажмите <b>Сохранить</b>, чтобы создать."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to get started"
msgstr "Нажмите здесь чтобы начать"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "Нажмите здесь, чтобы начать курс"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr ""
"Нажмите на “Новый” в правом верхнем углу, чтобы написать свой первый курс."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"Click on the \"Edit\" button in the top corner of the screen to edit your "
"slide content."
msgstr ""
"Нажмите на кнопку \"Редактировать\" в верхнем углу экрана, чтобы "
"отредактировать содержимое слайда."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click on the <b>Save</b> button to create your first course."
msgstr "Нажмите на кнопку <b>Сохранить</b>, чтобы создать свой первый курс."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr "Нажмите на свой <b>курс</b>, чтобы вернуться к оглавлению."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Close"
msgstr "Закрыть"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "Цвет"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "Цветовой индекс"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "Цветной"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "Come back later to check the feedbacks given by your Attendees."
msgstr "Зайдите позже, чтобы проверить отзывы ваших слушателей."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "Come back later to oversee how well your Attendees are doing."
msgstr ""
"Вернитесь позже, чтобы проконтролировать, насколько хорошо работают ваши "
"участники."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "Комментарий"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Commenting is not enabled on this course."
msgstr "Комментирование на этом курсе не включено."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "Комментарии"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr ""
"Общие задачи для специалиста по информатике - задавать правильные вопросы и "
"отвечать на них. На этом курсе вы будете изучать эти темы с помощью "
"упражнений по математике, естественным наукам и логике."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions..."
msgstr ""
"Общая задача компьютерщика - задавать правильные вопросы и отвечать на "
"них..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "Связь"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "Герой сообщества"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "Количество курсов компании"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "Сравнение твердости пород древесины"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "Завершить курс"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr "Заполните свой профиль"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
msgid "Completed"
msgstr "Завершено"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Completed Course"
msgstr "Завершенные курсы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "Завершенные курсы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "Завершение"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Notification"
msgstr "Уведомление о завершении"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "Время прохождения"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "Написать письмо"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "Конфигурация"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulations! You completed {{ object.channel_id.name }}"
msgstr "Поздравляем! Вы завершили {{ object.channel_id.name }}"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr ""
"Поздравляем! Ваш первый урок доступен. Давайте посмотрим на варианты, "
"доступные здесь. Тег \"<b>Новый</b>\" указывает на то, что этот урок был "
"создан менее 7 дней назад."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "Поздравляю, вы достигли высшего звания!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr ""
"Поздравляем, вы создали свой первый курс.<br/>Нажмите на название этого "
"материала, чтобы увидеть его в полноэкранном режиме."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr ""
"Поздравляем, ваш курс создан, но в нем еще нет никакого содержимого. Для "
"начала давайте добавим <b>раздел</b>, чтобы придать курсу структуру."

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
msgid "Contact Responsible"
msgstr "Контактное лицо"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Contact the responsible to enroll."
msgstr "Свяжитесь с ответственным лицом, чтобы записаться."

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "Свяжитесь с нами"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "Содержание"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Content Preview"
msgstr "Предварительный просмотр содержимого"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "Вопрос о содержании опроса"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "Теги контента"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "Заголовок контента"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Type"
msgstr "Тип содержимого"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid ""
"Content are the lessons that compose a course\n"
"                    <br>and can be of different types (presentations, documents, videos, ...)."
msgstr ""
"Содержание - это уроки, из которых состоит курс\n"
"                   <br>и могут быть разных типов (презентации, документы, видео, ...)."

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "Содержание"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Continue"
msgstr "Продолжить"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copied"
msgstr "Скопировано"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copy Embed Code"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copy Link"
msgstr "Копировать ссылку"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct!"
msgstr "Правильно!"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct! A shovel is the perfect tool to dig a hole."
msgstr "Правильно! Лопата - идеальный инструмент для рытья ямы."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct! A strawberry is a fruit because it's the product of a tree."
msgstr "Правильно! Клубника - это фрукт, потому что это плод дерева."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct! Congratulations you have time to loose"
msgstr "Правильно! Поздравляю, у вас есть время"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct! You did it!"
msgstr "Правильно! Вы сделали это!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Could not find your video. Please check if your link is correct and if the "
"video can be accessed."
msgstr ""
"Не удалось найти ваше видео. Пожалуйста, проверьте, правильна ли ваша ссылка"
" и можно ли получить доступ к видео."

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "Курс"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Course Attendees"
msgstr "Слушатели курса"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "Количество курсов"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Finished"
msgstr "Курс завершен"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "Название группы курса"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "Группы курсов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_invite_url
msgid "Course Link"
msgstr "Ссылка Курса"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Course Member"
msgstr "Член курса"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "Название курса"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_channel_pages_list
msgid "Course Pages"
msgstr "Страницы курса"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/gamification_karma_tracking.py:0
msgid "Course Quiz"
msgstr "Викторина по курсу"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Ranked"
msgstr "Рейтинг курса"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Set Uncompleted"
msgstr "Набор курсов не завершен"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "Метки курса"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "Группа тегов курса"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "Группы тегов курса"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "Тэги   курса"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "Название курса"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
msgid "Course Type"
msgstr "Тип курса"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "Курс завершен"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course not published yet"
msgstr "Курс еще не опубликован"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "Курс ранжирован"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "Тип курса"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Course: %s"
msgstr "Курс: %s"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_partner.py:0
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.menu_slide_channel_pages
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Courses"
msgstr "Курсы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Courses Page"
msgstr "Страница курсов"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Courses that have this course as prerequisite."
msgstr "Предметы, для которых этот курс является обязательным условием."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__cover_properties
msgid "Cover Properties"
msgstr "Свойства обложки"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Create New Category \""
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Create New Tag \""
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Create a Content Tag"
msgstr "Создайте тег содержания"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Create a Course Group"
msgstr "Создайте группу курсов"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let Attendees answer each others' questions."
msgstr ""
"Создайте сообщество и позвольте участникам отвечать на вопросы друг друга."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr "Создание нового контента для вашего электронного обучения"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Create this tag \""
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Create this tag group\""
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "Создано"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "Создано"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of category 'Article'."
msgstr "Пользовательский HTML-контент для слайдов категории 'Article'."

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "Сделай сам Мебель"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Дата (от новой к старой)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Дата (от старой до новой)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "Default training image"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Определите видимость задачи с помощью меню"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Defines how people can enroll to your Course."
msgstr "Определяет, как люди могут записаться на ваш курс."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid "Defines the content that will be promoted on the course home page"
msgstr ""
"Определяет содержание, которое будет отображаться на главной странице курса"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid ""
"Defines the email your Attendees will receive each time you upload new "
"content."
msgstr ""
"Определяет электронное письмо, которое ваши посетители будут получать каждый"
" раз, когда вы загружаете новый контент."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid ""
"Defines the email your Attendees will receive once they reach the end of "
"your course."
msgstr ""
"Определяет электронное письмо, которое получат слушатели по окончании курса."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid "Defines who can access your courses and their content."
msgstr ""
"Определяет, кто может получить доступ к вашим курсам и их содержимому."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Delete"
msgstr "Удалить"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Delete Category"
msgstr "Удалить категорию"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Delete Question"
msgstr "Удалить Вопрос"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Description"
msgstr "Описание"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr "Подробное описание"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article?"
msgstr "Вы прочитали всю статью?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Discard"
msgstr "Отменить"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "Подробнее"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislike"
msgstr "Не нравится"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Dislikes"
msgstr "Не нравится"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "Показать"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees?"
msgstr "Делаете ли вы балки из лимонных деревьев?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams?"
msgstr "Делаете ли вы лимоны из балок?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Do you really want to leave the course?"
msgstr "Ты действительно хочешь уйти с курса?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name?"
msgstr "Как вы думаете, хорошее ли имя у Гарри Горшка?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Do you want to install \"%s\"?"
msgstr ""

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly?"
msgstr "Вы хотите ответить правильно?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Do you want to request access to this course?"
msgstr "Вы хотите запросить доступ к этому курсу?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "Документ"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__doc
msgid "Document (Word, Google Doc, ...)"
msgstr "Документ (Word, Google Doc, ...)"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_google_url
msgid "Document Link"
msgstr "Ссылка на документ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Document Source"
msgstr "Источник документа"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "Документация"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "Документы"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "Дружелюбное отношение к собакам"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Don't have an account?"
msgstr "Нет аккаунта?"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "Готово"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "Подсчет завершен"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Done!"
msgstr "Готово!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "Скачать"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "Скачать содержимое"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__download_url
msgid "Download URL"
msgstr "URL загрузки"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr "Рисунок 1"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr "Рисунок 2"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Duration"
msgstr "Продолжительность"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Earn more Karma to leave a comment."
msgstr "Заработайте больше кармы, чтобы оставить комментарий."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "Редактировать"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "Редактировать в бэкэнд"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_enroll
msgid "Elearning: Add Attendees to Course"
msgstr "Elearning: Добавление слушателей в курс"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_completed
msgid "Elearning: Completed Course"
msgstr "Elearning: Пройденный курс"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Elearning: Course Share"
msgstr "Elearning: Доля курса"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Elearning: New Course Content Notification"
msgstr "Elearning: Уведомление о новом содержании курса"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Elearning: Promotional Course Invitation"
msgstr "Elearning: Приглашение на рекламный курс"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "Email"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_channel_template_id
msgid "Email template used when sharing a channel"
msgstr ""
"Шаблон электронной почты, используемый при совместном использовании канала"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_slide_template_id
msgid "Email template used when sharing a slide"
msgstr ""
"Шаблон электронной почты используется для того чтобы поделится материалом"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "Email(s) sent."
msgstr "Отправленный(ые) адрес(ы) электронной почты."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "Код для вставки"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_embed_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "Embed Views"
msgstr "Встраивание просмотров"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Embed code"
msgstr "Код для вставки"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in another Website"
msgstr "Вставить на другой сайт"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "Встроенный счетчик просмотров слайдов"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "End course"
msgstr "Конец курса"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "Факты об энергоэффективности"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content!"
msgstr "Наслаждайтесь этим эксклюзивным контентом!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Enroll Attendees to %(course_name)s"
msgstr "Записать участников на %(course_name)s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "Зарегистрировать сообщение"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "Регистрационная политика"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__enroll_mode
msgid "Enroll partners"
msgstr "Регистрация партнеров"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Enrolled Attendees Information"
msgstr "Информация о зарегистрированных посетителях"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "Enrolled partners in the course"
msgstr "Записавшиеся на курс партнеры"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Enter at least two possible <b>Answers</b>."
msgstr "Укажите не менее двух возможных <b>Ответов</b>."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr "Введите свой <b>Вопрос</b>. Будьте ясны и лаконичны."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answers_validation_error
msgid "Error on Answers"
msgstr "Ошибка в ответах"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Estimated Completion Time"
msgstr "Предполагаемое время завершения"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate the knowledge of your Attendees and certify their skills."
msgstr "Оцените знания ваших слушателей и подтвердите их квалификацию."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
msgid "Everyone"
msgstr "Все"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "Упражнения"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Exit Fullscreen"
msgstr "Выход из полноэкранного режима"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code_external
msgid "External Embed Code"
msgstr "Внешний код для встраивания"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_ids
msgid "External Slide Embeds"
msgstr "Внешние вставки слайдов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "External URL"
msgstr "Внешний URL"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "External Website"
msgstr "Внешний сайт"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Failed to install \"%s\""
msgstr "Не удалось установить \"%s\""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Featured Content"
msgstr "Избранные материалы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__binary_content
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__file
msgid "File"
msgstr "Файл"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__file_name
msgid "File Name"
msgstr "Имя файла"

#. module: website_slides
#. odoo-javascript
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "File is too big. File size cannot exceed 25MB"
msgstr "Файл слишком большой. Размер файла не может превышать 25 МБ"

#. module: website_slides
#: model:ir.model,name:website_slides.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "Вспомогательная модель потоковой передачи файлов для контроллеров"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "Фильтр &amp; заказ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Finally you can click here to enjoy your content in fullscreen"
msgstr ""
"Наконец, вы можете нажать здесь, чтобы насладиться содержимым в "
"полноэкранном режиме"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Finish"
msgstr "Завершить"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "Завершить курс"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__completed
msgid "Finished"
msgstr "Завершено"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First Try"
msgstr "Первая попытка"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr ""
"Сначала создайте свой урок, а затем отредактируйте его с помощью "
"конструктора сайтов. Вы сможете перетаскивать блоки на свою страницу и "
"редактировать их."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "First, let's add a <b>Document</b>. It has to be a .pdf file."
msgstr "Сначала добавим <b>Документ</b>. Это должен быть файл в формате .pdf."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload the file on your Google Drive account."
msgstr "Сначала загрузите файл на свой аккаунт Google Drive."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload your videos on Vimeo and mark them as"
msgstr "Сначала загрузите видео на Vimeo и отметьте его как"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload your videos on YouTube and mark them as"
msgstr "Сначала загрузите видео на YouTube и отметьте его как"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Шрифт, отличный значок, например. к.-а.-задачи"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "Предисловие"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr "Предисловие к этой документации: как использовать, основные моменты"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "Форум"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth Try & More"
msgstr "Четвертая попытка и многое другое"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr "От куска дерева до полностью функциональной мебели - шаг за шагом."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
msgid ""
"From here you'll be able to monitor attendees and to track their progress."
msgstr ""
"Отсюда вы сможете контролировать участников и отслеживать их прогресс."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Fullscreen"
msgstr "Полный экран"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "Дизайнер мебели"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "Технические характеристики мебели"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr "GLork"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Геймификация испытания"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "Садовник"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "Садоводство: Ноу-хау"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "Получить сертификат"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "Начать"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Give your course a helpful <b>Description</b>."
msgstr "Дайте своему курсу полезное <b>Описание</b>."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Give your course an engaging <b>Title</b>."
msgstr "Дайте своему курсу увлекательное <b>Название</b>."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Go through all its content to see a Course in this section. <br/>"
msgstr ""
"Просмотрите все его содержимое, чтобы увидеть Курс в этом разделе. <br/>"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "Ключ к документу Google"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__google_drive
msgid "Google Drive"
msgstr "Google Диск"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "Ключ API Google Drive"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__google_drive_id
msgid "Google Drive ID of the external URL"
msgstr "Идентификатор Google Drive для внешнего URL-адреса"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__google_drive_video
msgid "Google Drive Video"
msgstr "Видео Google Drive"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
msgid "Grant Access"
msgstr "Предоставить доступ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "График выхода контента"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "Группа"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "Группировать по"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "Имя группы"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr ""
"Группе пользователей разрешено публиковать содержание документального курса."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "Последовательность в группе"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "Содержание HTML"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on!"
msgstr "Руки вверх!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_user_has_completed
msgid "Has Completed Prerequisite"
msgstr "Прошел предварительную подготовку"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
msgid "Has Menu Entry"
msgstr "Имеет вход в меню"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr ""
"Вот как получить самую сладкую клубнику, которую вы когда-либо пробовали!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "Главная"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "Домашнее садоводство"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr ""
"Как построить качественный обеденный стол с минимальным количеством "
"инструментов"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How do I add new content?"
msgstr "Как добавить новый контент?"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "Как вырастить и собрать лучшую клубнику | Основы"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr ""
"Как вырастить и собрать лучшую клубнику | Советы и рекомендации по "
"садоводству"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to create a Lesson as an Article?"
msgstr "Как создать урок в виде статьи?"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "Как найти качественную древесину"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted.list"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr "Как загрузить презентации PowerPoint или документы Word?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to upload your videos?"
msgstr "Как загрузить видео?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to use Google Drive?"
msgstr "Как использовать Google Диск?"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr "Как украсить стену, посадив дерево в подвесные пластиковые бутылки."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "Инструкция"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "ID"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий исключение Дела."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr ""
"Если вы ищете технические характеристики, посмотрите эту документацию."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr ""
"Если вы хотите убедиться, что слушатели поняли и запомнили содержание, вы "
"можете добавить в урок тест. Нажмите кнопку <b>Добавить тест</b>."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"If you want to use other types of files, you may want to use an external "
"source (Google Drive) instead."
msgstr ""
"Если вы хотите использовать другие типы файлов, возможно, вам стоит "
"воспользоваться внешним источником (Google Drive)."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__infographic
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__image
msgid "Image"
msgstr "Изображение"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "Изображение 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "Изображение 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "Изображение 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "Изображение 512"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_binary_content
msgid "Image Content"
msgstr "Содержание изображения"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_google_url
msgid "Image Link"
msgstr "Ссылка на изображение"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Image Source"
msgstr "Источник изображения"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid ""
"Impossible to send emails. Select a \"Channel Share Template\" for courses "
"%(course_names)s first"
msgstr ""
"Невозможно отправлять электронные письма. Сначала выберите \"Шаблон общего "
"доступа к каналу\" для курсов %(course_names)s"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Impossible to send emails. Select a \"Share Template\" for courses "
"%(course_names)s first"
msgstr ""
"Невозможно отправлять электронные письма. Сначала выберите \"Шаблон общего "
"доступа\" для курсов %(course_names)s"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect!"
msgstr "Неверно!"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect! A strawberry is not a vegetable."
msgstr "Неверно! Клубника не является овощем."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect! A table is a piece of furniture."
msgstr "Неверно! Стол - это предмет мебели."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect! Good luck digging a hole with a spoon..."
msgstr "Неправильно! Удачи в рытье ямы ложкой..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect! Seriously?"
msgstr "Неверно! Серьезно?"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect! You better think twice..."
msgstr "Неправильно! Лучше подумайте дважды..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect! You really should read it."
msgstr "Неверно! Вам действительно стоит прочитать это."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect! of course not ..."
msgstr "Неверно! Конечно, нет..."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "Инфографика"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.xml:0
msgid "Install"
msgstr "Установить"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog_select.xml:0
msgid "Install the"
msgstr "Установите"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Installing \"%s\"..."
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "Интересные факты"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting List Facts"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close!"
msgstr "Интересная информация о домашнем садоводстве. Держите это рядом!"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "Средний"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"Внутренняя ошибка сервера, пожалуйста попробуйте позже или свяжитесь с администратором.\n"
"Сообщение о ошибке: %s"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Invalid file type. Please select pdf or image file"
msgstr "Неверный тип файла. Пожалуйста, выберите PDF или файл изображения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__invitation_link
msgid "Invitation Link"
msgstr "Ссылка на приглашение"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "Пригласить"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Invite Attendees to %(course_name)s"
msgstr "Пригласить участников на %(course_name)s"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__invited
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Invite Sent"
msgstr "Приглашение отправлено"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed_category
msgid "Is Category Completed"
msgstr "Категория завершена"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Редактор"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Enrolled Attendee"
msgstr "Зачислен ли участник"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member_invited
msgid "Is Invited Attendee"
msgstr "Приглашенный участник"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed
msgid "Is Member"
msgstr "Является участником"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr "Новый слайд"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "Опубликовано"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "Является категорией"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "Правильный ответ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member
msgid "Is the attendee actively enrolled."
msgstr "Активно ли участвует в программе."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member_invited
msgid "Is the invitation for this attendee pending."
msgstr "Ожидается ли приглашение для этого участника."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "It should look similar to"
msgstr "Она должна выглядеть следующим образом"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""
"Джим и Тодд сажают дерево в горшке для клиента компании Knecht's Nurseries "
"and Landscaping. Рассказывает Лейф Кнехт, владелец компании."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Join & Submit"
msgstr "Регистрация и отправка"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "Join the Course"
msgstr "Присоединиться к курсу"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Join the course to take the quiz and verify your answers!"
msgstr "Присоединяйтесь к курсу, чтобы пройти тест и проверить свои ответы!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "Join this Course"
msgstr "Присоединяйтесь к этому курсу"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__joined
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Joined"
msgstr "Присоединился"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "Несколько основных фактов об энергоэффективности."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "Интересные факты о деревьях."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "Просто некоторые основы инфографики о деревьях."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "Karma"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr "Карме нужно было добавить комментарий к слайду этого курса"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "Карме нужно было добавить отзыв о курсе"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr "Карме нужно было понравиться/не понравится слайд этого курса."

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "Знай себя"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"Очень важно знать, какую древесину использовать в зависимости от области применения. В этом курсе вы\n"
"вы узнаете об основных характеристиках древесины."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr ""
"Знание характеристик древесины необходимо для того, чтобы знать, какую "
"породу дерева использовать в той или иной ситуации."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "Язык"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr "Последнее действие"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Invitation"
msgstr "Последнее приглашение"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__last_invitation_date
msgid "Last Invitation Date"
msgstr "Дата последнего приглашения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "Последнее обновление"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Created"
msgstr "Последнее созданое"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "Последние достижения"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "Таблица лидеров"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr ""
"Узнайте, как ухаживать за вашими любимыми деревьями. Узнайте, когда сажать, "
"как ухаживать за деревьями в горшках, ..."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening!"
msgstr "Изучите основы садоводства!"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr ""
"Научитесь определять качество древесины, чтобы создавать прочные предметы "
"мебели."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Leave the course"
msgstr "Покинуть курс"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "Урок"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "Содержание урока"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "Уроки"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.js:0
msgid "Level up!"
msgstr "Уровень повышен!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Like"
msgstr "Нравится"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Likes"
msgstr "Лайки"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__link
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__url
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Link"
msgstr "Ссылка"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_google_url
msgid ""
"Link of the document (we currently only support Google Drive as source)"
msgstr ""
"Ссылка на документ (в настоящее время мы поддерживаем только Google Drive в "
"качестве источника)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__image_google_url
msgid "Link of the image (we currently only support Google Drive as source)"
msgstr ""
"Ссылка на изображение (в настоящее время мы поддерживаем только Google Drive"
" в качестве источника)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__video_url
msgid ""
"Link of the video (we support YouTube, Google Drive and Vimeo as sources)"
msgstr ""
"Ссылка на видео (мы поддерживаем YouTube, Google Drive и Vimeo в качестве "
"источников)"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "List Infographic"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "List planting in hanging bottles on wall"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Loading content..."
msgstr "Загрузка содержимого..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Log in"
msgstr "Войти"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "Шаблон сообщения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "Рассылка"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "Основные категории деревьев"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "Руководитель"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Mark Done"
msgstr "Отметка Готово"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Mark To Do"
msgstr "Отметить, чтобы сделать"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Mark as done"
msgstr "Подтвердить выполнение"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Mark as not done"
msgstr "Отметить как невыполненное"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr "Отметьте правильный ответ, поставив <b>галочку</b>."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "Участники"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "Мнения участников"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr "Участники этих групп автоматически добавляются как участники канала."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "Вход в меню"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "Сообщение, объясняющее процесс зачисления"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "Методы"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "Могучая морковь"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr ""
"Могучие леса не появляются за несколько недель. Узнайте, как время сделало "
"наши леса могучими и таинственными."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Minutes"
msgstr "Минут"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr "Отсутствует \"Группа тегов\" для создания нового \"Тега\"."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "Мобильная вкладка"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "Больше информации"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "Просматриваемые"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "Самые популярные"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "Самые популярные курсы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "My Content"
msgstr "Мои материалы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "Мои курсы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "Мои курсы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "Имя"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "Навигация"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Need help? Review related content:"
msgstr "Нужна помощь? Просмотрите соответствующие материалы:"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/views/slide_channel_partner_list/slide_channel_partner_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "New"
msgstr "Новый"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Notification"
msgstr "Уведомление о новом содержимом"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "New Content Ribbon"
msgstr "Новая лента контента"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_add
msgid "New Course"
msgstr "Новый курс"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid ""
"New {{ object.slide_category }} published on {{ object.channel_id.name }}"
msgstr ""
"Новый {{ object.slide_category }} опубликован на {{ object.channel_id.name "
"}}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "Новые"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "Новейшие курсы"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Next"
msgstr "Следующий"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следующее событие календаря активности"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Следующий срок мероприятия"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего действия"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "Следующий Тип Мероприятия"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__next_slide_id
msgid "Next Lesson"
msgstr "Следующий урок"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "Следующий ранг:"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
msgid "No"
msgstr "Нет"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "No Attendee has completed this course yet!"
msgstr "Ни один слушатель еще не прошел этот курс!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "No Attendees Yet!"
msgstr "Посетителей пока нет!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "Курс еще не создан."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "No Notification"
msgstr "Нет уведомления"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "No Quiz data yet!"
msgstr "Данные викторины пока отсутствуют!"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "No Reviews yet!"
msgstr "Отзывов пока нет!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "Пока нет завершенных курсов!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "По вашему запросу контент не найден"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "Не найдено ни одного курса, соответствующего вашему запросу"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "Не найдено ни одного курса, соответствующего вашему запросу."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "Пока нет данных!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "В настоящее время нет лидеров"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_placeholder
msgid "No lessons are available yet."
msgstr "Уроки пока не доступны."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No ongoing courses yet!"
msgstr "Текущих курсов пока нет!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "Презентация отсутствует."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr "Не найдено результатов для '"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "Нет"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "Not Published"
msgstr "Не опубликовано"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Not enough karma to comment"
msgstr "Недостаточно кармы, чтобы комментировать"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Not enough karma to review"
msgstr "Недостаточно кармы для обзора"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
msgid "Notifications"
msgstr "Уведомления"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_article
msgid "Number of Articles"
msgstr "Кол-во статей"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
msgid "Number of Contents"
msgstr "Количество содержаний"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "Количество документов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Images"
msgstr "Номер изображения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "Количество опросов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "Количество видео"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "Количество комментариев"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "Количество вопросов"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "Odoo"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "Odoo - Изображение и текст"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "Офицер"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On Google Drive"
msgstr "На диске Google"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "По приглашению"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On Vimeo"
msgstr "На Vimeo"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On YouTube"
msgstr "На YouTube"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr "Как только вы закончите, не забудьте <b>опубликовать</b> свой курс."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__ongoing
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Ongoing"
msgstr "Текущие"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Ongoing Courses"
msgstr "Текущие курсы"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Only a single review can be posted per course."
msgstr "На один курс можно разместить только один отзыв."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
msgid "Open"
msgstr "Открыть"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Необязательный язык перевода (код ISO) для выбора при отправке сообщения "
"электронной почты. Если он не задан, будет использоваться английская версия."
" Как правило, это должно быть выражение-заполнитель, обеспечивающее "
"соответствующий язык, например {{ object.partner_id.lang }}."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "Опции"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_binary_content
msgid "PDF Content"
msgstr "PDF Содержание"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Paid Courses"
msgstr "Платные курсы"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "Партнер"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr "У партнера новый контент"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_pivot
msgid "Pivot"
msgstr "Сводная таблица"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please"
msgstr "Пожалуйста"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid ""
"Please <a href=\"/web/login?redirect=%(url)s\">login</a> or <a "
"href=\"/web/signup?redirect=%(url)s\">create an account</a> to vote for this"
" lesson"
msgstr ""
"Пожалуйста, <a href=\"/web/login?redirect=%(url)s\">войдите</a> или <a "
"href=\"/web/signup?redirect=%(url)s\">создайте учетную запись</a>, чтобы "
"проголосовать за этот урок"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid ""
"Please <a href=\"/web/login?redirect=%(url)s\">login</a> to vote for this "
"lesson"
msgstr ""
"Пожалуйста, <a href=\"/web/login?redirect=%(url)s\">войдите в</a> систему, "
"чтобы проголосовать за этот урок"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Please enter a valid Vimeo video link"
msgstr "Пожалуйста, введите действующую ссылку на видео Vimeo"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Please enter valid Google Drive Link"
msgstr "Введите действительную ссылку на Google Диск"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please enter valid email(s)"
msgstr "Пожалуйста, введите действительный адрес электронной почты"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
msgid "Please fill in the question"
msgstr "Пожалуйста, заполните вопрос"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
msgid "Please select at least one recipient."
msgstr "Пожалуйста, выберите хотя бы одного получателя."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Points Rewards"
msgstr "Вознаграждение за баллы"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "Опытный пользователь"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "Работает на"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Prerequisite Of"
msgstr "Предпосылки"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisite courses to complete before accessing this one."
msgstr ""
"Перед тем как приступить к изучению этого предмета, необходимо пройти "
"предварительные курсы."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisites"
msgstr "Предварительные условия"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
msgid "Presentation"
msgstr "Презентация"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "Презентация опубликована"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "Предпросмотр"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Previous"
msgstr "Предыдущий"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Private"
msgstr "Частный"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Private Course"
msgstr "Частный курс"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "Прогресс"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_xp_progress_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "Progress bar"
msgstr "Прогресс-бар"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr "Продвигаемый слайд"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "Паблик Просмотров"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Дата публикации"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "Дата публикации"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "Опубликовано"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Published Contents"
msgstr "Опубликованное содержание"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "Дата публикации"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr ""
"Публикация запрещается ответственными за учебные курсы или участниками "
"группы издателей для документации курсов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Question"
msgstr "Вопрос"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "Имя вопроса"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "Вопросы"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__quiz
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Quiz"
msgstr "Тест"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Quiz Completed"
msgstr "Тест завершён"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Quiz Demo Data"
msgstr "Демонстрационные данные викторины"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Quiz Set Uncompleted"
msgstr "Набор для викторины не завершен"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "Количество попыток прохождения теста"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr "Тесты"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "Средняя оценка (Звезды)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "Рейтинг Avg Текст"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Рейтинг последней обратной связи"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Рейтинг Последнего Изображения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Рейтинг Последнее значение"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Оценка удовлетворенности"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_text
msgid "Rating Text"
msgstr "Текст рейтинга"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "Количество рейтингов"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Rating of %s"
msgstr "Рейтинг %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__rating_ids
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
msgid "Ratings"
msgstr "Рейтинги"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "Достигни 2000 XP"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "Достигайте новых высот"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "Получатели"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
msgid "Refuse Access"
msgstr "Доступ к отходам"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr "Зарегистрируйтесь на платформе"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "Похожие"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Remove the answer comment"
msgstr "Удалить комментарий к ответу"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Remove this answer"
msgstr "Удалить этот ответ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr "Модель рендеринга"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "Отчет"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Request Access."
msgstr "Запросить доступ."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Request sent!"
msgstr "Запрос отправлен!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Reset"
msgstr "Сброс"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
msgid "Resource"
msgstr "Ресурс"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
msgid ""
"Resource %(resource_name)s is a link and should not contain a data file"
msgstr ""
"Ресурс %(resource_name)s является ссылкой и не должен содержать файл данных"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__resource_type
msgid "Resource Type"
msgstr "Тип ресурса"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Responsible"
msgstr "Ответственный"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "Ответственный пользователь"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Responsible already contacted."
msgstr "Ответственный уже связался с нами."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict to a specific website."
msgstr "Ограничить конкретным веб-сайтом."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__external
msgid "Retrieve from Google Drive"
msgstr "Извлечение из Google Drive"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.xml:0
msgid "Retry"
msgstr "Повторить"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.profile_access_denied
msgid "Return to the course."
msgstr "Обратно на курс."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "Проверка курса"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Review Date"
msgstr "Дата отзыва"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "Отзывы"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/chatter/frontend/portal_chatter_patch.js:0
msgid "Reviews (%s)"
msgstr "Отзывы (%s)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr "Награда: каждая попытка после третьей"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr "Награда: первая попытка"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr "Награда: вторая попытка"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr "Награда: третья попытка"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Rewards"
msgstr "Награды"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO-оптимизированный"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__embed_code_external
msgid ""
"Same as 'Embed Code' but used to embed the content on an external website."
msgstr ""
"То же самое, что и \"Код вставки\", но используется для вставки содержимого "
"на внешний сайт."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "Sample"
msgstr "Пример"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Save"
msgstr "Сохранить"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Save and Publish"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Save your presentations or documents as PDF files and upload them."
msgstr ""
"Сохраните свои презентации или документы в формате PDF и загрузите их."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Score"
msgstr "Оценка"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "Поиск"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr "Поиск содержимого"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "Поиск курсов"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "Поиск по содержанию"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second Try"
msgstr "Вторая попытка"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
msgid "Section"
msgstr "Раздел"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "Раздел подзаголовка"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
msgid "Section name"
msgstr "Название раздела"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "Токен безопасности"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_users.py:0
msgid "See our eLearning"
msgstr "Ознакомьтесь с нашим электронным обучением"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Select <b>Course</b> to create it and manage it."
msgstr "Выберите <b>Курс</b>, чтобы создать его и управлять им."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Select Manually"
msgstr "Выберите вручную"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Select or create a category"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.js:0
msgid "Select or create a tag"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.js:0
msgid "Select or create a tag group"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_select_tags.xml:0
msgid "Select or create tags"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Select the correct answer below:"
msgstr "Выберите правильный ответ ниже:"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Sell access to your courses on your website and track revenues."
msgstr ""
"Продавайте доступ к вашим курсам на своем сайте и отслеживайте доходы."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "Продавать в eCommerce"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "Отправить"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__send_email
msgid "Send Email"
msgstr "Отправить Email"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_channel_completed
msgid "Sent to attendees once they've completed the course"
msgstr "Рассылка слушателям после завершения курса"

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_published
msgid "Sent to attendees when new course is published"
msgstr "Рассылка слушателям о публикации нового курса"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_enroll
msgid "Sent to attendees when they are added to a course"
msgstr "Отправляется слушателям, когда они добавляются в курс"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_invite
msgid "Sent to potential attendees to check out the course."
msgstr "Рассылка потенциальным слушателям для ознакомления с курсом."

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_shared
msgid "Sent when attendees share the course by email"
msgstr "Отправляется, когда слушатели сообщают о курсе по электронной почте"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "Имя Seo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "Настройки"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Share"
msgstr "Поделиться"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "Поделится Каналом"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "Share Link"
msgstr "Поделиться ссылкой"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_slide_template_id
msgid "Share Template"
msgstr "Поделится Шаблоном"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Share This Content"
msgstr "Поделиться этим контентом"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_share_url
msgid "Share URL"
msgstr "Поделиться URL"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Share by Email"
msgstr "Поделиться через Email"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Facebook"
msgstr "Поделиться в Facebook"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on LinkedIn"
msgstr "Поделиться в LinkedIn"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Pinterest"
msgstr "Поделиться в Pinterest"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
msgid "Share on Social Media"
msgstr "Поделиться в соц сетях"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Whatsapp"
msgstr "Поделиться в WhatsApp"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on X"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Share this Content"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Share this Course"
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "Sharing is caring!"
msgstr "Делиться - значит заботиться!"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__sheet
msgid "Sheet (Excel, Google Sheet, ...)"
msgstr "Лист (Excel, Google Sheet, ...)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr "Краткое описание"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Show Course To"
msgstr "Показать курс"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge!"
msgstr "Покажите свои новые знания!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Sign Up!"
msgstr "Зарегистрироваться!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Sign in and join the course to verify your answers!"
msgstr ""
"Войдите в систему и присоединитесь к курсу, чтобы проверить свои ответы!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Sign up"
msgstr "Регистрация"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__connected
msgid "Signed In"
msgstr "Вошел"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr ""
"Повышайте квалификацию и добивайтесь успеха! Ваша бизнес-карьера начинается "
"здесь.<br/>Время начать курс."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "Слайд"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "Слайд / Партнер украсил m2m"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_icon_class
msgid "Slide Icon fa-class"
msgstr "Иконка слайда fa-class"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr "Слайд Ответ на вопрос"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "Тег для слайдов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
msgid "Slide Type"
msgstr "Тип слайда"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "Данные пользователей слайда"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Slide image"
msgstr "Изображение слайда"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr ""
"Слайд с вопросами должен быть отмечен как выполненный при правильном ответе "
"на все вопросы "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model:ir.model,name:website_slides.model_slide_slide
msgid "Slides"
msgstr "Слайдер"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__slides
msgid "Slides (PowerPoint, Google Slides, ...)"
msgstr "Слайды (PowerPoint, Google Slides, ...)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "Слайды и категории"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "Так много удивительных сертификатов."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "Сортировать по"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__source_type
msgid "Source Type"
msgstr "Тип источника"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start Course"
msgstr "Начать курс"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Start at Page"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start this Course"
msgstr "Начните этот курс"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Начните с клиента - узнайте, чего он хочет, и дайте ему это."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr "Начните свой онлайн-курс уже сегодня!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Status"
msgstr "Статус"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус, основанный на мероприятии\n"
"Просроченная: Дата, уже прошла\n"
"Сегодня: Дата мероприятия сегодня\n"
"Запланировано: будущая деятельность."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "Тема"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "Тема…"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
msgid "Subscribe"
msgstr "Подписаться"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "Информация о подписчике"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "Информация о подписчике для текущего вошедшего в систему пользователя"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "Подписчики"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "Информация об подписчиках"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"Subtype of the slide category, allows more precision on the actual file type"
" / source type."
msgstr ""
"Подтип категории слайдов, позволяет более точно определить тип файла / тип "
"источника."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Succeed and gain karma"
msgstr "Добейтесь успеха и получите карму"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
msgid "Tag"
msgstr "Тег"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Tag Group"
msgstr "Группа тегов"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "Название тега"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr ""
"Цвет тега, используемый как в бекенде, так и на сайте. Отсутствие цвета "
"означает отсутствие отображения в kanban или front-end, чтобы отличать "
"внутренние теги от публичных тегов категоризации"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
msgid "Tags"
msgstr "Теги"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "Теги..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Take Quiz"
msgstr "Начать тест"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "Уход за деревьями"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr "Технические чертежи"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr "Технический рисунок"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "Проверьте себя"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "Проверьте свои знания"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge!"
msgstr "Проверьте свои знания!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Test your students with small Quizzes"
msgstr "Проверьте своих учеников с помощью небольших викторин"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr ""
"<b>Продолжительность</b> урока зависит от количества страниц вашего "
"документа. Вы можете изменить это число, если вашим слушателям потребуется "
"больше времени для усвоения материала."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr ""
"<b>Название</b> урока заполняется автоматически, но вы можете изменить его "
"по своему усмотрению.</br>В правой части экрана доступен <b>предварительный "
"просмотр</b> вашего файла."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_check_enroll
msgid ""
"The Enroll Policy should be set to 'On Invitation' when visibility is set to"
" 'Course Attendees'"
msgstr ""
"Политика зачисления должна быть установлена на \"По приглашению\", если "
"видимость установлена на \"Слушатели курса\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The Google Drive link can be obtained by using the 'share' button in the "
"Google interface."
msgstr ""
"Ссылку на Google Drive можно получить с помощью кнопки \"поделиться\" в "
"интерфейсе Google."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The Google Drive link to use here can be obtained by clicking the \"Share\" "
"button in the Google interface."
msgstr ""
"Ссылку на Google Drive можно получить, нажав кнопку \"Поделиться\" в "
"интерфейсе Google."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_check_completion
msgid ""
"The completion of a channel is a percentage and should be between 0% and "
"100."
msgstr ""
"Завершение канала выражается в процентах и должно находиться в диапазоне от "
"0% aдо 100."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "The contact associated with this invitation does not seem to be valid."
msgstr "Контакт, связанный с этим приглашением, похоже, недействителен."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr ""
"Курс доступен для всех: пользователям не нужно присоединяться к каналу, "
"чтобы получить доступ к содержанию курса."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr "Описание, которое отображается на карточке курса"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr ""
"Описание, которое отображается в верхней части страницы курса, сразу под "
"заголовком"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "Полный URL-адрес для доступа к документу через веб-сайт."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_completed
msgid "The slide can be marked as completed even without opening it"
msgstr "Слайд можно пометить как завершенный, даже не открывая его"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "The slide can be marked as not completed and the progression"
msgstr "Слайд можно пометить как незавершенный, а прогрессия"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The video link to input here can be obtained by using the 'share' button in "
"the Vimeo interface."
msgstr ""

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_check_vote
msgid "The vote must be 1, 0 or -1."
msgstr "Голос должен быть равен 1, 0 или -1."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Then, go into the file permissions and set it as \"Anyone with the link\"."
msgstr ""
"Затем зайдите в права доступа к файлу и установите его как \"Любой, у кого "
"есть ссылка\"."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "Теория"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "There are no comments for now."
msgstr "На данный момент комментарии отсутствуют."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"There are no comments for now. Earn more Karma to be the first to leave a "
"comment."
msgstr ""
"На данный момент нет комментариев. Заработайте больше кармы, чтобы первым "
"оставить комментарий."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "There was an error validating this quiz."
msgstr "При проверке этого теста произошла ошибка."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "URL-адрес веб-сайта третьей стороны"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third Try"
msgstr "Третья попытка"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if they select this answer"
msgstr ""
"Этот комментарий будет показан пользователю, если он выберет этот ответ"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This course does not exist."
msgstr "Этого курса не существует."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid ""
"This course is not published. Attendees may not be able to access its "
"contents."
msgstr ""
"Этот курс не опубликован. Слушатели не смогут получить доступ к его "
"содержанию."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "This course is private."
msgstr "Это приватный курс."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This identification link does not seem to be valid."
msgstr "Эта идентификационная ссылка, похоже, не действует."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link has an invalid hash."
msgstr "Эта ссылка на приглашение имеет неверный хэш."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link has expired."
msgstr "Срок действия этой ссылки на приглашение истек."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link is not for this contact."
msgstr "Эта ссылка на приглашение не для этого контакта."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "This is the correct answer"
msgstr "Это правильный ответ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "This is the correct answer, congratulations"
msgstr "Это правильный ответ, поздравляем"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
msgid ""
"This question must have at least one correct answer and one incorrect "
"answer."
msgstr ""
"Этот вопрос должен содержать как минимум один правильный и один неправильный"
" ответ."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "This quiz is already done. Retaking it is not possible."
msgstr "Это тестирование пройдено. Повторное прохождение невозможно."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This slide can not be marked as completed."
msgstr "Этот слайд не может быть отмечен как завершенный."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This slide can not be marked as uncompleted."
msgstr "Этот слайд не может быть помечен как незавершенный."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This video already exists in this channel on the following content: %s"
msgstr "Это видео уже существует на этом канале в следующем контенте: %s"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"Through Google Drive, we support most common types of documents.\n"
"            Including regular documents (Google Doc, .docx), Sheets (Google Sheet, .xlsx), PowerPoints, ..."
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
msgid "Title"
msgstr "Заголовок"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "Переключить навигацию"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "Инструменты"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "Инструменты и методы"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "Инструменты, которые вам понадобятся для прохождения этого курса."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Total"
msgstr "Всего"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Attendees"
msgstr "Всего участников"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Completed"
msgstr "Всего завершено"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Duration"
msgstr "Общая длительность"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Questions"
msgstr "Всего вопросов"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "Всего слайдов"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Views"
msgstr "Всего просмотров"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "Отслеживание изменений кармы"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "Тренинг"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "Деревья"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "Деревья, лес и сады"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "Triumphant hero"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "Тип"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип Дела для исключения в записи."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "URL of the Google Drive file or URL of the YouTube video"
msgstr "URL-адрес файла Google Drive или URL-адрес видеоролика YouTube"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Невозможно отправить сообщение, пожалуйста, настройте адрес электронной "
"почты отправителя."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Unarchive"
msgstr "Разархивировать"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Uncategorized"
msgstr "Без категории"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "Незабываемые инструменты"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_embed.py:0
msgid "Unknown Website"
msgstr "Неизвестный сайт"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "Unknown error"
msgstr "Неизвестная ошибка"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Unknown error, try again."
msgstr "Неизвестная ошибка, повторите попытку."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "Unpublished"
msgstr "Неопубликованный"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Update"
msgstr "Обновить"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Update all your Attendees at once through mass mailings."
msgstr "Обновляйте всех участников одновременно с помощью массовых рассылок."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Upload Document"
msgstr "Загрузить документ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "Загрузка групп"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__local_file
msgid "Upload from Device"
msgstr "Загрузка с устройства"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "Загружено"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Uploading document ..."
msgstr "Загрузка документа ..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Use Content Tags to classify your Content."
msgstr "Используйте теги содержания для классификации контента."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Use Course Groups to classify and organize your Courses."
msgstr "Используйте группы курсов для классификации и организации курсов."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Use template"
msgstr "Используйте шаблон"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr ""
"Используется для категоризации и фильтрации отображаемых каналов/курсов"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "Используется для оформления представления канбан"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "Голосование пользователей"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Validation error"
msgstr "Ошибка валидации"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__video
msgid "Video"
msgstr "Видео"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_url
msgid "Video Link"
msgstr "Ссылка на видео"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_source_type
msgid "Video Source"
msgstr "Источник видео"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__vimeo_id
msgid "Video Vimeo ID"
msgstr "Видео Vimeo ID"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__youtube_id
msgid "Video YouTube ID"
msgstr "Идентификатор видео на YouTube"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "Видео"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "Просмотр"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "Посмотреть все"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "Посмотреть курс"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Views"
msgstr "Просмотры"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "Просмотров •"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__vimeo
msgid "Vimeo"
msgstr "Vimeo"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__vimeo_video
msgid "Vimeo Video"
msgstr "Видео Vimeo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "Видимый на текущем веб-сайте"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "Посещения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "Голос"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "Голоса"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "Votes and comments are disabled for this course"
msgstr "Голосование и комментарии для этого курса отключены"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "Жду подтверждения"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Want to test and certify your students?"
msgstr "Хотите протестировать и сертифицировать своих учеников?"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "Наблюдение за работой мастера (мастеров)"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say!"
msgstr ""
"Мы немного поболтали с Гарри Горшком, уверены, ему есть что рассказать!"

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__website_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "Сайт"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr "Вебсайт / Слайды"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "Адрес сайта"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "Мета-описание веб-сайта"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "Мета-ключевые слова сайта"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "Мета-название Веб-сайта"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "Изображение на сайте opengraph"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr ""
"Добро пожаловать на главную страницу вашего курса. Пока что она пуста. "
"Нажмите на кнопку<b>\"Новый</b>\", чтобы написать свой первый курс."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "What does"
msgstr "Что делает"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry?"
msgstr "Что такое клубника?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants?"
msgstr "Каким инструментом лучше всего копать яму для растений?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "What types of documents do we support?"
msgstr "Какие типы документов мы поддерживаем?"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again?"
msgstr "Что это был за вопрос?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "When using local files, we only support PDF files."
msgstr "При использовании локальных файлов мы поддерживаем только файлы PDF."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__enroll_mode
msgid ""
"Whether invited partners will be added as enrolled. Otherwise, they will be "
"added as invited."
msgstr ""
"Будут ли приглашенные партнеры добавлены как зарегистрированные. В противном"
" случае они будут добавлены как приглашенные."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video!"
msgstr ""
"Какая порода дерева лучше всего подходит для мебели из массива? На этот "
"вопрос мы поможем вам ответить в этом видео!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr ""
"С помощью викторин вы можете держать своих учеников в фокусе и мотивировать "
"их, отвечая на вопросы и набирая очки кармы"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "Дерево"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "Гнутье древесины с помощью парового ящика"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "Характеристики древесины"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr "Виды древесины"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "Работа с деревом"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Напишите один или два абзаца, описывающих ваш продукт или услуги. <br>Чтобы "
"быть успешным, ваш контент должен быть полезным для читателей."

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Напишите один или два абзаца, описывающих ваш продукт, услуги или конкретную"
" особенность.<br> Чтобы добиться успеха, ваш контент должен быть полезным "
"для читателей."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "XP"
msgstr "XP"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Yes"
msgstr "Да"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr ""
"Вам не разрешено добавлять участников в этот курс. Пожалуйста, свяжитесь с "
"ответственным за курс или администратором."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr ""
"Вы можете добавлять <b>комментарии</b> к ответам. Они будут видны в "
"результатах, если пользователь выберет этот ответ."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "You can add questions to this quiz in the 'Quiz' tab."
msgstr "Вы можете добавить вопросы к этой викторине на вкладке \"Викторина\"."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"You can either upload a file from your computer or insert a Google Drive "
"link."
msgstr ""
"Вы можете загрузить файл со своего компьютера или вставить ссылку на Google "
"Диск."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "You can not upload password protected file."
msgstr "Вы не можете загрузить файл защищённый паролем."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You cannot add tags to this course."
msgstr "Вы не можете добавлять теги к этому курсу."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr ""
"Вы не можете отметить слайд как завершенный, если вы не участник слайда."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide as uncompleted if you are not among its members."
msgstr ""
"Вы не можете пометить слайд как незавершенный, если вы не входите в число "
"его участников."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr ""
"Вы не можете отметить слайд как просмотренный, если вы не участник слайда."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members "
"or it is unpublished."
msgstr ""
"Вы не можете отметить слайд-викторину как завершенную, если вы не входите в "
"число ее участников или она не опубликована."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide quiz as not completed if you are not among its "
"members or it is unpublished."
msgstr ""
"Вы не можете отметить слайд-викторину как незавершенную, если вы не входите "
"в число ее участников или она не опубликована."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You cannot upload on this channel."
msgstr "Вы не можете загрузить файл на этот канал."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "You did it!"
msgstr "Вы сделали это!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You do not have permission to access this course."
msgstr "У вас нет разрешения на доступ к этому курсу."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You don't have access to this lesson"
msgstr "У вас нет доступа к этому уроку"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You don't have enough karma to vote"
msgstr "У вас недостаточно кармы, чтобы проголосовать"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "You gained"
msgstr "Вы заработали"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
msgid "You have already joined this channel"
msgstr "Вы уже присоединились к этому каналу"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to check out {{ object.channel_id.name }}"
msgstr "Вас пригласили проверить {{ object.channel_id.name }}"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_enroll
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr "Вас пригласили присоединиться к {{ object.channel_id.name }}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You have been invited to this course."
msgstr "Вы приглашены на этот курс."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "You have to sign in before"
msgstr "Вы должны войти в систему, прежде чем"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr "Теперь вы можете участвовать в нашем электронном обучении."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "You must be logged to submit the quiz."
msgstr ""
"Вы должны быть зарегистрированы, чтобы отправить тестирование на проверку."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You must be member of this course to vote"
msgstr "Вы должны быть участником этого курса, чтобы проголосовать"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You need to join this course to access \""
msgstr "Вы должны присоединиться к этому курсу, чтобы получить доступ к \""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "Вы не поверите в эти факты о моркови."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "You're enrolled"
msgstr "Курс начат"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__youtube
msgid "YouTube"
msgstr "YouTube"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__youtube_video
msgid "YouTube Video"
msgstr "YouTube видео"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr "Ваш"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "Ваш уровень"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "Ваша должность"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid ""
"Your eLearning platform starts here!<br>\n"
"                    Upload content, set up rewards, manage attendees..."
msgstr ""
"Ваша платформа электронного обучения начинается здесь!<br>\n"
"                    Загружайте контент, устанавливайте вознаграждения, управляйте участниками..."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your file could not be found on Google Drive, please check the link and/or "
"privacy settings"
msgstr ""
"Ваш файл не удалось найти на Google Диске, проверьте ссылку и/или настройки "
"конфиденциальности"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create an article or link "
"a video."
msgstr ""
"Ваш первый раздел создан, теперь пришло время добавить уроки в курс. Нажмите"
" на кнопку <b>Добавить содержимое</b>, чтобы загрузить документ, создать "
"статью или связать видео."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your video could not be found on Vimeo, please check the link and/or privacy"
" settings"
msgstr ""
"Ваше видео не удалось найти на Vimeo, пожалуйста, проверьте ссылку и/или "
"настройки конфиденциальности"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your video could not be found on YouTube, please check the link and/or "
"privacy settings"
msgstr ""
"Ваше видео не удалось найти на YouTube, пожалуйста, проверьте ссылку и/или "
"настройки конфиденциальности"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "a course"
msgstr "курс"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "получен"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "and join this Community"
msgstr "и присоединяйтесь к этому сообществу"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "anyway"
msgstr "в любом случае"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog_select.xml:0
msgid "app."
msgstr "приложение."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "хлебные крошки"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "by email."
msgstr "по email."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "copy"
msgstr "копировать"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "course"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "courses"
msgstr "курсы"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "create an account"
msgstr "и присоеденяйтесь к нам"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "direct access"
msgstr "прямой доступ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g \"https://drive.google.com/file/...\""
msgstr "например, \"https://drive.google.com/file/...\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
msgid "e.g \"https://www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "например, \"https://www.youtube.com/watch?v=ebBez6bcSEc\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g \"www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "например, \"www.youtube.com/watch?v=ebBez6bcSEc\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
msgid "e.g 'HowTo'"
msgstr "например, 'HowTo'"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "e.g. \"15\""
msgstr "например, \"15\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. \"Computer Science for kids\""
msgstr "например, \"Компьютерные науки для детей\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
msgid "e.g. \"Introduction\""
msgstr "например, \"Введение\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "e.g. \"Which animal cannot fly?\""
msgstr "например, \"Какое животное не умеет летать?\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "e.g. Computer Science for kids"
msgstr "например, Компьютерные науки для детей"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr ""
"например, в этом видео мы расскажем вам о том, как Odoo может помочь вам в "
"развитии вашего бизнеса. В конце мы предложим вам викторину, чтобы проверить"
" ваши знания."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. Setting up your computer"
msgstr "например, настройка компьютера"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "e.g. What powers a computer?"
msgstr "например, что питает компьютер?"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr "например, \"Ваш уровень"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "eLearning"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "eLearning Курсы"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "eLearning Обзор"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "for 'Private' videos and similar to"
msgstr "для видео \"Частное\" и аналогично"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "for public ones."
msgstr "для общественных."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://drive.google.com/file/d/ABC/view?usp=sharing"
msgstr "https://drive.google.com/file/d/ABC/view?usp=sharing"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://vimeo.com/558907333/30da9ff3d8"
msgstr "https://vimeo.com/558907333/30da9ff3d8"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://vimeo.com/558907555"
msgstr "https://vimeo.com/558907555"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "join"
msgstr "присоединиться"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "login"
msgstr "логин"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"mean? The Vimeo \"Private\" privacy setting means it is a video which can be viewed only by the users with the link to it.\n"
"                Your video will never come up in the search results nor on your channel."
msgstr ""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""
"что это значит? YouTube \"unlisted\" означает, что это видео, которое могут "
"просматривать только те пользователи, у которых есть ссылка на него. Ваше "
"видео никогда не появится ни в результатах поиска, ни на вашем канале."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "or"
msgstr "или"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "or Leave the course"
msgstr "или Покинуть курс"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "request"
msgstr "запрос"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "sign in"
msgstr "вход"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "start"
msgstr "начало"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "этапов"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to access resources"
msgstr "для доступа к ресурсам"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "to be the first to leave a comment."
msgstr "чтобы первым оставить комментарий."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "to browse preview content and enroll."
msgstr "чтобы просмотреть содержимое предварительного просмотра и записаться."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to contact responsible"
msgstr "связаться с ответственным"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "to enroll."
msgstr "начать курс."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "to join this course"
msgstr "чтобы присоединиться к этому курсу"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to request access"
msgstr "запросить доступ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "to share this"
msgstr "поделится этим"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to unlock"
msgstr "разблокировать"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "unlisted"
msgstr "не включенные в список"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "xp"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_shared
msgid "{{ user.name }} shared a Course"
msgstr "{{ user.name }} поделился Курсом"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_category }} with you!"
msgstr "{{ user.name }} поделился с вами {{ object.slide_category }}!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ms-1\">Uncategorized</span>"
msgstr "<span class=\"ms-1\">└Без рубрики</span>"
