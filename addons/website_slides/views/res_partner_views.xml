<?xml version="1.0" encoding="utf-8"?>
<odoo><data>

    <record id="res_partner_view_form" model="ir.ui.view">
        <field name="name">res.partner.view.form.inherit.slides</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button class="oe_stat_button" type="object"
                    icon="fa-graduation-cap" name="action_view_courses"
                    groups="website_slides.group_website_slides_officer"
                    invisible="slide_channel_count == 0 or is_company">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="slide_channel_count"/></span>
                        <span class="o_stat_text">Courses</span>
                    </div>
                </button>
                <button class="oe_stat_button" type="object"
                    icon="fa-graduation-cap" name="action_view_courses"
                    groups="website_slides.group_website_slides_officer"
                    invisible="slide_channel_company_count == 0 or not is_company">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="slide_channel_company_count" /></span>
                        <span class="o_stat_text">Courses</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>

</data></odoo>
