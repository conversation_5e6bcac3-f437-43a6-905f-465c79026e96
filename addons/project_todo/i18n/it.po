# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_todo
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"&amp;#128075; <br/>\n"
"    Welcome to the To-do app!"
msgstr ""
"&amp;#128075; <br/>\n"
"    Benvenuto nell'app To-do!"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Aggiungi un elenco di controllo\n"
"                    (/<span style=\"font-style: italic;\">elenco di controllo</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Aggiungi un separatore\n"
"                    (/<span style=\"font-style: italic;\">separatore</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Utilizza\n"
"                    /<span style=\"font-style: italic;\">titolo</span>\n"
"                    per convertire un testo in un titolo\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Dopo la lista, prova i\n"
"            <span style=\"font-weight: bolder;\">comandi</span>\n"
"            \n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">scrivendo</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font style=\"background-color: #017E84; color: white\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Seleziona il testo per\n"
"            <font style=\"background-color: #017E84; color: white\">evidenziarlo</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">barrarlo</span>\n"
"            o\n"
"            <span style=\"font-weight: bolder;\">modificarlo</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">a piacimento</span>\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Access your personal pipeline with your to-dos and assigned tasks by going to the Project app and clicking\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">My Tasks</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        There, your to-dos are listed as\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">private tasks.</font>\n"
"        </span>\n"
"        Any task you create privately will also be included in your to-dos. Essentially, they are interchangeable.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Accedi alla tua pipeline personale con le attività da svolgere e assegnate aprendo l'app Progetto e facendo clic su\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">I miei lavori</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Qui, tutti i to-do sono elencati come\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">lavori privati.</font>\n"
"        </span>\n"
"        Qualsiasi lavoro creato privatamente verrà incluso nei to-do. In pratica, sono interscambiabili.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        By default, to-dos are only visible to you. You can share them with other users by adding them as\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">assignees</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Per impostazione predefinita, i to-do sono visibili solo a te. Puoi condividerli con altri utenti aggiungendoli come\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">assegnatari</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Customize the stages from the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Kanban view</font>\n"
"        </span>\n"
"        to reflect your preferred workflow.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Personalizza le fasi dalla\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">vista Kanban</font>\n"
"        </span>\n"
"        per adottare il flusso di lavoro che preferisci.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        If you want to assign your to-do to a specific project, open the ⚙️ menu and click\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Convert to Task</font>.\n"
"        </span>\n"
"        This action will make it visible to other users.\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Se vuoi assegnare il tuo to-do ad un progetto specifico, apri il menu ⚙️ e fai clic su\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Converti in lavoro</font>.\n"
"        </span>\n"
"        L'azione lo renderà visibile ad altri utenti.\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Use it to manage your work, take notes on the go, and create tasks based on them.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        È possibile utilizzarlo per gestire attività, prendere note e creare nuovi lavori.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Wherever you are, use the magic keyboard shortcut to add yourself a reminder &amp;#128161;\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Usa le scelte rapide da tastiera, ovunque ti trovi, per aggiungere un promemoria &amp;#128161;\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Spunta la casella per indicare che qualcosa"
" è stato fatto</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Fai clic in qualsiasi punto della pagina e "
"inizia a scrivere</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Premi Ctrl+Z/⌘+Z per annullare qualsiasi "
"modifica</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private to-do is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Questa lista privata di cose da fare è per permetterti di scoprire un po' come funziona.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Sei pronto per metterti alla prova?</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following</span>"
msgstr "<span style=\"font-size: 14px;\">Prova le seguenti funzioni</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">ALT + MAIUSC + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOs)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">CTRL + MAIUSC + T</font>\n"
"            </span>\n"
"            (MacOs)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add To-Do"
msgstr "Aggiungi compito"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/web/activity/activity_menu_patch.js:0
msgid "Add a To-Do"
msgstr "Aggiungi un compito"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add details about your to-do..."
msgstr "Aggiungi dettagli sul tuo to-do..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Archived"
msgstr "In archivio"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__user_id
msgid "Assigned to"
msgstr "Assegnato a"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Assignees"
msgstr "Assegnatari"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By assigned tags"
msgstr "Per tag assegnati"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By personal stages"
msgstr "Per fasi personali"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Choose tags from the selected project"
msgstr "Scegli i tag dal progetto selezionato"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed"
msgstr "Chiuso"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed On"
msgstr "Chiuso il"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_controller.js:0
#: model:ir.actions.act_window,name:project_todo.project_task_action_convert_todo_to_task
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Convert to Task"
msgstr "Converti in lavoro"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Convert to-dos into tasks"
msgstr "Converti to-do in lavori"

#. module: project_todo
#: model:ir.model,name:project_todo.model_mail_activity_todo_create
msgid "Create activity and todo at the same time"
msgstr "Crea un'attività e un to-do"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Create to-dos from anywhere"
msgstr "Crea to-do da qualsiasi punto"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_date
msgid "Created on"
msgstr "Creato il"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Delete"
msgstr "Elimina"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Discard"
msgstr "Abbandona"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__date_deadline
msgid "Due Date"
msgstr "Data scadenza"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Future Activities"
msgstr "Attività future"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Hey"
msgstr "Hei"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__id
msgid "ID"
msgstr "ID"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid ""
"Keep your work organized by using memos and to-do lists.\n"
"                Your to-do items are private by default, but you can choose to share them with others by adding them as assignees."
msgstr ""
"Organizza il tuo lavoro grazie a promemoria e liste delle cose da fare.\n"
"                I tuoi compiti sono privati per impostazione predefinita ma puoi scegliere di condividerli con altre persone aggiungendo assegnatari."

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Late Activities"
msgstr "Attività in ritardo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Manage your to-dos and assigned tasks from a single place"
msgstr "Gestisci i to-do e i lavori assegnati da un singolo posto"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid "No to-do found. Let's create one!"
msgstr "Nessun compito trovato. Creiamone uno!"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__note
msgid "Note"
msgstr "Nota"

#. module: project_todo
#: model:res.groups,name:project_todo.group_onboarding_todo
msgid "Onboarding todo already generated for those users"
msgstr "Integrazione todo già creata per questi utenti"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Open"
msgstr "Apri"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Organize your to-dos however you want"
msgstr "Organizza i to-do come preferisci"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Reminder to..."
msgstr "Promemoria per..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Set Cover Image"
msgstr "Imposta copertina"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Show all records which has next action date is before today"
msgstr "Mostra tutti i record con data prossima azione precedente a oggi"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "Stage"
msgstr "Fase"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Starred"
msgstr "Preferiti"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__summary
msgid "Summary"
msgstr "Riepilogo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Tags"
msgstr "Etichette"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
#: model:ir.model,name:project_todo.model_project_task
msgid "Task"
msgstr "Lavoro"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
msgid "To-Do"
msgstr "To-Do"

#. module: project_todo
#: model:ir.ui.menu,name:project_todo.menu_todo_todos
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do"
msgstr "Da fare"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "To-do Title"
msgstr "Titolo compito"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do..."
msgstr "Da fare..."

#. module: project_todo
#: model:ir.actions.act_window,name:project_todo.project_task_action_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_activity
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "To-dos"
msgstr "Compiti"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Today Activities"
msgstr "Attività odierne"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Todos"
msgstr "Compiti"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_control_panel.xml:0
msgid "Toggle chatter"
msgstr "Apri chatter"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "Type Here..."
msgstr "Scrivi qui..."

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Untitled to-do"
msgstr "Compito senza titolo"

#. module: project_todo
#: model:ir.model,name:project_todo.model_res_users
msgid "User"
msgstr "Utente"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Using the editor"
msgstr "Utilizzo dell'editor"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Welcome %s!"
msgstr "Benvenuto%s!"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Who has access to what?"
msgstr "Chi ha accesso a cosa?"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/wizard/mail_activity_todo_create.py:0
msgid "Your to-do has been successfully added to your pipeline."
msgstr "Il tuo compito è stato aggiunto con successo al tuo flusso."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "convert-todo"
msgstr "convert-todo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "e.g. Send Invitations"
msgstr "ad es. Invia inviti"

#. module: project_todo
#: model:ir.actions.server,name:project_todo.project_task_preload_action_todo
msgid "menu load To-dos"
msgstr "menu carica compiti"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "todo-access"
msgstr "todo-access"
