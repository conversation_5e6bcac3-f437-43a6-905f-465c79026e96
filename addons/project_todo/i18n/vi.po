# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_todo
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"&amp;#128075; <br/>\n"
"    Welcome to the To-do app!"
msgstr ""
"&amp;#128075; <br/>\n"
"    Chào mừng bạn đến với ứng dụng Việc cần làm!"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Thêm một danh sách\n"
"                    (/<span style=\"font-style: italic;\">danh sách</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Thêm một dấu phân cách\n"
"                    (/<span style=\"font-style: italic;\">dấu phân cách</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Sử dụng\n"
"                    /<span style=\"font-style: italic;\">thẻ tiêu đề</span>\n"
"                    để chuyển văn bản thành tiêu đề\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Dưới danh sách này, hãy thử sử dụng\n"
"            <span style=\"font-weight: bolder;\">lệnh</span>\n"
"            bằng cách\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">nhập</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font style=\"background-color: #017E84; color: white\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Chọn văn bản để\n"
"            <font style=\"background-color: #017E84; color: white\">tô màu</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">gạch ngang</span>\n"
"            hoặc\n"
"            <span style=\"font-weight: bolder;\">tạo kiểu</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">cho văn bản đó</span>\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Access your personal pipeline with your to-dos and assigned tasks by going to the Project app and clicking\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">My Tasks</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        There, your to-dos are listed as\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">private tasks.</font>\n"
"        </span>\n"
"        Any task you create privately will also be included in your to-dos. Essentially, they are interchangeable.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Truy cập quy trình cá nhân chứa các việc cần làm và nhiệm vụ được giao cho bạn bằng cách vào ứng dụng Project và nhấp\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Nhiệm vụ của tôi</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Ở đó, những việc cần làm của bạn được liệt kê dưới dạng\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">nhiệm vụ riêng tư.</font>\n"
"        </span>\n"
"        Bất kỳ nhiệm vụ nào bạn tạo riêng cũng sẽ được đưa vào danh sách việc cần làm này. Về cơ bản, chúng tương tự nhau.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        By default, to-dos are only visible to you. You can share them with other users by adding them as\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">assignees</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Theo mặc định, việc cần làm chỉ hiển thị cho bạn. Bạn có thể chia sẻ chúng với những người dùng khác bằng cách thêm họ làm\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">người được phân công</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Customize the stages from the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Kanban view</font>\n"
"        </span>\n"
"        to reflect your preferred workflow.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Tùy chỉnh các giai đoạn từ\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">chế độ xem Kanban</font>\n"
"        </span>\n"
"        để tạo quy trình làm việc ưa thích của bạn.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        If you want to assign your to-do to a specific project, open the ⚙️ menu and click\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Convert to Task</font>.\n"
"        </span>\n"
"        This action will make it visible to other users.\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Nếu bạn muốn chuyển việc cần làm vào một dự án cụ thể, hãy mở menu ⚙️ và nhấp\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Chuyển đổi thành Nhiệm vụ</font>.\n"
"        </span>\n"
"        Hành động này sẽ cho phép người dùng khác thấy được nhiệm vụ đó.\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Use it to manage your work, take notes on the go, and create tasks based on them.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Sử dụng để quản lý công việc của bạn, ghi chú khi đang di chuyển và tạo nhiệm vụ từ chúng.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Wherever you are, use the magic keyboard shortcut to add yourself a reminder &amp;#128161;\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Dù bạn ở đâu, hãy sử dụng phím tắt thần kỳ để thêm nhắc nhở cho chính mình &amp;#128161;\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Đánh dấu ô này để cho biết đã hoàn "
"tất</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Nhấp vào bất kỳ đâu, rồi chỉ cần bắt đầu "
"nhập</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Bấm Ctrl+Z/⌘+Z để hoàn tác mọi thay "
"đổi</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private to-do is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Việc cần làm riêng tư này là nơi bạn được thoả sức sử dụng.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Bạn đã sẵn sàng thử chưa?</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following</span>"
msgstr "<span style=\"font-size: 14px;\">Thử các tính năng sau</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOs)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOs)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add To-Do"
msgstr "Thêm việc cần làm"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/web/activity/activity_menu_patch.js:0
msgid "Add a To-Do"
msgstr "Thêm một việc cần làm"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add details about your to-do..."
msgstr "Thêm thông tin về việc cần làm của bạn..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__user_id
msgid "Assigned to"
msgstr "Phân công cho"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Assignees"
msgstr "Người được phân công"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By assigned tags"
msgstr "Theo thẻ người được giao"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By personal stages"
msgstr "Theo giai đoạn cá nhân"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Choose tags from the selected project"
msgstr "Chọn thẻ từ dự án đã chọn"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed"
msgstr "Đã chốt"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed On"
msgstr "Đóng vào"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_controller.js:0
#: model:ir.actions.act_window,name:project_todo.project_task_action_convert_todo_to_task
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Convert to Task"
msgstr "Chuyển đổi thành nhiệm vụ"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Convert to-dos into tasks"
msgstr "Chuyển đổi việc cần làm thành nhiệm vụ"

#. module: project_todo
#: model:ir.model,name:project_todo.model_mail_activity_todo_create
msgid "Create activity and todo at the same time"
msgstr "Tạo hoạt động và việc cần làm cùng một lúc"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Create to-dos from anywhere"
msgstr "Tạo việc cần làm từ bất kỳ đâu"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Delete"
msgstr "Xoá"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__date_deadline
msgid "Due Date"
msgstr "Ngày đến hạn"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Future Activities"
msgstr "Hoạt động trong tương lai"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Hey"
msgstr "Nè"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__id
msgid "ID"
msgstr "ID"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid ""
"Keep your work organized by using memos and to-do lists.\n"
"                Your to-do items are private by default, but you can choose to share them with others by adding them as assignees."
msgstr ""
"Quản lý công việc một cách ngăn nắp bằng cách sử dụng các bản ghi nhớ và danh sách việc cần làm.\n"
"                  Theo mặc định, các đầu mục việc cần làm của bạn ở chế độ riêng tư, nhưng bạn có thể chia sẻ chúng với người khác bằng cách thêm họ làm người được giao."

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Late Activities"
msgstr "Hoạt động chậm trễ"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Manage your to-dos and assigned tasks from a single place"
msgstr "Quản lý việc cần làm và nhiệm vụ được giao từ một nơi duy nhất"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid "No to-do found. Let's create one!"
msgstr "Không tìm thấy việc cần làm nào. Hãy tạo mới!"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__note
msgid "Note"
msgstr "Ghi chú"

#. module: project_todo
#: model:res.groups,name:project_todo.group_onboarding_todo
msgid "Onboarding todo already generated for those users"
msgstr "Onboarding về việc cần làm đã được tạo cho những người dùng đó"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Open"
msgstr "Mở"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Organize your to-dos however you want"
msgstr "Sắp xếp việc cần làm theo cách bạn muốn"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Reminder to..."
msgstr "Nhắc nhở..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Set Cover Image"
msgstr "Đặt ảnh bìa"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Show all records which has next action date is before today"
msgstr "Hiển thị tất cả bản ghi có ngày xử lý tiếp theo trước ngày hôm nay"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "Stage"
msgstr "Giai đoạn"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Starred"
msgstr "Được gắn sao"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__summary
msgid "Summary"
msgstr "Tóm tắt"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Tags"
msgstr "Thẻ"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
#: model:ir.model,name:project_todo.model_project_task
msgid "Task"
msgstr "Nhiệm vụ"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
msgid "To-Do"
msgstr "Việc cần làm"

#. module: project_todo
#: model:ir.ui.menu,name:project_todo.menu_todo_todos
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do"
msgstr "Việc cần làm"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "To-do Title"
msgstr "Tiêu đề việc cần làm"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do..."
msgstr "Việc cần làm..."

#. module: project_todo
#: model:ir.actions.act_window,name:project_todo.project_task_action_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_activity
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "To-dos"
msgstr "Việc cần làm"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Todos"
msgstr "Việc cần làm"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_control_panel.xml:0
msgid "Toggle chatter"
msgstr "Bật/tắt cửa sổ trò chuyện"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "Type Here..."
msgstr "Nhập vào đây..."

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Untitled to-do"
msgstr "Việc cần làm không có tiêu đề"

#. module: project_todo
#: model:ir.model,name:project_todo.model_res_users
msgid "User"
msgstr "Người dùng"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Using the editor"
msgstr "Sử dụng trình soạn thảo"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Welcome %s!"
msgstr "Chào mừng %s!"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Who has access to what?"
msgstr "Ai có thể truy cập vào thông tin nào?"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/wizard/mail_activity_todo_create.py:0
msgid "Your to-do has been successfully added to your pipeline."
msgstr "Việc cần làm đã được thêm thành công vào lộ trình của bạn."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "convert-todo"
msgstr "convert-todo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "e.g. Send Invitations"
msgstr "VD: Gửi lời mời"

#. module: project_todo
#: model:ir.actions.server,name:project_todo.project_task_preload_action_todo
msgid "menu load To-dos"
msgstr "menu load To-dos"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "todo-access"
msgstr "todo-access"
