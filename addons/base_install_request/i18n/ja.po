# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_install_request
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_install_request
#: model:mail.template,body_html:base_install_request.mail_template_base_install_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"/> has requested to activate the <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"/> module. This module is included in your subscription. It has <span style=\"color: #875A7B; font-weight: bold;\">no extra cost</span>, but an administrator role is required to activate it.\n"
"        <br/><br/>\n"
"        </p><blockquote data-o-mail-quote-node=\"1\" data-o-mail-quote=\"1\">\n"
"            <t t-out=\"object.body_html\"/>\n"
"        </blockquote>\n"
"        <br data-o-mail-quote=\"1\"/><br data-o-mail-quote=\"1\"/>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/odoo/{{ object.module_id.id }}/action-base_install_request.action_base_module_install_review?menu_id={{ ctx['menu_id'] }}\">Review Request</a>\n"
"        <br/><br/>\n"
"        Thanks,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    \n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        こんにちは。\n"
"        <br/><br/>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"/> が <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"/> モジュールの有効化を要求しました。 このモジュールはお客様のサブスクリプションに含まれています。 <span style=\"color: #875A7B; font-weight: bold;\">追加コストなし</span>ですが、有効化するには管理者である必要があります。\n"
"        <br/><br/>\n"
"        </p><blockquote data-o-mail-quote-node=\"1\" data-o-mail-quote=\"1\">\n"
"            <t t-out=\"object.body_html\"/>\n"
"        </blockquote>\n"
"        <br data-o-mail-quote=\"1\"/><br data-o-mail-quote=\"1\"/>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/odoo/{{ object.module_id.id }}/action-base_install_request.action_base_module_install_review?menu_id={{ ctx['menu_id'] }}\">要求を確認</a>\n"
"        <br/><br/>\n"
"        宜しくお願いします。\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    \n"
"</div>\n"
"            "

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/models/ir_module_module.py:0
msgid "Activation Request of \"%s\""
msgstr "\"%s\"の有効化要求"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__body_html
msgid "Body"
msgstr "表示文"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Cancel"
msgstr "キャンセル"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_uid
msgid "Created by"
msgstr "作成者"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_date
msgid "Created on"
msgstr "作成日"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_ids
msgid "Depending Apps"
msgstr "依存アプリ"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__display_name
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__display_name
msgid "Display Name"
msgstr "表示名"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__id
msgid "ID"
msgstr "ID"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Install App"
msgstr "アプリをインストール"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: base_install_request
#: model:mail.template,name:base_install_request.mail_template_base_install_request
msgid "Mail: Install Request"
msgstr "メール: インストール要求"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_ir_module_module
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__module_id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_id
msgid "Module"
msgstr "モジュール"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_request
msgid "Module Activation Request"
msgstr "モジュール有効化リクエスト"

#. module: base_install_request
#: model:mail.template,subject:base_install_request.mail_template_base_install_request
msgid "Module Activation Request for \"{{ object.module_id.shortdesc }}\""
msgstr " \"{{ object.module_id.shortdesc }}\"用モジュール有効化リクエスト"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_review
msgid "Module Activation Review"
msgstr "モジュール有効化レビュー"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__modules_description
msgid "Modules Description"
msgstr "モジュール説明"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "No extra cost, this application is free."
msgstr "追加の費用はかかりません。このアプリケーションは無料です。"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
msgid "No module selected."
msgstr "モジュールが選択されていません。"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.ir_module_module_view_kanban
msgid "Request Access"
msgstr "アクセス要求"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Request Activation"
msgstr "有効化要求"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_ids
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Send to:"
msgstr "以下に送信:"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_description
msgid "The following apps will be installed:"
msgstr "次のアプリがインストールされます:"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
msgid "The module is already installed."
msgstr "モジュールはすでにインストール済です。"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"This app is included in your subscription. It's free to activate, but only "
"an administrator can do it. Fill this form to send an activation request."
msgstr ""
"このアプリはサブスクリプションに含まれています。有効化は無料ですが、管理者のみが行うことができます。有効化の要求を送信するには、このフォームに記入して下さい。"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_id
msgid "User"
msgstr "ユーザ"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Why do you need this module?"
msgstr "なぜこのモジュールが必要ですか？"

#. module: base_install_request
#: model:ir.actions.act_window,name:base_install_request.action_base_module_install_review
msgid "You are about to install an extra application"
msgstr "追加のアプリケーションをインストールしようとしています。"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
msgid "Your request has been successfully sent"
msgstr "要求が正常に送信されました。"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"e.g. I'd like to use the SMS Marketing module to organize the promotion of "
"our internal events, and exhibitions. I need access for 3 people of my team."
msgstr "例:社内イベントや展示会のプロモーションのためにSMSマーケティングモジュールを使用したいです。私のチームの3人名のアクセスが必要です。"
