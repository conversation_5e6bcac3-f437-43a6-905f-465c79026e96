# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_tbai_pos
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-21 14:26+0000\n"
"PO-Revision-Date: 2025-05-21 14:26+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es_edi_tbai_pos
#. odoo-javascript
#: code:addons/l10n_es_edi_tbai_pos/static/src/app/add_tbai_refund_reason_popup/add_tbai_refund_reason_popup.xml:0
msgid "Additional Refund Information"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,help:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_refund_reason
msgid ""
"BOE-A-1992-28740. Ley 37/1992, de 28 de diciembre, del Impuesto sobre el "
"Valor Añadido. Artículo 80. Modificación de la base imponible."
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model,name:l10n_es_edi_tbai_pos.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,field_description:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_refund_reason
msgid "Invoice Refund Reason Code (TicketBai)"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,help:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_chain_index
msgid ""
"Invoice index in chain, set if and only if an in-chain XML was submitted and"
" did not error"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,field_description:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_post_document_id
msgid "L10N Es Tbai Post Document"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#. odoo-javascript
#: code:addons/l10n_es_edi_tbai_pos/static/src/app/add_tbai_refund_reason_popup/add_tbai_refund_reason_popup.xml:0
msgid "Ok"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#. odoo-python
#: code:addons/l10n_es_edi_tbai_pos/models/pos_order.py:0
msgid "Please create an invoice for an amount over %s."
msgstr ""

#. module: l10n_es_edi_tbai_pos
#. odoo-python
#: code:addons/l10n_es_edi_tbai_pos/models/pos_order.py:0
msgid "Please invoice the refund as the linked order has been invoiced."
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model,name:l10n_es_edi_tbai_pos.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model,name:l10n_es_edi_tbai_pos.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai_pos.selection__pos_order__l10n_es_tbai_refund_reason__r1
msgid "R1: Art. 80.1, 80.2, 80.6 and rights founded error"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai_pos.selection__pos_order__l10n_es_tbai_refund_reason__r2
msgid "R2: Art. 80.3"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai_pos.selection__pos_order__l10n_es_tbai_refund_reason__r3
msgid "R3: Art. 80.4"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai_pos.selection__pos_order__l10n_es_tbai_refund_reason__r4
msgid "R4: Art. 80 - other"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai_pos.selection__pos_order__l10n_es_tbai_refund_reason__r5
msgid "R5: Factura rectificativa en facturas simplificadas"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai_pos.view_pos_order_form_inherit_l10n_es_pos_tbai
msgid "Send to TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai_pos.selection__pos_order__l10n_es_tbai_state__sent
msgid "Sent"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai_pos.view_pos_order_form_inherit_l10n_es_pos_tbai
msgid "TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,field_description:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_post_file_name
msgid "TicketBAI Post Attachment Name"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,field_description:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_post_file
msgid "TicketBAI Post File"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#. odoo-javascript
#: code:addons/l10n_es_edi_tbai_pos/static/src/app/add_tbai_refund_reason_popup/add_tbai_refund_reason_popup.xml:0
msgid "TicketBAI Refund Reason:"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,field_description:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_chain_index
msgid "TicketBAI chain index"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,field_description:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_is_required
msgid "TicketBAI required"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields,field_description:l10n_es_edi_tbai_pos.field_pos_order__l10n_es_tbai_state
msgid "TicketBAI status"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#. odoo-javascript
#: code:addons/l10n_es_edi_tbai_pos/static/src/overrides/components/order_receipt/order_receipt.xml:0
msgid "TicketBai QR Code"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai_pos.selection__pos_order__l10n_es_tbai_state__to_send
msgid "To Send"
msgstr ""

#. module: l10n_es_edi_tbai_pos
#. odoo-python
#: code:addons/l10n_es_edi_tbai_pos/models/pos_order.py:0
msgid "You cannot invoice a refund whose linked order hasn't been invoiced."
msgstr ""
