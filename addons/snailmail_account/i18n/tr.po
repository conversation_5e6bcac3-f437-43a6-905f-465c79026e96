# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>ih Melik Sonmez, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: snailmail_account
#. odoo-python
#: code:addons/snailmail_account/wizard/account_move_send_batch_wizard.py:0
msgid "(Stamps: %s)"
msgstr ""

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_account_move_send
msgid "Account Move Send"
msgstr "Hesap Hareketi Yollandı"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_account_move_send_batch_wizard
msgid "Account Move Send Batch Wizard"
msgstr "Hesap Hareketi Toplu Gönderme Sihirbazı"

#. module: snailmail_account
#. odoo-python
#: code:addons/snailmail_account/models/account_move_send.py:0
msgid "Check Invoice(s)"
msgstr ""

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_res_partner__invoice_sending_method
#: model:ir.model.fields,field_description:snailmail_account.field_res_users__invoice_sending_method
msgid "Invoice sending"
msgstr "Fatura gönderme"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_move_send_batch_wizard__send_by_post_stamps
msgid "Send By Post Stamps"
msgstr "Posta Pulu ile Gönderin"

#. module: snailmail_account
#. odoo-python
#: code:addons/snailmail_account/models/account_move_send.py:0
msgid ""
"The partners on the following invoices have no valid address, so those "
"invoices will not be sent: %s"
msgstr ""

#. module: snailmail_account
#. odoo-python
#: code:addons/snailmail_account/models/account_move_send.py:0
msgid "View Invoice(s)"
msgstr ""

#. module: snailmail_account
#. odoo-python
#: code:addons/snailmail_account/controllers/portal.py:0
#: model:ir.model.fields.selection,name:snailmail_account.selection__res_partner__invoice_sending_method__snailmail
msgid "by Post"
msgstr ""
