<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">

    <record model="survey.survey" id="survey_feedback">
        <field name="title">Feedback Form</field>
        <field name="access_token">b135640d-14d4-4748-9ef6-344ca256531e</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="access_mode">public</field>
        <field name="users_can_go_back" eval="True" />
        <field name="questions_layout">page_per_section</field>
        <field name="description" type="html">
<p>This survey allows you to give a feedback about your experience with our products.
    Filling it helps us improving your experience.</p></field>
    </record>

    <!-- Page1: general information -->
    <record model="survey.question" id="survey_feedback_p1">
        <field name="title">About you</field>
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">1</field>
        <field name="question_type" eval="False" />
        <field name="is_page" eval="True" />
        <field name="description" type="html">
<p>This section is about general information about you. Answering them helps qualifying your answers.</p></field>
    </record>
    <record model="survey.question" id="survey_feedback_p1_q1">
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">2</field>
        <field name="title">Where do you live?</field>
        <field name="question_type">char_box</field>
        <field name="question_placeholder">Brussels</field>
        <field name="constr_mandatory" eval="False"/>
    </record>
    <record model="survey.question" id="survey_feedback_p1_q2">
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">3</field>
        <field name="title">When is your date of birth?</field>
        <field name="question_type">date</field>
        <field name="constr_mandatory" eval="False"/>
    </record>
    <record model="survey.question" id="survey_feedback_p1_q3">
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">4</field>
        <field name="title">How frequently do you buy products online?</field>
        <field name="question_type">simple_choice</field>
        <field name="comments_allowed" eval="True"/>
        <field name="comment_count_as_answer" eval="True"/>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p1_q3_sug1">
        <field name="question_id" ref="survey_feedback_p1_q3"/>
        <field name="sequence">1</field>
        <field name="value">Once a day</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p1_q3_sug2">
        <field name="question_id" ref="survey_feedback_p1_q3"/>
        <field name="sequence">2</field>
        <field name="value">Once a week</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p1_q3_sug3">
        <field name="question_id" ref="survey_feedback_p1_q3"/>
        <field name="sequence">3</field>
        <field name="value">Once a month</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p1_q3_sug4">
        <field name="question_id" ref="survey_feedback_p1_q3"/>
        <field name="sequence">4</field>
        <field name="value">Once a year</field>
    </record>
    <record model="survey.question" id="survey_feedback_p1_q4">
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">5</field>
        <field name="title">How many times did you order products on our website?</field>
        <field name="question_type">numerical_box</field>
        <field name="constr_mandatory" eval="True"/>
    </record>

    <!-- Page 2 -->
    <record model="survey.question" id="survey_feedback_p2">
        <field name="title">About our ecommerce</field>
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">6</field>
        <field name="is_page" eval="True" />
        <field name="question_type" eval="False" />
        <field name="description" type="html">
<p>This section is about our eCommerce experience itself.</p></field>
    </record>
    <record model="survey.question" id="survey_feedback_p2_q1">
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">7</field>
        <field name="title">Which of the following words would you use to describe our products?</field>
        <field name="question_type">multiple_choice</field>
        <field name="constr_mandatory" eval="True"/>
        <field name="comments_allowed" eval="True"/>
        <field name="comment_count_as_answer" eval="False"/>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug1">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">1</field>
        <field name="value">High quality</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug2">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">2</field>
        <field name="value">Useful</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug3">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">3</field>
        <field name="value">Unique</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug4">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">4</field>
        <field name="value">Good value for money</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug5">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">5</field>
        <field name="value">Overpriced</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug6">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">6</field>
        <field name="value">Impractical</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug7">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">7</field>
        <field name="value">Ineffective</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q1_sug8">
        <field name="question_id" ref="survey_feedback_p2_q1"/>
        <field name="sequence">8</field>
        <field name="value">Poor quality</field>
    </record>
    <record model="survey.question" id="survey_feedback_p2_q2">
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">8</field>
        <field name="title">What do you think about our new eCommerce?</field>
        <field name="question_type">matrix</field>
        <field name="matrix_subtype">multiple</field>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_col1">
        <field name="question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">1</field>
        <field name="value">Totally disagree</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_col2">
        <field name="question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">2</field>
        <field name="value">Disagree</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_col3">
        <field name="question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">3</field>
        <field name="value">Agree</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_col4">
        <field name="question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">4</field>
        <field name="value">Totally agree</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_row1">
        <field name="matrix_question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">1</field>
        <field name="value">The new layout and design is fresh and up-to-date</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_row2">
        <field name="matrix_question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">2</field>
        <field name="value">It is easy to find the product that I want</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_row3">
        <field name="matrix_question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">3</field>
        <field name="value">The tool to compare the products is useful to make a choice</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_row4">
        <field name="matrix_question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">4</field>
        <field name="value">The checkout process is clear and secure</field>
    </record>
    <record model="survey.question.answer" id="survey_feedback_p2_q2_row5">
        <field name="matrix_question_id" ref="survey_feedback_p2_q2"/>
        <field name="sequence">5</field>
        <field name="value">I have added products to my wishlist</field>
    </record>
    <record model="survey.question" id="survey_feedback_p2_q3">
        <field name="survey_id" ref="survey_feedback" />
        <field name="sequence">9</field>
        <field name="title">Do you have any other comments, questions, or concerns?</field>
        <field name="question_type">text_box</field>
        <field name="constr_mandatory" eval="False"/>
    </record>

</data></odoo>
