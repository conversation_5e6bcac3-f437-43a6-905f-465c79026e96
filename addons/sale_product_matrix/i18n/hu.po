# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_matrix
# 
# Translators:
# krnkris, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: gezza <<EMAIL>>, 2025\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_template__product_add_mode
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order_line__product_add_mode
msgid "Add product mode"
msgstr "Termék mód hozzáadása"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,help:sale_product_matrix.field_product_template__product_add_mode
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order_line__product_add_mode
msgid ""
"Configurator: choose attribute values to add the matching product variant to the order.\n"
"Grid: add several variants at once from the grid of attribute values"
msgstr ""
"Konfigurátor: válasszon termékjellemző értékeket a termékváltozat rendeléshez adásához.\n"
"Táblázat: adjon egyszerre több termékváltozatot a termékjellemzők táblázatából"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "Táblázat terméksablon"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_update
msgid "Grid Update"
msgstr "Táblázat frissítés"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid
msgid "Matrix local storage"
msgstr "Mátrix helyi tárolás"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__matrix
msgid "Order Grid Entry"
msgstr "Rendelés rács tétel"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__report_grids
msgid "Print Variant Grids"
msgstr "Variáns táblázatok nyomtatása"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_product_template
msgid "Product"
msgstr "Termék"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__configurator
msgid "Product Configurator"
msgstr "Termék konfigurátor"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_sale_order
msgid "Sales Order"
msgstr "Vevői rendelés"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_sale_order_line
msgid "Sales Order Line"
msgstr "Vevői rendelés sor"

#. module: sale_product_matrix
#: model_terms:ir.ui.view,arch_db:sale_product_matrix.product_template_grid_view_form
msgid "Sales Variant Selection"
msgstr "Értékesítési változat kiválasztás"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid
msgid ""
"Technical local storage of grid. \n"
"If grid_update, will be loaded on the SO.\n"
"If not, represents the matrix to open."
msgstr ""
"A táblázat technikai helyi tárolása. \n"
"Ha grid_update, akkor betöltésre kerül a vevői rendelésbe.\n"
"Ha más, akkor a megjelenítendő mátrixot jelenti."

#. module: sale_product_matrix
#. odoo-python
#: code:addons/sale_product_matrix/models/sale_order.py:0
msgid ""
"You cannot change the quantity of a product present in multiple sale lines."
msgstr ""
"Nem lehet megváltoztatni a több rendelési soron is szereplő termékek "
"mennyiségét."
