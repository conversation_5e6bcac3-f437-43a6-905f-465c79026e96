# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil O<PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# <PERSON> Vacaru, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> Vacaru, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "#{timesheet.employee_id.name}"
msgstr "#{timesheet.employee_id.name}"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(effective)s %(uom_name)s"
msgstr "%(effective)s %(uom_name)s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(effective)s / %(allocated)s %(uom_name)s"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(effective)s / %(allocated)s %(uom_name)s (%(success_rate)s%%)"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(exceeding_hours)s %(uom_name)s (+%(exceeding_rate)s%%)"
msgstr ""

#. module: hr_timesheet
#. odoo-javascript
#: code:addons/hr_timesheet/static/src/components/time_hour_field/time_hour_field.js:0
msgid "%(hours)sh"
msgstr ""

#. module: hr_timesheet
#. odoo-javascript
#: code:addons/hr_timesheet/static/src/components/time_hour_field/time_hour_field.js:0
msgid "%(hours)sh%(minutes)s"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(name)s's Timesheets"
msgstr "Foaia de pontaj a %(name)s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the project "
"'%(project_name)s' linked to the timesheet."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr "(%(sign)s%(hours)s:%(minutes)s rămase)"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "(%s days remaining)"
msgstr "(%s zile rămase)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "(incl."
msgstr "(incl."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "1 day"
msgstr "1 zi"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "2 hours"
msgstr "2 ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "2021-09-01"
msgstr "2021-09-01"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr "<b class=\"tip_title\">Sfat: Înregistrați-vă foiele de timp mai rapid</b>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar me-1\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar me-1\" role=\"img\" aria-label=\"Data\" title=\"Data\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<i class=\"fa fa-print\"/> View Details"
msgstr "<i class=\"fa fa-print\"/> Vizualizare detalii"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr "<span class=\"o_stat_text\">Pontaje </span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "<span class=\"text-nowrap\">Time Spent on Sub-tasks:</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid ""
"<span invisible=\"not has_timesheet\">\n"
"                        You cannot delete employees who have timesheets.\n"
"                        <span invisible=\"not has_active_employee\">\n"
"                            You can either archive these employees or first delete all of their timesheets.\n"
"                        </span>\n"
"                        <span invisible=\"has_active_employee\" groups=\"hr_timesheet.group_hr_timesheet_approver\">\n"
"                            Please first delete all of their timesheets.\n"
"                        </span>\n"
"                    </span>\n"
"                    <span invisible=\"has_timesheet\">\n"
"                        Are you sure you want to delete these employees?\n"
"                    </span>"
msgstr ""
"<span invisible=\"not has_timesheet\">\n"
"                      Nu puteți șterge angajați cu fișe de pontaj.\n"
"                     <span invisible=\"not has_active_employee\">\n"
"                       Aveți posibilitatea să arhivați acești angajați, altfel, pentru a continua, ștergeți fișele de pontaj apoi reîncercați.\n"
"                       </span>\n"
"                         <span invisible=\"has_active_employee\" groups=\"hr_timesheet.group_hr_timesheet_approver\">\n"
"                           Vă rog, ștergeți toate foile de pontaj ale angajatului.\n"
"                          </span>\n"
"</span>\n"
"<span invisible=\"has_timesheet\">\n"
"            Sunteți siguri că doriți să ștergeți acești angajați?\n"
"</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr "<span style=\"margin-right: 15px;\">Total (Zile)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr "<span style=\"margin-right: 15px;\">Total (Ore)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr "<span>Dată</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr "<span>Descriere</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Employee</span>"
msgstr "<span>Angajat</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Time Spent</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr "<strong>Progres:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Time Remaining: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Time recorded on sub-tasks: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Total Days: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Total Hours: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Total Time Spent: </strong>"
msgstr ""

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "All"
msgstr "Tot"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr "Toate pontajele"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allocated_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__allocated_time
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__allocated_hours
msgid "Allocated Time"
msgstr "Timp Alocat"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__amount
msgid "Amount"
msgstr "Sumă"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/__init__.py:0
msgid "Analysis"
msgstr "Analiză"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "Înregistrare analitică"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linie analitică"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Aplicabilități ale planului analitic"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Analyze the projects and tasks on which your employees spend their time.<br>\n"
"                Evaluate which part is billable and what costs it represents."
msgstr ""
"Analizați proiectele și sarcinile la care angajații dvs. petrec timpul. <br>\n"
"Evaluați care parte este facturabilă și ce costuri reprezintă."

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_allow
msgid "Approver Reminder"
msgstr "Reminder aprobator"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Archive Employees"
msgstr "Arhivează angajați"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Audrey Peterson"
msgstr "Audrey Peterson"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Average of Progress"
msgstr "Media de progres"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr "După angajat"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr "După proiect"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr "După sracină"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Call client and discuss project"
msgstr "Întalnire cu clientul sugerată"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Colaboratori în proiectul partajat"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__company_id
msgid "Company"
msgstr "Companie"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "Configurare"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
msgid "Confirmation"
msgstr "Confirmare"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__currency_id
msgid "Currency"
msgstr "Monedă"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__date
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Date"
msgstr "Dată"

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__days
msgid "Days / Half-Days"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid "Days Remaining"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_active
#: model:ir.model.fields,help:hr_timesheet.field_project_task__analytic_account_active
msgid "Deactivate the account."
msgstr "Dezactivați contul."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""
"Valoarea implicită a proiectului pentru fișa de pontaj generată din tipul de"
" concediu."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_account_analytic_line__employee_id
msgid ""
"Define an 'hourly cost' on the employee to track the cost of their time."
msgstr ""
"Definiți un 'cost orar' pentru angajat pentru a urmări costul timpului lui."

#. module: hr_timesheet
#: model:ir.actions.server,name:hr_timesheet.unlink_employee_action
msgid "Delete"
msgstr "Șterge"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Delete Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_account_analytic_line__milestone_id
#: model:ir.model.fields,help:hr_timesheet.field_timesheets_analysis_report__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""
"Livrați serviciile dumneavoastră automat atunci când o etapă este atinsă "
"prin legarea acesteia la un element din comanda de vânzare."

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Department"
msgstr "Departament"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Describe your activity"
msgstr "Descrieți-vă activitatea"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__name
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Description"
msgstr "Descriere"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Discard"
msgstr "Abandonează"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domeniu"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr "Ore efective"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Employee"
msgstr "Angajat"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_hr_employee_delete_wizard
msgid "Employee Delete Wizard"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "Memento Angajat"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
msgid "Employee Termination"
msgstr "Terminare Angajat"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
msgid "Employees' Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_method
msgid "Encoding Method"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "Extra Time"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Generate timesheets for validated time off requests and public holidays"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr "Rutare HTTP"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__hours
msgid "Hours / Minutes"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__total_hours_spent
msgid "Hours By Task (Including Subtasks)"
msgstr "Ore după sarcină (inclusiv sub-sarcini)"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
msgid "Internal"
msgstr "Intern"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr "Proiect intern"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "Invalid operator: %s"
msgstr "Operator nevalid: %s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "Invalid value: %s"
msgstr "Valoare nevalidă: %s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Last Month"
msgstr "Ultima lună"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Last Quarter"
msgstr "Ultimul trimestru"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Last Week"
msgstr "Ultima săptămână"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Last Year"
msgstr "Ultimul an"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
msgid "Log time on tasks"
msgstr "Jurnal de timp pe sarcini"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__manager_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__manager_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Manager"
msgstr "Manager"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
msgid "Meeting"
msgstr "Întâlnire"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr "Meniu"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__message_partner_ids
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__message_partner_ids
msgid "Message Partner"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__milestone_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__milestone_id
msgid "Milestone"
msgstr "Etapa de referință"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Department's Updates"
msgstr "Actualizările departamentului meu"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Team's Updates"
msgstr "Actualizările echipei mele"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr "Pontajele mele"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Newest"
msgstr "Cele mai noi"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "No Parent Task"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "No Task"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr "Nu s-au găsit activități. Să începem una nouă!"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No data yet!"
msgstr "Nu există date încă!"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "None"
msgstr "Fără"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid "Number of allocated hours minus the number of hours spent."
msgstr "Numărul de ore alocate minus numărul de ore petrecute."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Ok"
msgstr "Ok"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__overtime
msgid "Overtime"
msgstr "Suplimentare"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__parent_task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__parent_task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Parent Task"
msgstr "Sarcină părinte"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__partner_id
msgid "Partner"
msgstr "Partener"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unitate de măsură produs"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
msgid "Progress"
msgstr "În desfășurare"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Project"
msgstr "Proiect"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__account_id
msgid "Project Account"
msgstr "Contul de proiect"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr "Unitate de timp Proiect"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_update
msgid "Project Update"
msgstr "Actualizare proiect"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr "Fișele de pontaj ale proiectului"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_from_employee
msgid "Record a new activity"
msgstr "Înregistrați activitate"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid ""
"Record your timesheets in an instant by pressing Shift + the corresponding "
"hotkey to add 15min to your projects."
msgstr ""
"Înregistrați fișele de pontaj într-un timp instant prin apăsarea tastei "
"Shift + tasta de acces rapid corespunzătoare pentru a adăuga 15 minute la "
"proiectele dvs."

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "Raportare"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "Research and Development"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Research and Development/New Portal System"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
msgid "Review all timesheets related to your projects"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Description"
msgstr "Căutare in descriere"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Employee"
msgstr "Căutare în Angajat"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Parent Task"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Project"
msgstr "Căutare în proiect"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Task"
msgstr "Căutare in Sarcină"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "See Timesheets"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "See timesheet entries"
msgstr "Vedeți intrările din fișa de pontaj"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Send a periodical email reminder to timesheets approvers that still have "
"timesheets to validate"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Send a periodical email reminder to timesheets users that still have "
"timesheets to encode"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "Setări"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_report_subtask
msgid "Sub-Task of '"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Time Spent"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Task"
msgstr "Sarcină"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr "Fișe de pontaj ale sarcinii"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Analiza Sarcinilor"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
msgid "The Internal Project of a company should be in that company."
msgstr "Proiectul intern al unei companii ar trebui să fie în acea companie."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"The project, the task and the analytic accounts of the timesheet must belong"
" to the same company."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr "Nu există fișe de pontaj"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid ""
"These projects have some timesheet entries referencing them. Before removing"
" these projects, you have to remove these timesheet entries."
msgstr ""
"Aceste proiecte au unele intrări de fișe de pontaj care le referă. Înainte "
"de a elimina aceste proiecte, trebuie să eliminați aceste intrări din fișa "
"de pontaj."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid ""
"These tasks have some timesheet entries referencing them. Before removing "
"these tasks, you have to remove these timesheet entries."
msgstr ""
"Aceste sarcini au unele intrări de fisă de pontaj care le referă. Înainte de"
" a elimina aceste sarcini, trebuie să eliminați aceste intrări din fișa de "
"pontaj."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "This Month"
msgstr "Luna aceasta"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "This Quarter"
msgstr "Acest trimestru"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "This Week"
msgstr "Săpt. curentă"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "This Year"
msgstr "Anul acesta"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "This operator %s is not supported in this search method."
msgstr "Acest operator %s nu este acceptat în această metodă de căutare."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid ""
"This project has some timesheet entries referencing it. Before removing this"
" project, you have to remove these timesheet entries."
msgstr ""
"Acest proiect are câteva intrări de fișă de timp referitoare la el. Înainte "
"de a elimina acest proiect, trebuie să eliminați aceste intrări din fișa de "
"timp."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid ""
"This task cannot be private because there are some timesheets linked to it."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid ""
"This task has some timesheet entries referencing it. Before removing this "
"task, you have to remove these timesheet entries."
msgstr ""
"Această sarcină are câteva intrări de fișă de timp care o referă. Înainte de"
" a elimina această sarcină, trebuie să eliminați aceste intrări din fișa de "
"pontaj."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""
"Acesta va seta unitatea de măsură folosită în proiecte și sarcini.\n"
"Dacă folosiți fișa de pontaj asociată proiectelor, nu uitați să setați unitatea de masură corectă la angajații dumneavoastră."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr "Codificare Timp"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr "Concediu"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Time Remaining"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours_percentage
msgid "Time Remaining Percentage"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Time Remaining on SO"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Time Spent"
msgstr "Timp consumat"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__subtask_effective_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Time Spent on Sub-Tasks"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time Spent on Sub-tasks"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
#: model:ir.model.fields,help:hr_timesheet.field_report_project_task_user__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""
"Timpul petrecut pentru sub-sarcinile (și propriile sub-sarcini) ale acestei "
"sarcini."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task and its sub-tasks (and their own sub-tasks)."
msgstr ""
"Timpul petrecut pentru această sarcină și sub-sarcinile sale (și propriile "
"sub-sarcini)."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_report_project_task_user__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr ""
"Timpul petrecut pentru această sarcină, inclusiv sub-sarcinile acesteia."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr "Unitate de timp utilizată pentru a vă înregistra fișele de pontaj"

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__account_analytic_applicability__business_domain__timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr "Fișă de pontaj"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr "Activități fișă de pontaj"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_list
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr "Costuri pontaj"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Unitate de codificare a fișei de pontaj"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_report_search
msgid "Timesheet Report"
msgstr "Raport fișă de pontaj"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__timesheet_time
msgid "Timesheet Time"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task_timesheets
#: model:ir.embedded.actions,name:hr_timesheet.project_embedded_action_timesheets
#: model:ir.embedded.actions,name:hr_timesheet.project_embedded_action_timesheets_dashboard
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheets"
msgstr "Fișe de pontaj"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "Timesheets - %s"
msgstr "Fișe de pontaj - %s"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
msgid "Timesheets 80%"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_project_filter_inherit_timesheet
msgid "Timesheets >100%"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
msgid "Timesheets Analysis"
msgstr "Analiza fișelor de pontaj"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Raport de analiză a fișelor de pontaj"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr "Control fișe de pontaj"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr "Fișe de pontaj după angajat"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr "Fișe de pontaj după proiect"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr "Fișe de pontaj după sarcină"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "Timesheets cannot be created on a private task."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"Timesheets must be created with an active employee in the selected "
"companies."
msgstr ""
"Fișele de pontaj trebuie create cu un angajat activ în companiile selectate."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"Timesheets must be created with at least an active analytic account defined "
"in the plan '%(plan_name)s'."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
msgid "Timesheets of %(name)s"
msgstr "Fișă de pontaj a %(name)s"

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr "Sfat: înregistrați-vă fișele de pontaj mai rapid"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid ""
"To use the timesheets feature, you need an analytic account for your "
"project. Please set one up in the plan '%(plan_name)s' or turn off the "
"timesheets feature."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Today"
msgstr "Astăzi"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_list
msgid "Total"
msgstr "Total"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Allocated Time"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid "Total Days Spent"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Time Spent"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr "Total:"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Urmăriți orele de lucru pe proiecte în fiecare zi și facturați acest timp "
"clienților dvs."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
msgid "Training"
msgstr "Instructaj"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__user_id
msgid "User"
msgstr "Utilizator"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr "Utilizator: toate fișele de pontaj"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr "Utilizator: doar propriile fișe de pontaj"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "View Details"
msgstr "Vizualizare detalii"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_from_employee
msgid ""
"You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required."
msgstr ""
"Puteți înregistra și urmări orele de lucru pe proiect în fiecare zi. Fiecare\n"
"                    dată petrecută pe un proiect va deveni o cheltuială și poate fi re-facturat\n"
"                    clienților dacă este necesar."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "You cannot access timesheets that are not yours."
msgstr "Nu puteți accesa fișele de pontaj care nu sunt ale dvs."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
msgid "You cannot delete employees who have timesheets."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive analytic account.<br/>\n"
"                            Please switch to another account, or reactivate the current one to timesheet on the project."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive "
"analytic account.<br/> Please change this account, or reactivate the current"
" one to timesheet on the project."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "You cannot set an archived employee on existing timesheets."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
msgid "for"
msgstr "pentru"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "for the"
msgstr "pentru"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"on\n"
"                            <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"on\n"
"                        <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""
