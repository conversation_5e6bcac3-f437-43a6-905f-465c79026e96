# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp
# 
# Translators:
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2024
# <PERSON>, 2024
# Will Sensors, 2024
# ievaput<PERSON> <ievai.putnin<PERSON>@gmail.com>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
msgid "%(browser)s on %(platform)s"
msgstr ""

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_wizard
msgid "2-Factor Setup Wizard"
msgstr ""

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
msgid "2-Factor authentication is now enabled."
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-md-none d-block\">Or install an authenticator app</span>\n"
"                                        <span class=\"d-none d-md-block\">Install an authenticator app on your mobile device</span>"
msgstr ""
"<span class=\"d-md-none d-block\">Vai uzstādiet autentifikācijas lietotni</span>\n"
"                                        <span class=\"d-none d-md-block\">Uzstādiet autentifikācijas lietotni Jūsu mobilajā iekārtā</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-none d-md-block\">When requested to do so, scan the barcode below</span>\n"
"                                    <span class=\"d-block d-md-none\">When requested to do so, copy the key below</span>"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"text-muted\">Popular ones include Authy, Google Authenticator "
"or the Microsoft Authenticator.</span>"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">This account is "
"protected!</span>"
msgstr ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">Šis konts ir "
"aizsargāts!</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">Your account is "
"protected!</span>"
msgstr ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">Jūsu konts ir "
"aizsargāts!</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Activate"
msgstr "Aktivizēt"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Added On"
msgstr "Pievienots"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Are you sure? The user may be asked to enter two-factor codes again on those"
" devices"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Are you sure? You may be asked to enter two-factor codes again on those "
"devices"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Authentication Code"
msgstr "Autentifikācijas kods"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_device
msgid "Authentication Device"
msgstr "Autentifikācijas ierīce"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Authenticator App Setup"
msgstr "Autentifikācijas lietotnes uzstādīšana"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cancel"
msgstr "Atcelt"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cannot scan it?"
msgstr "Nevarat to noskenēt?"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Click on this link to open your authenticator app"
msgstr "Piemiedziet uz šo saiti, lai atvērt Jūsu autentifikācijas lietotni"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__create_date
msgid "Creation Date"
msgstr "Izveidošanas datums"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__name
msgid "Description"
msgstr "Apraksts"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Device"
msgstr "Ierīce"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Disable 2FA"
msgstr "Atspējot 2FA"

#. module: auth_totp
#: model:ir.actions.server,name:auth_totp.action_disable_totp
msgid "Disable two-factor authentication"
msgstr "Atspējot divfaktoru autentifikāciju"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__display_name
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__display_name
msgid "Display Name"
msgstr "Nosaukums"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Don't ask again on this device"
msgstr "Neprasīt atkārtoti šajā ierīcē"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Enable 2FA"
msgstr "Iespējot 2FA"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enter your six-digit code below"
msgstr "Ievadiet Jūsu sešu ciparu kodu zemāk"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__expiration_date
msgid "Expiration Date"
msgstr "Derīguma beigu datums"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__id
msgid "ID"
msgstr "ID"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
msgid "Invalid authentication code format."
msgstr "Nederīgs autentifikācijas koda formāts."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_uid
msgid "Last Updated by"
msgstr "Pēdējo reizi atjaunoja"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_date
msgid "Last Updated on"
msgstr "Pēdējās izmaiņas"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Learn More"
msgstr "Uzzināt vairāk"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Log in"
msgstr "Ienākt"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Look for an \"Add an account\" button"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Apple Store"
msgstr "Pakalpojumā Apple Store"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Google Play"
msgstr "Pakalpojumā Google Play"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__qrcode
msgid "Qrcode"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke"
msgstr "Atsaukt"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke All"
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__scope
msgid "Scope"
msgstr "Darbības joma"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__secret
msgid "Secret"
msgstr ""

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
msgid "The verification code should only contain numbers"
msgstr "Verifikācijas kodam būtu jāsatur tikai skaitļi"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"To login, enter below the six-digit authentication code provided by your Authenticator app.\n"
"                        <br/>"
msgstr ""
"Lai pierakstīties, ievadiet zemās sešu ciparu autentifikācijas kodu norādīto Jūsu autentifikācijas lietotnē.\n"
"                        <br/>"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_secret
msgid "Totp Secret"
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_trusted_device_ids
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Devices"
msgstr "Uzticamās ierīces"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
msgid "Two-Factor Authentication Activation"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Two-factor Authentication"
msgstr "Divfaktoru autentifikācija"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_enabled
msgid "Two-factor authentication"
msgstr "Divfaktoru autentifikācija"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Disabled"
msgstr "Divfaktoru autentifikācija atspējota"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Enabled"
msgstr "Divfaktoru autentifikācija iespējota"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
msgid "Two-factor authentication already enabled"
msgstr "Divfaktoru autentifikācija jau ir iespējota"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
msgid "Two-factor authentication can only be enabled for yourself"
msgstr "Tikai Jūs varat iespējot divfaktoru autentifikāciju"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
msgid "Two-factor authentication disabled for the following user(s): %s"
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__url
msgid "Url"
msgstr "Url"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_res_users
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__user_id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__user_id
msgid "User"
msgstr "Lietotājs"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__code
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Verification Code"
msgstr "Verifikācijas kods"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
msgid "Verification failed, please double-check the 6-digit code"
msgstr "Verifikācija izgāzās, lūdzu, atkārtoti pārbaudiet 6 ciparu kodu"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "e.g. 123456"
msgstr "piem., 123456"
