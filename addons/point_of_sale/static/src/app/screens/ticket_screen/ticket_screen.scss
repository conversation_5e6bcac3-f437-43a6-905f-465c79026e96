.left-pane {
    max-width: 500px;
}

.orders .col {
    flex: 1;

    &.very-narrow {
        flex: 0.2;
    }

    &.narrow {
        flex: 0.5;
    }

    &.wide {
        flex: 1.5;
    }
}

.order-row:hover {
    color: white;
    background-color:  $primary !important;
}

.buttons {
    grid-area: buttons;
}

.pos-search-bar {
    grid-area: search;
}

.item {
    grid-area: pagination;
}

@include media-breakpoint-down(sm) {
    .screen-full-width {
        flex-direction: column;
        height: auto;
        min-height: 100%;
    }

    .ticket-screen .order,
    .ticket-screen .leftpane {
        max-width: inherit;
    }

    .ticket-screen .rightpane {
        width: auto;
    }
    .pos .orders {
        position: sticky;
        z-index: 1;
    }

    .controls {
        grid-template-columns: auto auto;
        grid-template-areas: 
            "buttons pagination"
            "search search";
    }
    .search .fields {
        z-index: 2;
    }
}

@include media-breakpoint-up(lg){
    .order-row {
        display: flex;
        cursor: pointer;
    }

    .order-row:nth-child(odd) {
        background: $gray-200;
    }

    .order-row:nth-child(even) {
        background: white;
    }

    .pos .orders .col.end {
        justify-content: flex-end;
    }
}

@include media-breakpoint-down(lg) {
    .order-row {
        margin: 5px;
        border: 1px solid #C9CCD2; /* $border-color */
        background: white; /* $o-view-background-color */
    }

    .pos .orders .order-row > .col > div:first-child {
        font-weight: bold;
    }

    .ticket-screen .order-row .delete-button {
        margin-top: 5px;
        justify-content: center;
        background: #d23f3a /* o-text-color('danger') */ ;
        color: white;
    }

    .ticket-screen .order-row .delete-button .fa-trash {
        margin-right: 5px;
    }
}
