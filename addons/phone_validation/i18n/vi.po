# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* phone_validation
# 
# Translators:
# <PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/phone_blacklist.py:0
msgid "%(error)s Please correct the number and try again."
msgstr "%(error)s <PERSON><PERSON> lòng sửa số và thử lại."

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Add a phone number in the blacklist"
msgstr "Thêm một số điện thoại vào danh sách hạn chế"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
msgid "Are you sure you want to unblacklist this Phone Number?"
msgstr "Bạn có chắc chắn muốn bỏ hạn chế Số điện thoại này không?"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/phone_blacklist.py:0
msgid "Are you sure you want to unblacklist this phone number?"
msgstr "Bạn có chắc chắn muốn bỏ hạn chế số điện thoại này không?"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_attachment_count
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_base
msgid "Base"
msgstr "Cơ sở"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Blacklist"
msgstr "Danh sách hạn chế"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Ngày vào danh sách hạn chế"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Số điện thoại bị hạn chế là số di động"

#. module: phone_validation
#: model:ir.actions.act_window,name:phone_validation.phone_blacklist_action
msgid "Blacklisted Phone Numbers"
msgstr "Số điện thoại bị hạn chế"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Số điện thoại bị hạn chế là số điện thoại bàn"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Blacklisted phone numbers won't receive SMS Mailings anymore."
msgstr "Các số điện thoại bị hạn chế sẽ không nhận được Thư SMS nữa."

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/res_users.py:0
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"Bị chặn do việc xóa tài khoản cổng thông tin %(portal_user_name)s bởi "
"%(user_name)s (#%(user_id)s)"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__display_name
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Trường được sử dụng để chứa số điện thoại được làm sạch. Giúp tăng tốc tìm "
"kiếm và so sánh. "

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_follower_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_partner_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__has_message
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__id
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__id
msgid "ID"
msgstr "ID"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Nếu số điện thoại được làm sạch nằm trong danh sách hạn chế, thì liên hệ sẽ "
"không nhận được sms hàng loạt từ bất kỳ danh sách nào nữa"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
msgid "Impossible number %s: not a valid country prefix."
msgstr "Số không đúng %s: không phải là tiền tố quốc gia hợp lệ."

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
msgid "Impossible number %s: not enough digits."
msgstr "Số không đúng %s: không đủ chữ số."

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
msgid "Impossible number %s: probably invalid number of digits."
msgstr "Số không đúng %s: có vẻ số lượng chữ số không hợp lệ."

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
msgid "Impossible number %s: too many digits."
msgstr "Số không đúng %s: quá nhiều chữ số."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Cho biết số điện thoại được làm sạch trong danh sách hạn chế có phải là số "
"di động hay không. Giúp phân biệt số nào bị hạn chế             khi có cả "
"trường di động và trường điện thoại bàn trong một mô hình."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Cho biết số điện thoại được làm sạch trong danh sách hạn chế có phải là số "
"điện thoại bàn hay không. Giúp phân biệt số nào bị hạn chế             khi "
"có cả trường di động và trường điện thoại bàn trong một mô hình."

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
msgid "Invalid number %s: probably incorrect prefix."
msgstr "Số không hợp lệ %s: có thể tiền tố không chính xác."

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
msgid "Invalid primary phone field on model %s"
msgstr "Trường điện thoại chính không hợp lệ trên mô hình %s"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_is_follower
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
msgid "Missing definition of phone fields."
msgstr "Thiếu định nghĩa trường điện thoại."

#. module: phone_validation
#: model:ir.model.constraint,message:phone_validation.constraint_phone_blacklist_unique_number
msgid "Number already exists"
msgstr "Số đã tồn tại"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__number
msgid "Number should be E164 formatted"
msgstr "Số phải được định dạng E164"

#. module: phone_validation
#: model:ir.ui.menu,name:phone_validation.phone_menu_main
msgid "Phone / SMS"
msgstr "Điện thoại/SMS"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist
#: model:ir.ui.menu,name:phone_validation.phone_blacklist_menu
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Phone Blacklist"
msgstr "Danh sách hạn chế số điện thoại"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr "Mixin danh sách hạn chế số điện thoại"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Số điện thoại hạn chế"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__number
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__phone
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Phone Number"
msgstr "Số Điện thoại"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Điện thoại/di động"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr "Vui lòng nhập ít nhất 3 ký tự khi tìm kiếm Số điện thoại/di động."

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Reason"
msgstr "Lý do"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist_remove
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Remove phone from blacklist"
msgstr "Xoá số điện thoại khỏi danh sách hạn chế"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized
msgid "Sanitized Number"
msgstr "Số đã làm sạch "

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
msgid "Unable to parse %(phone)s: %(error)s"
msgstr "Không thể phân tích cú pháp %(phone)s: %(error)s"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Unblacklist"
msgstr "Bỏ khỏi danh sách đen"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/wizard/phone_blacklist_remove.py:0
msgid "Unblock Reason: %(reason)s"
msgstr "Lý do bỏ chặn: %(reason)s"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_users
msgid "User"
msgstr "Người dùng"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
msgid ""
"You do not have the access right to unblacklist phone numbers. Please "
"contact your administrator."
msgstr ""
"Bạn không có quyền truy cập để bỏ hạn chế số điện thoại. Vui lòng liên hệ "
"với quản trị viên."

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "e.g \"Asked to receive our next newsletters\""
msgstr "VD: \"Đã yêu cầu nhận bản tin email sắp tới của chúng ta\""

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "phone_blacklist_removal"
msgstr "phone_blacklist_removal"
