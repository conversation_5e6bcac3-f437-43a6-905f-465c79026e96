# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.action_report_purchase_order
msgid ""
"\n"
"                (object.state in ('draft', 'sent') and 'Request for Quotation - %s' % (object.name) or\n"
"                'Purchase Order - %s' % (object.name))"
msgstr ""
"\n"
"                (object.state in ('draft', 'sent') and 'Demande de prix - %s' % (object.name) or\n"
"                'Bon de commande - %s' % (object.name))"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_bill_count
msgid "# Vendor Bills"
msgstr "# Factures fournisseurs"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__nbr_lines
msgid "# of Lines"
msgstr "# de lignes"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%(product)s from %(original_receipt_date)s to %(new_receipt_date)s"
msgstr "%(product)s de %(original_receipt_date)s verts %(new_receipt_date)s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%(vendor)s confirmed the receipt will take place on %(date)s."
msgstr "%(vendor)s a confirmé que la réception se fera le %(date)s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%s modified receipt dates for the following products:"
msgstr "%s a modifié les dates de réception pour les produits suivants :"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.report_purchase_quotation
msgid "'Request for Quotation - %s' % (object.name)"
msgstr "'Demande de prix - %s' % (object.name)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
", it's 100% free! You'll save time creating beautiful quotations and track "
"sales."
msgstr ""
", c'est 100% gratuit ! Vous gagnerez du temps en créant de magnifiques devis"
" tout en suivant vos ventes."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "-&gt;"
msgstr "-&gt;"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr "Correspondance à trois voies"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr "Rapprochement à trois voies : achats, réceptions et factures"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_reminder
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is a reminder that the delivery of the purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <span style=\"font-weight:bold;\">(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</span>\n"
"        </t>\n"
"        is expected for \n"
"        <t t-if=\"object.date_planned\">\n"
"            <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <span style=\"font-weight:bold;\">undefined</span>.\n"
"        </t>\n"
"        Could you please confirm it will be delivered on time?\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Bonjour <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Voici un rappel concernant la livraison de la commande <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <span style=\"font-weight:bold;\">(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</span>\n"
"        </t>\n"
"        prévue le \n"
"        <t t-if=\"object.date_planned\">\n"
"            <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <span style=\"font-weight:bold;\">indéfini</span>.\n"
"        </t>\n"
"        Pourriez-vous confirmer que la livraison se fera en temps et en heure ?\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br/><br/>\n"
"        <t t-if=\"object.date_planned\">\n"
"            The receipt is expected for <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"            <br/><br/>\n"
"            Could you please acknowledge the receipt of this order?\n"
"        </t>\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Bonjour <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Vous trouverez ci-joint le bon de commande <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            avec la référence : <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        pour un montant de <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">10.00 €</span>\n"
"        de la part de <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br/><br/>\n"
"        <t t-if=\"object.date_planned\">\n"
"            La réception est prévue le <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"            <br/><br/>\n"
"            Pourriez-vous confirmer la bonne réception de cette commande ?\n"
"        </t>\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a request for quotation <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        If you have any questions, please do not hesitate to contact us.\n"
"        <br/><br/>\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='reception')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='decline')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        <br/><br/>\n"
"        Best regards,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Bonjour <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Veuillez trouver ci-joint une demande de prix <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            avec la référence : <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        de <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        N'hésitez pas à nous contacter si vous avez des questions.\n"
"        <br/><br/>\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='reception')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accepter</a>\n"
"\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='decline')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Décliner</a>\n"
"        <br/><br/>\n"
"        Cordialement,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_partner_kanban_view
msgid ""
"<i class=\"fa fa-credit-card me-1\" aria-label=\"Purchases\" role=\"img\" "
"title=\"Purchases\"/>"
msgstr ""
"<i class=\"fa fa-credit-card me-1\" aria-label=\"Achats\" role=\"img\" "
"title=\"Achats\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Done</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Terminé</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/>Paid"
msgstr "<i class=\"fa fa-fw fa-check\"/>Payé"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/>Reversed"
msgstr "<i class=\"fa fa-fw fa-check\"/>Inversé"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Waiting Payment"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>En attente de paiement"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Waiting for Bill</span>"
msgstr ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> En "
"attente de facture</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Cancelled</span>"
msgstr ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Annulé </span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-print me-1\"/>Download / Print"
msgstr "<i class=\"fa fa-print me-1\"/>Télécharger / Imprimer"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Confirmation Date</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmation</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Date de confirmation</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmation</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Purchase Order #</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Bon de commande #</span>\n"
"                          <span class=\"d-block d-md-none\">Réf.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid ""
"<span class=\"d-none d-md-inline\">Request for Quotation #</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Demande de prix #</span>\n"
"                        <span class=\"d-block d-md-none\">Réf.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"o_form_label\" invisible=\"state not in ('draft', 'sent')\">Request for Quotation </span>\n"
"                        <span class=\"o_form_label\" invisible=\"state in ('draft', 'sent')\">Purchase Order </span>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"state not in ('draft', 'sent')\">Demande de prix </span>\n"
"                        <span class=\"o_form_label\" invisible=\"state in ('draft', 'sent')\">Bon de commande </span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span class=\"o_stat_text\">Bill Matching</span>"
msgstr "<span class=\"o_stat_text\">Rapprochement des factures</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "<span class=\"o_stat_text\">Purchase Matching</span>"
msgstr "<span class=\"o_stat_text\">Rapprochement des factures d'achat</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "<span class=\"o_stat_text\">Purchased</span>"
msgstr "<span class=\"o_stat_text\">Acheté</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" invisible=\"not "
"mail_reminder_confirmed\">(confirmed by vendor)</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"not "
"mail_reminder_confirmed\">(confirmé par fournisseur)</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_partner_property_form
msgid "<span> day(s) before</span>"
msgstr "<span> jour(s) avant</span>"

#. module: purchase
#: model_terms:web_tour.tour,rainbow_man_message:purchase.purchase_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Beau travail !</b> Vous avez terminé toutes les étapes de cette "
"visite.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>Montant</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span>Ask confirmation</span>"
msgstr "<span>Demande de confirmation</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>TVA</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">From:</strong>"
msgstr "<strong class=\"d-block mb-1\">De :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Factures</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong class=\"d-block mt-3\">Shipping address:</strong>"
msgstr "<strong class=\"d-block mt-3\">Adresse de livraison :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"d-block mt-3\">Shipping address</strong>"
msgstr "<strong class=\"d-block mt-3\">Adresse de livraison</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Sous-total</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr "<strong>Montant</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Buyer</strong>"
msgstr "<strong>Acheteur</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Confirmation Date:</strong>"
msgstr "<strong>Date de confirmation :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Description</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Disc.</strong>"
msgstr "<strong>Remises</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Expected Arrival:</strong>"
msgstr "<strong>Arrivée prévue :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Date prévue</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Date de la commande :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Deadline:</strong>"
msgstr "<strong>Échéance de commande :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Payment Terms: </strong>"
msgstr "<strong>Modalités de paiement : </strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Qté</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Receipt Date:</strong>"
msgstr "<strong>Date de réception :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Request For Quotation Date:</strong>"
msgstr "<strong>Date de demande de prix :</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Taxes</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr "<strong>La quantité commandée a été mise à jour.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
msgid "<strong>The received quantity has been updated.</strong>"
msgstr "<strong>La quantité reçue a été mise à jour.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This purchase has been cancelled.</strong>"
msgstr "<strong>Cet achat a été annulé.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This quotation has been accepted.</strong>"
msgstr "<strong>Ce devis a été accepté.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This quotation has been declined.</strong>"
msgstr "<strong>Ce devis a été rejeté.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Prix unitaire</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference</strong>"
msgstr "<strong>Votre référence de commande</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"A blanket purchase order is a purchase agreement between a buyer and a "
"supplier to deliver goods or services to the buyer at a preset price for a "
"period of time."
msgstr ""
"Un contrat-cadre désigne un accord entre un acheteur et un fournisseur, qui "
"vise à livrer des biens ou des services à l'acheteur à un prix prédéterminé,"
" au cours d'une période donnée."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "A sample email has been sent to %s."
msgstr "Un exemple d'e-mail a été envoyé à %s."

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr ""
"Un avertissement peut être défini pour un produit ou un fournisseur (achats)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Ability to select a package type in purchase orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Possibilité de sélectionner un type de colis dans les bons de commande et de"
" forcer une quantité qui est un multiple du nombre d'unités par colis."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Accept"
msgstr "Accepter"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_warning
msgid "Access warning"
msgstr "Avertissement d'accès"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"En fonction de la configuration du produit, la quantité reçue peut être calculée automatiquement par mécanisme : \n"
"  - Manuel : la quantité est définie manuellement sur la ligne\n"
"  - Mouvements de stock : la quantité provient des transferts confirmés\n"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__account_move_id
msgid "Account Move"
msgstr "Écriture comptable"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_accrued_expense_entry
msgid "Accrued Expense Entry"
msgstr "Écriture des charges à payer"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_state
msgid "Activity State"
msgstr "Statut de l'activité"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "Add Down Payment"
msgstr "Ajouter un acompte"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "Add Products"
msgstr "Ajouter des produits"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a note"
msgstr "Ajouter une note"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a product"
msgstr "Ajouter un produit"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a section"
msgstr "Ajouter une section"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Add several variants to the purchase order from a grid"
msgstr "Ajouter plusieurs variantes sur le bon de commande depuis une grille"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Add some products or services to your quotation."
msgstr "Ajouter des produits ou services à votre devis."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Add to PO"
msgstr "Ajouter au bon de commande"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Add to Purchase Order"
msgstr "Ajouter au bon de commande"

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Administrator"
msgstr "Administrateur"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "All"
msgstr "Tous"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Draft RFQs"
msgstr "Toutes les demandes de prix en brouillon"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Late RFQs"
msgstr "Toutes les demandes de prix en retard"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All RFQs"
msgstr "Toutes les demandes de prix"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Waiting RFQs"
msgstr "Toutes les demandes de prix en attente"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__group_send_reminder
msgid "Allow automatically send email to remind your vendor the receipt date"
msgstr ""
"Autoriser l'envoi automatique d'e-mail de rappel de date de réception au "
"fournisseur"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__edit
msgid "Allow to edit purchase orders"
msgstr "Autoriser l'édition des bons de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__aml_id
msgid "Aml"
msgstr "Aml"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__amount
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Amount"
msgstr "Montant"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay_pass
msgid ""
"Amount of time between date planned and order by date for each purchase "
"order line."
msgstr ""
"Durée entre la date prévue et la date de commande pour chaque ligne du bon "
"de commande."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay
msgid "Amount of time between purchase approval and order by date."
msgstr "Durée entre la validation d'achat et la date de commande"

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_account
msgid "Analytic Account"
msgstr "Compte analytique"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Répartition Analytique"

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Applicabilités de la dimension analytique"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Précision analytique"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Approuver la commande"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Are you sure you want to cancel the selected RFQs/Orders?"
msgstr ""
"Êtes-vous sûr de vouloir annuler les demandes de prix /commandes "
"sélectionnées ?"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Valeurs d'attribut"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Attributes"
msgstr "Attributs"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Auto-Complete"
msgstr "Saisie automatique"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_vendor_bill_id
msgid "Auto-complete"
msgstr "Saisie automatique"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_vendor_bill_id
msgid "Auto-complete from a past bill / purchase order."
msgstr "Saisie automatique depuis une facture / commande passée."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_id
msgid "Auto-complete from a past purchase order."
msgstr "Saisie automatique depuis un bon de commande passé."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically lock confirmed orders to prevent editing"
msgstr ""
"Verrouiller automatiquement les commandes confirmées pour éviter de les "
"modifier"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically remind the receipt date to your vendors"
msgstr "Rappel automatique de la date de réception à vos fournisseurs"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_users__receipt_reminder_email
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"Automatically send a confirmation email to the vendor X days before the "
"expected receipt date, asking him to confirm the exact date."
msgstr ""
"Envoyez automatiquement un e-mail de confirmation au vendeur X jours avant "
"la date de réception prévue, lui demandant de confirmer la date exacte."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_average
msgid "Average Cost"
msgstr "Coût moyen"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Avg Order Value"
msgstr "Valeur moyenne de la commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__default_purchase_method
msgid "Bill Control"
msgstr "Contrôle des factures"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_count
msgid "Bill Count"
msgstr "Nombre de factures"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__invoice_lines
msgid "Bill Lines"
msgstr "Lignes de facturation"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Bill Matching"
msgstr "Rapprochement des factures"

#. module: purchase
#: model:ir.model,name:purchase.model_bill_to_po_wizard
msgid "Bill to Purchase Order"
msgstr " Facturer à la commande d'achat"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed"
msgstr "Facturé"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__billed_amount_untaxed
msgid "Billed Amount Untaxed"
msgstr "Montant facturé hors taxes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_invoiced
msgid "Billed Qty"
msgstr "Qté facturée"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed Quantity"
msgstr "Quantité facturée"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr "Quantité facturée :"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_status
msgid "Billing Status"
msgstr "Statut de facturation"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_ids
msgid "Bills"
msgstr "Factures fournisseurs"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Bills Received"
msgstr "Factures reçues"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__block
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__block
msgid "Blocking Message"
msgstr "Message bloquant"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__user_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__user_id
#: model:ir.model.fields,field_description:purchase.field_res_partner__buyer_id
#: model:ir.model.fields,field_description:purchase.field_res_users__buyer_id
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Buyer"
msgstr "Acheteur"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Vue calendrier"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Cancel"
msgstr "Annuler"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__cancel
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__cancel
msgid "Cancelled"
msgstr "Annulé"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Cancelled Purchase Order #"
msgstr "Numéro du bon de commande annulé"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Cannot delete a purchase order line which is in state “%s”."
msgstr ""
"Impossible de supprimer une ligne de commande d'achat qui est dans l'état  "
"“%s”."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Catalog"
msgstr "Catalogue"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_category_id
msgid "Category"
msgstr "Catégorie"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Close"
msgstr "Fermer"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entité commerciale"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Communication history"
msgstr "Historique de la communication"

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Société"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__company_currency_id
msgid "Company Currency"
msgstr "Devise de la société"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total_cc
msgid "Company Total"
msgstr "Total de la société"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Compose Email"
msgstr "Rédiger un e-mail"

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Configuration"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Confirmer la commande"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_confirm_rfqs
msgid "Confirm RFQ"
msgstr "Confirmer la demande de prix"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Receipt Date"
msgstr "Confirmer la date de réception"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__one_step
msgid "Confirm purchase orders in one step"
msgstr "Confirmer les bons de commande en une seule étape"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Confirm your purchase."
msgstr "Confirmez votre achat."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date"
msgstr "Date de confirmation"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date Last Year"
msgstr "Date de confirmation de l'année dernière"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__lock
msgid "Confirmed purchase orders are not editable"
msgstr "Les bons de commande confirmés ne sont pas modifiables"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Connect with your software!"
msgstr "Connectez-vous à votre logiciel !"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Connect your software"
msgstr "Connecter votre logiciel"

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_method
msgid "Control Policy"
msgstr "Politique de contrôle"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Il est possible de convertir deux unités de mesures si elles appartiennent à"
" la même catégorie. Cette conversion utilise les facteurs définis pour ces "
"unités."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Copy"
msgstr "Copier"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__country_code
msgid "Country code"
msgstr "Code pays"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Create Bill"
msgstr "Créer une facture fournisseur"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Create Bills"
msgstr "Créer des factures"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_batch_bills
msgid "Create Vendor Bills"
msgstr "Créer des factures fournisseurs"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Create a new product variant"
msgstr "Créer une nouvelle variante de produit"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_date
msgid "Created on"
msgstr "Créé le"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__currency_id
msgid "Currency"
msgstr "Devise"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_rate
msgid "Currency Rate"
msgstr "Taux de change"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__access_url
msgid "Customer Portal URL"
msgstr "URl du portail client"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__date
msgid "Date"
msgstr "Date"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_calendar_start
msgid "Date Calendar Start"
msgstr "Date calendrier de début "

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Date Updated"
msgstr "Date de mise à jour"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Date:"
msgstr "Date :"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Days"
msgstr "Jours"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_users__reminder_date_before_receipt
msgid "Days Before Receipt"
msgstr "Jours avant réception"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay
msgid "Days to Confirm"
msgstr "Jours pour confirmer "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay_pass
msgid "Days to Receive"
msgstr "Jours pour recevoir"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Decline"
msgstr "Refuser"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_price_include
msgid "Default Sales Price Include"
msgstr "Inclure prix de vente par défaut"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__company_price_include
msgid ""
"Default on whether the sales price used on the product and invoices with "
"this Company includes its taxes."
msgstr ""
"Le taxes sont incluses par défaut dans le prix de vente utilisé pour le "
"produit et les factures de cette entreprise."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_bill_line_match__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unité de mesure par défaut utilisée pour toutes les opérations de stock."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr "Définissez vos conditions générales…"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_planned
msgid ""
"Delivery date expected from vendor. This date respectively defaults to "
"vendor pricelist lead time then today's date."
msgstr ""
"Date de livraison prévue du fournisseur. Cette date correspond par défaut au"
" délai de livraison du fournisseur, puis à la date du jour."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_planned
msgid ""
"Delivery date promised by vendor. This date is used to determine expected "
"arrival of products."
msgstr ""
"Date de livraison promise par le fournisseur. Cette date est utilisée pour "
"déterminer la date de réception prévue des produits."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_order
msgid ""
"Depicts the date within which the Quotation should be confirmed and "
"converted into a purchase order."
msgstr ""
"Représente la date à laquelle le devis devrait être validé et converti en "
"bon de commande."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__name
msgid "Description"
msgstr "Description"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Disc.%"
msgstr "Rem.%"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__discount
msgid "Discount (%)"
msgstr "Remise (%)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Discount:"
msgstr "Remise :"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_type
msgid "Display Type"
msgstr "Type d'affichage"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Compte analytique de répartition"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domaine"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__done
msgid "Done"
msgstr "Terminé"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation_amount
msgid "Double validation amount"
msgstr "Double validation du montant"

#. module: purchase
#. odoo-python
#: code:addons/purchase/wizard/bill_to_po_wizard.py:0
msgid "Down Payment (ref: %(ref)s)"
msgstr "Acompte (réf : %(ref)s)"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Down Payments"
msgstr "Acomptes"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__draft
msgid "Draft RFQ"
msgstr "Demande de prix en brouillon"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Draft RFQs"
msgstr "Demandes de prix en brouillon"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
"Drag and drop the request for quotation PDF file into your list of "
"quotations in Odoo. Enjoy the automation!"
msgstr ""
"Faites glisser et déposez le fichier PDF de la demande de prix dans votre "
"liste de devis sur Odoo. Profitez de l'automatisation !"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "Adresse de dropshipping"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_planned
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_planned
msgid "Expected Arrival"
msgstr "Arrivée prévue"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Filtres étendus"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Extra line with %s "
msgstr "Ligne supplémentaire avec %s "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__fiscal_position_id
msgid "Fiscal Position"
msgstr "Position fiscale"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Follow orders you have to fulfill"
msgstr "Suivre les commandes que vous devez exécuter"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Follow your Requests for Quotation"
msgstr "Suivre vos demandes de prix"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable purchase order line"
msgstr "Valeurs interdites sur des lignes de bon de commande non justifiables"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "From %s"
msgstr "De %s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "From Electronic Document"
msgstr "Du Document électronique"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__invoiced
msgid "Fully Billed"
msgstr "Entièrement facturé"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr "Activités futures"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__two_step
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Obtenir 2 niveaux de validation pour confirmer un bon de commande"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr ""
"Recevoir des avertissements sur les commandes pour des produits ou "
"fournisseur"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""
"Les biens sont des matériaux tangibles et des marchandises que vous fournissez.\n"
"Un service est un produit non matériel que vous fournissez."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__weight
msgid "Gross Weight"
msgstr "Poids brut"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Regrouper par"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__has_message
msgid "Has Message"
msgstr "A un message"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Masquer les lignes annulées"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""
"Si cette option est activée, cela permet d'effectuer une correspondance à 3 "
"voies sur les factures de fournisseurs : les articles doivent être reçus "
"pour payer la facture."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If installed, the product variants will be added to purchase orders through "
"a grid entry."
msgstr ""
"Si installées, les variantes de produit seront ajoutées aux bons de commande"
" via une grille."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_packaging__purchase
msgid "If true, the packaging can be used for purchase orders"
msgstr ""
"Si vrai, le conditionnement peut être utilisé dans des bons de commande"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
msgid "Import Template for Products"
msgstr "Modèle d'importation pour les produits"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "In order to delete a purchase order, you must cancel it first."
msgstr "Pour supprimer un bon de commande, vous devez d'abord l'annuler."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"In selected purchase order to merge these details must be same\n"
"Vendor, currency, destination, dropship address and agreement"
msgstr ""
"Dans le bon de commande sélectionné, les détails suivants doivent être identiques pour pouvoir les fusionner :\n"
"Fournisseur, devise, destination, adresse de livraison et accord."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_view_search_catalog
msgid "In the Order"
msgstr "Dans la commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Incoterm"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Indicate the product quantity you want to order."
msgstr "Indiquez la quantité de produits que vous souhaitez commander."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Les Incoterms sont une série de termes commerciaux prédéfinis utilisés dans "
"les transactions internationales."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Factures et réceptions"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr "Facturation"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_move_line__is_downpayment
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__is_downpayment
msgid "Is Downpayment"
msgstr "Est un acompte "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__is_in_purchase_order
msgid "Is In Purchase Order"
msgstr "Bon de commande fournisseur"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__is_purchase_matched
#: model:ir.model.fields,field_description:purchase.field_account_move__is_purchase_matched
msgid "Is Purchase Matched"
msgstr "Est un achat rapproché"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Late"
msgstr "En retard"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr "Activités en retard"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late RFQs"
msgstr "Demandes de prix en retard"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Lead Time to Purchase"
msgstr "Délai pour les achats"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Let's create your first request for quotation."
msgstr "Créons votre première demande de prix."

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid ""
"Let's try the Purchase app to manage the flow from purchase to reception and"
" invoice control."
msgstr ""
"Essayons l'application Achats pour gérer le flux depuis l'achat jusqu'au "
"contrôle de la facture"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation
msgid "Levels of Approvals"
msgstr "Niveaux de validation"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation
msgid "Levels of Approvals *"
msgstr "Niveaux de validation *"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_amount_untaxed
msgid "Line Amount Untaxed"
msgstr "Montant de la ligne hors taxes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_qty
msgid "Line Qty"
msgstr "Quantité de la ligne"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_uom_id
msgid "Line Uom"
msgstr "UdM de la ligne"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr "Verrouiller"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr "Verrouiller les commandes confirmées"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__done
msgid "Locked"
msgstr "Verrouillé"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr ""
"S'assurer de payer uniquement les factures des marchandises commandées et "
"reçues"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage blanket orders and purchase templates"
msgstr "Gérer les contrats-cadres et les modèles d'achat"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__qty_received_method__manual
msgid "Manual"
msgstr "Manuelle"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr "Factures manuelles"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_manual
msgid "Manual Received Qty"
msgstr "Qté manuellement reçue"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lead
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Marge d'erreur pour les délais des fournisseurs. Quand le système génère des"
" bons de commande pour l'approvisionnement d'un produit, ceux-ci seront "
"programmés autant de jours plus tôt pour faire face aux retards imprévus des"
" fournisseurs."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__use_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Marge d'erreur pour les délais des fournisseurs. Lorsque le système génère "
"des commandes de réapprovisionnement, ils sont programmés tant de jours à "
"l'avance pour faire face aux retards imprévus des fournisseurs."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Match"
msgstr "Faire correspondre"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_merger
msgid "Merge"
msgstr "Fusionner"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn_msg
msgid "Message for Purchase Order"
msgstr "Message pour bon de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Message pour la ligne de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_ids
msgid "Messages"
msgstr "Messages"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum Amount"
msgstr "Montant minimum"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation_amount
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Montant minimum pour lequel une double validation est requise"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_accountable_required_fields
msgid "Missing required fields on accountable purchase order line."
msgstr "Champs requis manquants sur des lignes de commande justifiables."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Draft RFQs"
msgstr "Mes demandes de prix en brouillon"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Late RFQs"
msgstr "Mes demandes de prix en retard"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "My Orders"
msgstr "Mes commandes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Purchases"
msgstr "Mes achats"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My RFQs"
msgstr "Mes demandes de prix"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Waiting RFQs"
msgstr "Mes demandes de prix en attente"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "Name"
msgstr "Nom"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Name, TIN, Email, or Reference"
msgstr "Nom, numéro de TVA, e-mail ou référence"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "Newest"
msgstr "Le plus récent"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l'activité suivante"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__no-message
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__no-message
msgid "No Message"
msgstr "Aucun message"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid "No Purchase Analysis"
msgstr "Aucune analyse d'achat"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "No product found. Let's create one!"
msgstr "Aucun produit trouvé. Créons-en un !"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid "No purchase order found. Let's create one!"
msgstr "Aucun bon de commande trouvé. Créons-en un !"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_history
msgid "No purchase order were made for this product yet!"
msgstr "Aucun bon de commande n'a encore été fait pour ce produit !"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "No request for quotation found. Let's create one!"
msgstr "Aucune demande de prix trouvée. Créons-en une !"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__0
msgid "Normal"
msgstr "Normal"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Not Acknowledged"
msgstr "Non confirmé"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Not using Odoo?"
msgstr "Vous n'utilisez pas Odoo ?"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Note"
msgstr "Note"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Notes"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__no
msgid "Nothing to Bill"
msgstr "Rien à facturer"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_users__reminder_date_before_receipt
msgid "Number of days to send reminder email before the promised receipt date"
msgstr ""
"Nombre de jours pour envoyer le mail de rappel avant la date de réception "
"promise"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Odoo"
msgstr "Odoo"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__purchase
msgid "On ordered quantities"
msgstr "Sur base des quantités commandés"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_method
#: model:ir.model.fields,help:purchase.field_product_template__purchase_method
msgid ""
"On ordered quantities: Control bills based on ordered quantities.\n"
"On received quantities: Control bills based on received quantities."
msgstr ""
"Sur base des quantités commandées : factures de contrôle basées sur les quantités commandées. \n"
"Sur base des quantités reçues : factures de contrôle basées sur les quantités reçues."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__receive
msgid "On received quantities"
msgstr "Sur base des quantités reçues"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid ""
"Once you get the price from the vendor, you can complete the purchase order "
"with the right price."
msgstr ""
"Une fois que vous recevez le prix du fournisseur, vous pouvez compléter le "
"bon de commande avec le prix correct."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Once you ordered your products to your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""
"Une fois que vous avez commandé vos produits à votre fournisseur, confirmez "
"votre demande de prix et cela la transformera en bon de commande."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
msgid "Operation not supported"
msgstr "Opération non prise en charge"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order"
msgstr "Commander"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Date"
msgstr "Date de la commande"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Date: Last 365 Days"
msgstr "Date de la commande : 365 derniers jours"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_order
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Order Deadline"
msgstr "Échéance de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__order_line
msgid "Order Lines"
msgstr "Lignes de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Référence de commande"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr "Quantité commandée :"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__purchase
msgid "Ordered quantities"
msgstr "Quantités commandées"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Orders"
msgstr "Commandes"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Order due %(date)s"
msgstr "Commande attendue le %(date)s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Other Information"
msgstr "Autres informations"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Our Orders"
msgstr "Nos commandes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_id
msgid "Packaging"
msgstr "Conditionnement"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Qté de conditionnements"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__partner_id
msgid "Partner"
msgstr "Partenaire"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__country_id
msgid "Partner Country"
msgstr "Pays partenaire"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__payment_term_id
msgid "Payment Terms"
msgstr "Conditions de paiement"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Payment terms"
msgstr "Conditions de paiement"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Please select at least two purchase orders with state RFQ and RFQ sent to "
"merge."
msgstr ""
"Veuillez sélectionner au moins deux bons de commande avec le statut Demande "
"de devis et Demande de devis envoyée pour les fusionner."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__pol_id
msgid "Pol"
msgstr "Pol"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_url
msgid "Portal Access URL"
msgstr "URl d'accès au portail"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Preview the reminder email by sending it to yourself."
msgstr "Prévisualisez le mail de rappel en l'envoyant à vous-même."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Price"
msgstr "Prix"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/product_catalog/purchase_order_line/purchase_order_line.xml:0
msgid "Price:"
msgstr "Prix :"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Pricing"
msgstr "Tarif"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Imprimer la demande de prix"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__priority
msgid "Priority"
msgstr "Priorité"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product"
msgstr "Produit"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Catégories de produits"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Catégorie de produits"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Product Description"
msgstr "Description du produit"

#. module: purchase
#: model:ir.model,name:purchase.model_product_packaging
msgid "Product Packaging"
msgstr "Conditionnement des produits"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_tmpl_id
msgid "Product Template"
msgstr "Modèle de produit"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_type
msgid "Product Type"
msgstr "Type de produit"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_price
msgid "Product Uom Price"
msgstr "Prix UdM Produit"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_qty
msgid "Product Uom Qty"
msgstr "Qté UdM Produit"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Variantes de produit"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Valeurs d'attributs de produit qui ne créent pas de variantes"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_products
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Produits"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Fournir un méchanisme de double validation pour les achats"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_packaging__purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model:ir.ui.menu,name:purchase.purchase_report
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase"
msgstr "Achats"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_requisition
msgid "Purchase Agreements"
msgstr "Conventions d'achat"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__purchase_amount_untaxed
msgid "Purchase Amount Untaxed"
msgstr "Montant d'achat hors taxes"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Analysis"
msgstr "Analyse des achats"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Purchase Bill Lines"
msgstr "Lignes de facturation d'achat"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Description"
msgstr "Description des achats"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_product_matrix
msgid "Purchase Grid Entry"
msgstr "Grille d'entrée d'achats"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Purchase History"
msgstr "Historique des achats"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Purchase History for %s"
msgstr "Historique des achats pour %s"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lead
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lead
msgid "Purchase Lead Time"
msgstr "Délai des achats"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_line_match
msgid "Purchase Line and Vendor Bill line matching view"
msgstr ""
"Vue des rapprochement lignes de commande d'achat et de facture fournisseur"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "Purchase Matching"
msgstr "Rapprochement des achats"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: code:addons/purchase/models/purchase_order.py:0
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__purchase_order_id
#: model:ir.model.fields.selection,name:purchase.selection__account_analytic_applicability__business_domain__purchase_order
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_activity
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Purchase Order"
msgstr "Bon de commande fournisseur"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order #"
msgstr "Bon de commande #"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_order_approval
msgid "Purchase Order Approval"
msgstr "Validation du bon de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_account__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_order_count
msgid "Purchase Order Count"
msgstr "Nombre de bons de commande"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_history
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Ligne de bon de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn
msgid "Purchase Order Line Warning"
msgstr "Avertissement ligne de commande "

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Lignes de bon de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lock
msgid "Purchase Order Modification"
msgstr "Modification du bon de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lock
msgid "Purchase Order Modification *"
msgstr "Modification du bon de commande *"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""
"L'option de modification du bon de commande est utilisée lorsque vous "
"souhaitez que les bons de commande soient éditables après confirmation"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_order_name
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_order_name
msgid "Purchase Order Name"
msgstr "Nom du bon de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn
msgid "Purchase Order Warning"
msgstr "Avertissement bon de commande"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/analytic_account.py:0
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.account_analytic_account_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Orders"
msgstr "Bons de commande fournisseur"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchase Report"
msgstr "Rapport d'achat"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_warning_purchase
msgid "Purchase Warnings"
msgstr "Avertissements d'achats"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that have been invoiced."
msgstr "Bons de commande facturés."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that include lines not invoiced."
msgstr "Bons de commande qui possèdent des lignes non facturées."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase products by multiple of unit # per package"
msgstr "Acheter les produits par multiples du # d'unités par colis"

#. module: purchase
#: model:ir.actions.server,name:purchase.purchase_send_reminder_mail_ir_actions_server
msgid "Purchase reminder"
msgstr "Rappel d'achat"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase variants of a product using attributes (size, color, etc.)"
msgstr ""
"Acheter des variantes d'un produit en utilisant des des attributs (taille, "
"couleur, etc..)"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_done
msgid "Purchase: Purchase Order"
msgstr "Achats : Bon de commande"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase
msgid "Purchase: Request For Quotation"
msgstr "Achats : Demande de prix"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_reminder
msgid "Purchase: Vendor Reminder"
msgstr "Achats : Rappel Fournisseur"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchased_product_qty
#: model:ir.model.fields,field_description:purchase.field_product_template__purchased_product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Purchased"
msgstr "Acheté"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Purchased Last 7 Days"
msgstr "Acheté ces 7 derniers jours"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchased in the last 365 days"
msgstr "Acheté au cours des 365 derniers jours"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Purchases"
msgstr "Achats"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_union
msgid "Purchases & Bills Union"
msgstr "Union des achats & factures"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Ajoutez une adresse si vous voulez livrer directement du fournisseur au "
"client. Sinon, laissez vide pour vous faire livrer à votre société."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Qty"
msgstr "Qté"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_billed
msgid "Qty Billed"
msgstr "Qté facturée"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__qty_invoiced
msgid "Qty Invoiced"
msgstr "Qté facturée"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_ordered
msgid "Qty Ordered"
msgstr "Qté commandée"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_received
msgid "Qty Received"
msgstr "Qté reçue"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_to_be_billed
msgid "Qty to be Billed"
msgstr "Qté à facturer"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr "Quantités facturées par les fournisseurs"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Quantity"
msgstr "Quantité"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Quantity:"
msgstr "Quantité :"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__draft
msgid "RFQ"
msgstr "Demande de prix"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr "Demande de prix approuvée"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr "Demande de prix confirmée"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr "Demande de prix terminée"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__sent
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__sent
#: model:mail.message.subtype,name:purchase.mt_rfq_sent
msgid "RFQ Sent"
msgstr "Envoyé"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "RFQ merged with %(oldest_rfq_name)s and %(cancelled_rfq)s"
msgstr "Devis fusionné avec %(oldest_rfq_name)s et %(cancelled_rfq)s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "RFQ merged with %s"
msgstr "Devis fusionné avec %s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "RFQs"
msgstr "Demandes de prix"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "RFQs Sent Last 7 Days"
msgstr "Demandes de prix envoyées les 7 derniers jours"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "Demandes de prix et achats"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send by Email"
msgstr "Renvoyer par e-mail"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Read the documentation"
msgstr "Lisez la documentation"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_send_reminder
#: model:ir.model.fields,field_description:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,field_description:purchase.field_res_users__receipt_reminder_email
msgid "Receipt Reminder"
msgstr "Rappel de réception"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__receipt_reminder_email
msgid "Receipt Reminder Email"
msgstr "E-mail de rappel de réception"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received"
msgstr "Reçu"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received
msgid "Received Qty"
msgstr "Qté reçue"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Méthode de la quantité reçue"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received Quantity"
msgstr "Quantité reçue"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr "Quantité reçue :"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__receive
msgid "Received quantities"
msgstr "Quantités reçues"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_confirmed
msgid "Reception Confirmed"
msgstr "Réception confirmée"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_declined
msgid "Reception Declined"
msgstr "Réception refusée"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Record a new vendor bill"
msgstr "Enregistrer une nouvelle facture fournisseur"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__reference
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Reference"
msgstr "Référence"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_tree
msgid "Reference Document"
msgstr "Document de référence"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_uom
msgid "Reference Unit of Measure"
msgstr "Unité de mesure de référence"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a"
" sales order)"
msgstr ""
"Référence du document qui a généré cette demande de bon de commande (par ex."
" une commande client)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""
"Référence de la commande client ou de l'offre envoyée par le fournisseur. "
"Utilisé principalement pour faire la correspondance lors de la réception des"
" produits, puisque cette référence est généralement écrite sur le bon de "
"livraison envoyé par votre fournisseur."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reminder_confirmed
msgid "Reminder Confirmed"
msgstr "Rappel confirmé"

#. module: purchase
#: model:ir.model,name:purchase.model_ir_actions_report
msgid "Report Action"
msgstr "Action de rapport"

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report_main
msgid "Reporting"
msgstr "Analyse"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Request for Quotation"
msgstr "Demande de prix"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Demande de prix #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request managers to approve orders above a minimum amount"
msgstr ""
"Demander aux managers d'approuver les commandes supérieures à un montant "
"minimum"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Requests For Quotation"
msgstr "Demandes de prix"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_rfq_form
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Requests for Quotation"
msgstr "Demandes de prix"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Requests for quotation are documents that will be sent to your suppliers to request prices for different products you consider buying.\n"
"                Once an agreement has been found with the supplier, they will be confirmed and turned into purchase orders."
msgstr ""
"Les demandes de prix sont des documents que vous envoyez à vos fournisseurs pour leur demander les prix des différents produits que vous voulez acheter.\n"
"                 Une fois qu'un accord a été trouvé avec le fournisseur, les demandes sont confirmées et converties en bon de commande."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Scheduled Date"
msgstr "Date planifiée"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Chercher un bon de commande"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Search Reference Document"
msgstr "Chercher le document de référence"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Search a vendor name, or create one on the fly."
msgstr "Recherchez un nom de fournisseur ou créez-en un à la volée."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_section
msgid "Section"
msgstr "Section"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Nom de section (par ex. Produits, Services)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__use_po_lead
msgid "Security Lead Time for Purchase"
msgstr "Délai de sécurité pour les achats"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_token
msgid "Security Token"
msgstr "Jeton de sécurité"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Select Vendor Bill lines to add to a Purchase Order"
msgstr ""
"Sélectionnez les lignes de facture fournisseur à ajouter à un bon de "
"commande."

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Select a product, or create a new one on the fly."
msgstr "Sélectionnez un produit, ou créez-en un à la volée."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Select a purchase order or an old bill"
msgstr "Sélectionnez un bon de commande ou une ancienne facture"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users__purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Sélectionner l'option 'Avertissement' notifiera l'utilisateur avec le "
"Message. Sélectionner 'Message bloquant' lancera une exception avec le "
"message et bloquera le flux. Le message doit être encodé dans le champ "
"suivant."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Sell and purchase products in different units of measure"
msgstr "Acheter et vendre des produits dans des unités de mesure différentes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Envoyer le bon de commande par e-mail"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_send_reminder
msgid "Send Reminder"
msgstr "Envoyer le rappel"

#. module: purchase
#: model:res.groups,name:purchase.group_send_reminder
msgid "Send an automatic reminder email to confirm delivery"
msgstr "Envoyer un rappel automatique par e-mail pour confirmer la livraison"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send by Email"
msgstr "Envoyer par e-mail"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Send the request for quotation to your vendor."
msgstr "Envoyez la demande de prix à votre fournisseur."

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase
msgid "Sent manually to vendor to request a quotation"
msgstr "Envoyé manuellement au fournisseur pour demander un devis"

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_done
msgid "Sent to vendor with the purchase order in attachment"
msgstr "Envoyé au fournisseur avec le bon de commande en pièce jointe"

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_reminder
msgid ""
"Sent to vendors before expected arrival, based on the purchase order setting"
msgstr ""
"Envoyé aux fournisseurs avant l'arrivée prévue, en fonction du paramètre du "
"bon de commande"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Marquer comme brouillon"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Paramètres"

#. module: purchase
#: model:ir.actions.server,name:purchase.model_purchase_order_action_share
msgid "Share"
msgstr "Partager"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez tous les enregistrements pour lesquels la date des prochaines "
"actions est pour aujourd'hui ou avant. "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__reference
msgid "Source"
msgstr "Source"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__origin
msgid "Source Document"
msgstr "Document d'origine"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Starred"
msgstr "Favoris"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Start on Odoo"
msgstr "Lancez-vous sur Odoo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__state
msgid "State"
msgstr "Statut"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__state
#: model:ir.model.fields,field_description:purchase.field_purchase_report__state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Statut"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_subtotal
msgid "Subtotal"
msgstr "Sous-total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Billed"
msgstr "Somme des quantités facturées"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Ordered"
msgstr "Somme des quantités commandées"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Received"
msgstr "Somme des quantités reçues"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Total"
msgstr "Somme des totaux"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Somme des totaux hors taxes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users__property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Devise du fournisseur"

#. module: purchase
#: model:ir.model,name:purchase.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Liste de prix du fournisseur"

#. module: purchase
#: model:ir.model,name:purchase.model_account_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_tax
msgid "Tax"
msgstr "Taxe"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_country_id
msgid "Tax Country"
msgstr "Pays de la taxe"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_totals
msgid "Tax Totals"
msgstr "Taxes totales"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_calculation_rounding_method
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Méthode d'arrondi pour le calcul des taxes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Tax excl.:"
msgstr "Hors taxes :"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Tax incl.:"
msgstr "Toutes taxes comprises :"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__taxes_id
msgid "Taxes"
msgstr "Taxes"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "Champ technique utilisé à des fins d'expérience utilisateur."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr ""
"Champ technique pour filtrer les taxes disponibles en fonction du pays "
"fiscal et de la position fiscale."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Conditions générales"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__notes
msgid "Terms and Conditions"
msgstr "Conditions générales"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Code de pays ISO en deux caractères. \n"
"Vous pouvez utiliser ce champ pour une recherche rapide."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The RFQ has been acknowledged by %s."
msgstr "Le devis a été confirmée par %s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The RFQ has been declined by %s."
msgstr "Le devis a été refusé par %s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The order receipt has been acknowledged by %s."
msgstr "La réception de la commande à été constatée par %s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The order receipt has been declined by %s."
msgstr "La réception de la commande à été refusée par %s."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""
"La demande de prix est la première étape du flux d'achat. Dès\n"
"qu'elle est convertie en bon de commande, vous serez capable de contrôler la réception \n"
"des produits et la facture fournisseur."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"The vendor asked to decline this confirmed RfQ, if you agree on that, cancel"
" this PO"
msgstr ""
"Le fournisseur a demandé d'annuler ce devis confirmé. Si vous êtes d'accord,"
" annulez ce bon de commande"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "There are currently no purchase orders for your account."
msgstr "Il n'y a actuellement pas de commandes pour votre compte."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "There are currently no requests for quotation for your account."
msgstr "Il n'y a actuellement pas de demandes de prix pour votre compte."

#. module: purchase
#. odoo-python
#: code:addons/purchase/wizard/bill_to_po_wizard.py:0
msgid ""
"There are no products to add to the Purchase Order. Are these Down Payments?"
msgstr ""
"Il n'y a aucun produit à ajouter au bon de commande. S'agit-il d'acomptes ?"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"There is no invoiceable line. If a product has a control policy based on "
"received quantity, please make sure that a quantity has been received."
msgstr ""
"Il n'y a aucune ligne facturable. Si un produit a une politique de contrôle "
"basée sur les quantités réservées,  veuillez vous assurer qu'une quantité a "
"été reçue."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"This analysis allows you to easily check and analyse your company purchase history and performance.\n"
"                You can track your negotiation performance, the delivery performance of your vendors, etc"
msgstr ""
"Cette analyse vous permet de vérifier et analyser facilement l'historique et la performance des achats de votre entreprise.\n"
"À partir de ce menu, vous pouvez suivre votre performance de négociation, la performance de livraison de vos fournisseurs, etc."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,help:purchase.field_res_users__property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Cette devise sera utilisée, au lieu de celle par défaut, pour les achats "
"depuis ce partenaire actuel."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Cette valeur par défaut s'applique à tout nouveau produit créé. Elle peut "
"être modifiée dans la fiche détaillée du produit."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note is added to purchase orders."
msgstr "Cette note est ajoutée aux bons de commande."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should "
"purchase %(quantity).2f %(unit)s."
msgstr ""
"Le produit est emballé par %(pack_size).2f%(pack_name)s. Vous devriez "
"acheter %(quantity).2f%(unit)s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "This vendor bill has been created from: "
msgstr "Cette facture fournisseur a été créée depuis :"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "This vendor bill has been modified from: "
msgstr "Cette facture fournisseur a été modifiée depuis :"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Create a new RfQ"
msgstr ""
"Ce fournisseur n'a pas de bons de commande. Créez une nouvelle demande de "
"prix"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_0
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid "Tip: How to keep late receipts under control?"
msgstr "Astuce : Comment garder les réceptions en retard sous contrôle ?"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_1
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid "Tip: Never miss a purchase order"
msgstr "Astuce : Ne ratez jamais un bon de commande"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__to_approve
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__to_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "To Approve"
msgstr "À approuver"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Quantité à facturer"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "To Send"
msgstr "À envoyer"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr "Activités du jour"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_total
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Total"
msgstr "Total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Total Billed"
msgstr "Total facturé"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Total Purchased"
msgstr "Total acheté"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_pivot
msgid "Total Qty purchased"
msgstr "Quantité totale achetée"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_qty
msgid "Total Quantity"
msgstr "Quantité totale"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
msgid "Total Untaxed"
msgstr "Total hors taxes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total Untaxed amount"
msgstr "Montant total hors taxes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total amount"
msgstr "Montant total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_pivot
msgid "Total purchased amount"
msgstr "Montant total acheté"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_confirmed
msgid "True if PO reception is confirmed by the vendor."
msgstr ""
"Vrai si la réception du bon de commande est confirmée par le fournisseur."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_declined
msgid "True if PO reception is declined by the vendor."
msgstr ""
" Vrai si la réception du bon de commande est refusée par le fournisseur."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reminder_confirmed
msgid "True if the reminder email is confirmed by the vendor."
msgstr "Vrai si le mail de rappel est confirmé par le fournisseur."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Type a message..."
msgstr "Écrivez un message..."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Unable to cancel purchase order(s): %s. You must first cancel their related "
"vendor bills."
msgstr ""
"Impossible d'annuler la/les bon(s) d'achat : %s. Vous devez d'abord annuler "
"les factures fournisseurs correspondantes."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Unit"
msgstr "Unité"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Unit Price"
msgstr "Prix unitaire"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit_discounted
msgid "Unit Price (Discounted)"
msgstr "Prix unitaire (remise)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unit Price:"
msgstr "Prix unitaire :"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Units Of Measure"
msgstr "Unités de mesure"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Unités de mesure"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Catégories d'unités de mesure"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_unit_of_measure_in_config_purchase
msgid "Units of Measures"
msgstr "Unités de mesure"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr "Débloquer"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Untaxed"
msgstr "Hors taxes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Montant hors taxes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__untaxed_total
msgid "Untaxed Total"
msgstr "Total hors taxes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "UoM"
msgstr "UdM"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Update Dates"
msgstr "Dates de mise à jour"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__1
msgid "Urgent"
msgstr "Urgent"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
"Use the above REST URL to get structured data of the purchase order in UBL "
"format."
msgstr ""
"Utilisez l'URL REST ci-dessus pour obtenir les données structurées de la "
"commande d'achat au format UBL."

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Utilisateur"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Variant Grid Entry"
msgstr "Grille d'entrée des variantes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase.product_template_search_view_purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_view_search_catalog
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Fournisseur"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__vendor_bill_id
msgid "Vendor Bill"
msgstr "Facture fournisseur"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Vendor Bill lines can only be added to one Purchase Order."
msgstr ""
"Les lignes de facture fournisseur ne peuvent être ajoutées qu'à un seul bon "
"de commande."

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Vendor Bills"
msgstr "Factures fournisseurs"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor Country"
msgstr "Pays du fournisseur"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr "Listes de prix du fournisseur"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_ref
msgid "Vendor Reference"
msgstr "Référence fournisseur"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
msgid "Vendors"
msgstr "Fournisseurs"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""
"Les factures fournisseurs peuvent être pré-générées et basées sur les bons\n"
"de commande ou les reçus. Ceci vous permet de contrôler les factures\n"
"que vous recevez de votre fournisseur en fonction du document \n"
"brouillon dans Odoo."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View"
msgstr "Vue"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "View Details"
msgstr "Voir les détails"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View Order"
msgstr "Voir le bon de commande"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View Quotation"
msgstr "Voir le devis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__volume
msgid "Volume"
msgstr "Volume"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Waiting"
msgstr "En attente"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Waiting Bills"
msgstr "Factures en attente"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Waiting RFQs"
msgstr "Demandes de prix en attente"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Want to import this document in Odoo?"
msgstr "Vous souhaitez importer ce document dans Odoo ?"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__warning
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__warning
msgid "Warning"
msgstr "Avertissement"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Warning for %s"
msgstr "Avertissement pour %s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr "Avertissement sur le bon de commande"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr "Avertissement lors d'un achat de ce produit"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Warnings"
msgstr "Avertissements"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid ""
"When creating a purchase order, have a look at the vendor's <i>On Time "
"Delivery</i> rate: the percentage of products shipped on time. If it is too "
"low, activate the <i>automated reminders</i>. A few days before the due "
"shipment, Odoo will send the vendor an email to ask confirmation of shipment"
" dates and keep you informed in case of any delays. To get the vendor's "
"performance statistics, click on the OTD rate."
msgstr ""
"Quand vous créez un bon de commande, jetez un coup d'œil au taux "
"<i>Livraison dans les temps</i> : le pourcentage des produits envoyés à "
"temps. S'il est trop faible, activez les <i>rappels automatiques</i>. "
"Quelques jours avant l'expédition attendue, Odoo enverra un e-mail au "
"fournisseur afin de confirmer les dates d'expédition ou de vous informer "
"dans le cas d'un retard éventuel. Pour obtenir les statistiques de "
"performance du fournisseur, cliquez sur le taux OTD."

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid ""
"When sending a purchase order by email, Odoo asks the vendor to acknowledge "
"the reception of the order. When the vendor acknowledges the order by "
"clicking on a button in the email, the information is added on the purchase "
"order. Use filters to track orders that have not been acknowledged."
msgstr ""
"Quand un bon de commande est envoyé par e-mail, Odoo demande au fournisseur "
"de confirmer la réception de la commande. Lorsque le fournisseur confirme en"
" cliquant sur un bouton dans l'e-mail, l'information est ajoutée au bon de "
"commande. Utilisez les filtre pour suivre les commandes qui n'ont pas encore"
" été confirmées."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,help:purchase.field_purchase_order_line__partner_id
msgid "You can find a vendor by its Name, TIN, Email or Internal Reference."
msgstr ""
"Vous pouvez trouver un fournisseur par son nom, numéro de TVA, e-mail ou sa "
"référence interne."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "You can't select lines from multiple Vendor Bill to do the matching."
msgstr ""
"Vous ne pouvez pas sélectionner des lignes provenant de plusieurs factures "
"fournisseurs pour effectuer le rapprochement."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid ""
"You cannot change the type of a purchase order line. Instead you should "
"delete the current line and create a new line of the proper type."
msgstr ""
"Vous ne pouvez pas changer le type d'une ligne de bon de commande. Vous "
"devez plutôt supprimer la ligne actuelle et créer une nouvelle ligne du bon "
"type."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "You don't use a good CRM software?"
msgstr "Vous n'avez pas un bon logiciel CRM ?"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Vous devez définir un produit pour tout ce que vous vendez ou achetez,\n"
"                que ce soit un produit stockable, consommable ou un service."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you sell or purchase,\n"
"            whether it's a storable product, a consumable or a service."
msgstr ""
"Vous devez définir un produit pour tout ce que vous vendez ou achetez,\n"
"            qu'il s'agisse d'un produit stockable, consommable ou d'un service."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid ""
"You must select at least one Purchase Order line to match or create bill."
msgstr ""
"Vous devez sélectionner au moins une ligne de bon de commande pour effectuer"
" le rapprochement ou créer une facture."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Votre devis contient des produits de la société %(product_company)s malgré que votre devis est lié à la société %(quote_company)s.\n"
"Veuillez changer la société de votre devis ou retirer les produits d'autres sociétés (%(bad_products)s)."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "close"
msgstr "fermer"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "day(s) before"
msgstr "jour(s) avant"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "or create new"
msgstr "ou en créer un nouveau"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "purchase orders merged"
msgstr "bons de commande fusionnés"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "to create quotes automatically."
msgstr "pour créer des devis automatiquement."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "to learn all the ways to connect your software with"
msgstr "pour connaître toutes les façons de connecter votre logiciel à"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "with"
msgstr "avec"

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
#: model:mail.template,subject:purchase.email_template_edi_purchase_reminder
msgid "{{ object.company_id.name }} Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Commande (Ref {{ object.name or 'n/a' }})"
