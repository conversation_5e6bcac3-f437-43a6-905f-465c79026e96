# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase
# 
# Translators:
# Гэрэлтцог Цогтбаатар, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <chee<PERSON><PERSON>@gmail.com>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Munk<PERSON>bil<PERSON><PERSON> Altankhuyag <munkh<PERSON><PERSON><EMAIL>>, 2024
# tser<PERSON><PERSON><PERSON> tsogtoo <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Torbat Jargalsaikhan, 2024
# Bayarkhuu Bataa, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# hish, 2024
# Гүррагчаа Ууганбаяр, 2024
# Martin Trigaux, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Baskhuu Lodoikhuu <<EMAIL>>, 2025\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.action_report_purchase_order
msgid ""
"\n"
"                (object.state in ('draft', 'sent') and 'Request for Quotation - %s' % (object.name) or\n"
"                'Purchase Order - %s' % (object.name))"
msgstr ""
"\n"
"                (object.state in ('draft', 'sent') and 'Үнийн санал - %s' % (object.name) or\n"
"                'Худалдан авалт - %s' % (object.name))"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_bill_count
msgid "# Vendor Bills"
msgstr "# Нийлүүлэгчийн тооцоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__nbr_lines
msgid "# of Lines"
msgstr "# мөрийн тоо"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%(product)s from %(original_receipt_date)s to %(new_receipt_date)s"
msgstr "%(product)s %(original_receipt_date)s-с %(new_receipt_date)sхүртэл"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%(vendor)s confirmed the receipt will take place on %(date)s."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%s modified receipt dates for the following products:"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.report_purchase_quotation
msgid "'Request for Quotation - %s' % (object.name)"
msgstr "\"Үнийн санал - %s\" % (object.name)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
", it's 100% free! You'll save time creating beautiful quotations and track "
"sales."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "-&gt;"
msgstr "-&gt;"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr "3-н талт харгалзаа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr "3-н талт харгалзаа: Захиалга, орлого, төлбөр"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_reminder
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is a reminder that the delivery of the purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <span style=\"font-weight:bold;\">(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</span>\n"
"        </t>\n"
"        is expected for \n"
"        <t t-if=\"object.date_planned\">\n"
"            <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <span style=\"font-weight:bold;\">undefined</span>.\n"
"        </t>\n"
"        Could you please confirm it will be delivered on time?\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br/><br/>\n"
"        <t t-if=\"object.date_planned\">\n"
"            The receipt is expected for <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"            <br/><br/>\n"
"            Could you please acknowledge the receipt of this order?\n"
"        </t>\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a request for quotation <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        If you have any questions, please do not hesitate to contact us.\n"
"        <br/><br/>\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='reception')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='decline')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        <br/><br/>\n"
"        Best regards,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_partner_kanban_view
msgid ""
"<i class=\"fa fa-credit-card me-1\" aria-label=\"Purchases\" role=\"img\" "
"title=\"Purchases\"/>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Done</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/>Paid"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/>Reversed"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Waiting Payment"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Waiting for Bill</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Cancelled</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-print me-1\"/>Download / Print"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Confirmation Date</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmation</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Purchase Order #</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid ""
"<span class=\"d-none d-md-inline\">Request for Quotation #</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"o_form_label\" invisible=\"state not in ('draft', 'sent')\">Request for Quotation </span>\n"
"                        <span class=\"o_form_label\" invisible=\"state in ('draft', 'sent')\">Purchase Order </span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span class=\"o_stat_text\">Bill Matching</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "<span class=\"o_stat_text\">Purchase Matching</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "<span class=\"o_stat_text\">Purchased</span>"
msgstr "<span class=\"o_stat_text\">Худалдаж авсан</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" invisible=\"not "
"mail_reminder_confirmed\">(confirmed by vendor)</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_partner_property_form
msgid "<span> day(s) before</span>"
msgstr ""

#. module: purchase
#: model_terms:web_tour.tour,rainbow_man_message:purchase.purchase_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Сайн ажиллалаа!</b>Та энэхүү аяллын бүх алхмуудыг дамжсан "
"байна.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>Дүн</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span>Ask confirmation</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Татвар</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">From:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Нэхэмжлэл</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong class=\"d-block mt-3\">Shipping address:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"d-block mt-3\">Shipping address</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Дэд дүн</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr "<strong>Дүн</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Buyer</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Confirmation Date:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Тодорхойлолт</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Disc.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Expected Arrival:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Зорилтот огноо</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Захиалгын огноо:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Deadline:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Payment Terms: </strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Тоо</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Receipt Date:</strong>"
msgstr "<strong>Хүлээн авсан огноо\\:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Request For Quotation Date:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Татвар</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr "<strong>Захиалсан тоо хэмжээ шинэчлэгдсэн.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
msgid "<strong>The received quantity has been updated.</strong>"
msgstr "<strong>Хүлээн авсан тоо хэмжээ шинэчлэгдэв.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This purchase has been cancelled.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This quotation has been accepted.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This quotation has been declined.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Нэгж үнэ</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"A blanket purchase order is a purchase agreement between a buyer and a "
"supplier to deliver goods or services to the buyer at a preset price for a "
"period of time."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "A sample email has been sent to %s."
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr "(Худалдан авалт) дээр бараа ба харилцагчын Анхааруулга харуулах"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Ability to select a package type in purchase orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Accept"
msgstr "Зөвшөөрөх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_warning
msgid "Access warning"
msgstr "Хандалтын анхааруулга"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__account_move_id
msgid "Account Move"
msgstr "Ажил гүйлгээ"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_accrued_expense_entry
msgid "Accrued Expense Entry"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction
msgid "Action Needed"
msgstr "Үйлдэл шаардлагатай"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_ids
msgid "Activities"
msgstr "Ажилбар"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Ажилбарын тайлбар"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_state
msgid "Activity State"
msgstr "Ажилбарын төлөв"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ажилбарын төрлийн зураг"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "Add Down Payment"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "Add Products"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a note"
msgstr "Тэмдэглэл нэмэх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a product"
msgstr "Бараа нэмэх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a section"
msgstr "Бүлэг нэмэх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Add several variants to the purchase order from a grid"
msgstr ""
"Хувилбарын хүснэгтээс худалдан авалтын захиалганд барааны олон хувилбарыг "
"нэмнэ."

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Add some products or services to your quotation."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Add to PO"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Add to Purchase Order"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Administrator"
msgstr "Администратор"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "All"
msgstr "Бүх"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Draft RFQs"
msgstr "Бүх үнийн саналууд"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Late RFQs"
msgstr "Хоцорсон захиалгууд"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All RFQs"
msgstr "Бүх захиалгууд"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Waiting RFQs"
msgstr "Хүлээгдэж буй захиалгууд"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__group_send_reminder
msgid "Allow automatically send email to remind your vendor the receipt date"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__edit
msgid "Allow to edit purchase orders"
msgstr "Худалдан авалтын захиалга засахыг зөвшөөрөх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__aml_id
msgid "Aml"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__amount
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Amount"
msgstr "Дүн"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay_pass
msgid ""
"Amount of time between date planned and order by date for each purchase "
"order line."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay
msgid "Amount of time between purchase approval and order by date."
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_account
msgid "Analytic Account"
msgstr "Шинжилгээний данс"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Аналитик хуваарилалт"

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Аналитик нарийвчлал"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Захиалга батлах"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Are you sure you want to cancel the selected RFQs/Orders?"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_attachment_count
msgid "Attachment Count"
msgstr "Хавсралтын тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Шинж чанарын утгууд"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Attributes"
msgstr "Шинж чанарууд"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Auto-Complete"
msgstr "Автомат-Бөглөгдөх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_vendor_bill_id
msgid "Auto-complete"
msgstr "Автоматаар бөглөх"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_vendor_bill_id
msgid "Auto-complete from a past bill / purchase order."
msgstr "Өмнөх нэхэмжлэл/захиалгаас автоматаар бөглөх"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_id
msgid "Auto-complete from a past purchase order."
msgstr "Өмнөх захиалгаас автоматаар бөглөх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically lock confirmed orders to prevent editing"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically remind the receipt date to your vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_users__receipt_reminder_email
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"Automatically send a confirmation email to the vendor X days before the "
"expected receipt date, asking him to confirm the exact date."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_average
msgid "Average Cost"
msgstr "Дундаж өртөг"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Avg Order Value"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__default_purchase_method
msgid "Bill Control"
msgstr "Нэхэмжлэл хяналт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_count
msgid "Bill Count"
msgstr "Нэхэмжлэлийн тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__invoice_lines
msgid "Bill Lines"
msgstr "Нэхэмжлэлийн мөр"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Bill Matching"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_bill_to_po_wizard
msgid "Bill to Purchase Order"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed"
msgstr "Нэхэмжилсэн"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__billed_amount_untaxed
msgid "Billed Amount Untaxed"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_invoiced
msgid "Billed Qty"
msgstr "Нэхэмжилсэн тоо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed Quantity"
msgstr "Нэхэмжилсэн Тоо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr "Нэхэмжилсэн Тоо хэмжээ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_status
msgid "Billing Status"
msgstr "Нэхэмжлэлийн төлөв"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_ids
msgid "Bills"
msgstr "Нэхэмжлэл"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Bills Received"
msgstr "Хүлээн авсан нэхэмжлэл"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__block
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__block
msgid "Blocking Message"
msgstr "Хориглох мэдэгдэл"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__user_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__user_id
#: model:ir.model.fields,field_description:purchase.field_res_partner__buyer_id
#: model:ir.model.fields,field_description:purchase.field_res_users__buyer_id
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Buyer"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Хуанли харагдац"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Cancel"
msgstr "Цуцлах"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__cancel
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__cancel
msgid "Cancelled"
msgstr "Цуцлагдсан"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Cancelled Purchase Order #"
msgstr "Цуцлагдсан худалдан авалтын #"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Cannot delete a purchase order line which is in state “%s”."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Catalog"
msgstr "Катлог"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_category_id
msgid "Category"
msgstr "Ангилал"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Close"
msgstr "Хаах"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Худалдааны этгээд"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Communication history"
msgstr "Харилцааны түүх"

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Компани"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__company_currency_id
msgid "Company Currency"
msgstr "Компанийн валют"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total_cc
msgid "Company Total"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Compose Email"
msgstr "Имэйл бичих"

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Тохиргоо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Захиалга батлах"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_confirm_rfqs
msgid "Confirm RFQ"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Receipt Date"
msgstr "Хүлээн авах огноог баталгаажуулах"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__one_step
msgid "Confirm purchase orders in one step"
msgstr "Худалдан авалтын захиалгыг нэг алхамаар батлах"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Confirm your purchase."
msgstr "Худалдан авалтаа батлана уу."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date"
msgstr "Баталгаажсан огноо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date Last Year"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__lock
msgid "Confirmed purchase orders are not editable"
msgstr "Батлагдсан худалдан авалтын захиалгыг засварлах боломжгүй"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Connect with your software!"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Connect your software"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
msgid "Contact"
msgstr "Харилцах хаяг"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_method
msgid "Control Policy"
msgstr "Хяналтын бодлого"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Хэмжих нэгжийг хооронд нь хөрвүүлэх явдал нь зөвхөн нэг ангилалд хамаарч "
"байвал л хийгдэнэ. Хөрвүүлэлт нь харьцаан дээр суурилж явагдана."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Copy"
msgstr "Хуулах"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__country_code
msgid "Country code"
msgstr "Улсын код"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Create Bill"
msgstr "Нэхэмжлэл үүсгэх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Create Bills"
msgstr ""

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_batch_bills
msgid "Create Vendor Bills"
msgstr "Нийлүүлэгчийн нэхэмжлэл үүсгэх"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Create a new product variant"
msgstr "Барааны хувилбар шинээр үүсгэх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__currency_id
msgid "Currency"
msgstr "Валют"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_rate
msgid "Currency Rate"
msgstr "Валютын ханш"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__access_url
msgid "Customer Portal URL"
msgstr "Захиалагчийн хандах URL"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__date
msgid "Date"
msgstr "Огноо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_calendar_start
msgid "Date Calendar Start"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Date Updated"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Date:"
msgstr "Огноо:"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Days"
msgstr "Өдөр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_users__reminder_date_before_receipt
msgid "Days Before Receipt"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay
msgid "Days to Confirm"
msgstr "Захиалга баталсан хугацаа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay_pass
msgid "Days to Receive"
msgstr "Хүлээн авах хоног"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Decline"
msgstr "Татгалзах"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_price_include
msgid "Default Sales Price Include"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__company_price_include
msgid ""
"Default on whether the sales price used on the product and invoices with "
"this Company includes its taxes."
msgstr ""
"Энэ компанид ашиглагдах бүтээгдэхүүн болон нэхэмжлэхийн борлуулалтын үнэд "
"татварууд орсон эсэхийг анхдагчаар тодорхойлно."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_bill_line_match__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Агуулахын үйл ажиллагаанд ашиглагдах үндсэн хэмжих нэгж"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr "Өөрийн худалдааны нөхцөл, журмыг тодорхойлох"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_planned
msgid ""
"Delivery date expected from vendor. This date respectively defaults to "
"vendor pricelist lead time then today's date."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_planned
msgid ""
"Delivery date promised by vendor. This date is used to determine expected "
"arrival of products."
msgstr ""
"Нийлүүлэгчийн хүргэлт хийхээр амалсан огноо. Энэ огноо нь бараа хүлээн "
"авалтын төлөвлөлтөнд ашиглагдана."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_order
msgid ""
"Depicts the date within which the Quotation should be confirmed and "
"converted into a purchase order."
msgstr ""
"Үнийн санал батлагдаж борлуулалтын захиалга болох боломжтой огноог "
"тодорхойлж өгнө."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__name
msgid "Description"
msgstr "Тайлбар"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Disc.%"
msgstr "ХӨН.%"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__discount
msgid "Discount (%)"
msgstr "Хөнгөлөлт (%)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Discount:"
msgstr "Хөнгөлөлт:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_type
msgid "Display Type"
msgstr "Харагдах хэлбэр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Шинжилгээний данс хуваарилалт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Домэйн"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__done
msgid "Done"
msgstr "Дууссан"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation_amount
msgid "Double validation amount"
msgstr "Давхар хяналт шаардах дүн"

#. module: purchase
#. odoo-python
#: code:addons/purchase/wizard/bill_to_po_wizard.py:0
msgid "Down Payment (ref: %(ref)s)"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Down Payments"
msgstr "Урьдчилгаа төлбөрүүд"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__draft
msgid "Draft RFQ"
msgstr "Ноорог үнийн санал"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Draft RFQs"
msgstr "Үнийн санал"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
"Drag and drop the request for quotation PDF file into your list of "
"quotations in Odoo. Enjoy the automation!"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_planned
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_planned
msgid "Expected Arrival"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Өргөтгөсөн шүүлт"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Extra line with %s "
msgstr "%s-тэй нэмэлт мөр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__fiscal_position_id
msgid "Fiscal Position"
msgstr "Татварын тайлангийн тохируулга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Follow orders you have to fulfill"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Follow your Requests for Quotation"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_follower_ids
msgid "Followers"
msgstr "Дагагчид"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Дагагчид (Харилцагчид)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon ж.ш. fa-tasks"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable purchase order line"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "From %s"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "From Electronic Document"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__invoiced
msgid "Fully Billed"
msgstr "Бүрэн нэхэмжилсэн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr "Ирээдүйн үйл ажиллагаанууд"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__two_step
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Худалдан авалтын захиалгын батлах үе шатыг 2 болгох"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr ""
"Захиалга бүртгэх явцад бараа эсвэл нийлүүлэгчээс хамаарсан анхааруулга "
"мессеж харуулах"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__weight
msgid "Gross Weight"
msgstr "Нийт жин"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__has_message
msgid "Has Message"
msgstr "Мессежтэй"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Цуцлагдсан мөрийг нуух"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_icon
msgid "Icon"
msgstr "Дүрс"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ажилбар дээр сануулга гарсныг илэрхийлэх зураг."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Хэрэв сонгогдсон бол, шинэ зурвасууд таны анхаарлыг шаардана."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Үүнийг сонговол алдаа үүсэх үед зурвасууд ирнэ."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""
"Үүнийг сонговол, нийлүүлэгчийн нэхэмжлэл дээр 3н талт харгалзаа идэвхижнэ: "
"Нэхэмжлэл төлөхийн өмнө захиалсан барааг хүлээн авсан байх ёстой"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If installed, the product variants will be added to purchase orders through "
"a grid entry."
msgstr ""
"Хэрэв идэвхижүүлбэл худалдан авалтанд барааны хувилбарыг хүснэгтэн "
"бүртгэлээс нэмэх боломжтой болно."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_packaging__purchase
msgid "If true, the packaging can be used for purchase orders"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
msgid "Import Template for Products"
msgstr "Барааны загвар импортлох"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "In order to delete a purchase order, you must cancel it first."
msgstr ""
"Худалдан авалтын захиалгыг устгахын тулд эхлээд захиалгыг цуцлах хэрэгтэй."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"In selected purchase order to merge these details must be same\n"
"Vendor, currency, destination, dropship address and agreement"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_view_search_catalog
msgid "In the Order"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Худалдааны нөхцөл"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Indicate the product quantity you want to order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Олон улсын худалдааны нөхцөл нь улс дамнасан арилжаа худалдаанд зориулан "
"тодорхойлогдсон нөхцлүүдийн жагсаалт юм."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Нэхэмжлэл болон Орлогын баримтууд"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr "Нэхэмжлэл"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_move_line__is_downpayment
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__is_downpayment
msgid "Is Downpayment"
msgstr "Урьдчилгаа төлбөр эсэх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_is_follower
msgid "Is Follower"
msgstr "Дагагч эсэх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__is_in_purchase_order
msgid "Is In Purchase Order"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__is_purchase_matched
#: model:ir.model.fields,field_description:purchase.field_account_move__is_purchase_matched
msgid "Is Purchase Matched"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_account_move
msgid "Journal Entry"
msgstr "Ажил гүйлгээ"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move_line
msgid "Journal Item"
msgstr "Журналын бичилт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Late"
msgstr "Хоцролт"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr "Хоцорсон ажилбар"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late RFQs"
msgstr "Хоцорсон захиалгууд"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Lead Time to Purchase"
msgstr "Худалдан авалтын урьтал хугацаа"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Let's create your first request for quotation."
msgstr ""

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid ""
"Let's try the Purchase app to manage the flow from purchase to reception and"
" invoice control."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation
msgid "Levels of Approvals"
msgstr "Батлах шат дамжлага"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation
msgid "Levels of Approvals *"
msgstr "Батлах шат дамжлага *"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_amount_untaxed
msgid "Line Amount Untaxed"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_qty
msgid "Line Qty"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_uom_id
msgid "Line Uom"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr "Түгжих"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr "Батлагдсан захиалгыг түгжих"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__done
msgid "Locked"
msgstr "Түгжигдсэн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr ""
"Та зөвхөн захиалсан барааныхаа хүлээн авсан хэсэгт үндэслэн нэхэмжлэлээ "
"төлөх хэрэгтэйг анхаарна уу."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage blanket orders and purchase templates"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__qty_received_method__manual
msgid "Manual"
msgstr "Гараар"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr "Гараар нэхэмжлэл үүсгэх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_manual
msgid "Manual Received Qty"
msgstr "Хүлээн авсан тоо гараар"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lead
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Нийлүүлэгчийн урьтал хугацааны хоцролтоос сэргийлэх. Систем хэрэгцээт "
"бараанд худалдан авах захиалгыг үүсгэж хүлээн авах огноог тооцохдоо "
"нийлүүлэгчийн урьтал хугацаан дээр энд тохируулсан нөөц хоногийн хугацааг "
"нэмж тооцоолно."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__use_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Нийлүүлэгчийн урьтал хугацааны хоцролтоос сэргийлэх. Систем хэрэгцээт "
"бараанд худалдан авах захиалгыг үүсгэж хүлээн авах огноог тооцохдоо "
"нийлүүлэгчийн урьтал хугацаан дээр энд тохируулсан нөөц хоногийн хугацааг "
"нэмж тооцоолно."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Match"
msgstr "Тулгах"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_merger
msgid "Merge"
msgstr "Нэгтгэх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error
msgid "Message Delivery error"
msgstr "Зурвас илгээх алдаа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn_msg
msgid "Message for Purchase Order"
msgstr "Худалдан авалтын захиалгын мессеж"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Худалдан авалтын захиалгын мөрийн мэссэж"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_ids
msgid "Messages"
msgstr "Зурвасууд"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum Amount"
msgstr "Хамгийн бага дүн"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation_amount
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Давхар хяналт шаардагдах хамгийн бага дүн"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_accountable_required_fields
msgid "Missing required fields on accountable purchase order line."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Миний ажилбарын эцсийн огноо"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Draft RFQs"
msgstr "Миний үнийн саналууд"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Late RFQs"
msgstr "Миний хоцорсон захиалгууд"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "My Orders"
msgstr "Миний захиалга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Purchases"
msgstr "Миний худалдан авалт"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My RFQs"
msgstr "Миний захиалгууд"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Waiting RFQs"
msgstr "Миний хүлээгдэж буй захиалгууд"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "Name"
msgstr "Нэр"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Name, TIN, Email, or Reference"
msgstr "Нэр, ТТД, Имэйл, эсвэл Кодчлол"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "Newest"
msgstr "Хамгийн шинэ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календар дээрх дараагийн Үйл ажиллагаа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дараагийн ажилбарын эцсийн огноо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_summary
msgid "Next Activity Summary"
msgstr "Дараагийн ажилбарын гарчиг"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_id
msgid "Next Activity Type"
msgstr "Дараагийн ажилбарын төрөл"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__no-message
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__no-message
msgid "No Message"
msgstr "Мессеж байхгүй"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid "No Purchase Analysis"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "No product found. Let's create one!"
msgstr "Барааны бүртгэл алга. Шинийг үүсгээрэй!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid "No purchase order found. Let's create one!"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_history
msgid "No purchase order were made for this product yet!"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "No request for quotation found. Let's create one!"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__0
msgid "Normal"
msgstr "Хэвийн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Not Acknowledged"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Not using Odoo?"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Note"
msgstr "Тэмдэглэл"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Тэмдэглэлүүд"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__no
msgid "Nothing to Bill"
msgstr "Нэхэмжлэх зүйл алга"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Үйлдлийн тоо"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_users__reminder_date_before_receipt
msgid "Number of days to send reminder email before the promised receipt date"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error_counter
msgid "Number of errors"
msgstr "Алдааны тоо"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Үйлдэл шаардсан зурвасын тоо"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Алдааны мэдэгдэл бүхий зурвасын тоо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Odoo"
msgstr "Odoo"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__purchase
msgid "On ordered quantities"
msgstr "Захиалсан тоонд үндэслэх"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_method
#: model:ir.model.fields,help:purchase.field_product_template__purchase_method
msgid ""
"On ordered quantities: Control bills based on ordered quantities.\n"
"On received quantities: Control bills based on received quantities."
msgstr ""
"Захиалсан тоонд үндэслэх: Нэхэмжлэл нь захиалсан тоонд үндэслэн бүртгэгдэнэ.\n"
"Хүлээн авсан тоонд үнэслэх: Нэхэмжлэл нь хүлээн авсан барааны тоонд үндэслэн бүртгэгдэнэ."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__receive
msgid "On received quantities"
msgstr "Хүлээн авсан тоонд үндэслэх"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid ""
"Once you get the price from the vendor, you can complete the purchase order "
"with the right price."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Once you ordered your products to your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
msgid "Operation not supported"
msgstr "Энэ үйлдэл зөвшөөрөгдөөгүй"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order"
msgstr "Захиалга"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Date"
msgstr "Захиалгын огноо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Date: Last 365 Days"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_order
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Order Deadline"
msgstr "Захиалгын эцсийн огноо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__order_line
msgid "Order Lines"
msgstr "Захиалгын мөр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Захиалгын дугаар"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr "Захиалсан Тоо хэмжээ"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__purchase
msgid "Ordered quantities"
msgstr "Захиалсан тоо хэмжээ"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Orders"
msgstr "Захиалгууд"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Order due %(date)s"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Other Information"
msgstr "Бусад мэдээлэл"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Our Orders"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_id
msgid "Packaging"
msgstr "Савлагаа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__partner_id
msgid "Partner"
msgstr "Харилцагч"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__country_id
msgid "Partner Country"
msgstr "Харилцагч улс"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__payment_term_id
msgid "Payment Terms"
msgstr "Төлбөрийн нөхцөл"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Payment terms"
msgstr "Төлбөрийн нөхцөл"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Please select at least two purchase orders with state RFQ and RFQ sent to "
"merge."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__pol_id
msgid "Pol"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_url
msgid "Portal Access URL"
msgstr "Гаднаас хандах URL"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Preview the reminder email by sending it to yourself."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Price"
msgstr "Үнэ"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/product_catalog/purchase_order_line/purchase_order_line.xml:0
msgid "Price:"
msgstr "Үнэ:"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Pricing"
msgstr "Үнэ тооцоолол"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Захиалга хэвлэх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__priority
msgid "Priority"
msgstr "Урьтамж"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product"
msgstr "Бараа"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Барааны ангилал"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Барааны ангилал"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Product Description"
msgstr "Барааны тайлбар"

#. module: purchase
#: model:ir.model,name:purchase.model_product_packaging
msgid "Product Packaging"
msgstr "Барааны Баглаа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_tmpl_id
msgid "Product Template"
msgstr "Барааны загвар"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_type
msgid "Product Type"
msgstr "Барааны төрөл"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_price
msgid "Product Uom Price"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_qty
msgid "Product Uom Qty"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
msgid "Product Variant"
msgstr "Барааны хувилбар"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Барааны хувилбар"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Барааны хувилбар үүсгэдэггүй шинж чанарын утгууд"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_products
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Бараа"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Худалдан авалтын процесст давхар хяналтын тогтолцоог бий болгодог"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_packaging__purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model:ir.ui.menu,name:purchase.purchase_report
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase"
msgstr "Худалдан авалт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_requisition
msgid "Purchase Agreements"
msgstr "Худалдан авалтын гэрээ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__purchase_amount_untaxed
msgid "Purchase Amount Untaxed"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Analysis"
msgstr "Худалдан авалтын шинжилгээ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Purchase Bill Lines"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Description"
msgstr "Худалдан авалтын Тэмдэглэл"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_product_matrix
msgid "Purchase Grid Entry"
msgstr "Худалдан авалтын хүснэгтэн бүртгэл"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Purchase History"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Purchase History for %s"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lead
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lead
msgid "Purchase Lead Time"
msgstr "Нийлүүлэгчийн урьтал хугацаа"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_line_match
msgid "Purchase Line and Vendor Bill line matching view"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "Purchase Matching"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: code:addons/purchase/models/purchase_order.py:0
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__purchase_order_id
#: model:ir.model.fields.selection,name:purchase.selection__account_analytic_applicability__business_domain__purchase_order
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_activity
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Purchase Order"
msgstr "Худалдан авалтын захиалга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order #"
msgstr "Худалдан авалтын захиалга #"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_order_approval
msgid "Purchase Order Approval"
msgstr "Худалдан авалт батлах"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_account__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_order_count
msgid "Purchase Order Count"
msgstr "Худалдан авалтын баримтын тоо"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_history
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Худалдан авалтын захиалгын мөр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn
msgid "Purchase Order Line Warning"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Худалдан авалтын захиалгын мөр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lock
msgid "Purchase Order Modification"
msgstr "Худалдан авалтын захиалга өөрчлөх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lock
msgid "Purchase Order Modification *"
msgstr "Худалдан авалтын захиалга өөрчлөх *"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""
"Худалдан авах захиалгыг өөрчлөх гэдэг нь захиалга батлагдсаны дараа "
"захиалгын тоо, үнийн дүнг өөрчлөх боломжтой байхыг хэлнэ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_order_name
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_order_name
msgid "Purchase Order Name"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn
msgid "Purchase Order Warning"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/analytic_account.py:0
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.account_analytic_account_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Orders"
msgstr "Худалдан авалт"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchase Report"
msgstr "Худалдан авалтын тайлан"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_warning_purchase
msgid "Purchase Warnings"
msgstr "Худалдан авалтын анхааруулга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that have been invoiced."
msgstr "Нэхэмжлэгдсэн худалдан авалтын захиалгууд."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that include lines not invoiced."
msgstr "Нэхэмжлэгдээгүй мөр агуулж байгаа худалдан авах захиалгууд"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase products by multiple of unit # per package"
msgstr ""

#. module: purchase
#: model:ir.actions.server,name:purchase.purchase_send_reminder_mail_ir_actions_server
msgid "Purchase reminder"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase variants of a product using attributes (size, color, etc.)"
msgstr ""

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_done
msgid "Purchase: Purchase Order"
msgstr ""

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase
msgid "Purchase: Request For Quotation"
msgstr ""

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_reminder
msgid "Purchase: Vendor Reminder"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchased_product_qty
#: model:ir.model.fields,field_description:purchase.field_product_template__purchased_product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Purchased"
msgstr "Худалдан авсан"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Purchased Last 7 Days"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchased in the last 365 days"
msgstr "Сүүлийн 365 хоногт худалдан авсан"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Purchases"
msgstr "Худалдан авалт"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_union
msgid "Purchases & Bills Union"
msgstr "Худалдан авалт & нэхэмжлэл нэгтгэл"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Хэрэв шууд нийлүүлэгчээс захиалагчид хүргэж өгөхийг хүсвэл энэ хаягийг "
"бөглөнө. Үгүй бол хоосон үлдээснээр өөрийн компанид хүргүүлнэ."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Qty"
msgstr "Тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_billed
msgid "Qty Billed"
msgstr "Нэхэмжилсэн тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__qty_invoiced
msgid "Qty Invoiced"
msgstr "Нэхэмжлэгдсэн тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_ordered
msgid "Qty Ordered"
msgstr "Захиалагдсан тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_received
msgid "Qty Received"
msgstr "Хүлээн авсан тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_to_be_billed
msgid "Qty to be Billed"
msgstr "Нэхэмжлэх ёстой тоо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr "Нийлүүлэгчээс нэхэмжлэгдсэн тоо хэмжээ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Quantity"
msgstr "Тоо хэмжээ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Quantity:"
msgstr "Тоо хэмжээ:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__draft
msgid "RFQ"
msgstr "Үнийн санал"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr "Захиалга батлагдсан"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr "Захиалга зөвшөөрөгдсөн"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr "Захиалга дууссан"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__sent
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__sent
#: model:mail.message.subtype,name:purchase.mt_rfq_sent
msgid "RFQ Sent"
msgstr "Захиалга илгээгдсэн"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "RFQ merged with %(oldest_rfq_name)s and %(cancelled_rfq)s"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "RFQ merged with %s"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "RFQs"
msgstr "Захиалгууд"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "RFQs Sent Last 7 Days"
msgstr "Сүүлийн 7 хоногийн илгээгдсэн захиалгууд"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "Үнийн санал болон захиалгууд"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__rating_ids
msgid "Ratings"
msgstr "Үнэлгээнүүд"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send by Email"
msgstr "Имэйлээр дахин илгээх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Read the documentation"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_send_reminder
#: model:ir.model.fields,field_description:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,field_description:purchase.field_res_users__receipt_reminder_email
msgid "Receipt Reminder"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__receipt_reminder_email
msgid "Receipt Reminder Email"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received"
msgstr "Хүлээн авсан"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received
msgid "Received Qty"
msgstr "Хүлээн авсан тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Хүлээн авсан тооны бодлого"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received Quantity"
msgstr "Хүлээн авсан Тоо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr "Хүлээн авсан тоо хэмжээ:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__receive
msgid "Received quantities"
msgstr "Хүлээн авсан тоо"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_confirmed
msgid "Reception Confirmed"
msgstr "Орлого батлагдсан"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_declined
msgid "Reception Declined"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Record a new vendor bill"
msgstr "Нийлүүлэгчийн нэхэмжлэл шинээр бүртгэх"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__reference
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Reference"
msgstr "Холбогдол"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_tree
msgid "Reference Document"
msgstr "Холбогдох анхан шатны баримт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_uom
msgid "Reference Unit of Measure"
msgstr "Суурь хэмжих нэгж"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a"
" sales order)"
msgstr ""
"Энэхүү худалдан авалтын захиалга бүртгэгдэх учир шалтгаан болсон эх баримтын"
" дугаар (ж.ш: эсрэг талын борлуулалтын захиалга)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""
"Нийлүүлэгчээс хүлээн авсан үнийн санал эсвэл батлагдсан захиалгын дугаар. "
"Энэхүү дугаарыг ашиглан та бараагаа хүлээн авахдаа нийлүүлэгчээс ирсэн "
"хүргэлтийн хуудсыг өөрийн систем дээрх худалдан авалтын захиалгатай "
"харгалзуулан олж танихад ашиглаж болно."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reminder_confirmed
msgid "Reminder Confirmed"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_ir_actions_report
msgid "Report Action"
msgstr "Тайлагнах үйлдэл"

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report_main
msgid "Reporting"
msgstr "Тайлан"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Request for Quotation"
msgstr "Үнийн санал"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Үнийн санал #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request managers to approve orders above a minimum amount"
msgstr ""
"Хамгийн бага дүнгээс хэтэрсэн хэмжээтэй захиалгыг менежер эрхтэй хүн "
"батална."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Requests For Quotation"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_rfq_form
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Requests for Quotation"
msgstr "Үнийн санал"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Requests for quotation are documents that will be sent to your suppliers to request prices for different products you consider buying.\n"
"                Once an agreement has been found with the supplier, they will be confirmed and turned into purchase orders."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_user_id
msgid "Responsible User"
msgstr "Эд хариуцагч"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS илгээлтийн алдаа"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Scheduled Date"
msgstr "Товлогдсон огноо"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Худалдан авалт хайх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Search Reference Document"
msgstr "Баримтын холбогдлоор хайх"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Search a vendor name, or create one on the fly."
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_section
msgid "Section"
msgstr "Бүлэг"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Бүлгийн нэр (жш: Бараа, Үйлчилгээ)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__use_po_lead
msgid "Security Lead Time for Purchase"
msgstr "Худалдан авалтын аюулгүй урьтал хугацаа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_token
msgid "Security Token"
msgstr "Аюулгүй байдлын токен"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Select Vendor Bill lines to add to a Purchase Order"
msgstr ""

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Select a product, or create a new one on the fly."
msgstr "Бараа сонго, эсвэл шинийг үүсгэж ашигла."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Select a purchase order or an old bill"
msgstr "Худалдан авалт эсвэл өмнөх нэхэмжлэлийг сонгох"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users__purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"\"Анхааруулга\" сонголтыг сонгосноор хэрэглэгчид зурвас толилуулах болно. "
"\"Хориглох Мессеж\"-г сонгосноор зурвас хэлээд зогсохгүй тухайн үйлдлийг "
"хорих болно. Зурвасыг дараагийн талбарт бичих ёстой."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Sell and purchase products in different units of measure"
msgstr "Худалдан авах болон борлуулахдаа ялгаатай хэмжих нэгж ашиглах"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "PO-г имэйлээр илгээх"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_send_reminder
msgid "Send Reminder"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_send_reminder
msgid "Send an automatic reminder email to confirm delivery"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send by Email"
msgstr "Имэйлээр илгээх"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Send the request for quotation to your vendor."
msgstr ""

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase
msgid "Sent manually to vendor to request a quotation"
msgstr ""

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_done
msgid "Sent to vendor with the purchase order in attachment"
msgstr ""

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_reminder
msgid ""
"Sent to vendors before expected arrival, based on the purchase order setting"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__sequence
msgid "Sequence"
msgstr "Дугаарлалт"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Ноорог болгох"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Тохиргоо"

#. module: purchase
#: model:ir.actions.server,name:purchase.model_purchase_order_action_share
msgid "Share"
msgstr "Хуваалцах"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Өнөөдрийг хүртэлх хугацаанд дараагийн ажилбарын огноо нь тохируулагдсан бүх "
"тэмдэглэлүүд"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__reference
msgid "Source"
msgstr "Эх үүсвэр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__origin
msgid "Source Document"
msgstr "Эх үүсвэр баримт"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Starred"
msgstr "Одоор тэмдэглэсэн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Start on Odoo"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__state
msgid "State"
msgstr "Төлөв"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__state
#: model:ir.model.fields,field_description:purchase.field_purchase_report__state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Төлөв"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Ажилбаруудын төлөв байдал\n"
"Хоцорсон: Гүйцэтгэх огноо нь аль хэдий нь өнгөрсөн\n"
"Өнөөдөр: Өнөөдөр гүйцэтгэх ёстой\n"
"Төлөвлөгдсөн: Ирээдүйд гүйцэтгэх ажилбарууд"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_subtotal
msgid "Subtotal"
msgstr "Дэд дүн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Billed"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Ordered"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Received"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Total"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Untaxed Total"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users__property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Нийлүүлэгчийн валют"

#. module: purchase
#: model:ir.model,name:purchase.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Нийлүүлэлтийн үнийн хүснэгт"

#. module: purchase
#: model:ir.model,name:purchase.model_account_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_tax
msgid "Tax"
msgstr "Татвар"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_country_id
msgid "Tax Country"
msgstr "Татварын улс"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_totals
msgid "Tax Totals"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_calculation_rounding_method
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Татвар тооцооллын тоймлох арга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Tax excl.:"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Tax incl.:"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__taxes_id
msgid "Taxes"
msgstr "Татвар"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "UX зорилготой техникийн талбар"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Худалдааны нөхцөл &amp; шалгуур"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__notes
msgid "Terms and Conditions"
msgstr "Худалдааны нөхцөл, журам"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Улсын ISO код нь хоёр тэмдэгтэй байна.\n"
"Та үүнийг хурдан хайлтад хэрэглэх боломжтой."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The RFQ has been acknowledged by %s."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The RFQ has been declined by %s."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The order receipt has been acknowledged by %s."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The order receipt has been declined by %s."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""
"Үнийн саналын хүсэлт нь худалдан авах үйл ажиллагааны эхний алхам юм.\n"
"                    Худалдан авалтын захиалга болж хөрвөсний дараа бараа хүлээн авалт\n"
"                    болон нийлүүлэгчийн нэхэмжлэлийг хянах боломжтой болно."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"The vendor asked to decline this confirmed RfQ, if you agree on that, cancel"
" this PO"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "There are currently no purchase orders for your account."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "There are currently no requests for quotation for your account."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/wizard/bill_to_po_wizard.py:0
msgid ""
"There are no products to add to the Purchase Order. Are these Down Payments?"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"There is no invoiceable line. If a product has a control policy based on "
"received quantity, please make sure that a quantity has been received."
msgstr ""
"Нэхэмжлэх боломжтой мөр алга. Хэрэв бараа нь хүлээж авсан тоо хэмжээнд "
"суурилсан хяналтын бодлоготой бол хүлээж авсан тоо байгаа эсэхийг шалгана "
"уу."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"This analysis allows you to easily check and analyse your company purchase history and performance.\n"
"                You can track your negotiation performance, the delivery performance of your vendors, etc"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,help:purchase.field_res_users__property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Нийлүүлэгчээс худалдан авалт хийх үед үндсэн валютын оронд энэ валют "
"ашиглагдах болно"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Энэ утга нь шинээр бараа бүртгэх бүрт ашиглагдана. Энэ утгыг барааны "
"дэлгэрэнгүй форм дээр өөрчлөх боломжтой."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note is added to purchase orders."
msgstr "Энэ тэмдэглэлийг худалдан авалтын захиалганд нэмлээ."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should "
"purchase %(quantity).2f %(unit)s."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "This vendor bill has been created from: "
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "This vendor bill has been modified from: "
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Create a new RfQ"
msgstr "Энэ нийлүүлэгч одоогоор захиалгагүй байна. Шинэ захиалга үүсгэх"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_0
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid "Tip: How to keep late receipts under control?"
msgstr ""

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_1
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid "Tip: Never miss a purchase order"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__to_approve
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__to_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "To Approve"
msgstr "Зөвшөөрөл хүлээж буй"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Нэхэмжлэх тоо хэмжээ"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "To Send"
msgstr "Илгээх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr "Өнөөдрийн ажилбар"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_total
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Total"
msgstr "Нийт дүн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Total Billed"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Total Purchased"
msgstr "Нийт худалдан авсан дүн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_pivot
msgid "Total Qty purchased"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_qty
msgid "Total Quantity"
msgstr "Нийт тоо хэмжээ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
msgid "Total Untaxed"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total Untaxed amount"
msgstr "Нийт татваргүй дүн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total amount"
msgstr "Нийт дүн"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_pivot
msgid "Total purchased amount"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_confirmed
msgid "True if PO reception is confirmed by the vendor."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_declined
msgid "True if PO reception is declined by the vendor."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reminder_confirmed
msgid "True if the reminder email is confirmed by the vendor."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Type a message..."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Бичлэг дээрх асуудал бүхий ажилбарын төрөл"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Unable to cancel purchase order(s): %s. You must first cancel their related "
"vendor bills."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Unit"
msgstr "Ширхэг"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Unit Price"
msgstr "Нэгж үнэ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit_discounted
msgid "Unit Price (Discounted)"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unit Price:"
msgstr "Нэгж үнэ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom
msgid "Unit of Measure"
msgstr "Хэмжих нэгж"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Units Of Measure"
msgstr "Хэмжих нэгж"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Хэмжих нэгж"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Хэмжих нэгжийн ангилал"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_unit_of_measure_in_config_purchase
msgid "Units of Measures"
msgstr "Хэмжих нэгж"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr "Түгжээг нээх"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Untaxed"
msgstr "Татваргүй"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Татваргүй дүн"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__untaxed_total
msgid "Untaxed Total"
msgstr "Татваргүй нийт"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "UoM"
msgstr "Хэмжих нэгж"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Update Dates"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__1
msgid "Urgent"
msgstr "Яаралтай"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
"Use the above REST URL to get structured data of the purchase order in UBL "
"format."
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Хэрэглэгч"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Variant Grid Entry"
msgstr "Хувилбарын хүснэгтэн бүртгэл"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase.product_template_search_view_purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_view_search_catalog
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Нийлүүлэгч"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__vendor_bill_id
msgid "Vendor Bill"
msgstr "Нийлүүлэгчийн нэхэмжлэл"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Vendor Bill lines can only be added to one Purchase Order."
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Vendor Bills"
msgstr "Нийлүүлэгчийн нэхэмжлэл"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor Country"
msgstr "Нийлүүлэгч улс"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr "Нийлүүлэгчийн үнийн жагсаалт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_ref
msgid "Vendor Reference"
msgstr "Нийлүүлэгчийн дугаар"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
msgid "Vendors"
msgstr "Нийлүүлэгчид"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""
"Нийлүүлэгчийн нэхэмжлэл нь батлагдсан захиалга\n"
"                    эсвэл хүлээн авсан барааны баримт дээр үндэслэн урьдчилан бүртгэгдэх боломжтой. \n"
"                    Ингэснээр Odoo та нийлүүлэгчээс хүлээн авах нэхэмжлэлийг өөрт бүртгэлтэй байгаа ноорог мэдээлэлтэй харьцуулан хянах боломжийг олгоно."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View"
msgstr "Харах"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "View Details"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View Order"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View Quotation"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__volume
msgid "Volume"
msgstr "Эзлэхүүн"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Waiting"
msgstr "Хүлээгдэж буй"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Waiting Bills"
msgstr "Нэхэмжлэл хүлээж буй"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Waiting RFQs"
msgstr "Хүлээгдэж буй захиалгууд"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Want to import this document in Odoo?"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__warning
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__warning
msgid "Warning"
msgstr "Анхааруулга"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Warning for %s"
msgstr "%s-д зориулсан анхааруулга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr "Худалдан авалтын захиалгын анхааруулга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr "Энэ барааг худалдан авахтай холбоотой анхааруулга"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Warnings"
msgstr "Анхааруулга"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__website_message_ids
msgid "Website Messages"
msgstr "Вебсайтын зурвас"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__website_message_ids
msgid "Website communication history"
msgstr "Вебсайтын харилцааны түүх"

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid ""
"When creating a purchase order, have a look at the vendor's <i>On Time "
"Delivery</i> rate: the percentage of products shipped on time. If it is too "
"low, activate the <i>automated reminders</i>. A few days before the due "
"shipment, Odoo will send the vendor an email to ask confirmation of shipment"
" dates and keep you informed in case of any delays. To get the vendor's "
"performance statistics, click on the OTD rate."
msgstr ""

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid ""
"When sending a purchase order by email, Odoo asks the vendor to acknowledge "
"the reception of the order. When the vendor acknowledges the order by "
"clicking on a button in the email, the information is added on the purchase "
"order. Use filters to track orders that have not been acknowledged."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,help:purchase.field_purchase_order_line__partner_id
msgid "You can find a vendor by its Name, TIN, Email or Internal Reference."
msgstr ""
"Та нийлүүлэгчийг Нэр, ТТД, Имэйл эсвэл дотоод кодоор хайж олох боломжтой."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "You can't select lines from multiple Vendor Bill to do the matching."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid ""
"You cannot change the type of a purchase order line. Instead you should "
"delete the current line and create a new line of the proper type."
msgstr ""
"Худалдан авалтын захиалгын мөрийн төрлийг өөрчлөх боломжгүй. Түүний оронд "
"тухайн мөрөө устгаад шинээр энэ төрлийн мөр нэмнэ үү."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "You don't use a good CRM software?"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Та тухайн зүйлийг нөөцлөх, үйлчилгээ эсвэл хангамж төрлөөс нь үл хамааран \n"
"борлуулах эсвэл худалдан авч л байгаа бол бараа болон бүртгэх шаардлагатай. "

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you sell or purchase,\n"
"            whether it's a storable product, a consumable or a service."
msgstr ""
"Та тухайн зүйлийг нөөцлөх, үйлчилгээ эсвэл хангамж төрлөөс нь үл хамааран \n"
"борлуулах эсвэл худалдан авч л байгаа бол бараа болон бүртгэх шаардлагатай. "

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid ""
"You must select at least one Purchase Order line to match or create bill."
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "close"
msgstr "хаах"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "day(s) before"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "or create new"
msgstr ""

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "purchase orders merged"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "to create quotes automatically."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "to learn all the ways to connect your software with"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "with"
msgstr ""

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
#: model:mail.template,subject:purchase.email_template_edi_purchase_reminder
msgid "{{ object.company_id.name }} Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
