# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.action_report_purchase_order
msgid ""
"\n"
"                (object.state in ('draft', 'sent') and 'Request for Quotation - %s' % (object.name) or\n"
"                'Purchase Order - %s' % (object.name))"
msgstr ""
"\n"
"                (object.state in ('draft', 'sent') and 'Solicitação de cotação  - %s' % (object.name) or\n"
"                'Pedido de compra - %s' % (object.name))"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_bill_count
msgid "# Vendor Bills"
msgstr "Nº de faturas de fornecedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__nbr_lines
msgid "# of Lines"
msgstr "Nº de linhas"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%(product)s from %(original_receipt_date)s to %(new_receipt_date)s"
msgstr "%(product)s de %(original_receipt_date)s a %(new_receipt_date)s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%(vendor)s confirmed the receipt will take place on %(date)s."
msgstr "%(vendor)s confirmou que o recebimento ocorrerá em %(date)s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "%s modified receipt dates for the following products:"
msgstr "%s modificou as datas de recebimento para os seguintes produtos:"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.report_purchase_quotation
msgid "'Request for Quotation - %s' % (object.name)"
msgstr "'Solicitação de cotação - %s' % (object.name)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
", it's 100% free! You'll save time creating beautiful quotations and track "
"sales."
msgstr ""
"e é 100% gratuito! Você economizará tempo ao criar belas cotações e "
"acompanhar as vendas."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "-&gt;"
msgstr "-&gt;"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr "Correspondência de 3 vias"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr "Correspondência de 3 vias: compras, recebimentos e faturas"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_reminder
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is a reminder that the delivery of the purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <span style=\"font-weight:bold;\">(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</span>\n"
"        </t>\n"
"        is expected for \n"
"        <t t-if=\"object.date_planned\">\n"
"            <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <span style=\"font-weight:bold;\">undefined</span>.\n"
"        </t>\n"
"        Could you please confirm it will be delivered on time?\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Prezado <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Este é um lembrete de que a entrega do pedido de compra <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <span style=\"font-weight:bold;\">(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</span>\n"
"        </t>\n"
"        está prevista para \n"
"        <t t-if=\"object.date_planned\">\n"
"            <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <span style=\"font-weight:bold;\">indefinido</span>.\n"
"        </t>\n"
"        Poderia confirmar se a entrega será feita dentro do prazo?\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br/><br/>\n"
"        <t t-if=\"object.date_planned\">\n"
"            The receipt is expected for <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"            <br/><br/>\n"
"            Could you please acknowledge the receipt of this order?\n"
"        </t>\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Prezado <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Segue em anexo o pedido de compra <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            com a referência: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        no valor de <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">US$ 10,00</span>\n"
"        da <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br/><br/>\n"
"        <t t-if=\"object.date_planned\">\n"
"            O recebimento é esperado em <span style=\"font-weight:bold;\" t-out=\"format_date(object.date_planned) or ''\">05/05/2021</span>.\n"
"            <br/><br/>\n"
"            Poderia confirmar o recebimento deste pedido?\n"
"        </t>\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a request for quotation <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        If you have any questions, please do not hesitate to contact us.\n"
"        <br/><br/>\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='reception')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='decline')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        <br/><br/>\n"
"        Best regards,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Prezado <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Segue em anexo a solicitação de cotação <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            com a referência: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        da <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        Em caso de dúvidas, entre em contato conosco.\n"
"        <br/><br/>\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='reception')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Aceitar</a>\n"
"\n"
"            <a t-att-href=\"object.get_confirm_url(confirm_type='decline')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Recusar</a>\n"
"        <br/><br/>\n"
"        Atenciosamente,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_partner_kanban_view
msgid ""
"<i class=\"fa fa-credit-card me-1\" aria-label=\"Purchases\" role=\"img\" "
"title=\"Purchases\"/>"
msgstr ""
"<i class=\"fa fa-credit-card me-1\" aria-label=\"Purchases\" role=\"img\" "
"title=\"Purchases\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Done</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Concluído</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/>Paid"
msgstr "<i class=\"fa fa-fw fa-check\"/>Pago"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/>Reversed"
msgstr "<i class=\"fa fa-fw fa-check\"/> Revertido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Waiting Payment"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>Aguardando pagamento"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Waiting for Bill</span>"
msgstr ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Aguardando fatura do fornecedor</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Cancelled</span>"
msgstr ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Cancelado</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-print me-1\"/>Download / Print"
msgstr "<i class=\"fa fa-print me-1\"/>Baixar/Imprimir"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Confirmation Date</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmation</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Data de confirmação</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmação</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Purchase Order #</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Pedido de compra nº</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid ""
"<span class=\"d-none d-md-inline\">Request for Quotation #</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Solicitação de cotação nº</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"o_form_label\" invisible=\"state not in ('draft', 'sent')\">Request for Quotation </span>\n"
"                        <span class=\"o_form_label\" invisible=\"state in ('draft', 'sent')\">Purchase Order </span>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"state not in ('draft', 'sent')\">Solicitação de cotação </span>\n"
"                        <span class=\"o_form_label\" invisible=\"state in ('draft', 'sent')\">Pedido de compra </span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span class=\"o_stat_text\">Bill Matching</span>"
msgstr "<span class=\"o_stat_text\">Correspondência de contas</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "<span class=\"o_stat_text\">Purchase Matching</span>"
msgstr "<span class=\"o_stat_text\">Correspondência de compras</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "<span class=\"o_stat_text\">Purchased</span>"
msgstr "<span class=\"o_stat_text\">Comprado</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" invisible=\"not "
"mail_reminder_confirmed\">(confirmed by vendor)</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"not "
"mail_reminder_confirmed\">(confirmado pelo fornecedor)</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_partner_property_form
msgid "<span> day(s) before</span>"
msgstr "<span> dia(s) antes</span>"

#. module: purchase
#: model_terms:web_tour.tour,rainbow_man_message:purchase.purchase_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Muito bem!</b> Você passou por todas as etapas deste tour.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>Valor</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span>Ask confirmation</span>"
msgstr "<span>Solicitar confirmação</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Impostos</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">From:</strong>"
msgstr "<strong class=\"d-block mb-1\">De:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Faturas</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong class=\"d-block mt-3\">Shipping address:</strong>"
msgstr "<strong class=\"d-block mt-3\">Endereço de entrega:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"d-block mt-3\">Shipping address</strong>"
msgstr "<strong class=\"d-block mt-3\">Endereço de entrega</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Subtotal</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr "<strong>Total</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Buyer</strong>"
msgstr "<strong>Comprador</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Confirmation Date:</strong>"
msgstr "<strong>Data de confirmação:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Descrição</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Disc.</strong>"
msgstr "<strong>Desc.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Expected Arrival:</strong>"
msgstr "<strong>Chegada prevista:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Data prevista</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Data do pedido:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Deadline:</strong>"
msgstr "<strong>Prazo do pedido:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Payment Terms: </strong>"
msgstr "<strong>Condições de pagamento: </strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Qtd.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Receipt Date:</strong>"
msgstr "<strong>Data de recebimento:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Request For Quotation Date:</strong>"
msgstr "<strong>Data da solicitação de cotação:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Impostos</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr "<strong>A quantidade pedida foi atualizada.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
msgid "<strong>The received quantity has been updated.</strong>"
msgstr "<strong>A quantidade recebida foi atualizada.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This purchase has been cancelled.</strong>"
msgstr "<strong>Esta compra foi cancelada.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This quotation has been accepted.</strong>"
msgstr "<strong>Essa cotação foi aceita.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This quotation has been declined.</strong>"
msgstr "<strong>Essa cotação foi recusada.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Preço unitário</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference</strong>"
msgstr "<strong>Referência do seu pedido</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"A blanket purchase order is a purchase agreement between a buyer and a "
"supplier to deliver goods or services to the buyer at a preset price for a "
"period of time."
msgstr ""
"Um pedido de compra genérico é um contrato de compra entre um comprador e um"
" fornecedor para entregar bens ou serviços ao comprador a um preço "
"predefinido por um período de tempo."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "A sample email has been sent to %s."
msgstr "Um e-mail de exemplo foi enviado para %s."

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr "Um aviso pode ser configurado em um produto ou um cliente (compra)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Ability to select a package type in purchase orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Possibilita selecionar um tipo de embalagem em pedidos de compra e forçar "
"uma quantidade que seja um múltiplo do número de unidades por embalagem."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Accept"
msgstr "Aceitar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_warning
msgid "Access warning"
msgstr "Aviso de acesso"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"De acordo com a configuração do produto, a quantidade recebida pode ser calculada automaticamente pelo mecanismo:\n"
"  - Manual: a quantidade é definida manualmente na linha\n"
"  - Movimentações no estoque: a quantidade vem de separações confirmadas\n"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__account_move_id
msgid "Account Move"
msgstr "Movimentação contábil"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_accrued_expense_entry
msgid "Accrued Expense Entry"
msgstr "Entrada de despesa acumulada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction
msgid "Action Needed"
msgstr "Requer ação"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_ids
msgid "Activities"
msgstr "Atividades"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoração de atividade excepcional"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_state
msgid "Activity State"
msgstr "Status da atividade"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone do tipo de atividade"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "Add Down Payment"
msgstr "Adicionar pagamento de entrada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "Add Products"
msgstr "Adicionar produtos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a note"
msgstr "Adicionar uma nota"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a product"
msgstr "Adicionar produto"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a section"
msgstr "Adiciona uma seção"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Add several variants to the purchase order from a grid"
msgstr "Adicione várias variantes ao pedido de compra a partir de uma grade"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Add some products or services to your quotation."
msgstr "Adicione alguns produtos ou serviços à sua cotação."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Add to PO"
msgstr "Adicionar ao pedido"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Add to Purchase Order"
msgstr "Adicionar ao pedido de compra"

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Administrator"
msgstr "Administrador"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "All"
msgstr "Tudo"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Draft RFQs"
msgstr "Todas as SDCs em rascunho"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Late RFQs"
msgstr "Todas as SDCs atrasadas"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All RFQs"
msgstr "Todas as SDCs"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "All Waiting RFQs"
msgstr "Todas as SDCs aguardando"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__group_send_reminder
msgid "Allow automatically send email to remind your vendor the receipt date"
msgstr ""
"Permitir o envio automático de e-mail para lembrar seu fornecedor da data de"
" recebimento"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__edit
msgid "Allow to edit purchase orders"
msgstr "Permitir editar pedidos de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__aml_id
msgid "Aml"
msgstr "Aml"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__amount
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Amount"
msgstr "Valor"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay_pass
msgid ""
"Amount of time between date planned and order by date for each purchase "
"order line."
msgstr ""
"Tempo entre a data planejada e o pedido por data para cada linha de pedido "
"de compra."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay
msgid "Amount of time between purchase approval and order by date."
msgstr "Período de tempo entre a aprovação da compra e o pedido por data."

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_account
msgid "Analytic Account"
msgstr "Conta analítica"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribuição analítica"

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Aplicações do plano analítico"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Precisão analítica"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Aprovar pedido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Are you sure you want to cancel the selected RFQs/Orders?"
msgstr ""
"Tem certeza de que quer cancelar as solicitações de cotação/pedidos "
"selecionados?"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_attachment_count
msgid "Attachment Count"
msgstr "Contagem de anexos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Valores de atributos"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Attributes"
msgstr "Atributos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Auto-Complete"
msgstr "Preenchimento automático"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_vendor_bill_id
msgid "Auto-complete"
msgstr "Preenchimento automático"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_vendor_bill_id
msgid "Auto-complete from a past bill / purchase order."
msgstr ""
"Preencher automaticamente a partir de uma fatura/pedido de compra anterior."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_id
msgid "Auto-complete from a past purchase order."
msgstr "Preencher automaticamente a partir de um pedido de compra anterior."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically lock confirmed orders to prevent editing"
msgstr "Travar automaticamente pedidos confirmados para impedir a edição"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically remind the receipt date to your vendors"
msgstr "Lembrar automaticamente a data de recebimento aos seus fornecedores"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_users__receipt_reminder_email
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"Automatically send a confirmation email to the vendor X days before the "
"expected receipt date, asking him to confirm the exact date."
msgstr ""
"Enviar automaticamente um e-mail de confirmação ao fornecedor X dias antes "
"da data prevista de recebimento, solicitando a confirmação da data exata."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_average
msgid "Average Cost"
msgstr "Custo médio"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Avg Order Value"
msgstr "Valor médio do pedido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__default_purchase_method
msgid "Bill Control"
msgstr "Controle de fatura"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_count
msgid "Bill Count"
msgstr "Contagem de faturas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__invoice_lines
msgid "Bill Lines"
msgstr "Linhas da fatura"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Bill Matching"
msgstr "Correspondência de contas"

#. module: purchase
#: model:ir.model,name:purchase.model_bill_to_po_wizard
msgid "Bill to Purchase Order"
msgstr "Conta para pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed"
msgstr "Faturado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__billed_amount_untaxed
msgid "Billed Amount Untaxed"
msgstr "Valor faturado sem impostos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_invoiced
msgid "Billed Qty"
msgstr "Qtd. faturada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed Quantity"
msgstr "Quantidade faturada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr "Quantidade faturada:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_status
msgid "Billing Status"
msgstr "Status do faturamento"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_ids
msgid "Bills"
msgstr "Faturas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Bills Received"
msgstr "Faturas recebidas"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__block
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__block
msgid "Blocking Message"
msgstr "Mensagem de bloqueio"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__user_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__user_id
#: model:ir.model.fields,field_description:purchase.field_res_partner__buyer_id
#: model:ir.model.fields,field_description:purchase.field_res_users__buyer_id
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Buyer"
msgstr "Comprador"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Visualização de calendário"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Cancel"
msgstr "Cancelar"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__cancel
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Cancelled Purchase Order #"
msgstr "Pedido de compra cancelado nº"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Cannot delete a purchase order line which is in state “%s”."
msgstr ""
"Não é possível excluir uma linha de pedido de compra que está no estado "
"\"%s\"."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Catalog"
msgstr "Catálogo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_category_id
msgid "Category"
msgstr "Categoria"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Close"
msgstr "Fechar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entidade comercial"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Communication history"
msgstr "Histórico de comunicação"

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Empresa"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__company_currency_id
msgid "Company Currency"
msgstr "Moeda da empresa"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total_cc
msgid "Company Total"
msgstr "Total da empresa"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Compose Email"
msgstr "Escrever e-mail"

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Configuração"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Confirmar pedido"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_confirm_rfqs
msgid "Confirm RFQ"
msgstr "Confirmar SDC"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Receipt Date"
msgstr "Confirmar data de recebimento"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__one_step
msgid "Confirm purchase orders in one step"
msgstr "Confirmar pedidos de compra em uma etapa"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Confirm your purchase."
msgstr "Confirme sua compra."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date"
msgstr "Data de confirmação"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date Last Year"
msgstr "Data de confirmação no último ano"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__lock
msgid "Confirmed purchase orders are not editable"
msgstr "Pedidos de compra confirmados não são editáveis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Connect with your software!"
msgstr "Conecte-se com seu software!"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Connect your software"
msgstr "Conecte seu software"

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_method
msgid "Control Policy"
msgstr "Política de controle"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"A conversão entre unidades de medida só pode ocorrer se elas pertencerem à "
"mesma categoria. A conversão será feita com base nas proporções."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Copy"
msgstr "Copiar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__country_code
msgid "Country code"
msgstr "Código do país"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Create Bill"
msgstr "Criar fatura"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Create Bills"
msgstr "Criar faturas"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_batch_bills
msgid "Create Vendor Bills"
msgstr "Criar faturas de fornecedor"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Create a new product variant"
msgstr "Criar uma nova variante de produto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_date
msgid "Created on"
msgstr "Criado em"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_rate
msgid "Currency Rate"
msgstr "Taxa de câmbio"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__access_url
msgid "Customer Portal URL"
msgstr "URL do portal do cliente"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__date
msgid "Date"
msgstr "Data"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_calendar_start
msgid "Date Calendar Start"
msgstr "Data de início do calendário"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Date Updated"
msgstr "Data atualizada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Date:"
msgstr "Data:"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Days"
msgstr "Dias"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_users__reminder_date_before_receipt
msgid "Days Before Receipt"
msgstr "Dias antes do recebimento"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay
msgid "Days to Confirm"
msgstr "Dias para confirmar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay_pass
msgid "Days to Receive"
msgstr "Dias para receber"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Decline"
msgstr "Recusar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_price_include
msgid "Default Sales Price Include"
msgstr "Padrão - Preço de venda inclui"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__company_price_include
msgid ""
"Default on whether the sales price used on the product and invoices with "
"this Company includes its taxes."
msgstr ""
"Predeterminação sobre a inclusão de impostos no preço de venda usado nos "
"produtos e nas faturas dessa empresa."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_bill_line_match__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Unidade de medida padrão usada para todas as operações de estoque."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr "Defina seus termos e condições..."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_planned
msgid ""
"Delivery date expected from vendor. This date respectively defaults to "
"vendor pricelist lead time then today's date."
msgstr ""
"Data de entrega esperada do fornecedor. Essa data assume como padrão o prazo"
" constado na lista de preços do fornecedor e a data de hoje, "
"respectivamente."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_planned
msgid ""
"Delivery date promised by vendor. This date is used to determine expected "
"arrival of products."
msgstr ""
"Data de entrega prometida pelo fornecedor. Esta data é usada para determinar"
" a expectativa de chegada dos produtos."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_order
msgid ""
"Depicts the date within which the Quotation should be confirmed and "
"converted into a purchase order."
msgstr ""
"Representa até quando a cotação deve ser confirmada e convertida em um "
"pedido de compra."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__name
msgid "Description"
msgstr "Descrição"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Disc.%"
msgstr "% desc."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__discount
msgid "Discount (%)"
msgstr "Desconto (%)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Discount:"
msgstr "Desconto:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_type
msgid "Display Type"
msgstr "Tipo de exibição"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Conta analítica de distribuição"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domínio"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__done
msgid "Done"
msgstr "Concluído"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation_amount
msgid "Double validation amount"
msgstr "Quantia de validação dupla"

#. module: purchase
#. odoo-python
#: code:addons/purchase/wizard/bill_to_po_wizard.py:0
msgid "Down Payment (ref: %(ref)s)"
msgstr "Entrada (ref: %(ref)s)"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Down Payments"
msgstr "Entradas"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__draft
msgid "Draft RFQ"
msgstr "SDC em rascunho"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Draft RFQs"
msgstr "SDCs em rascunho"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
"Drag and drop the request for quotation PDF file into your list of "
"quotations in Odoo. Enjoy the automation!"
msgstr ""
"Arraste e solte o arquivo PDF da solicitação de cotação em sua lista de "
"cotações no Odoo. Aproveite a automação!"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "Endereço de envio direto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_planned
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_planned
msgid "Expected Arrival"
msgstr "Chegada prevista"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Filtros avançados"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Extra line with %s "
msgstr "Linha extra com %s "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__fiscal_position_id
msgid "Fiscal Position"
msgstr "Posição fiscal"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Follow orders you have to fulfill"
msgstr "Acompanhe os pedidos que você precisa atender"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Follow your Requests for Quotation"
msgstr "Acompanhe suas solicitações de cotação"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (usuários)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ícone do Font Awesome. Ex.: fa-tasks"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable purchase order line"
msgstr "Valores inválidos em uma linha não faturável do pedido de compra."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "From %s"
msgstr "De %s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "From Electronic Document"
msgstr "Do documento eletrônico"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__invoiced
msgid "Fully Billed"
msgstr "Totalmente faturado"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr "Atividades futuras"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__two_step
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Obter 2 níveis de aprovações para confirmar um pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr "Receber alertas em pedidos para produtos ou fornecedores"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""
"Mercadorias são materiais tangíveis e produtos fornecidos.\n"
"Um serviço é um produto não material oferecido."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__weight
msgid "Gross Weight"
msgstr "Peso bruto"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Agrupar por"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__has_message
msgid "Has Message"
msgstr "Tem uma mensagem"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Ocultar linhas canceladas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ícone para indicar uma atividade excepcional."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se marcado, novas mensagens solicitarão sua atenção."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se marcado, algumas mensagens têm um erro de entrega."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""
"Se habilitado, ativa a correspondência de 3 vias para faturas de fornecedor:"
" os itens devem ser recebidos para que a fatura seja paga."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If installed, the product variants will be added to purchase orders through "
"a grid entry."
msgstr ""
"Se instalado, as variantes do produto serão adicionadas aos pedidos de "
"compra por meio de uma entrada na grade."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_packaging__purchase
msgid "If true, the packaging can be used for purchase orders"
msgstr "Se verdadeiro, a embalagem pode ser utilizada para pedidos de compra"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
msgid "Import Template for Products"
msgstr "Importar modelo para produtos"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "In order to delete a purchase order, you must cancel it first."
msgstr "Para excluir um pedido de compra, é necessário cancelá-lo primeiro."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"In selected purchase order to merge these details must be same\n"
"Vendor, currency, destination, dropship address and agreement"
msgstr ""
"No pedido de compra selecionado para mesclagem, essas informações devem ser as mesmas\n"
"Fornecedor, moeda, destino, endereço de dropship e contrato"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_view_search_catalog
msgid "In the Order"
msgstr "No pedido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Incoterm"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Indicate the product quantity you want to order."
msgstr "Indique a quantidade de produtos que você deseja comprar."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"International Commercial Terms são uma série de termos comerciais pré-"
"definidos utilizados em transações internacionais."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Faturas e remessas recebidas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr "Faturamento"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_move_line__is_downpayment
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__is_downpayment
msgid "Is Downpayment"
msgstr "É entrada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_is_follower
msgid "Is Follower"
msgstr "É um seguidor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__is_in_purchase_order
msgid "Is In Purchase Order"
msgstr "Está no pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__is_purchase_matched
#: model:ir.model.fields,field_description:purchase.field_account_move__is_purchase_matched
msgid "Is Purchase Matched"
msgstr "Tem correspondência de compra"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move
msgid "Journal Entry"
msgstr "Entrada no diário"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move_line
msgid "Journal Item"
msgstr "Item do diário"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Late"
msgstr "Atrasado"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr "Atividades atrasadas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late RFQs"
msgstr "SDCs atrasadas"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Lead Time to Purchase"
msgstr "Prazo para compra"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Let's create your first request for quotation."
msgstr "Crie sua primeira solicitação de cotação."

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid ""
"Let's try the Purchase app to manage the flow from purchase to reception and"
" invoice control."
msgstr ""
"Vamos testar o aplicativo de Compra para gerenciar o fluxo desde a compra "
"até o recebimento e o controle de fatura."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation
msgid "Levels of Approvals"
msgstr "Níveis de aprovações"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation
msgid "Levels of Approvals *"
msgstr "Níveis de aprovação *"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_amount_untaxed
msgid "Line Amount Untaxed"
msgstr "Valor sem impostos da linha"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_qty
msgid "Line Qty"
msgstr "Qtd. da linha"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__line_uom_id
msgid "Line Uom"
msgstr "UM da linha"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr "Travar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr "Travar pedidos confirmados"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__done
msgid "Locked"
msgstr "Travado"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr "Somente pague as faturas dos pedidos cujos produtos você recebeu"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage blanket orders and purchase templates"
msgstr "Gerenciar pedidos genérico e modelos de compra"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__qty_received_method__manual
msgid "Manual"
msgstr "Manual"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr "Faturamento manual"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_manual
msgid "Manual Received Qty"
msgstr "Qtd. recebida manualmente"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lead
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"A margem de erro para os prazos de entrega do fornecedor. Quando o sistema "
"gera pedidos de compra para a aquisição de produtos, eles serão agendados "
"esse número de dias antes para considerar atrasos inesperados de "
"fornecedores."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__use_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"A margem de erro para os prazos de entrega do fornecedor. Quando o sistema "
"gera pedidos de compra para a aquisição de produtos, eles serão agendados "
"esse número de dias antes para considerar atrasos inesperados de "
"fornecedores."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Match"
msgstr "Conciliar"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_merger
msgid "Merge"
msgstr "Mesclar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error
msgid "Message Delivery error"
msgstr "Erro na entrega da mensagem"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn_msg
msgid "Message for Purchase Order"
msgstr "Mensagem para pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Mensagem para linha do pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum Amount"
msgstr "Valor mínimo"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation_amount
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Valor mínimo para o qual é necessário uma dupla validação"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_accountable_required_fields
msgid "Missing required fields on accountable purchase order line."
msgstr ""
"Campos obrigatórios ausentes em linha contabilizável do pedido de compra."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo da minha atividade"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Draft RFQs"
msgstr "Minhas SDCs em rascunho"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Late RFQs"
msgstr "Minhas SDCs atrasadas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "My Orders"
msgstr "Meus pedidos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Purchases"
msgstr "Minhas compras"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My RFQs"
msgstr "Minhas SDCs"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "My Waiting RFQs"
msgstr "Minhas SDCs aguardando"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "Name"
msgstr "Nome"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Name, TIN, Email, or Reference"
msgstr "Nome, TIN, e-mail ou referência"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
msgid "Newest"
msgstr "Mais recente"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Evento no calendário para a próxima atividade"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prazo da próxima atividade"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_summary
msgid "Next Activity Summary"
msgstr "Resumo da próxima atividade"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da próxima atividade"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__no-message
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__no-message
msgid "No Message"
msgstr "Sem mensagem"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid "No Purchase Analysis"
msgstr "Sem análise de compra"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "No product found. Let's create one!"
msgstr "Nenhum produto encontrado. Vamos criar o primeiro!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid "No purchase order found. Let's create one!"
msgstr "Nenhum pedido de compra encontrado. Vamos criar o primeiro!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_history
msgid "No purchase order were made for this product yet!"
msgstr "Nenhum pedido de compra foi realizado para este produto ainda."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "No request for quotation found. Let's create one!"
msgstr "Nenhuma solicitação de cotação encontrada. Vamos criar a primeira!"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__0
msgid "Normal"
msgstr "Normal"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Not Acknowledged"
msgstr "Não reconhecido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Not using Odoo?"
msgstr "Não está usando o Odoo?"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Note"
msgstr "Observação"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Notas"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__no
msgid "Nothing to Bill"
msgstr "Nada para faturar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de ações"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_users__reminder_date_before_receipt
msgid "Number of days to send reminder email before the promised receipt date"
msgstr ""
"Número de dias para enviar um lembrete por e-mail antes da data de "
"recebimento prometida"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensagens que requerem ação"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com erro de entrega"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Odoo"
msgstr "Odoo"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__purchase
msgid "On ordered quantities"
msgstr "Em quantidades solicitadas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_method
#: model:ir.model.fields,help:purchase.field_product_template__purchase_method
msgid ""
"On ordered quantities: Control bills based on ordered quantities.\n"
"On received quantities: Control bills based on received quantities."
msgstr ""
"Em quantidades solicitadas: controlar faturas com base nas quantidades solicitadas.\n"
"Em quantidades recebidas: controlar faturas com base nas quantidades recebidas."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__receive
msgid "On received quantities"
msgstr "Em quantidades recebidas"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid ""
"Once you get the price from the vendor, you can complete the purchase order "
"with the right price."
msgstr ""
"Depois de obter o preço do fornecedor, você pode concluir o pedido de compra"
" com o preço certo."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Once you ordered your products to your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""
"Após fazer o pedido dos seus produtos ao fornecedor, confirme sua "
"solicitação de cotação e ela se tornará um pedido de compra."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
msgid "Operation not supported"
msgstr "Operação não suportada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order"
msgstr "Pedido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Date"
msgstr "Data do pedido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Date: Last 365 Days"
msgstr "Data do pedido: últimos 365 dias"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_order
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Order Deadline"
msgstr "Prazo do pedido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__order_line
msgid "Order Lines"
msgstr "Linhas do pedido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Referência do pedido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr "Quantidade solicitada:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__purchase
msgid "Ordered quantities"
msgstr "Quantidades solicitadas"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Orders"
msgstr "Pedidos"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Order due %(date)s"
msgstr "Prazo do pedido %(date)s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Other Information"
msgstr "Outras informações"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
msgid "Our Orders"
msgstr "Nossos pedidos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_id
msgid "Packaging"
msgstr "Embalagem"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Quantidade por embalagem"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__partner_id
msgid "Partner"
msgstr "Usuário"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__country_id
msgid "Partner Country"
msgstr "País do usuário"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__payment_term_id
msgid "Payment Terms"
msgstr "Condições de pagamento"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Payment terms"
msgstr "Condições de pagamento"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Please select at least two purchase orders with state RFQ and RFQ sent to "
"merge."
msgstr ""
"Selecione pelo menos dois pedidos de compra no estado de SDC e SDC enviada "
"para mesclar."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__pol_id
msgid "Pol"
msgstr "Pol"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_url
msgid "Portal Access URL"
msgstr "URL de acesso ao portal"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Preview the reminder email by sending it to yourself."
msgstr "Pré-visualize o e-mail de lembrete enviando-o para você mesmo."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Price"
msgstr "Preço"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/product_catalog/purchase_order_line/purchase_order_line.xml:0
msgid "Price:"
msgstr "Preço:"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Pricing"
msgstr "Preços"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Imprimir SDC"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__priority
msgid "Priority"
msgstr "Prioridade"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product"
msgstr "Produto"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Categorias de produtos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Categoria de produtos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Product Description"
msgstr "Decrição do produto"

#. module: purchase
#: model:ir.model,name:purchase.model_product_packaging
msgid "Product Packaging"
msgstr "Embalagem do produto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_tmpl_id
msgid "Product Template"
msgstr "Modelo de produto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_type
msgid "Product Type"
msgstr "Tipo de produto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_price
msgid "Product Uom Price"
msgstr "Preço da UM do produto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_qty
msgid "Product Uom Qty"
msgstr "Qtd. da UM do produto"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
msgid "Product Variant"
msgstr "Variante do produto"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Variantes de produto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Valores de atributo do produto que não criam variantes"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_products
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Produtos"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Fornecer um mecanismo de dupla validação para compras"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_packaging__purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model:ir.ui.menu,name:purchase.purchase_report
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase"
msgstr "Compras"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_requisition
msgid "Purchase Agreements"
msgstr "Acordos de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__purchase_amount_untaxed
msgid "Purchase Amount Untaxed"
msgstr "Valor da compra sem impostos"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Analysis"
msgstr "Análise de compras"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Purchase Bill Lines"
msgstr "Linhas de conta de compras"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Description"
msgstr "Descrição da compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_product_matrix
msgid "Purchase Grid Entry"
msgstr "Entrada de grade de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Purchase History"
msgstr "Histórico de compras"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Purchase History for %s"
msgstr "Histórico de compras para %s"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lead
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lead
msgid "Purchase Lead Time"
msgstr "Prazo de entrega da compra"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_line_match
msgid "Purchase Line and Vendor Bill line matching view"
msgstr ""
"Visualização da correspondência de linhas de compras e de contas do "
"fornecedor"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "Purchase Matching"
msgstr "Correspondência de compras"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: code:addons/purchase/models/purchase_order.py:0
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_bill_to_po_wizard__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__purchase_order_id
#: model:ir.model.fields.selection,name:purchase.selection__account_analytic_applicability__business_domain__purchase_order
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_activity
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Purchase Order"
msgstr "Pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order #"
msgstr "Pedido de compra nº"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_order_approval
msgid "Purchase Order Approval"
msgstr "Aprovação do pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_account__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_order_count
msgid "Purchase Order Count"
msgstr "Contagem de pedidos de compra"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_history
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Linha do pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn
msgid "Purchase Order Line Warning"
msgstr "Aviso da linha do pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Linhas do pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lock
msgid "Purchase Order Modification"
msgstr "Modificação do pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lock
msgid "Purchase Order Modification *"
msgstr "Modificação do pedido de compra *"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""
"Modificação do pedido de compra é usado quando você deseja que o pedido de "
"compra seja editável após a confirmação"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_order_name
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_order_name
msgid "Purchase Order Name"
msgstr "Nome do pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn
msgid "Purchase Order Warning"
msgstr "Aviso de pedido de compra"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/analytic_account.py:0
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.account_analytic_account_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Orders"
msgstr "Pedidos de compra"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchase Report"
msgstr "Relatório de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_warning_purchase
msgid "Purchase Warnings"
msgstr "Avisos de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that have been invoiced."
msgstr "Pedidos de compra que foram faturados."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that include lines not invoiced."
msgstr "Pedidos de compra com linhas não faturadas."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase products by multiple of unit # per package"
msgstr "Comprar produtos em múltiplos do número de unidades por embalagem"

#. module: purchase
#: model:ir.actions.server,name:purchase.purchase_send_reminder_mail_ir_actions_server
msgid "Purchase reminder"
msgstr "Lembrete de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase variants of a product using attributes (size, color, etc.)"
msgstr "Compre variantes de um produto usando atributos (tamanho, cor etc.)"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_done
msgid "Purchase: Purchase Order"
msgstr "Compra: pedido de compra"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase
msgid "Purchase: Request For Quotation"
msgstr "Compra: solicitação de cotação"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_reminder
msgid "Purchase: Vendor Reminder"
msgstr "Compra: lembrete ao fornecedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchased_product_qty
#: model:ir.model.fields,field_description:purchase.field_product_template__purchased_product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Purchased"
msgstr "Comprado"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Purchased Last 7 Days"
msgstr "Comprado nos últimos 7 dias"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchased in the last 365 days"
msgstr "Comprado nos últimos 365 dias"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Purchases"
msgstr "Compras"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_union
msgid "Purchases & Bills Union"
msgstr "União de compras e faturas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Insira um endereço caso deseje entregar diretamente do fornecedor ao "
"cliente, ou mantenha o campo vazio para entregar para a sua própria empresa."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Qty"
msgstr "Qtd"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_billed
msgid "Qty Billed"
msgstr "Qtd. faturada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__qty_invoiced
msgid "Qty Invoiced"
msgstr "Qtd Faturada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_ordered
msgid "Qty Ordered"
msgstr "Qtd. solicitada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_received
msgid "Qty Received"
msgstr "Qtd. recebida"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_to_be_billed
msgid "Qty to be Billed"
msgstr "Qtd. a faturar"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr "Quantidades cobradas pelos fornecedores"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Quantity"
msgstr "Quantidade"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Quantity:"
msgstr "Quantidade:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__draft
msgid "RFQ"
msgstr "SDC"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr "SDC aprovada"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr "SDC confirmada"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr "SDC concluída"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__sent
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__sent
#: model:mail.message.subtype,name:purchase.mt_rfq_sent
msgid "RFQ Sent"
msgstr "SDC enviada"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "RFQ merged with %(oldest_rfq_name)s and %(cancelled_rfq)s"
msgstr "SDC mesclada com %(oldest_rfq_name)s e %(cancelled_rfq)s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "RFQ merged with %s"
msgstr "SDC mesclada com %s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "RFQs"
msgstr "SDCs"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "RFQs Sent Last 7 Days"
msgstr "SDCs enviadas nos últimos 7 dias"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "SDCs e compras"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__rating_ids
msgid "Ratings"
msgstr "Avaliações"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send by Email"
msgstr "Reenviar por e-mail"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Read the documentation"
msgstr "Leia a documentação"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_send_reminder
#: model:ir.model.fields,field_description:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,field_description:purchase.field_res_users__receipt_reminder_email
msgid "Receipt Reminder"
msgstr "Lembrete de recebimento"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__receipt_reminder_email
msgid "Receipt Reminder Email"
msgstr "E-mail de lembrete de recebimento"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received"
msgstr "Recebido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received
msgid "Received Qty"
msgstr "Qtd. recebida"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Método de qtd. recebida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received Quantity"
msgstr "Quantidade recebida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr "Quantidade recebida:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__receive
msgid "Received quantities"
msgstr "Quantidades recebidas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_confirmed
msgid "Reception Confirmed"
msgstr "Recebimento confirmado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_declined
msgid "Reception Declined"
msgstr "Recebimento recusado"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Record a new vendor bill"
msgstr "Registrar uma nova fatura de fornecedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__reference
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Reference"
msgstr "Referência"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_tree
msgid "Reference Document"
msgstr "Documento de referência"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_uom
msgid "Reference Unit of Measure"
msgstr "Unidade de medida de referência"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a"
" sales order)"
msgstr ""
"Referência do documento que gerou essa solicitação de pedido de compra (ex.:"
" um pedido de venda)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""
"Referência do pedido de venda ou oferta enviada pelo fornecedor. É usada "
"para fazer a correspondência quando você recebe os produtos, já que esta "
"referência geralmente vem escrita no pedido de entrega enviado pelo seu "
"fornecedor."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reminder_confirmed
msgid "Reminder Confirmed"
msgstr "Lembrete confirmado"

#. module: purchase
#: model:ir.model,name:purchase.model_ir_actions_report
msgid "Report Action"
msgstr "Ação de relatório"

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report_main
msgid "Reporting"
msgstr "Relatórios"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Request for Quotation"
msgstr "Solicitação de cotação"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Solicitação de cotação nº"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request managers to approve orders above a minimum amount"
msgstr "Solicite que gerentes aprovem pedidos acima de um valor mínimo"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Requests For Quotation"
msgstr "Solicitações de cotação"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_rfq_form
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Requests for Quotation"
msgstr "Solicitações de cotação"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Requests for quotation are documents that will be sent to your suppliers to request prices for different products you consider buying.\n"
"                Once an agreement has been found with the supplier, they will be confirmed and turned into purchase orders."
msgstr ""
"As solicitações de cotação são documentos que serão enviados aos seus fornecedores para solicitar preços de diferentes produtos que você considera comprar.\n"
"                Ao chegar a um acordo com o fornecedor, elas serão confirmadas e transformadas em pedidos de compra."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_user_id
msgid "Responsible User"
msgstr "Usuário responsável"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro no envio de SMS"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Scheduled Date"
msgstr "Data programada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Buscar pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Search Reference Document"
msgstr "Buscar documento de referência"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Search a vendor name, or create one on the fly."
msgstr "Busque o nome de um fornecedor ou crie um na hora."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_section
msgid "Section"
msgstr "Seção"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Nome da seção (ex., produtos, serviços)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__use_po_lead
msgid "Security Lead Time for Purchase"
msgstr "Margem de segurança de tempo para compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_token
msgid "Security Token"
msgstr "Token de segurança"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Select Vendor Bill lines to add to a Purchase Order"
msgstr ""
"Selecione as linhas da fatura do fornecedor para adicionar a um pedido de "
"compra"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Select a product, or create a new one on the fly."
msgstr "Selecione um produto ou crie um novo na hora."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Select a purchase order or an old bill"
msgstr "Selecione um pedido de compra ou uma fatura antiga"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users__purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Selecionar a opção de “Aviso” notificará o usuário com a mensagem. Se "
"selecionar “Mensagem de bloqueio”, isso lançará uma exceção com a mensagem e"
" bloqueará o fluxo. A mensagem tem de ser escrita no campo seguinte."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Sell and purchase products in different units of measure"
msgstr "Venda e compre produtos em diferentes unidades de medidas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Enviar pedido de compra por e-mail"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_send_reminder
msgid "Send Reminder"
msgstr "Enviar lembrete"

#. module: purchase
#: model:res.groups,name:purchase.group_send_reminder
msgid "Send an automatic reminder email to confirm delivery"
msgstr "Enviar um lembrete automático por e-mail para confirmar a entrega"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send by Email"
msgstr "Enviar por e-mail"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
msgid "Send the request for quotation to your vendor."
msgstr "Envie a solicitação de cotação ao seu fornecedor."

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase
msgid "Sent manually to vendor to request a quotation"
msgstr "Enviado manualmente ao fornecedor para solicitar uma cotação"

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_done
msgid "Sent to vendor with the purchase order in attachment"
msgstr "Enviado ao fornecedor com o pedido de compra em anexo"

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_reminder
msgid ""
"Sent to vendors before expected arrival, based on the purchase order setting"
msgstr ""
"Enviado aos fornecedores antes da chegada prevista, com base na configuração"
" do pedido de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Definir como rascunho"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Definições"

#. module: purchase
#: model:ir.actions.server,name:purchase.model_purchase_order_action_share
msgid "Share"
msgstr "Compartilhar"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos os registros em que a próxima data de ação seja antes de hoje"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__reference
msgid "Source"
msgstr "Origem"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__origin
msgid "Source Document"
msgstr "Documento de origem"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Starred"
msgstr "Favoritos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Start on Odoo"
msgstr "Iniciar no Odoo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__state
msgid "State"
msgstr "Estado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__state
#: model:ir.model.fields,field_description:purchase.field_purchase_report__state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Status"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseado em atividades\n"
"Atrasado: data de vencimento já passou\n"
"Hoje: data da atividade é hoje\n"
"Planejado: atividades futuras."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Billed"
msgstr "Soma da quantidade faturada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Ordered"
msgstr "Soma da quantidade solicitada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Received"
msgstr "Some da quantidade recebida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Total"
msgstr "Soma do total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Soma do total sem impostos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users__property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Moeda do fornecedor"

#. module: purchase
#: model:ir.model,name:purchase.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Lista de preços do fornecedor"

#. module: purchase
#: model:ir.model,name:purchase.model_account_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_tax
msgid "Tax"
msgstr "Imposto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_country_id
msgid "Tax Country"
msgstr "País fiscal"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_totals
msgid "Tax Totals"
msgstr "Total de impostos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_calculation_rounding_method
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Método de arredondamento de imposto"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Tax excl.:"
msgstr "Sem impostos:"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Tax incl.:"
msgstr "Impostos incl.:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__taxes_id
msgid "Taxes"
msgstr "Impostos"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "Campo técnico para fins de UX."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr ""
"Campo técnico para filtrar os impostos disponíveis dependo do país fiscal e "
"da posição fiscal."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Termos e Condições"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__notes
msgid "Terms and Conditions"
msgstr "Termos e Condições"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"O código ISO do país de dois caracteres. \n"
"Use este campo para fazer uma busca."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The RFQ has been acknowledged by %s."
msgstr "A SDC foi reconhecida por %s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The RFQ has been declined by %s."
msgstr "A SDC foi recusada por %s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The order receipt has been acknowledged by %s."
msgstr "O recebimento do pedido foi confirmado por %s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "The order receipt has been declined by %s."
msgstr "O recebimento do pedido foi recusado por %s."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""
"A solicitação de cotação é a primeira etapa do fluxo compra. Após ser\n"
"                    convertida em um pedido de compra, você poderá controlar o recebimento\n"
"                    dos produtos e a fatura do fornecedor."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"The vendor asked to decline this confirmed RfQ, if you agree on that, cancel"
" this PO"
msgstr ""
"O fornecedor pediu para recusar essa SDC confirmada. Se você concordar com "
"isso, cancele esse PO"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "There are currently no purchase orders for your account."
msgstr "Não há pedidos de compra para a sua conta no momento."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "There are currently no requests for quotation for your account."
msgstr "Não há solicitações de cotação para a sua conta no momento."

#. module: purchase
#. odoo-python
#: code:addons/purchase/wizard/bill_to_po_wizard.py:0
msgid ""
"There are no products to add to the Purchase Order. Are these Down Payments?"
msgstr ""
"Não há produtos para adicionar ao pedido de compra. Trata-se de pagamentos "
"de entrada?"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"There is no invoiceable line. If a product has a control policy based on "
"received quantity, please make sure that a quantity has been received."
msgstr ""
"Não há nenhuma linha faturável. Se um produto possui uma política de "
"controle baseada na quantidade recebida, certifique-se de que a quantidade "
"foi recebida."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"This analysis allows you to easily check and analyse your company purchase history and performance.\n"
"                You can track your negotiation performance, the delivery performance of your vendors, etc"
msgstr ""
"Esta análise permite a você conferir e analisar facilmente o histórico e desempenho de compra da sua empresa.\n"
"                É possível monitorar o desempenho de negociação, o desempenho de entrega dos fornecedores etc."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,help:purchase.field_res_users__property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Esta moeda será utilizada, em vez da padrão, para compras do usuário atual"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Esse valor padrão é aplicado a qualquer novo produto criado. Isso pode ser "
"alterado no formulário de informações do produto."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note is added to purchase orders."
msgstr "Esta nota é adicionada a pedidos de compra."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should "
"purchase %(quantity).2f %(unit)s."
msgstr ""
"O produto é embalado por %(pack_size).2f%(pack_name)s. É necessário comprar "
"%(quantity).2f%(unit)s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "This vendor bill has been created from: "
msgstr "Esta fatura de fornecedor foi criada a partir de:"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
msgid "This vendor bill has been modified from: "
msgstr "Esta fatura de fornecedor foi modificada a partir de:"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Create a new RfQ"
msgstr "Este fornecedor não tem pedidos de compra. Crie uma nova SDC."

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_0
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid "Tip: How to keep late receipts under control?"
msgstr "Dica: como manter os recebimentos atrasados ​​sob controle?"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_1
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid "Tip: Never miss a purchase order"
msgstr "Dica: nunca perca um pedido de compra"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__to_approve
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__to_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "To Approve"
msgstr "A aprovar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Quantidade a faturar"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "To Send"
msgstr "A enviar"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr "Atividades de hoje"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_total
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Total"
msgstr "Total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Total Billed"
msgstr "Total faturado"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Total Purchased"
msgstr "Total Adquirido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_pivot
msgid "Total Qty purchased"
msgstr "Quantidade total comprada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_qty
msgid "Total Quantity"
msgstr "Quantidade total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
msgid "Total Untaxed"
msgstr "Total sem impostos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total Untaxed amount"
msgstr "Valor sem impostos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total amount"
msgstr "Valor total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_pivot
msgid "Total purchased amount"
msgstr "Valor total da compra"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_confirmed
msgid "True if PO reception is confirmed by the vendor."
msgstr ""
"Verdadeiro se o recebimento do pedido de compra foi confirmado pelo "
"fornecedor."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_declined
msgid "True if PO reception is declined by the vendor."
msgstr ""
"Verdadeiro se o recebimento do pedido de compra foi recusado pelo "
"fornecedor."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reminder_confirmed
msgid "True if the reminder email is confirmed by the vendor."
msgstr "Verdadeiro se o e-mail de lembrete foi confirmado pelo fornecedor."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Type a message..."
msgstr "Digite uma mensagem..."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de atividade de exceção registrada."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Unable to cancel purchase order(s): %s. You must first cancel their related "
"vendor bills."
msgstr ""
"Não é possível cancelar o(s) pedido(s) de compra: %s Você deve primeiro "
"cancelar as faturas de fornecedor relacionadas."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
msgid "Unit"
msgstr "Unidade"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Unit Price"
msgstr "Preço unitário"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit_discounted
msgid "Unit Price (Discounted)"
msgstr "Preço unitário (com desconto)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unit Price:"
msgstr "Preço unitário:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_line_match__product_uom_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom
msgid "Unit of Measure"
msgstr "Unidade de medida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Units Of Measure"
msgstr "Unidades de medida"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Categorias de unidade de medida"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_unit_of_measure_in_config_purchase
msgid "Units of Measures"
msgstr "Unidades de medida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr "Destravar"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Untaxed"
msgstr "Sem impostos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Valor sem impostos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__untaxed_total
msgid "Untaxed Total"
msgstr "Total sem impostos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "UoM"
msgstr "UM"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "Update Dates"
msgstr "Datas de atualização"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__1
msgid "Urgent"
msgstr "Urgente"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid ""
"Use the above REST URL to get structured data of the purchase order in UBL "
"format."
msgstr ""
"Use o URL REST acima para obter dados estruturados do pedido de compra no "
"formato UBL."

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Usuário"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Variant Grid Entry"
msgstr "Entrada de grade de variante"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase.product_template_search_view_purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_view_search_catalog
#: model_terms:ir.ui.view,arch_db:purchase.purchase_bill_line_match_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_history_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Fornecedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__vendor_bill_id
msgid "Vendor Bill"
msgstr "Fatura de fornecedor"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "Vendor Bill lines can only be added to one Purchase Order."
msgstr ""
"As linhas da conta do fornecedor só podem ser adicionadas a um pedido de "
"compra."

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Vendor Bills"
msgstr "Faturas de fornecedor"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor Country"
msgstr "País do fornecedor"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr "Listas de preços de fornecedores"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_ref
msgid "Vendor Reference"
msgstr "Ref. do fornecedor"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
msgid "Vendors"
msgstr "Fornecedores"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""
"Faturas de fornecedores podem ser previamente geradas com base no pedido\n"
"                    de compra ou em recibos. Isto possibilita controlar faturas\n"
"                    recebidas do seu fornecedor de acordo com o documento\n"
"                    em rascunho no Odoo."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View"
msgstr "Visualização"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "View Details"
msgstr "Ver detalhes"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View Order"
msgstr "Ver pedido"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "View Quotation"
msgstr "Ver cotação"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__volume
msgid "Volume"
msgstr "Volume"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
msgid "Waiting"
msgstr "Aguardando"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Waiting Bills"
msgstr "Faturas em espera"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Waiting RFQs"
msgstr "SDCs aguardando"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Want to import this document in Odoo?"
msgstr "Quer importar esse documento para o Odoo?"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__warning
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__warning
msgid "Warning"
msgstr "Aviso"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
#: code:addons/purchase/models/purchase_order_line.py:0
msgid "Warning for %s"
msgstr "Aviso para %s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr "Aviso no pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr "Aviso ao comprar este produto"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Warnings"
msgstr "Aviso"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do site"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do site"

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid ""
"When creating a purchase order, have a look at the vendor's <i>On Time "
"Delivery</i> rate: the percentage of products shipped on time. If it is too "
"low, activate the <i>automated reminders</i>. A few days before the due "
"shipment, Odoo will send the vendor an email to ask confirmation of shipment"
" dates and keep you informed in case of any delays. To get the vendor's "
"performance statistics, click on the OTD rate."
msgstr ""
"Ao criar um pedido de compra, confira a taxa de <i>entrega dentro do "
"prazo</i> do fornecedor: a porcentagem de produtos enviados dentro do prazo."
" Se estiver muito baixa, ative os <i>lembretes automáticos</i>. Poucos dias "
"antes do prazo de envio, o Odoo enviará ao fornecedor um e-mail para "
"solicitar a confirmação das datas de envio e para mantê-lo informado em caso"
" de atrasos. Para obter as estatísticas de desempenho do fornecedor, clique "
"na taxa de EDP."

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid ""
"When sending a purchase order by email, Odoo asks the vendor to acknowledge "
"the reception of the order. When the vendor acknowledges the order by "
"clicking on a button in the email, the information is added on the purchase "
"order. Use filters to track orders that have not been acknowledged."
msgstr ""
"Ao enviar um pedido de compra por e-mail, o Odoo pede ao fornecedor que "
"confirme o recebimento do pedido. Quando o fornecedor confirma o recebimento"
" do pedido clicando em um botão do e-mail, a informação é adicionada ao "
"pedido de compra. Use filtros para rastrear pedidos cujo recebimento não foi"
" confirmado."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,help:purchase.field_purchase_order_line__partner_id
msgid "You can find a vendor by its Name, TIN, Email or Internal Reference."
msgstr ""
"Você pode encontrar um fornecedor por seu nome, CNPJ, e-mail ou referência "
"interna."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid "You can't select lines from multiple Vendor Bill to do the matching."
msgstr ""
"Não é possível selecionar linhas de várias contas de fornecedores para fazer"
" a correspondência."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order_line.py:0
msgid ""
"You cannot change the type of a purchase order line. Instead you should "
"delete the current line and create a new line of the proper type."
msgstr ""
"Não é possível alterar o tipo de linha do pedido de compra. É necessário "
"excluir a linha atual e criar uma nova linha do tipo apropriado."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "You don't use a good CRM software?"
msgstr "Não está usando um bom software de CRM?"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"É necessário definir um produto para tudo que você vende ou compra,\n"
"                seja um produto armazenável, um consumível ou um serviço."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you sell or purchase,\n"
"            whether it's a storable product, a consumable or a service."
msgstr ""
"É necessário definir um produto para tudo que você vende ou compra,\n"
"            seja um produto armazenável, um consumível ou um serviço."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_bill_line_match.py:0
msgid ""
"You must select at least one Purchase Order line to match or create bill."
msgstr ""
"Você deve selecionar pelo menos uma linha do pedido de compra para "
"corresponder ou criar uma conta."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Sua cotação contém produtos da empresa %(product_company)s, e a sua cotação pertence à empresa %(quote_company)s.\n"
"Altere a empresa da sua cotação ou remova produtos de outras empresas (%(bad_products)s)."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "close"
msgstr "fechar"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "day(s) before"
msgstr "dia(s) antes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.bill_to_po_wizard_form
msgid "or create new"
msgstr "ou criar novo"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase_order.py:0
msgid "purchase orders merged"
msgstr "pedidos de compra mesclados"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "to create quotes automatically."
msgstr "para criar cotações automaticamente."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "to learn all the ways to connect your software with"
msgstr "para conhecer todas as maneiras de conectar seu software com"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "with"
msgstr "com"

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
#: model:mail.template,subject:purchase.email_template_edi_purchase_reminder
msgid "{{ object.company_id.name }} Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"Pedido: {{ object.company_id.name }} (Ref: {{ object.name or 'não "
"disponível' }})"
