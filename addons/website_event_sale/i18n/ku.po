# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "<i class=\"fa fa-check me-2\" role=\"img\"/>Registered"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "<small>Add this event to your calendar</small>"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_attendee_details
msgid ""
"<span>Sign In</span>\n"
"                <span class=\"fa fa-sign-in\"/>"
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Add to Google Agenda"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Add to iCal/Outlook"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_attendee_details
msgid "Confirm Registration"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Don't miss out!"
msgstr ""

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Event Subtitle"
msgstr ""

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Event Title"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.modal_ticket_registration
msgid "Free"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.modal_ticket_registration
msgid "From"
msgstr "لە"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_attendee_details
msgid "Go to Payment"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Google Agenda"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Outlook"
msgstr ""

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr ""

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_template
msgid "Product"
msgstr "بەرهەم"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product Variant"
msgstr "جۆری بەرهەم"

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_event_sale_report__is_published
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_sale_report_view_search
msgid "Published Events"
msgstr ""

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
msgid "The provided ticket doesn't exist"
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
msgid "The ticket doesn't match with this product."
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
msgid "Warning"
msgstr "ئاگادار کردنەوە"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "We are looking forward to meeting you at the following"
msgstr ""

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
msgid "You cannot raise manually the event ticket quantity in your cart"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "event"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "events"
msgstr ""

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.modal_ticket_registration
msgid "to"
msgstr "بۆ"
