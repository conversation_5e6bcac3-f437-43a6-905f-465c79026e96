# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx (Original)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx (Suggested)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "(ALT Tag)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "(TITLE Tag)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "(URL or Embed)"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "1x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "2 columns"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "2x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "3 Stars"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "3 columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "3x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "4 columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "4x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "5 Stars"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "5x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">deg</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2 ms-3\">Y</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2\">X</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Basic</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Creative</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Decorative</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Devices</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Linear</span>"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "ACCESS OPTIONS ANYWAY"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "AI Copywriter"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "AI Tools"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Above"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Accepts"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Add"
msgstr "زیادکردن"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Add Column"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Add Row"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Add URL"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a blockquote section"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a button"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a code section"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add an emoji"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy &amp; Zigs"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "Alert"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "All"
msgstr "گشت"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "All documents have been loaded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "All images have been loaded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Allow users to view and edit the field in HTML."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "An error occurred while fetching the entered URL."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Animated"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Anonymous"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Apply"
msgstr "جێبەجێکردن"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Are you sure you want to delete the block %s?"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "Are you sure you want to delete this file?"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Aspect Ratio"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Autoconvert to Relative Link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Autoconvert to relative link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Autoplay"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Back to one column"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Background Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Shapes"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Danger"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Info"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Success"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Warning"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banners"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_base
msgid "Base"
msgstr "بناغە"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Basic blocks"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Below"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Big section heading"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Block"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Block &amp; Rainy"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Blocks"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Style"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Width"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Brushed"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Bulleted list"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Button"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
msgid "Button Primary"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
msgid "Button Secondary"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "CSS Edit"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Cancel"
msgstr "ڕەتکردنەوە"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "Change media description and tooltip"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "ChatGPT"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Checklist"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Choose a record..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "Close"
msgstr "داخستن"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Code"
msgstr "کۆد"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Codeview"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Collaborative edition"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Collaborative trigger"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Color"
msgstr "ڕەنگ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Color Filter"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Colors"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Column"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Common colors"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composites"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composition"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Confirm"
msgstr "دڵنیاکردنەوە"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid "Content conflict"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Content generated"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 2 columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 3 columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 4 columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Copy-paste your URL or embed code here"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Could not install module %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_service.js:0
msgid "Could not load the file \"%s\"."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Create"
msgstr "دروستکردن"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create a list with numbering"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create a simple bulleted list"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create an URL."
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "دروستکراوە لەلایەن..."

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "دروستکراوە لە"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Crop Image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Custom"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Custom %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Custom Button"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Customize"
msgstr "بە خواستی خۆت بیکە"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Dailymotion"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Dashed"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Decoration"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Default"
msgstr "بنەڕەتی"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Default + Rounded"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Delete"
msgstr "سڕینەوە"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Delete %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Description"
msgstr "وەسف"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Devices"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Direct Download"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Discard"
msgstr "ڕەتکردنەوە"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid ""
"Discover a world of awesomeness in our copyright-free image haven. No legal "
"drama, just nice images!"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 1"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 2"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 3"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 4"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Do you want to install %s App?"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Documents"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Dotted"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Double"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Double-click to edit"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Drag and drop the building block."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Duplicate Container"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Dynamic Placeholder"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Edit media description"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed Image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed Youtube Video"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed the image in the document."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed the youtube video in the document."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Emoji"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Empty quote"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Error"
msgstr "هەڵە"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_html_field_history_mixin
msgid "Field html History"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "File has been uploaded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill + Rounded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Filter"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flat"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid "Flexible"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Flip"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flip Horizontal"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flip Vertical"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating Shapes"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Focus"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Font Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Font size"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "For technical reasons, this block cannot be dropped here"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Format"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Full screen"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
msgid "Fullscreen"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Generate Text with AI"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Generate or transform content with AI"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Generate or transform content with AI."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "Generating"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "Generating an alternative..."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Panels"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Rounded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Gradient"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 1"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 2"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 3"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 4"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 2"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 3"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 4"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 5"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 6"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 1"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 2"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 3"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 4"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 5"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 6"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Height"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide Dailymotion logo"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide fullscreen button"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide player controls"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide sharing button"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history
msgid "History data"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history_metadata
msgid "History metadata"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Horizontal mirror"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Html"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Icon"
msgstr "ئایکۆن"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Icon Formatting"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 1x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 2x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 3x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 4x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 5x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Icons"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Illustrations"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Image Formatting"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Image padding"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Images"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Inline Text"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
msgid "Insert"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Insert a Link / Button"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Insert a block"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a danger banner"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Insert a field"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a horizontal rule separator"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a picture"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a rating over 3 stars"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a rating over 5 stars"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a success banner"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a table"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a video"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a warning banner"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert above"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert an info banner"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert below"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Insert media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Insert or edit link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert right"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert your signature"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Install"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/add_snippet_dialog.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Install %s"
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_ui_view.py:0
msgid "Invalid field value for %(field_name)s: %(value)s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Invisible Elements"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Item"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Label"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Large"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "دوایین نوێکردنەوە لەلایەن..."

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "دوایین نوێکردنەوە لە..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Layout"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Light"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Label"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Shape"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Size"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Style"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "Link copied to clipboard."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link to an uploaded document"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "List"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Load more..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Loading..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Loop"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Marketing Tools"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Max height"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Medium"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Medium section heading"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Min height"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "More info about this app."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move down"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move right"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move up"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "My Images"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "ناو"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Navigation"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "No"
msgstr "نەخێر"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "No URL specified"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
msgid "No documents found."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "No images found."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "No more records"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "No pictograms found."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "No videos"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "None"
msgstr "هیچکامیان"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Normal"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Numbered list"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.tests
msgid "Odoo Editor Tests"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Open in New Window"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Open in new window"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "Optimized"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Outline"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Outline + Rounded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Overlay"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Page Options"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Panels"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Paragraph block"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Paste as URL"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Position"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Preview"
msgstr "پێشبینی"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "Progress bar"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Quote"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "REPLACE BY NEW VERSION"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Readonly field"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove (DELETE)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Remove Block"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove Current"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Remove columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Remove format"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Rename %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Rename the block"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Replace"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Replace media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Reset Image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Reset Size"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Resizable"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Default"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Full"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Half"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Quarter"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Right"
msgstr "ڕاست"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Rotate Left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Rotate Right"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate left"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate right"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Row"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Sandboxed preview"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Save"
msgstr "هەڵگرتن"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Save and Install"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Save and Reload"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "Search a document"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "Search a pictogram"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Search an image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Search for a block"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Search for a block (e.g. numbers, image wall, ...)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search for records..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search more..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search to show more records"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Select a block on your page to style it."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
msgid "Select a media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Send a message"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Separator"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shadow"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Shape"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Circle"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Rounded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Thumbnail"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shapes"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Show optimized images"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Show/Hide on Mobile"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Signature"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Small"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Small section heading"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Snippet name"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Snippets"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Solid"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid ""
"Someone with escalated rights previously modified this area, you are "
"therefore not able to modify it yourself."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid ""
"Specify when the collaboration starts. 'Focus' will start the collaboration "
"session when the user clicks inside the text field (default), 'Start' when "
"the record is loaded (could impact performance if set)."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Speed"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Start"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Stretch"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Structure"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Style"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Suggestions"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Switch direction"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Switch the text's direction"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Table"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Table Options"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Text"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Text Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Text align"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Text style"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "The URL does not seem to work."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "The URL seems valid."
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_qweb_fields.py:0
msgid "The datetime %(value)s does not match the format %(format)s"
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
msgid ""
"The document was already saved from someone with a different history for "
"model \"%(model)s\", field \"%(field)s\" with id \"%(id)d\"."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "The provided url does not reference any supported video"
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
msgid "The provided url is invalid"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "The provided url is not valid"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid ""
"The version from the database will be used.\n"
"                            If you need to keep your changes, copy the content below and edit the new document."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Theme"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Theme colors"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid "There is a conflict between your version and the one in the database."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Thinking..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "This URL is invalid. Preview couldn't be updated."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "This block cannot be dropped anywhere on this page."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "This block is outdated."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "This document is not saved!"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "This file is a public view attachment."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "This file is attached to the current record."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid "This image is an external image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Tip: Esc to preview"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Title"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle bold"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle checklist"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle icon spin"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle italic"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle ordered list"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle strikethrough"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle underline"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle unordered list"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Tooltip"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Track tasks with a checklist"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Translate"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Translate with AI"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
msgid "Translating..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Transparent colors"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "Try searching with other keywords."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Type"
msgstr "جۆر"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Type \"/\" for commands"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "URL or Email"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "Upload a document"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Upload an image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Uploaded image's format is not supported. Try with: "
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Vertical mirror"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Video"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Video Formatting"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Video code"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Videos"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Videos are muted when autoplay is enabled"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "بینین"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Vimeo"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Widgets"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Width"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid ""
"With the option enabled, all content can only be viewed in a sandboxed "
"iframe or in the code editor."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid ""
"Wow, it feels a bit empty in here. Upload from the button in the top right "
"corner!"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Write something..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "XL"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Yes"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid ""
"You can not replace a field by this image. If you want to use this image, "
"first save it on your computer and then upload it here."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "You can not use this image in a field"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "You can still access the block options but it might be ineffective."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "You might not be able to customize it anymore."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Youku"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Your URL"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Your content was successfully generated."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Youtube"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Zoom In"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Zoom Out"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "alternatives..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "and"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "px"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "to"
msgstr "بۆ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "videos"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_websocket
msgid "websocket message handling"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "www.example.com"
msgstr ""
