<?xml version='1.0' encoding='UTF-8'?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
  <cbc:ID>INV/2019/00001</cbc:ID>
  <cbc:IssueDate>2019-01-01</cbc:IssueDate>
  <cbc:IssueTime>10:00:00Z</cbc:IssueTime>
  <cbc:InvoiceTypeCode listVersionID="1.1">01</cbc:InvoiceTypeCode>
  <cbc:DocumentCurrencyCode>MYR</cbc:DocumentCurrencyCode>
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cbc:IndustryClassificationCode name="Growing of maize">01111</cbc:IndustryClassificationCode>
      <cac:PartyIdentification>
        <cbc:ID schemeID="TIN">C2584563200</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="BRN">************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>company_1_data</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:CityName>Main city</cbc:CityName>
        <cbc:CountrySubentity>Johor</cbc:CountrySubentity>
        <cbc:CountrySubentityCode>01</cbc:CountrySubentityCode>
        <cac:AddressLine>
          <cbc:Line>that one street, 5</cbc:Line>
        </cac:AddressLine>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1" listAgencyID="6">MYS</cbc:IdentificationCode>
          <cbc:Name>Malaysia</cbc:Name>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>company_1_data</cbc:RegistrationName>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:Name>company_1_data</cbc:Name>
        <cbc:Telephone>+***********</cbc:Telephone>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="TIN">EI00000000020</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="BRN">NA</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>partner_b</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:CityName>Main city</cbc:CityName>
        <cbc:CountrySubentity>Alabama</cbc:CountrySubentity>
        <cbc:CountrySubentityCode>AL</cbc:CountrySubentityCode>
        <cac:AddressLine>
          <cbc:Line>that other street, 3</cbc:Line>
        </cac:AddressLine>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1" listAgencyID="6">USA</cbc:IdentificationCode>
          <cbc:Name>United States</cbc:Name>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_b</cbc:RegistrationName>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:Name>partner_b</cbc:Name>
        <cbc:Telephone>+***********</cbc:Telephone>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:Delivery>
    <cac:DeliveryParty>
      <cac:PartyIdentification>
        <cbc:ID schemeID="TIN">EI00000000020</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="BRN">NA</cbc:ID>
      </cac:PartyIdentification>
      <cac:PostalAddress>
        <cbc:CityName>Main city</cbc:CityName>
        <cbc:CountrySubentity>Alabama</cbc:CountrySubentity>
        <cbc:CountrySubentityCode>AL</cbc:CountrySubentityCode>
        <cac:AddressLine>
          <cbc:Line>that other street, 3</cbc:Line>
        </cac:AddressLine>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1" listAgencyID="6">USA</cbc:IdentificationCode>
          <cbc:Name>United States</cbc:Name>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_b</cbc:RegistrationName>
      </cac:PartyLegalEntity>
    </cac:DeliveryParty>
  </cac:Delivery>
  <cac:PaymentTerms>
    <cbc:Note>Payment terms: 30% Advance End of Following Month</cbc:Note>
  </cac:PaymentTerms>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="MYR">0.00</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="MYR">1000.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="MYR">0.00</cbc:TaxAmount>
      <cbc:Percent>0.0</cbc:Percent>
      <cac:TaxCategory>
        <cbc:ID>E</cbc:ID>
        <cbc:Name>Exempt Customer</cbc:Name>
        <cbc:Percent>0.0</cbc:Percent>
        <cbc:TaxExemptionReason>Exempt Customer</cbc:TaxExemptionReason>
        <cac:TaxScheme>
          <cbc:ID schemeID="UN/ECE 5153" schemeAgencyID="6">OTH</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="MYR">1000.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="MYR">1000.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="MYR">1000.00</cbc:TaxInclusiveAmount>
    <cbc:PrepaidAmount currencyID="MYR">0.00</cbc:PrepaidAmount>
    <cbc:PayableAmount currencyID="MYR">1000.00</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>
  <cac:InvoiceLine>
    <cbc:ID>1</cbc:ID>
    <cbc:InvoicedQuantity unitCode="C62">1.0</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="MYR">1000.00</cbc:LineExtensionAmount>
    <cac:TaxTotal>
      <cbc:TaxAmount currencyID="MYR">0.00</cbc:TaxAmount>
      <cac:TaxSubtotal>
        <cbc:TaxableAmount currencyID="MYR">1000.00</cbc:TaxableAmount>
        <cbc:TaxAmount currencyID="MYR">0.00</cbc:TaxAmount>
        <cbc:Percent>0.0</cbc:Percent>
        <cac:TaxCategory>
          <cbc:ID>E</cbc:ID>
          <cbc:Name>Exempt Customer</cbc:Name>
          <cbc:Percent>0.0</cbc:Percent>
          <cbc:TaxExemptionReason>Exempt Customer</cbc:TaxExemptionReason>
          <cac:TaxScheme>
            <cbc:ID schemeID="UN/ECE 5153" schemeAgencyID="6">OTH</cbc:ID>
          </cac:TaxScheme>
        </cac:TaxCategory>
      </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:Item>
      <cbc:Description>product_a</cbc:Description>
      <cbc:Name>product_a</cbc:Name>
      <cac:CommodityClassification>
        <cbc:ItemClassificationCode listID="CLASS">001</cbc:ItemClassificationCode>
      </cac:CommodityClassification>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>E</cbc:ID>
        <cbc:Name>Exempt Customer</cbc:Name>
        <cbc:Percent>0.0</cbc:Percent>
        <cbc:TaxExemptionReason>Exempt Customer</cbc:TaxExemptionReason>
        <cac:TaxScheme>
          <cbc:ID schemeID="UN/ECE 5153" schemeAgencyID="6">OTH</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="MYR">1000.0</cbc:PriceAmount>
    </cac:Price>
    <cac:ItemPriceExtension>
      <cbc:Amount currencyID="MYR">1000.00</cbc:Amount>
    </cac:ItemPriceExtension>
  </cac:InvoiceLine>
</Invoice>
