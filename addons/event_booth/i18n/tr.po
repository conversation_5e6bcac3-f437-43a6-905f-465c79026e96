# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_booth
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Melih Melik Sonmez, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>lk<PERSON>r Gözütok, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>_<PERSON>do<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Deniz Guvener_Odoo <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Email</b>:"
msgstr "<b>Kiracı E-postası:</b>:"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Name</b>:"
msgstr "<b>Kiracı Adı</b>:"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Phone</b>:"
msgstr "<b>Kiracı Telefon</b>:"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_event_view_form
msgid "<span class=\"o_stat_text\">Booths</span>"
msgstr "<span class=\"o_stat_text\">Kabinler</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 Branded Booth</span>"
msgstr "<span>1 Markalı Stand</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 desk</span>"
msgstr "<span>1 sıra</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>10 + 1 passes</span>"
msgstr "<span>10 + 1 passes</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>100 words description on website</span>"
msgstr "<span>Web sitesinde 100 kelimelik açıklama</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 Branded Booth</span>"
msgstr "<span>2 Markalı Stand</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 desks</span>"
msgstr "<span>2 sıra</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 x 46\" display screens</span>"
msgstr "<span>2 x 46\" ekran görüntüsü</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>46\" display screen</span>"
msgstr "<span>46\" ekran</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>4m²</span>"
msgstr "<span>4m²</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
msgid "<span>50 words description on website</span>"
msgstr "<span>Web sitesinde 50 kelimelik açıklama</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>8m²</span>"
msgstr "<span>8m²</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>Logo &amp; link on website</span>"
msgstr "<span>Logo &amp; link on website</span>"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction
msgid "Action Needed"
msgstr "Aksiyon Gerekiyor"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__active
msgid "Active"
msgstr "Etkin"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_ids
msgid "Activities"
msgstr "Aktiviteler"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Etkinlik İstisna Dekorasyonu"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_state
msgid "Activity State"
msgstr "Aktivite Durumu"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivite Türü İmgesi"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__available
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Available"
msgstr "Uygun"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count_available
msgid "Available Booths"
msgstr "Mevcut Stantlar"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "Booth"
msgstr "Stand"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_00_event_7
#: model:event.booth,name:event_booth.event_booth_0_event_0
msgid "Booth A1"
msgstr "Stand A1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_01_event_7
#: model:event.booth,name:event_booth.event_booth_1_event_0
msgid "Booth A2"
msgstr "Stand A2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_02_event_7
#: model:event.booth,name:event_booth.event_booth_2_event_0
msgid "Booth A3"
msgstr "Stand A3"

#. module: event_booth
#: model:mail.message.subtype,name:event_booth.mt_event_booth_booked
msgid "Booth Booked"
msgstr "Stand Rezerve Edildi"

#. module: event_booth
#: model:ir.ui.menu,name:event_booth.menu_event_booth_category
msgid "Booth Categories"
msgstr "Stand Kategorileri"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_category_action
#: model:ir.model.fields,field_description:event_booth.field_event_booth__booth_category_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__booth_category_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Booth Category"
msgstr "Stand Kategorisi"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_event__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr "Hala stantların mevcut olduğu Stant Kategorisi. Ön uçta kullanılır"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Booth Type"
msgstr "Stand Tipi"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid ""
"Booth categories are used to represent the different types of booths you "
"rent (Premium Booth, Table and Chairs, ...)"
msgstr ""
"Stant kategorileri, kiraladığınız farklı stant türlerini temsil etmek için "
"kullanılır (Premium Stant, Masa ve Sandalyeler, ...)"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_action
#: model:ir.actions.act_window,name:event_booth.event_booth_action_from_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_type__event_type_booth_ids
#: model:ir.ui.menu,name:event_booth.menu_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_view_form
msgid "Booths"
msgstr "Standlar"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Booths are the physical stands that you rent during your event."
msgstr "Stantlar, etkinliğiniz sırasında kiraladığınız fiziksel stantlardır."

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
msgid "Create a Booth"
msgstr "Stand Oluştur"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid "Create a Booth Category"
msgstr "Stand Kategorisi Oluşturun"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Create a Type Booth"
msgstr "Bir Tip Stand Oluştur"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__description
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Description"
msgstr "Açıklama"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event"
msgstr "Etkinlik"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event Booth"
msgstr "Etkinlik Standı"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth_category
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_ids
msgid "Event Booth Category"
msgstr "Etkinlik Standı Kategorisi"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr "Etkinlik Standı Kategorisi Mevcuttur"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type_booth
msgid "Event Booth Template"
msgstr "Etkinlik Standı Şablonu"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_type_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__event_type_id
msgid "Event Category"
msgstr "Etkinlik Kategorisi"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type
msgid "Event Template"
msgstr "Etkinlik Şablonu"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Event Type Booth"
msgstr "Etkinlik Tipi Stand"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_type_booth_action
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_tree_from_type
msgid "Event Type Booths"
msgstr "Etkinlik Tipi Stantlar"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_graph
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_pivot
msgid "Event booth"
msgstr "Etkinlik standı"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Yazı tipi harika simgesi ör. fa-görevler"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_20_event_7
msgid "Gold Booth 1"
msgstr "Altın Stand 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_21_event_7
msgid "Gold Booth 2"
msgstr "Altın Stand 2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_22_event_7
msgid "Gold Booth 3"
msgstr "Altın Stand 3"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Group By"
msgstr "Grupla"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__has_message
msgid "Has Message"
msgstr "Mesaj Var"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__id
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__id
msgid "ID"
msgstr "ID"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_icon
msgid "Icon"
msgstr "Simge"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisnai bir etkinliği belirtmek için kullanılan simge."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse, bazı mesajlar gönderi hatası içermektedir."

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1920
msgid "Image"
msgstr "Görsel"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1024
msgid "Image 1024"
msgstr "Görsel 1024"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_128
msgid "Image 128"
msgstr "Görsel 128"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_256
msgid "Image 256"
msgstr "Görsel 256"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_512
msgid "Image 512"
msgstr "Görsel 512"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__is_available
msgid "Is Available"
msgstr "Kullanılabilir"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Aktivite Son Tarihim"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Name"
msgstr "Adı"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sonraki Aktivite Takvimi Etkinliği"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Sonraki Aktivite Son Tarihi"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_summary
msgid "Next Activity Summary"
msgstr "Sonraki Aktivite Özeti"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_id
msgid "Next Activity Type"
msgstr "Sonraki Aktivitie Türü"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction_counter
msgid "Number of Actions"
msgstr "Aksiyon Sayısı"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error_counter
msgid "Number of errors"
msgstr "Hata adedi"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "İşlem gerektiren mesaj sayısı"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj adedi"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_10_event_7
msgid "OpenWood Demonstrator 1"
msgstr "OpenWood Göstericisi 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_11_event_7
msgid "OpenWood Demonstrator 2"
msgstr "OpenWood Göstericisi 2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_12_event_7
msgid "OpenWood Demonstrator 3"
msgstr "OpenWood Göstericisi 3"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Pick a Booth Category..."
msgstr "Bir Stand Kategorisi Seçin..."

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
msgid "Pick a Renter..."
msgstr ""

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_premium
msgid "Premium Booth"
msgstr "Premium Stand"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_0
msgid "Premium Booth A4"
msgstr "Premium Stand A4"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_2_event_2
#: model:event.booth,name:event_booth.event_booth_2_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_2
msgid "Premium Showbooth 1"
msgstr "Premium Showbooth 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_2
#: model:event.booth,name:event_booth.event_booth_3_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_3
msgid "Premium Showbooth 2"
msgstr "Premium Showbooth 2"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__rating_ids
msgid "Ratings"
msgstr "Değerlendirmeler"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__partner_id
msgid "Renter"
msgstr "kiracı"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_email
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Email"
msgstr "Kiracı Eposta"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Name"
msgstr "Kiracı Adı"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_phone
msgid "Renter Phone"
msgstr "Kiracı Telefon"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_user_id
msgid "Responsible User"
msgstr "Sorumlu Kullanıcı"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS İleti hatası"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__sequence
msgid "Sequence"
msgstr "Sıralama"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_0_event_2
#: model:event.booth,name:event_booth.event_booth_0_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_0
msgid "Showbooth 1"
msgstr "Showbooth 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_1_event_2
#: model:event.booth,name:event_booth.event_booth_1_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_1
msgid "Showbooth 2"
msgstr "Showbooth 2"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_standard
msgid "Standard Booth"
msgstr "Standard Stand"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__state
msgid "Status"
msgstr "Durumu"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Aktivitelere göre durum \n"
"Gecikmiş\\: Son tarih geçmiş\n"
"Bugün\\: Aktivite tarihi bugün\n"
"Planlanmış\\: Gelecek Aktiviteler."

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count
msgid "Total Booths"
msgstr "Toplam Stand"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kayıtlardaki istisna etkinliğinin türü."

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__unavailable
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Unavailable"
msgstr "Kullanım dışı"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_vip
msgid "VIP Booth"
msgstr "VIP Stand"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_4_event_0
msgid "VIP Booth A5"
msgstr "VIP Stand A5"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__website_message_ids
msgid "Website Messages"
msgstr "Websitesi Mesajları"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__website_message_ids
msgid "Website communication history"
msgstr "Websitesi iletişim geçmişi"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. \"Those stands will be place near the entrance and...\""
msgstr "Örneğin, \"Bu stantlar girişin yanına yerleştirilecek ve...\""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "e.g. First Booth Alley 1"
msgstr "e.g. First Booth Alley 1"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. Premium Booth"
msgstr "e.g. Premium Stand"
