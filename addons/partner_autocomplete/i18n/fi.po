# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ka<PERSON><PERSON><PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr "Lisätietoja"

#. module: partner_autocomplete
#: model:iap.service,description:partner_autocomplete.iap_service_partner_autocomplete
msgid "Automatically enrich your contact base with corporate data."
msgstr "Rikasta yhteystietokantaasi automaattisesti yritystiedoilla"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Buy more credits"
msgstr "Osta lisää krediittejä"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr "Yrityksen tietokannan ID"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "Luotu"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__iap_enrich_auto_done
msgid "Enrich Done"
msgstr "Rikastus valmis"

#. module: partner_autocomplete
#: model:iap.service,unit_name:partner_autocomplete.iap_service_partner_autocomplete
msgid "Enrichments"
msgstr "Rikastuttamiset"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
msgid "IAP Account Token missing"
msgstr "IAP-tilin tunniste puuttuu"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_iap_autocomplete_api
msgid "IAP Partner Autocomplete API"
msgstr "IAP partnerin automaattisen täydennyksen API"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr "Ei riittävästi krediittejä"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr "On synkronoitu"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
msgid "No account token"
msgstr "Ei tilin tunnistetta"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
msgid "Not enough credits for Partner Autocomplete"
msgstr "Ei tarpeeksi krediittejä kumppanin automaattista täydennystä varten"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "Kumppani"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr "Partnerin automaattisen täydennyksen synkronointi"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
msgid "Partner Autocomplete: Sync with remote DB"
msgstr ""
"Kumppanin automaattinen täydentäminen: Synkronoi etätietokannan kanssa"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Search Worldwide 🌎"
msgstr "Etsi maailmanlaajuisesti 🌎"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_fieldchar.js:0
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
msgid "Searching Autocomplete..."
msgstr "Etsitään automaattista täydennystä..."

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Set Your Account Token"
msgstr "Aseta tilisi tunniste"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
msgid "Test mode"
msgstr "Testitila"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/res_partner.py:0
msgid "Unable to enrich company (no credit was consumed)."
msgstr "Yritystietoja ei voitu rikastaa (luottoa ei kulutettu)."
