# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "/ Guest"
msgstr "/ Visitante"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "18:45 John 4P"
msgstr "18:45 John 4P"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_cheeseburger_product_template
msgid ""
"200G Irish Black Angus beef, 9-month matured cheddar cheese, shredded "
"iceberg lettuce, caramelised onions, crushed tomatoes and Chef’s sauce."
msgstr ""
"200G de carne bovina Black Angus irlandesa, queijo cheddar maturado por 9 "
"meses, alface americana fatiada, cebola caramelizada, tomates triturados e "
"molho do chef."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_bacon_product_template
msgid ""
"200G Irish Black Angus beef, caramelized onions with paprika, chopped "
"iceberg salad, red onions, grilled bacon, tomato sauce, pickles, barbecue "
"sauce"
msgstr ""
"200g de carne Black Angus irlandesa, cebola caramelizada com páprica, salada"
" iceberg picada, cebola roxa, bacon grelhado, molho de tomate, picles, molho"
" barbecue"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Nome do piso: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sales:</strong>"
msgstr "<strong>Ponto de venda: </strong>"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"Um piso de restaurante representa a área onde clientes são servidos, onde você pode\n"
"                definir e posicionar as mesas."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Ativo"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Floor"
msgstr "Adicionar piso"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Table"
msgstr "Adicionar mesa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new floor to get started."
msgstr "Adicione um novo piso para começar."

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Adicionar um novo piso de restaurante"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new table to get started."
msgstr "Adicione uma mesa nova para começar."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Add a tip"
msgstr "Adicionar gorjeta"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment"
msgstr "Adicionar gorjeta após o pagamento"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Adjust Amount"
msgstr "Ajustar valor"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""
"Ajuste o valor autorizado por terminais de pagamento para adicionar gorjeta "
"após o cliente ir embora ou ao final do dia."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr "Ajuste a tarifa de imposto conforme retiradas ou refeições no local."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr "Permitir divisão da conta"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Allow to create orders for takeaway customers."
msgstr "Permitir a criação de pedidos para retirada."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr "Permitir impressão do recibo antes do pagamento"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "Permite imprimir a conta antes do pagamento."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Alternative Fiscal Position"
msgstr "Posição fiscal alternativa"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_anchovy
msgid "Anchovy"
msgstr "Anchova"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_aperol_spritz_product_template
msgid "Aperol Spritz"
msgstr "Aperol Spritz"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Aparência"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "Arquivado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Are you sure?"
msgstr "Tem certeza?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Back"
msgstr "Voltar"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Cor de fundo"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Imagem de fundo"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "Hambúrguer de bacon"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_fries
msgid "Belgian fresh homemade fries"
msgstr "Batatas fritas caseiras da Bélgica"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Bill"
msgstr "Conta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
msgid "Bill Printing"
msgstr "Impressão da conta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
msgid "Bill Splitting"
msgstr "Dividir conta"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_black_olives
msgid "Black olives"
msgstr "Azeitonas pretas"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Book table"
msgstr "Reservar mesa"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_old_fashioned_product_template
msgid "Bourbon, bitters, sugar, and a twist of citrus zest."
msgstr "Bourbon, bitters, açúcar e um toque de raspas de frutas cítricas."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.burger_drink_combo_product_template
msgid "Burger Menu Combo"
msgstr "Combo do cardápio de hambúrguer"

#. module: pos_restaurant
#: model:pos.payment.method,name:pos_restaurant.payment_method
msgid "Card"
msgstr "Cartão"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Cart"
msgstr "Carrinho"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Change Floor Background"
msgstr "Alterar plano de fundo do andar"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Change table number?"
msgstr "Trocar o número da mesa?"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "X-burguer"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "Sanduíche de curry de frango"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Clone"
msgstr "Clonar"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Close Tab"
msgstr "Fechar aba"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "Sanduíche clubhouse"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Coca-cola"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_cocktails
msgid "Cocktails"
msgstr "Coquetéis"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Cor"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Continue"
msgstr "Continuar"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_cosmopolitan_product_template
msgid "Cosmopolitan"
msgstr "Cosmopolitan"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_cosmopolitan_product_template
msgid "Cranberry Jus, lime jus, vodka and Cointreau"
msgstr "Suco de limão, suco de cranberry, vodca e Cointreau"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Criado em"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Delete"
msgstr "Excluir"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Delete Error"
msgstr "Erro ao excluir"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr "Projete pisos e atribua pedidos às mesas"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Dine in"
msgstr "Jantar no local"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Bebidas"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr "Impressão de recibo antecipada"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Eat in / Take out"
msgstr "Refeição no local/Retirada"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Edit Order Name"
msgstr "Editar nome do pedido"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Edit Plan"
msgstr "Editar plano"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "Possibilita a divisão de conta no ponto de venda."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Encountered error when loading image. Please try again."
msgstr "Erro encontrado ao carregar imagem. Por favor, tente novamente."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Enter a table number"
msgstr "Digite o número da mesa"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.espresso_product_template
msgid "Espresso"
msgstr "Espresso"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese_pasta
msgid "Extra cheese"
msgstr "Queijo extra"

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_extra_pasta
#: model:product.attribute,name:pos_restaurant.product_extra_pizza
msgid "Extras"
msgstr "Extras"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.fanta_product_template
msgid "Fanta"
msgstr "Fanta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "File"
msgstr "Arquivo"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Posição fiscal"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "Posições fiscais"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Piso"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__floor_background_image
msgid "Floor Background Image"
msgstr "Imagem de fundo do piso"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Nome do piso"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor Name ?"
msgstr "Nome do piso?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Floor Plan"
msgstr "Planta baixa"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Plantas baixas"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor name"
msgstr "Nome do piso"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid "Floor: %(floor)s - PoS Config: %(config)s \n"
msgstr "Piso: %(floor)s - Config do PDV: %(config)s \n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr "Pisos"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr "Mapa de pisos e mesas"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Comida"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr ""
"Para a sua conveniência, estamos fornecendo os seguintes cálculos de "
"gorjeta:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "Funghi"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_negroni_product_template
msgid "Gin, vermouth rosso, Campari, and an orange peel."
msgstr "Gin, vermute rosso, Campari e casca de laranja."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.green_tea_product_template
msgid "Green Tea"
msgstr "Chá verde"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_grilled_vegetables
msgid "Grilled vegetables"
msgstr "Legumes grelhados"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
msgid "Guests"
msgstr "Clientes"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "Guests?"
msgstr "Clientes?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Altura"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Posição horizontal"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.ice_tea_product_template
msgid "Ice Tea"
msgstr "Chá gelado"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Se falso, a mesa é desativada e não estará disponível no ponto de venda"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Jump"
msgstr "Ir para"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Keep Open"
msgstr "Manter aberto"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Loading"
msgstr "Carregando"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Loading Image Error"
msgstr "Erro de carregamento de imagem"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "Almoço Maki 18 pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "Almoço Salmão 20 pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "Almoço Mix Temaki 3 pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mai_tai_product_template
msgid "Mai Tai"
msgstr "Mai Tai"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Round"
msgstr "Usar redondo"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Square"
msgstr "Usar quadrado"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_margarita_product_template
msgid "Margarita"
msgstr "Margarita"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Message"
msgstr "Mensagem"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr "Milkshake de banana"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Del Valle"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mojito_product_template
msgid "Mojito"
msgstr "Mojito"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_moscow_mule_product_template
msgid "Moscow Mule"
msgstr "Moscow Mule"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "Sanduíche de muçarela"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom_pasta
msgid "Mushroom"
msgstr "Cogumelo"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_negroni_product_template
msgid "Negroni"
msgstr "Negroni"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "New Floor"
msgstr "Novo piso"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "No Tip"
msgstr "Sem gorjeta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Note"
msgstr "Nota"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Number of Seats?"
msgstr "Número de assentos?"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_old_fashioned_product_template
msgid "Old Fashioned"
msgstr "Old Fashioned"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Online reservation for restaurant"
msgstr "Reservas online para o restaurante"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""
"Apenas formatos de imagem compatíveis com a web, como .png ou .jpeg, são "
"suportados."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No floors available."
msgstr "Opa! Nenhum piso disponível."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No tables available."
msgstr "Opa! Nenhuma mesa disponível."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Open"
msgstr "Aberto"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Order"
msgstr "Pedido"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "PRO FORMA"
msgstr "PRO FORMA"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__parent_id
msgid "Parent Table"
msgstr "Mesa principal"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 Formaggi"
msgstr "Macarrão 4 queijos"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "Macarrão à bolonhesa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Pay"
msgstr "Pagar"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
msgid "Payment"
msgstr "Pagamento"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pepe, latte, gorgonzola dolce, taleggio, parmigiano reggiano"
msgstr "Pimenta, leite, gorgonzola doce, taleggio, parmesão"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_peperoni
msgid "Pepperoni"
msgstr "Pepperoni"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Picture"
msgstr "Foto"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_pina_colada_product_template
msgid "Pina colada"
msgstr "Piña colada"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Pizza Margherita"
msgstr "Pizza margherita"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_vege_product_template
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Pizza Vegetarian"
msgstr "Pizza vegetariana"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Plan"
msgstr "Plano"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuração do ponto de venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Pedidos do ponto de venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pagamentos de ponto de venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "Sessão do ponto de venda"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_ids
msgid "Point of Sales"
msgstr "Pontos de venda"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr "Imprimir conta PDV Iface"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr "Dividir conta PDV Iface"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr "PDV definir gorjeta após pagamento"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_potato_thyme
msgid "Potatoes with thyme"
msgstr "Batatas com tomilho"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
msgid "Print"
msgstr "Imprimir"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_aperol_spritz_product_template
msgid "Prosecco, aperol, soda"
msgstr "Prosecco, aperol, refrigerante"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release Order"
msgstr "Liberar pedido"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release table"
msgstr "Liberar mesa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a floor cannot be undone. Do you still want to remove %s?"
msgstr ""
"A remoção de um piso não pode ser desfeita. Tem certeza de que deseja "
"remover %s?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a table cannot be undone"
msgstr "Não é possível desfazer a ação de remover uma mesa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Rename"
msgstr "Renomear"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Reprint receipts"
msgstr "Reimprimir recibos"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
msgid "Restaurant"
msgstr "Restaurante"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Piso do restaurante"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Pisos do restaurante"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Mesa do restaurante"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse"
msgstr "Reverter"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse Payment"
msgstr "Reverter pagamento"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Redonda"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Round Shape"
msgstr "Formato redondo"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mai_tai_product_template
msgid "Rum, lime juice, orgeat syrup, and orange liqueur."
msgstr "Rum, suco de limão, xarope de laranja e licor de laranja."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "Salmão e abacate"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Save"
msgstr "Salvar"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Salve esta página e retorne aqui para configurar o recurso."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.schweppes_product_template
msgid "Schweppes"
msgstr "Schweppes"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
msgid "Seats"
msgstr "Assentos"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Select table to transfer order"
msgstr "Selecione a mesa para transferir o pedido"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "Definir gorjeta após pagamento"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Settle"
msgstr "Liquidar"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Forma"

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_sides_buns_pizza
msgid "Sides"
msgstr "Acompanhamento"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Signature"
msgstr "Assinatura"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_smashed_sweet_potatoes
msgid "Smashed sweet potatoes"
msgstr "Purê de batata-doce"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_soft_drinks
msgid "Soft drinks"
msgstr "Bebidas não alcóolicas"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "Sanduíche de atum apimentado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Split"
msgstr "Dividir"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
msgid "Split Order"
msgstr "Dividir o pedido"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr "Dividir total ou linhas de pedido"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Quadrado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Square Shape"
msgstr "Formato quadrado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Subtotal"
msgstr "Subtotal"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_sweet_potato
msgid "Sweet potato fries"
msgstr "Batata-doce frita"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Switch Floor View"
msgstr "Alternar visão do piso"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Dine in"
msgstr "Alternar para Consumo no local"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Takeaway"
msgstr "Alternar para Retirada"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
msgid "Table"
msgstr "Mesa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_header_patch.js:0
msgid "Table %(number)s"
msgstr "Mesa %(number)s"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_header_patch.js:0
msgid "Table %(number)s, Guests: %(count)s"
msgstr "Mesa %(number)s, Convidados: %(count)s"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant_appointment
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_module_pos_restaurant_appointment
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Table Booking"
msgstr "Reserva de mesa"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__table_number
msgid "Table Number"
msgstr "Número da mesa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Table Selector"
msgstr "Seletor de mesa"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Mesas"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__takeaway
msgid "Take Away"
msgstr "Retirada"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Take out"
msgstr "Retirada"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Takeaway"
msgstr "Retirada"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Taxes must be included in price."
msgstr "Os impostos devem estar incluídos no preço."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_margarita_product_template
msgid "Tequila Jose Cuervo, lime jus, sugar cane Cointreau"
msgstr "Tequila José Cuervo, suco de limão, açúcar de cana, Cointreau"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "A quantidade de clientes que foram servidos por este pedido."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr "A cor de fundo do piso em formato compatível com HTML"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "O número padrão de clientes servidos nesta mesa."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__table_number
msgid "The number of the table as displayed on the floor plan"
msgstr "O número da mesa, conforme exibido na planta baixa"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__parent_id
msgid "The parent table if this table is part of a group of tables"
msgstr "A mesa principal, caso esta mesa seja parte de um grupo de mesas"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "Os pisos do restaurante servidos por este ponto de venda."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "A mesa à qual este pedido foi servido"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""
"A cor da tabela, expressa como um valor de propriedade de CSS de 'fundo' "
"válido"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "A altura da mesa em pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""
"A posição horizontal da mesa, do lado esquerdo ao centro da mesa, em pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr "A posição vertical da mesa, do topo ao centro da mesa, em pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "A largura da mesa em pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Isso é útil em restaurantes com serviços no local e para viagem que "
"acarretam em tributação específica."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Esse pedido ainda não foi sincronizado com o servidor. Certifique-se de que "
"ele esteja sincronizado e tente novamente."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Tip"
msgstr "Gorjeta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Tip Amount"
msgstr "Valor da gorjeta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Tip:"
msgstr "Gorjeta:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Tipping"
msgstr "Gorjetas"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_margherita_product_template
msgid "Tomato sauce, Agerola mozzarella \"fior di latte\", fresh basil"
msgstr "Molho de tomate, mussarela Agerola 'fior di latte', manjericão fresco"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Total:"
msgstr "Total:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Transfer / Merge"
msgstr "Transferência/Mesclagem"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Unsupported File Format"
msgstr "Formato de arquivo não suportado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid "Unsynced order"
msgstr "Pedido não sincronizado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Username"
msgstr "Nome do usuário"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Posição vertical"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_moscow_mule_product_template
msgid "Vodka 42 Below, lime, sugar, ginger beer"
msgstr "Vodca 42 Below, limão, açúcar, cerveja de gengibre"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Água"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey Sour"
msgstr "Whiskey Sour"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey, lemon juice, sugar, and a dash of egg white."
msgstr "Whisky, suco de limão, açúcar e uma pitada de clara de ovo."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_pina_colada_product_template
msgid "White rhum, Malibu, Batida de coco, coconut liqueur, pineapple juice"
msgstr "Rum branco, Malibu, Batida de coco, licor de coco, suco de abacaxi"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mojito_product_template
msgid "White rum, sugar, lime juice, soda water, and mint."
msgstr "Rum branco, açúcar, suco de limão, água com gás e hortelã."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Largura"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a floor when orders are still in draft for this floor."
msgstr ""
"Não é possível excluir um piso quando há pedidos em rascunho para este piso."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a floor with orders still in draft for this floor."
msgstr ""
"Não é possível excluir um piso quando há pedidos em rascunho para este piso."

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a table when orders are still in draft for this table."
msgstr ""
"Não é possível excluir uma mesa quando há pedidos em rascunho para esta "
"mesa."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a table with orders still in draft for this table."
msgstr ""
"Não é possível excluir uma mesa quando há pedidos em rascunho para esta "
"mesa."

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""
"Não é possível remover um piso que é usado em uma sessão de PDV. Feche as "
"sessões primeiro:\n"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""
"Não é possível remover uma mesa que é usada em uma sessão de PDV. Feche as "
"sessões primeiro."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "changes"
msgstr "alterações"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "items"
msgstr "itens"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/order_widget/order_widget.js:0
msgid "or book the table for later"
msgstr "ou reservar a mesa para mais tarde"
