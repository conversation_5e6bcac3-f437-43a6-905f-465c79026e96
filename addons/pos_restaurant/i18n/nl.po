# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "/ Guest"
msgstr "/ Gast"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "18:45 John 4P"
msgstr "18:45 Johannes 4P"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_cheeseburger_product_template
msgid ""
"200G Irish Black Angus beef, 9-month matured cheddar cheese, shredded "
"iceberg lettuce, caramelised onions, crushed tomatoes and Chef’s sauce."
msgstr ""
"200 g Ierse Black Angus rundvlees, 9 maanden gerijpte cheddarkaas, geraspte "
"ijsbergsla, gekarameliseerde uien, geplette tomaten en saus van de chef."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_bacon_product_template
msgid ""
"200G Irish Black Angus beef, caramelized onions with paprika, chopped "
"iceberg salad, red onions, grilled bacon, tomato sauce, pickles, barbecue "
"sauce"
msgstr ""
"200 g Ierse Black Angus rundvlees, gekarameliseerde uien met paprika, "
"gehakte ijsbergsla, rode uien, gegrild spek, tomatensaus, augurken, "
"barbecuesaus"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Naam zaalplan: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sales:</strong>"
msgstr "<strong>Verkooppunt:</strong>"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"Een zaal stelt de plaats voor waarde klanten worden bediend, dit is waar je\n"
"de positie van de tafels kan definiëren."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Actief"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Floor"
msgstr "Zaal toevoegen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Table"
msgstr "Tafel toevoegen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new floor to get started."
msgstr "Voeg een nieuwe zaal toe om te beginnen."

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Voeg een nieuwe zaal toe"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new table to get started."
msgstr "Voeg een nieuwe tafel toe om te beginnen."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Add a tip"
msgstr "Voeg een fooi toe"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment"
msgstr "Voeg een fooi toe na betaling"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Adjust Amount"
msgstr "Pas bedrag aan"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""
"Pas het bedrag aan dat is geautoriseerd door betaalterminals om een fooi toe"
" te voegen nadat de klanten zijn vertrokken of aan het einde van de dag."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr ""
"Pas het belastingtarief aan op basis van het feit of klanten ter plaatse "
"dineren of kiezen voor afhalen."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr "Sta het splitsen van de rekening toe"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Allow to create orders for takeaway customers."
msgstr "Orders voor afhaalklanten maken."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr "Sta het afdrukken van de kassabon toe voordat er betaald wordt"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "Sta het afdrukken van de factuur toe, voordat er betaald wordt."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Alternative Fiscal Position"
msgstr "Alternatieve begrotingspositie"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_anchovy
msgid "Anchovy"
msgstr "Ansjovis"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_aperol_spritz_product_template
msgid "Aperol Spritz"
msgstr "Aperol Spritz"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Voorkomen"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Are you sure?"
msgstr "Weet je het zeker?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Back"
msgstr "Terug"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Achtergrondkleur"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Achtergrondafbeelding"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "Bacon Burger"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_fries
msgid "Belgian fresh homemade fries"
msgstr "Huisgemaakte frietjes"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Bill"
msgstr "Rekening"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
msgid "Bill Printing"
msgstr "Rekening afdrukken"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
msgid "Bill Splitting"
msgstr "Rekening splitsen"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_black_olives
msgid "Black olives"
msgstr "Zwarte olijven"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Book table"
msgstr "Boek een tafel"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_old_fashioned_product_template
msgid "Bourbon, bitters, sugar, and a twist of citrus zest."
msgstr "Bourbon, bitter, suiker en een vleugje citrusschil."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.burger_drink_combo_product_template
msgid "Burger Menu Combo"
msgstr "Combinatie burgermenu"

#. module: pos_restaurant
#: model:pos.payment.method,name:pos_restaurant.payment_method
msgid "Card"
msgstr "Kaart"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Cart"
msgstr "Winkelmandje"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Change Floor Background"
msgstr "Achtergrond vloer wijzigen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Change table number?"
msgstr "Tafelnummer wijzigen?"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "Cheese Burger"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "Kip curry sandwich"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Clone"
msgstr "Klonen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Close Tab"
msgstr "Sluit tab"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "Club Sandwich"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Coca-Cola"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_cocktails
msgid "Cocktails"
msgstr "Cocktails"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Kleur"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Continue"
msgstr "Doorgaan"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_cosmopolitan_product_template
msgid "Cosmopolitan"
msgstr "Cosmopolitan"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_cosmopolitan_product_template
msgid "Cranberry Jus, lime jus, vodka and Cointreau"
msgstr "Veenbessensap, limoensap, wodka en Cointreau"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Delete"
msgstr "Verwijderen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Delete Error"
msgstr "Foutmelding verwijderen"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr "Ontwerp zaalplannen en wijs orders toe aan tafels"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Dine in"
msgstr "Ter plaatse eten"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Dranken"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr "Vroegtijdig kassabon afdrukken"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Eat in / Take out"
msgstr "Eten/Afhalen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Edit Order Name"
msgstr "Naam order bewerken"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Edit Plan"
msgstr "Plan bijwerken"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "Sta splitsen van rekeningen toe in de kassa."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Encountered error when loading image. Please try again."
msgstr ""
"Fout opgetreden bij het laden van afbeelding. Probeer het a.je.b. opnieuw."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Enter a table number"
msgstr "Voer een tafelnummer in"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.espresso_product_template
msgid "Espresso"
msgstr "Espresso"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese_pasta
msgid "Extra cheese"
msgstr "Extra kaas"

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_extra_pasta
#: model:product.attribute,name:pos_restaurant.product_extra_pizza
msgid "Extras"
msgstr "Extra's"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.fanta_product_template
msgid "Fanta"
msgstr "Fanta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "File"
msgstr "Bestand"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Fiscale positie"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "Fiscale posities"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Zaal"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__floor_background_image
msgid "Floor Background Image"
msgstr "Achtergrondafbeelding van de zaal"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Zaalnaam"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor Name ?"
msgstr "Zaalnaam?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Floor Plan"
msgstr "Plattegrond"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Plattegronden"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor name"
msgstr "Zaalnaam"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid "Floor: %(floor)s - PoS Config: %(config)s \n"
msgstr "Zaal: %(floor)s - Kassa-configuratie: %(config)s \n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr "Vloeren"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr "Vloer & tafelplan"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Eten"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr "Gemakshalve bieden we de volgende fooiberekeningen:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "Fungi"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_negroni_product_template
msgid "Gin, vermouth rosso, Campari, and an orange peel."
msgstr "Gin, vermouth rosso, Campari, en een sinaasappelschil."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.green_tea_product_template
msgid "Green Tea"
msgstr "Groene thee"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_grilled_vegetables
msgid "Grilled vegetables"
msgstr "Gegrilde groenten"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
msgid "Guests"
msgstr "Gasten"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "Guests?"
msgstr "Gasten?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Hoogte"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Horizontale positie"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.ice_tea_product_template
msgid "Ice Tea"
msgstr "Ice Tea"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Indien onwaar wordt de tafel gedeactiveerd en is deze niet meer beschikbaar "
"in de kassa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Jump"
msgstr "Ga"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Keep Open"
msgstr "Openhouden"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Loading"
msgstr "Laden"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Loading Image Error"
msgstr "Fout bij laden van afbeelding"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "Lunch Maki 18pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "Lunch zalm 20st"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "Lunch Temaki mix 3pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mai_tai_product_template
msgid "Mai Tai"
msgstr "Mai Tai"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Round"
msgstr "Rond maken"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Square"
msgstr "Vierkant maken"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_margarita_product_template
msgid "Margarita"
msgstr "Margarita"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Message"
msgstr "Bericht"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr "Bananenmilkshake"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Minute Maid"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mojito_product_template
msgid "Mojito"
msgstr "Mojito"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_moscow_mule_product_template
msgid "Moscow Mule"
msgstr "Moscow Mule"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "Mozzarella Sandwich"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom_pasta
msgid "Mushroom"
msgstr "Paddenstoel"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_negroni_product_template
msgid "Negroni"
msgstr "Negroni"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "New Floor"
msgstr "Nieuwe zaal"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "No Tip"
msgstr "Geen fooi"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Note"
msgstr "Notitie"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Number of Seats?"
msgstr "Aantal plaatsen?"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_old_fashioned_product_template
msgid "Old Fashioned"
msgstr "Old Fashioned"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Online reservation for restaurant"
msgstr "Online reservering voor restaurant"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""
"Alleen web-compatibele afbeeldingsindelingen zoals .png of .jpeg worden "
"ondersteund."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No floors available."
msgstr "Oeps! Geen zaal beschikbaar."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No tables available."
msgstr "Oeps! Geen tafel beschikbaar."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Open"
msgstr "Openen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Order"
msgstr "Order"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "PRO FORMA"
msgstr "PRO FORMA"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__parent_id
msgid "Parent Table"
msgstr "Bovenstaande tafel"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 Formaggi"
msgstr "Pasta 4 Formaggi"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "Pasta Bolognese"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Pay"
msgstr "Betaal"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
msgid "Payment"
msgstr "Betaling"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pepe, latte, gorgonzola dolce, taleggio, parmigiano reggiano"
msgstr "Pepe, latte, gorgonzola dolce, taleggio, parmigiano reggiano"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_peperoni
msgid "Pepperoni"
msgstr "Pepperoni"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Picture"
msgstr "Afbeelding"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_pina_colada_product_template
msgid "Pina colada"
msgstr "Pina colada"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Pizza Margherita"
msgstr "Pizza Margherita"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_vege_product_template
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Pizza Vegetarian"
msgstr "Pizza Vegetarisch"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Plan"
msgstr "Plan"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassa-instellingen"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Kassaorders"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Kassa betalingen"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "Kassa sessie"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_ids
msgid "Point of Sales"
msgstr "Kassa's"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr "Kassa Iface Rekening afdrukken"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr "Kassa Iface Rekening splitsen"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr "Kassa stelt fooi in na betaling"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_potato_thyme
msgid "Potatoes with thyme"
msgstr "Aardappelen met tijm"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
msgid "Print"
msgstr "Afdrukken"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_aperol_spritz_product_template
msgid "Prosecco, aperol, soda"
msgstr "Prosecco, aperol, soda"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release Order"
msgstr "Order vrijgeven"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release table"
msgstr "Tafel vrijgeven"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a floor cannot be undone. Do you still want to remove %s?"
msgstr ""
"Het verwijderen van een zaal kan niet worden ongedaan gemaakt. Wil je %s nog"
" steeds verwijderen?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a table cannot be undone"
msgstr "Een tafel verwijderen kan niet ongedaan worden"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Rename"
msgstr "Naam wijzigen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Reprint receipts"
msgstr "Bonnen opnieuw afdrukken"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
msgid "Restaurant"
msgstr "Restaurant"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Restaurantzaal"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Restaurantvloeren"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Restauranttafel"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse"
msgstr "Omkeren"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse Payment"
msgstr "Omkering betaling"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Rond"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Round Shape"
msgstr "Ronde vorm"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mai_tai_product_template
msgid "Rum, lime juice, orgeat syrup, and orange liqueur."
msgstr "Rum, limoensap, siroop en sinaasappellikeur."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "Zalm en Avocado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Save"
msgstr "Opslaan"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""
"Sla deze pagina op en kom terug naar hier om deze functionaliteit te "
"activeren."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.schweppes_product_template
msgid "Schweppes"
msgstr "Scheppes"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
msgid "Seats"
msgstr "Plaatsen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Select table to transfer order"
msgstr "Selecteer een tafel om de order te verplaatsen"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "Stel fooi in na betaling"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Settle"
msgstr "Regelen"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Vorm"

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_sides_buns_pizza
msgid "Sides"
msgstr "Bijgerecht"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Signature"
msgstr "Handtekening"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_smashed_sweet_potatoes
msgid "Smashed sweet potatoes"
msgstr "Gestampte zoete aardappelen"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_soft_drinks
msgid "Soft drinks"
msgstr "Frisdranken"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "Pikante tonijnen sandwich"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Split"
msgstr "Splitsen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
msgid "Split Order"
msgstr "Order splitsen"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr "Split totaal of orderregels"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Vierkant"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Square Shape"
msgstr "Vierkante vorm"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Subtotal"
msgstr "Subtotaal"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_sweet_potato
msgid "Sweet potato fries"
msgstr "Zoete aardappelfrietjes"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Switch Floor View"
msgstr "Schakel naar zaalweergave"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Dine in"
msgstr "Overschakelen op dineren"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Takeaway"
msgstr "Overschakelen naar afhaalmaaltijden"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
msgid "Table"
msgstr "Tafel"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_header_patch.js:0
msgid "Table %(number)s"
msgstr "Tafel %(number)s"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_header_patch.js:0
msgid "Table %(number)s, Guests: %(count)s"
msgstr "Tafel %(number)s, Gasten: %(count)s"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant_appointment
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_module_pos_restaurant_appointment
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Table Booking"
msgstr "Tafelreservering"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__table_number
msgid "Table Number"
msgstr "Tafelnummer"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Table Selector"
msgstr "Tafelselector"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Tafels"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__takeaway
msgid "Take Away"
msgstr "Afhaalmaaltijd"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Take out"
msgstr "Afhalen"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Takeaway"
msgstr "Afhaalmaaltijd"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Taxes must be included in price."
msgstr "De btw moet bij de prijs zijn inbegrepen."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_margarita_product_template
msgid "Tequila Jose Cuervo, lime jus, sugar cane Cointreau"
msgstr "Tequila Jose Cuervo, limoensap, rietsuiker, Cointreau"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "Het aantal klanten dat bediend is met deze order."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr "De achtergrondkleur van je zaal in een HTML-compatibel formaat"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "Het standaard aantal klanten dat wordt geholpen aan deze tafel."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__table_number
msgid "The number of the table as displayed on the floor plan"
msgstr "Het nummer van de tafel zoals weergegeven op de plattegrond"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__parent_id
msgid "The parent table if this table is part of a group of tables"
msgstr ""
"De bovenstaande tafel als deze tafel deel uitmaakt van een groep tafels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "De restaurantvloeren bediend met deze kassa."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "De tafel waar deze order geserveerd is"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""
"De tafelkleur, voorgesteld als een valide 'background' CSS eigenschap waarde"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "De tafel zijn hoogte in pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""
"De tafel zijn horizontale positie van de linkse kant van de tafel naar het "
"midden van de tafel, in pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""
"De tafel zijn verticale positie van de bovenkant van de tafel naar het "
"midden van de tafel, in pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "De tafel zijn breedte in pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Dit is handig voor restaurants met op locatie- en afhaaldiensten die "
"specifieke BTW tarieven impliceren."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Deze order is nog niet gesynchroniseerd met de server. Zorg ervoor dat het "
"is gesynchroniseerd en probeer het opnieuw."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Tip"
msgstr "Fooi"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Tip Amount"
msgstr "Fooibedrag"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Tip:"
msgstr "Fooi:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Tipping"
msgstr "Fooien"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_margherita_product_template
msgid "Tomato sauce, Agerola mozzarella \"fior di latte\", fresh basil"
msgstr "Tomatensaus, Agerola mozzarella 'fior di latte', verse basilicum"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Total:"
msgstr "Totaal:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Transfer / Merge"
msgstr "Verplaatsen / Samenvoegen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Unsupported File Format"
msgstr "Bestandsformaat niet ondersteund"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid "Unsynced order"
msgstr "Niet-gesynchroniseerde order"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Username"
msgstr "Gebruikersnaam"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Verticale positie"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_moscow_mule_product_template
msgid "Vodka 42 Below, lime, sugar, ginger beer"
msgstr "Vodka 42 Below, limoen, suiker, gingerbeer"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Water"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey Sour"
msgstr "Whiskey Sour"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey, lemon juice, sugar, and a dash of egg white."
msgstr "Whisky, citroensap, suiker en een scheutje eiwit."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_pina_colada_product_template
msgid "White rhum, Malibu, Batida de coco, coconut liqueur, pineapple juice"
msgstr "Witte rum, Malibu, Batida de coco, kokoslikeur, ananassap"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mojito_product_template
msgid "White rum, sugar, lime juice, soda water, and mint."
msgstr "Witte rum, suiker, limoensap, sodawater en munt."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Breedte"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a floor when orders are still in draft for this floor."
msgstr ""
"Je kunt geen zaal verwijderen als orders nog in concept zijn voor deze zaal."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a floor with orders still in draft for this floor."
msgstr ""
"Je kunt een zaal niet verwijderen als er nog orders in concept zijn voor "
"deze zaal."

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a table when orders are still in draft for this table."
msgstr ""
"Je kunt een tafel niet verwijderen als er nog orders in concept zijn voor "
"deze tafel."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a table with orders still in draft for this table."
msgstr "Je kunt geen tafel verwijderen waarvan de orders nog in concept zijn."

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""
"Je kunt een zaal niet verwijderen die gebruikt werd in een kassasessie, "
"sluit de sessie(s) eerst af:\n"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""
"Je kunt een tafel die wordt gebruikt in een Kassa-sessie niet verwijderen, "
"sluit eerst de sessie(s)."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "changes"
msgstr "wijzigingen"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "items"
msgstr "items"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/order_widget/order_widget.js:0
msgid "or book the table for later"
msgstr "of reserveer een tafel voor later"
