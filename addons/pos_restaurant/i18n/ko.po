# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "/ Guest"
msgstr "/ 게스트"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "18:45 John 4P"
msgstr "18:45 John 4P"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_cheeseburger_product_template
msgid ""
"200G Irish Black Angus beef, 9-month matured cheddar cheese, shredded "
"iceberg lettuce, caramelised onions, crushed tomatoes and Chef’s sauce."
msgstr ""
"아이리시 블랙 앵거스 소고기 200g, 9개월 숙성 체다 치즈, 잘게 썬 양상추, 캐러멜라이즈 양파, 으깬 토마토, 셰프 소스."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_bacon_product_template
msgid ""
"200G Irish Black Angus beef, caramelized onions with paprika, chopped "
"iceberg salad, red onions, grilled bacon, tomato sauce, pickles, barbecue "
"sauce"
msgstr ""
"아이리시 블랙 앵거스 소고기 200g, 캐러멜라이즈 양파 파프리카, 다진 양상추 샐러드, 적양파, 구운 베이컨, 토마토 소스, 피클, "
"바비큐 소스"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>층 이름 :  </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sales:</strong>"
msgstr "<strong>POS:</strong>"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr "식당 층은 고객에게 서비스를 제공하는 장소를 나타냅니다. 테이블을 정의하고 배치하십시오."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "활성화"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Floor"
msgstr "층 추가"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Table"
msgstr "테이블 추가"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new floor to get started."
msgstr "새 플로어를 추가하여 시작하세요."

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "새로운 식당 층 추가"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new table to get started."
msgstr "새 테이블을 추가하여 시작하세요."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Add a tip"
msgstr "팁을 추가합니다."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment"
msgstr "결제 후 팁 추가"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Adjust Amount"
msgstr "금액 조정"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr "고객 퇴점 후 혹은 업무 종료 시 팁을 추가하려면 승인된 금액을 결제용 단말기에서 조정하십시오."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr "고객이 매장에서 식사를 하는지, 테이크아웃 하는지에 따라 세율을 조정합니다."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr "계산서 금액을 나누어 결제하는 것을 허용합니다."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Allow to create orders for takeaway customers."
msgstr "테이크아웃 고객을 위한 주문을 생성할 수 있도록 허용합니다."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr "결제 전에 영수증을 인쇄하는 것을 허용합니다."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "지불하기 전에 청구서를 인쇄할 수 있습니다."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Alternative Fiscal Position"
msgstr "대체 재정 상태"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_anchovy
msgid "Anchovy"
msgstr "멸치"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_aperol_spritz_product_template
msgid "Aperol Spritz"
msgstr "아페롤 스프리츠"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "외관"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "보관됨"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Are you sure?"
msgstr "진행할까요?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Back"
msgstr "뒤로"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "배경 색상"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "배경 이미지"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "베이컨 버거"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_fries
msgid "Belgian fresh homemade fries"
msgstr "벨기에 수제 감자튀김"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Bill"
msgstr "청구"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
msgid "Bill Printing"
msgstr "계산서 발행"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
msgid "Bill Splitting"
msgstr "계산서 분리"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_black_olives
msgid "Black olives"
msgstr "검정 올리브"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Book table"
msgstr "테이블 예약"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_old_fashioned_product_template
msgid "Bourbon, bitters, sugar, and a twist of citrus zest."
msgstr "버번, 비터, 설탕, 약간의 시트러스 제스트."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.burger_drink_combo_product_template
msgid "Burger Menu Combo"
msgstr "버거 메뉴 콤보"

#. module: pos_restaurant
#: model:pos.payment.method,name:pos_restaurant.payment_method
msgid "Card"
msgstr "카드"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Cart"
msgstr "장바구니"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Change Floor Background"
msgstr "바닥 배경 변경"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Change table number?"
msgstr "테이블 번호를 변경하시겠습니까?"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "치즈 버거"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "치킨 카레 샌드위치"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Clone"
msgstr "복제하기"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Close Tab"
msgstr "탭 닫기"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "클럽 샌드위치"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "코카콜라"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_cocktails
msgid "Cocktails"
msgstr "칵테일"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "색상"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Continue"
msgstr "계속"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_cosmopolitan_product_template
msgid "Cosmopolitan"
msgstr "코스모폴리탄"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_cosmopolitan_product_template
msgid "Cranberry Jus, lime jus, vodka and Cointreau"
msgstr "크랜베리 주스, 라임 주스, 보드카, 쿠앵트로"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "작성자"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "작성일자"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Delete"
msgstr "삭제"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Delete Error"
msgstr "삭제 오류"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr "플로어 디자인 및 테이블에 주문 배정"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Dine in"
msgstr "매장에서 식사"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "표시명"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "음료수"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr "미리 영수증 인쇄하기"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Eat in / Take out"
msgstr "매장 내 식사 / 테이크아웃"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Edit Order Name"
msgstr "주문 이름 편집"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Edit Plan"
msgstr "계획 수정"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "POS에서 청구서 분할을 활성화합니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Encountered error when loading image. Please try again."
msgstr "이미지를 불러오는 중 오류가 발생했습니다. 다시 시도해주십시오."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Enter a table number"
msgstr "테이블 번호를 입력하세요."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.espresso_product_template
msgid "Espresso"
msgstr "에스프레소"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese_pasta
msgid "Extra cheese"
msgstr "엑스트라 치즈"

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_extra_pasta
#: model:product.attribute,name:pos_restaurant.product_extra_pizza
msgid "Extras"
msgstr "추가"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.fanta_product_template
msgid "Fanta"
msgstr "환타"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "File"
msgstr "파일"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "재정 상태"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "재정 위치"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "층"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__floor_background_image
msgid "Floor Background Image"
msgstr "바닥 배경 이미지"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "층 이름"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor Name ?"
msgstr "플로어 이름 ?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Floor Plan"
msgstr "평면도"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "평면도"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor name"
msgstr "플로어 이름"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid "Floor: %(floor)s - PoS Config: %(config)s \n"
msgstr "층: %(floor)s - PoS 설정: %(config)s \n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr "층"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr "플로어 및 테이블 지도"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "음식"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr "편의를 위해 다음과 같이 팁 계산 방법을 참고하시기 바랍니다:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "펑기"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_negroni_product_template
msgid "Gin, vermouth rosso, Campari, and an orange peel."
msgstr "진, 베르무트 로쏘, 캄파리, 오렌지 껍질."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.green_tea_product_template
msgid "Green Tea"
msgstr "녹차"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_grilled_vegetables
msgid "Grilled vegetables"
msgstr "구운 야채"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
msgid "Guests"
msgstr "손님"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "Guests?"
msgstr "손님?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "높이"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "수직 위치"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.ice_tea_product_template
msgid "Ice Tea"
msgstr "아이스티"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr "False 인 경우 테이블이 비활성화되어 POS에서 사용할 수 없습니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Jump"
msgstr "이동"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Keep Open"
msgstr "오픈 상태로 유지"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Loading"
msgstr "불러오는 중"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Loading Image Error"
msgstr "이미지를 불러오는 중 오류 발생"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "점심 마키 18pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "점심 연어 20pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "점심 Temaki mix 3pc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mai_tai_product_template
msgid "Mai Tai"
msgstr "마이 타이"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Round"
msgstr "원형 만들기"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Square"
msgstr "사각형 만들기"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_margarita_product_template
msgid "Margarita"
msgstr "마가리타"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Message"
msgstr "메시지"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr "바나나 밀크쉐이크"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "미닛 메이드"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mojito_product_template
msgid "Mojito"
msgstr "모히토"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_moscow_mule_product_template
msgid "Moscow Mule"
msgstr "모스크바 뮬"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "모짜렐라 샌드위치"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom_pasta
msgid "Mushroom"
msgstr "버섯"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_negroni_product_template
msgid "Negroni"
msgstr "네그로니"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "New Floor"
msgstr "새 플로어"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "No Tip"
msgstr "팁 없음"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Note"
msgstr "노트"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Number of Seats?"
msgstr "좌석 수?"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_old_fashioned_product_template
msgid "Old Fashioned"
msgstr "구식의"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Online reservation for restaurant"
msgstr "레스토랑 온라인 예약"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ".png 나 .jpe 형식과 같은 웹 호환 이미지 형식만 지원됩니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No floors available."
msgstr "죄송합니다, 사용 가능한 플로어가 없습니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No tables available."
msgstr "죄송합니다, 사용 가능한 테이블이 없습니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Open"
msgstr "열기"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Order"
msgstr "주문"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "PRO FORMA"
msgstr "견적"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__parent_id
msgid "Parent Table"
msgstr "상위 테이블"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 Formaggi"
msgstr "파스타 치즈 4가지"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "파스타 볼로냐"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Pay"
msgstr "지불"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
msgid "Payment"
msgstr "결제"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pepe, latte, gorgonzola dolce, taleggio, parmigiano reggiano"
msgstr "후추, 우유, 스위트 고르곤졸라, 탈레지오, 파마산 치즈"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_peperoni
msgid "Pepperoni"
msgstr "페퍼로니"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Picture"
msgstr "사진"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_pina_colada_product_template
msgid "Pina colada"
msgstr "피나 콜라다"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Pizza Margherita"
msgstr "마르게리타 피자"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_vege_product_template
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Pizza Vegetarian"
msgstr "베지테리언 피자"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Plan"
msgstr "계획"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS 환경 설정"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "점포판매시스템 주문"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "점포판매시스템 결제"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "점포판매시스템 기간"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_ids
msgid "Point of Sales"
msgstr "점포판매시스템"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr "POS Iface 인쇄용 청구서"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr "POS Iface 계산서 나누기"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr "POS에 결제 후 팁 지급 설정"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_potato_thyme
msgid "Potatoes with thyme"
msgstr "타임을 곁들인 감자"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
msgid "Print"
msgstr "인쇄"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_aperol_spritz_product_template
msgid "Prosecco, aperol, soda"
msgstr "프로슈코, 아페롤, 소다"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release Order"
msgstr "출시 순서"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release table"
msgstr "테이블 비우기"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a floor cannot be undone. Do you still want to remove %s?"
msgstr "플로어 삭제 시 되돌릴 수 없습니다. %s를 삭제하시겠습니까?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a table cannot be undone"
msgstr "테이블 제거는 취소할 수 없습니다"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Rename"
msgstr "이름 바꾸기"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Reprint receipts"
msgstr "영수증 재인쇄"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
msgid "Restaurant"
msgstr "식당"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "식당 층"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "식당 층"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "식당 테이블"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse"
msgstr "역분개"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse Payment"
msgstr "역분개 결제"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "둥근"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Round Shape"
msgstr "둥근 모양"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mai_tai_product_template
msgid "Rum, lime juice, orgeat syrup, and orange liqueur."
msgstr "럼, 라임 주스, 오가트 시럽, 오렌지 리큐어."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "연어와 아보카도"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Save"
msgstr "저장"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "이 페이지를 저장하고 돌아와서 기능을 설정합니다."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.schweppes_product_template
msgid "Schweppes"
msgstr "Schweppes"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
msgid "Seats"
msgstr "좌석"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Select table to transfer order"
msgstr "주문을 전송할 테이블을 선택하세요"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "순서"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "결제 후 팁 지급 설정"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Settle"
msgstr "정산"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "모양"

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_sides_buns_pizza
msgid "Sides"
msgstr "사이드"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Signature"
msgstr "서명"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_smashed_sweet_potatoes
msgid "Smashed sweet potatoes"
msgstr "으깬 고구마"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_soft_drinks
msgid "Soft drinks"
msgstr "탄산 음료"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "매운 참치 샌드위치"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Split"
msgstr "분할"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
msgid "Split Order"
msgstr "분할 주문"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr "총계 또는 주문 명세 분할"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "정사각형"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Square Shape"
msgstr "사각형"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Subtotal"
msgstr "소계"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_sweet_potato
msgid "Sweet potato fries"
msgstr "고구마 튀김"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Switch Floor View"
msgstr "플로어 보기 전환"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Dine in"
msgstr "다이닝으로 전환"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Takeaway"
msgstr "테이크아웃으로 전환"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
msgid "Table"
msgstr "표"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_header_patch.js:0
msgid "Table %(number)s"
msgstr "테이블 %(number)s"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_header_patch.js:0
msgid "Table %(number)s, Guests: %(count)s"
msgstr "테이블 %(number)s, 게스트: %(count)s"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant_appointment
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_module_pos_restaurant_appointment
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Table Booking"
msgstr "테이블 예약"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__table_number
msgid "Table Number"
msgstr "테이블 번호"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Table Selector"
msgstr "테이블 선택 도구"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "표"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__takeaway
msgid "Take Away"
msgstr "테이크아웃"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Take out"
msgstr "테이크아웃"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Takeaway"
msgstr "테이크아웃"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Taxes must be included in price."
msgstr "가격에 세금이 포함되어 있어야 합니다."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_margarita_product_template
msgid "Tequila Jose Cuervo, lime jus, sugar cane Cointreau"
msgstr "데킬라 호세 쿠에르보, 라임 주스, 사탕수수 쿠앵트로"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "이 주문으로 서비스를 제공한 고객의 수입니다."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr "HTML 호환 형식으로 된 플로어 배경 색상"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "이 테이블에서 서비스를 제공한 기본 고객 수입니다."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__table_number
msgid "The number of the table as displayed on the floor plan"
msgstr "평면도에 표시된 테이블 번호입니다."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__parent_id
msgid "The parent table if this table is part of a group of tables"
msgstr "이 테이블이 테이블 그룹에 해당되는 경우 상위 테이블로 지정됩니다."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "이 POS에서 제공되는 식당 층입니다."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "이 주문이 제공된 테이블"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr "유효한 'background' CSS 속성값으로 표현된 테이블 색상"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "픽셀로 표현한 테이블의 높이"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr "왼쪽에서 테이블 중심까지의 픽셀로 표현한 테이블 가로 위치"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr "상단에서 테이블 중심까지 픽셀로 표현한 테이블 세로 위치"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "테이블의 폭 - 픽셀"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr "이는 특정 세율을 암시하는 현장 및 테이크 아웃 서비스가 있는 레스토랑에 유용합니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr "이 주문은 아직 서버와 동기화되지 않았습니다. 동기화 여부를 확인 한 후 다시 시도하십시오."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Tip"
msgstr "팁"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Tip Amount"
msgstr "팁 금액"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Tip:"
msgstr "팁:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Tipping"
msgstr "팁"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_margherita_product_template
msgid "Tomato sauce, Agerola mozzarella \"fior di latte\", fresh basil"
msgstr "토마토 소스, 아게롤라 모짜렐라 '피오르 디 라떼', 생 바질"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Total:"
msgstr "합계 :"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Transfer / Merge"
msgstr "이전 / 병합"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Unsupported File Format"
msgstr "지원되지 않는 파일 형식"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid "Unsynced order"
msgstr "동기화되지 않은 주문"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Username"
msgstr "사용자 이름"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "수직 위치"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_moscow_mule_product_template
msgid "Vodka 42 Below, lime, sugar, ginger beer"
msgstr "보드카 42 빌로우, 라임, 설탕, 진저비어"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "물"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey Sour"
msgstr "위스키 사워"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey, lemon juice, sugar, and a dash of egg white."
msgstr "위스키, 레몬즙, 설탕, 달걀흰자 약간."

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_pina_colada_product_template
msgid "White rhum, Malibu, Batida de coco, coconut liqueur, pineapple juice"
msgstr "화이트 럼, 말리부, 바티다 데 코코, 코코넛 리큐어, 파인애플 주스"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mojito_product_template
msgid "White rum, sugar, lime juice, soda water, and mint."
msgstr "화이트 럼, 설탕, 라임주스, 소다수, 민트."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "폭"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a floor when orders are still in draft for this floor."
msgstr "이 플로어에서 미결 상태인 주문서가 있는 경우 플로어를 삭제할 수 없습니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a floor with orders still in draft for this floor."
msgstr "이 플로어에서 미결 상태인 주문서가 있는 경우 플로어를 삭제할 수 없습니다."

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a table when orders are still in draft for this table."
msgstr "이 테이블에서 미결 상태인 주문서가 있는 경우 테이블을 삭제할 수 없습니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a table with orders still in draft for this table."
msgstr "이 테이블에서 미결 상태인 주문서가 있는 경우 테이블을 삭제할 수 없습니다."

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr "PoS 세션에서 사용된 층을 제거할 수 없습니다. 먼저 세션을 닫으십시오. : \n"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr "POS 세션에서 사용 중인 테이블은 삭제할 수 없습니다. 먼저 세션을 닫으시기 바랍니다."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "changes"
msgstr "거스름돈"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "items"
msgstr "항목"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/order_widget/order_widget.js:0
msgid "or book the table for later"
msgstr "또는 나중에 테이블을 예약할 수 있습니다."
