# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ro_edi_stock_batch
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-09 15:13+0000\n"
"PO-Revision-Date: 2025-01-09 15:13+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_report_picking_batch
msgid "3U3H3P4C2U5Y8N20"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_report_picking_batch
msgid "<strong>eTransport UIT:</strong>"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__32
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__32
msgid "Albița(MD)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid "All Pickings in a Batch Transfer should have the same Carrier"
msgstr ""
"Toate alegerile dintr-un transfer de lot ar trebui să aibă același operator"

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"All Pickings in a Batch Transfer should have the same Commercial Partner"
msgstr ""
"Toate alegerile dintr-un transfer de lot ar trebui să aibă același partener "
"comercial"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "Amend eTransport"
msgstr "Modificați eTransport"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__242901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__242901
msgid "BVF Aero Baia Mare (ROCJ0510)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__362902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__362902
msgid "BVF Aeroport Delta Dunării Tulcea (ROGL8910)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__302902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__302902
msgid "BVF Aeroport Satu Mare (ROCJ7830)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__372902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__372902
msgid "BVF Albiţa (ROIS0100)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__22901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__22901
msgid "BVF Arad Aeroport (ROTM0230)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__42901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__42901
msgid "BVF Bacău Aeroport (ROIS0620)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__162902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__162902
msgid "BVF Bechet (ROCR1720)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__92902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__92902
msgid "BVF Brăila (ROGL0700)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__402901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__402901
msgid "BVF Băneasa (ROBU1040)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__162903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__162903
msgid "BVF Calafat (ROCR1700)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__122901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__122901
msgid "BVF Cluj Napoca Aero (ROCJ1810)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__132904
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__132904
msgid "BVF Constanţa Port (ROCT1970)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__132901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__132901
msgid "BVF Constanţa Sud Agigea (ROCT1900)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__162901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__162901
msgid "BVF Craiova Aeroport (ROCR2110)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__332901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__332901
msgid "BVF Dorneşti (ROIS2700)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__252904
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__252904
msgid "BVF Drobeta Turnu Severin (ROCR9000)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__372901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__372901
msgid "BVF Fălciu (-)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__172904
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__172904
msgid "BVF Galaţi (ROGL3800)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__172902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__172902
msgid "BVF Giurgiuleşti (ROGL3850)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__302901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__302901
msgid "BVF Halmeu (ROCJ4310)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__222903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__222903
msgid "BVF Iaşi (ROIS4650)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__222901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__222901
msgid "BVF Iaşi Aero (ROIS4660)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__362904
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__362904
msgid "BVF Isaccea (ROGL8920)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__352901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__352901
msgid "BVF Jimbolia (ROTM5010)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__132903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__132903
msgid "BVF Mangalia (ROCT5400)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__132902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__132902
msgid "BVF Mihail Kogălniceanu (ROCT5100)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__352902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__352902
msgid "BVF Moraviţa (ROTM5510)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__112901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__112901
msgid "BVF Naidăș (ROTM6100)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__172903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__172903
msgid "BVF Oancea (ROGL3610)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__52901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__52901
msgid "BVF Oradea Aeroport (ROCJ6580)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__252901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__252901
msgid "BVF Orşova (ROCR7280)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__232901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__232901
msgid "BVF Otopeni Călători (ROBU1030)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__252902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__252902
msgid "BVF Porţile De Fier I (ROCR7270)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__252903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__252903
msgid "BVF Porţile De Fier II (ROCR7200)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__72902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__72902
msgid "BVF Rădăuţi Prut (ROIS1620)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__222902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__222902
msgid "BVF Sculeni (ROIS4990)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__322901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__322901
msgid "BVF Sibiu Aeroport (ROBV7910)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__242902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__242902
msgid "BVF Sighet (ROCJ8000)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__332902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__332902
msgid "BVF Siret (ROIS8200)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__72901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__72901
msgid "BVF Stanca Costeşti (ROIS1610)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__332903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__332903
msgid "BVF Suceava Aero (ROIS8250)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__362901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__362901
msgid "BVF Sulina (ROCT8300)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__352903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__352903
msgid "BVF Timişoara Aeroport (ROTM8730)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__362903
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__362903
msgid "BVF Tulcea (ROGL8900)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__342901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__342901
msgid "BVF Turnu Măgurele (ROCR9100)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__262901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__262901
msgid "BVF Târgu Mureş Aeroport (ROBV8820)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__332904
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__332904
msgid "BVF Vicovu De Sus (ROIS9620)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__342902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__342902
msgid "BVF Zimnicea (ROCR5800)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__92901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__92901
msgid "BVF Zona Liberă Brăila (ROGL0710)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__22902
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__22902
msgid "BVF Zona Liberă Curtici (ROTM2300)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__172901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__172901
msgid "BVF Zona Liberă Galaţi (ROGL3810)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__522901
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__522901
msgid "BVF Zona Liberă Giurgiu (ROBU3980)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__12801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__12801
msgid "BVI Alba Iulia (ROBV0300)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__342801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__342801
msgid "BVI Alexandria (ROCR0310)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__232801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__232801
msgid "BVI Antrepozite/Ilfov (ROBU1200)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__22801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__22801
msgid "BVI Arad (ROTM0200)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__42801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__42801
msgid "BVI Bacău (ROIS0600)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__242801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__242801
msgid "BVI Baia Mare (ROCJ0500)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__62801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__62801
msgid "BVI Bistriţa-Năsăud (ROCJ0400)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__72801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__72801
msgid "BVI Botoşani (ROIS1600)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__82801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__82801
msgid "BVI Braşov (ROBV0900)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__402801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__402801
msgid "BVI Bucureşti Poştă (ROBU1380)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__102801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__102801
msgid "BVI Buzău (ROGL1500)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__122801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__122801
msgid "BVI Cluj Napoca (ROCJ1800)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__282801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__282801
msgid "BVI Corabia (ROCR2000)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__162801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__162801
msgid "BVI Craiova (ROCR2100)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__512801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__512801
msgid "BVI Călăraşi (ROCT1710)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__202801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__202801
msgid "BVI Deva (ROTM8100)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__392801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__392801
msgid "BVI Focșani (ROGL3600)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__522801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__522801
msgid "BVI Giurgiu (ROBU3910)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__192801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__192801
msgid "BVI Miercurea Ciuc (ROBV5600)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__282802
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__282802
msgid "BVI Olt (ROCR8210)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__52801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__52801
msgid "BVI Oradea (ROCJ6570)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__272801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__272801
msgid "BVI Piatra Neamţ (ROIS7400)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__32801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__32801
msgid "BVI Pitești (ROCR7000)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__292801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__292801
msgid "BVI Ploiești (ROBU7100)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__112801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__112801
msgid "BVI Reșița (ROTM7600)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__382801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__382801
msgid "BVI Râmnicu Vâlcea (ROCR7700)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__302801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__302801
msgid "BVI Satu-Mare (ROCJ7810)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__142801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__142801
msgid "BVI Sfântu Gheorghe (ROBV7820)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__322801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__322801
msgid "BVI Sibiu (ROBV7900)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__212801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__212801
msgid "BVI Slobozia (ROCT8220)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__332801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__332801
msgid "BVI Suceava (ROIS8230)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__352802
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__352802
msgid "BVI Timişoara Bază (ROTM8720)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__152801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__152801
msgid "BVI Târgoviște (ROBU8600)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__182801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__182801
msgid "BVI Târgu Jiu (ROCR8810)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__262801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__262801
msgid "BVI Târgu Mureş (ROBV8800)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__402802
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__402802
msgid "BVI Târguri și Expoziții (ROBU1400)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__372801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__372801
msgid "BVI Vaslui (ROIS9610)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_customs_office__312801
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_customs_office__312801
msgid "BVI Zalău (ROCJ9700)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_l10n_ro_edi_document__batch_id
msgid "Batch"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model,name:l10n_ro_edi_stock_batch.model_stock_picking_batch
msgid "Batch Transfer"
msgstr "Transfer în lot"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__6
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__6
msgid "Bechet(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_loc_type__bcp
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_loc_type__bcp
msgid "Border Crossing Point"
msgstr "Punct de trecere a frontierei"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__38
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__38
msgid "Borș 2 - A3 (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__2
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__2
msgid "Borș(HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__5
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__5
msgid "Calafat (BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__16
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__16
msgid "Carei  (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__17
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__17
msgid "Cenad (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__401
msgid "Commercial equipment"
msgstr "Echipamente comerciale"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__35
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__35
msgid "Constanța Sud Agigea"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__14
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__14
msgid "Corabia(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_loc_type__customs
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_loc_type__customs
msgid "Customs Office"
msgstr "Biroul Vamal"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__13
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__13
msgid "Călărași(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__703
msgid "Delivery operations with installation"
msgstr "Operatii de livrare cu montaj"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model,name:l10n_ro_edi_stock_batch.model_l10n_ro_edi_document
msgid "Document object for tracking CIUS-RO XML sent to E-Factura"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__1101
msgid "Donations, help"
msgstr "Donații, ajutor"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_end_bcp
msgid "End Border Crossing Point"
msgstr "Sfârșitul punctului de trecere a frontierei"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_end_customs_office
msgid "End Customs Office"
msgstr "Biroul Vamal"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "End Location"
msgstr "Locația finală"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_end_loc_type
msgid "End Location Type"
msgstr "Tip Locație"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__18
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__18
msgid "Episcopia Bihor (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_state__stock_sending_failed
msgid "Error"
msgstr "Eroare"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__901
msgid "Exempt operations"
msgstr "Operațiuni scutite"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__50
msgid "Export"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_stock_picking_batch_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "Fetch Status"
msgstr "Preluare Stare"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__801
msgid "Financial/operational leasing"
msgstr "Leasing financiar/operational"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__501
msgid "Fixed assets"
msgstr "Mijloace fixe"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__34
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__34
msgid "Galați Giurgiulești(MD)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "General"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__9
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__9
msgid "Giurgiu(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__705
msgid "Goods made available to the customer"
msgstr "Bunuri puse la dispozitia clientului"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__802
msgid "Goods under warranty"
msgstr "Bunuri aflate in garantie"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__301
msgid "Gratuities"
msgstr "Gratuitatii"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__29
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__29
msgid "Halmeu (UA)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__40
msgid "Import"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__20
msgid "Intra-Community delivery"
msgstr "Livrare intracomunitara"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__10
msgid "Intra-community purchase"
msgstr "Achizitie intracomunitara"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__60
msgid ""
"Intra-community transaction - Entry for storage/formation of new transport"
msgstr ""
"Tranzacție intracomunitară - Intrare pentru depozitare/formare transport nou"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__70
msgid ""
"Intra-community transaction - Exit after storage/formation of new transport"
msgstr ""
"Tranzacție intracomunitară - Ieșire după depozitare/formare de transport nou"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__1001
msgid "Investment in progress"
msgstr "Investiție în curs"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__28
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__28
msgid "Jimbolia(RS)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_available_end_loc_types
msgid "L10N Ro Edi Stock Available End Loc Types"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_available_operation_scopes
msgid "L10N Ro Edi Stock Available Operation Scopes"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_available_start_loc_types
msgid "L10N Ro Edi Stock Available Start Loc Types"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_document_ids
msgid "L10N Ro Edi Stock Document"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_enable
msgid "L10N Ro Edi Stock Enable"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_enable_amend
msgid "L10N Ro Edi Stock Enable Amend"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_enable_fetch
msgid "L10N Ro Edi Stock Enable Fetch"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_enable_send
msgid "L10N Ro Edi Stock Enable Send"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_fields_readonly
msgid "L10N Ro Edi Stock Fields Readonly"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "Load Id"
msgstr "Index Incarcare"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_loc_type__location
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_loc_type__location
msgid "Location"
msgstr "Locaţie"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__101
msgid "Marketing"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__26
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__26
msgid "Naidăș(RS)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__11
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__11
msgid "Negru Vodă(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__37
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__37
msgid "Nădlac 2 - A1 (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__4
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__4
msgid "Nădlac(HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__33
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__33
msgid "Oancea(MD)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__15
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__15
msgid "Oltenița(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_operation_scope
msgid "Operation Scope"
msgstr "Domeniul de Aplicare"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__22
msgid "Operations in lohn system (EU) - exit"
msgstr "Operațiuni în sistem lohn (UE) - ieșire"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__12
msgid "Operations in lohn system (EU) - input"
msgstr "Operațiuni în sistemul lohn (UE) - intrare"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__10
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__10
msgid "Ostrov(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__9901
msgid "Other"
msgstr "Alte"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__201
msgid "Output"
msgstr "Ieșire"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__601
msgid "Own consumption"
msgstr "Consum propriu"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__1
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__1
msgid "Petea (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__25
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__25
msgid "Porțile de Fier 1 (RS)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_remarks
msgid "Remarks"
msgstr "Remarci"

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"Romanian access token not found. Please generate or fill it in the settings."
msgstr ""
"Tokenul de acces românesc nu a fost găsit. Vă rugăm să o generați sau să o "
"completați în setări."

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__19
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__19
msgid "Salonta (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__9999
msgid "Same with operation"
msgstr "La fel și cu funcționarea"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__31
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__31
msgid "Sculeni(MD)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "Send eTransport"
msgstr "Trimite eTransport"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_state__stock_sent
msgid "Sent"
msgstr "Trimis"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__36
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__36
msgid "Siret  (UA)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__27
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__27
msgid "Stamora Moravița(RS)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_start_bcp
msgid "Start Border Crossing Point"
msgstr "Începeți punctul de trecere a frontierei"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_start_customs_office
msgid "Start Customs Office"
msgstr "Începeți Biroul Vamal"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "Start Location"
msgstr "Locația de pornire"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_start_loc_type
msgid "Start Location Type"
msgstr "Tip Locație"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "Status"
msgstr "Stare"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__14
msgid "Stocks available to the customer (Call-off stock) - entry"
msgstr "Stocuri disponibile clientului (Call-off stock) - intrare"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__24
msgid "Stocks available to the customer (Call-off stock) - exit"
msgstr "Stocuri disponibile clientului (Call-off stock) - ieșire"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__30
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__30
msgid "Stânca Costești (MD)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__20
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__20
msgid "Săcuieni (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid "This document has already been successfully sent to anaf."
msgstr "Acest document a fost deja trimis cu succes către anaf."

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid "This document has not been corrected yet because it contains errors."
msgstr "Acest document nu a fost încă corectat deoarece conține erori."

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"This document has not been successfully sent yet because it contains errors."
msgstr ""
"Acest document nu a fost trimis încă cu succes deoarece conține erori."

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_trailer_1_number
msgid "Trailer 1 Number"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_trailer_2_number
msgid "Trailer 2 Number"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model,name:l10n_ro_edi_stock_batch.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_scope__704
msgid "Transfer between managements"
msgstr "Transfer între conduceri"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "Transport"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_operation_type__30
msgid "Transport on the national territory"
msgstr "Transport pe teritoriul national"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__21
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__21
msgid "Turnu (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__7
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__7
msgid "Turnu Măgurele(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "UIT"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Unhandled eTransport document state: %(state)s"
msgstr "Starea documentului eTransport netratată: %(state)s"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__22
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__22
msgid "Urziceni (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__23
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__23
msgid "Valea lui Mihai (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_state__stock_validated
msgid "Validated"
msgstr "Validat"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__12
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__12
msgid "Vama Veche(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_vehicle_number
msgid "Vehicle Number"
msgstr "Numărul vehiculului"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__24
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__24
msgid "Vladimirescu (HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__3
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__3
msgid "Vărșand(HU)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#. odoo-python
#: code:addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py:0
#, python-format
msgid "XML contains errors."
msgstr "XML conține erori."

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_end_bcp__8
#: model:ir.model.fields.selection,name:l10n_ro_edi_stock_batch.selection__stock_picking_batch__l10n_ro_edi_stock_start_bcp__8
msgid "Zimnicea(BG)"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "eTransport"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_view_batch_form
msgid "eTransport Documents"
msgstr "Documente eTransport"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_stock_picking_batch_filter
msgid "eTransport Error"
msgstr "Eroare de eTransport"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_operation_type
msgid "eTransport Operation Type"
msgstr "Tip Operațiune"

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_stock_picking_batch_filter
msgid "eTransport Sent"
msgstr "eTransport Trimis"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_state
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_stock_picking_batch_filter
msgid "eTransport Status"
msgstr "Starea eTransport"

#. module: l10n_ro_edi_stock_batch
#: model:ir.model.fields,field_description:l10n_ro_edi_stock_batch.field_stock_picking_batch__l10n_ro_edi_stock_document_uit
msgid "eTransport UIT"
msgstr ""

#. module: l10n_ro_edi_stock_batch
#: model_terms:ir.ui.view,arch_db:l10n_ro_edi_stock_batch.l10n_ro_edi_stock_stock_picking_batch_filter
msgid "eTransport Validated"
msgstr "eTransport Validat"
