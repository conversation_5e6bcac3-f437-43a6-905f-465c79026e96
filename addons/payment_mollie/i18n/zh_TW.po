# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-08 06:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_provider_form
msgid "API Key"
msgstr "API 金鑰"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
msgid "Cancelled payment with status: %s"
msgstr "付款已取消，狀態為：%s"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__code
msgid "Code"
msgstr "代碼"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "無法建立與 API 的連線。"

#. module: payment_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_provider__code__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__mollie_api_key
msgid "Mollie API Key"
msgstr "Mollie API 密鑰"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "沒有找到匹配參考 %s 的交易。"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
msgid "Received data with invalid payment status: %s"
msgstr "收到的付款狀態無效的資料：%s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the provider"
msgstr "測試或實時 API 密鑰，取決於服務商的配置"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
msgid ""
"The communication with the API failed. Mollie gave us the following "
"information: %s"
msgstr "與 API 通訊失敗。Mollie 提供了以下資訊：%s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "此付款服務商的技術代碼。"
