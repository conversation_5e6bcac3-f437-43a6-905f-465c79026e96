# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_collect
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_collect
#: model_terms:payment.provider,pending_msg:website_sale_collect.payment_provider_on_site
msgid ""
"<i>Your order has been confirmed.</i><br>Please come to the store to pay for"
" your products."
msgstr ""

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.payment_confirmation_status
msgid "<span class=\"text-muted\"> (In-store pickup)</span>"
msgstr ""

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "Available"
msgstr ""

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
msgid "Check availability"
msgstr ""

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.res_config_settings_form
msgid "Configure Pickup Locations"
msgstr ""

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/res_config_settings.py:0
msgid "Delivery Methods"
msgstr ""

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_website__in_store_dm_id
msgid "In-store Delivery Method"
msgstr ""

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
msgid "Not available"
msgstr ""

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "Only"
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/sale_order.py:0
msgid "Only %(new_qty)s available"
msgstr ""

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_stock_warehouse__opening_hours
msgid "Opening Hours"
msgstr ""

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "Out of stock"
msgstr ""

#. module: website_sale_collect
#: model:payment.provider,name:website_sale_collect.payment_provider_on_site
msgid "Pay on Site"
msgstr ""

#. module: website_sale_collect
#: model:ir.model.fields.selection,name:website_sale_collect.selection__payment_provider__custom_mode__on_site
#: model:payment.method,name:website_sale_collect.payment_method_pay_on_site
msgid "Pay on site"
msgstr ""

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_payment_provider
msgid "Payment Provider"
msgstr "دابینکەری پارەدان"

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_payment_transaction
msgid "Payment Transaction"
msgstr "مامەڵەی پارەدان"

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: model:delivery.carrier,name:website_sale_collect.carrier_pick_up_in_store
#: model:ir.model.fields.selection,name:website_sale_collect.selection__delivery_carrier__delivery_type__in_store
#: model:product.template,name:website_sale_collect.product_pick_up_in_store_product_template
msgid "Pick up in store"
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/main.py:0
msgid "Please choose a store to collect your order."
msgstr ""

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_product_template
msgid "Product"
msgstr "بەرهەم"

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "دابینکەر"

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.unavailable_products_warning
msgid "Remove from cart"
msgstr ""

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_delivery_carrier
msgid "Shipping Methods"
msgstr ""

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.unavailable_products_warning
msgid "Some of the products are not available at"
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/main.py:0
#: code:addons/website_sale_collect/models/sale_order.py:0
msgid "Some products are not available in the selected store."
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/main.py:0
msgid "Sorry, we are unable to ship your order."
msgstr ""

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_delivery_carrier__warehouse_ids
#: model_terms:ir.ui.view,arch_db:website_sale_collect.delivery_carrier_form
msgid "Stores"
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/delivery_carrier.py:0
msgid "The delivery method and a warehouse must share the same company"
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/delivery_carrier.py:0
msgid "The delivery method must have at least one warehouse to be published."
msgstr ""

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_stock_warehouse
msgid "Warehouse"
msgstr ""

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_website
msgid "Website"
msgstr "ماڵپەڕ"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/payment.py:0
msgid ""
"You can only pay on site when selecting the pick up in store delivery "
"method."
msgstr ""

#. module: website_sale_collect
#: model_terms:payment.provider,auth_msg:website_sale_collect.payment_provider_on_site
msgid "Your payment has been authorized."
msgstr ""

#. module: website_sale_collect
#: model_terms:payment.provider,cancel_msg:website_sale_collect.payment_provider_on_site
msgid "Your payment has been cancelled."
msgstr ""

#. module: website_sale_collect
#: model_terms:payment.provider,done_msg:website_sale_collect.payment_provider_on_site
msgid "Your payment has been successfully processed."
msgstr ""

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "available"
msgstr ""

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/payment_provider.py:0
msgid "no in-store delivery methods available"
msgstr ""
