# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_expense
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:32+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_count
msgid "# of Expenses"
msgstr "# Расходы"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__can_be_reinvoiced
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__can_be_reinvoiced
msgid "Can be reinvoiced"
msgstr "Можно повторно выставить счета"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__sale_order_id
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__sale_order_id
msgid "Customer to Reinvoice"
msgstr "Клиент для повторного выставления счета"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense
msgid "Expense"
msgstr "Расходы"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_product_product__expense_policy_tooltip
#: model:ir.model.fields,field_description:sale_expense.field_product_template__expense_policy_tooltip
msgid "Expense Policy Tooltip"
msgstr "Всплывающая подсказка по политике расходов"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Отчет о Расходах"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "Распределение расходов"

#. module: sale_expense
#: model:ir.actions.act_window,name:sale_expense.hr_expense_action_from_sale_order
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_ids
#: model_terms:ir.ui.view,arch_db:sale_expense.sale_order_form_view_inherit
msgid "Expenses"
msgstr "Расходы"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid "Expenses of this category may not be added to a Sales Order."
msgstr "Расходы этой категории не могут быть добавлены к заказу на продажу."

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid ""
"Expenses will be added to the Sales Order at their actual cost when posted."
msgstr ""
"Расходы будут добавлены к заказу на продажу по их фактической стоимости при "
"учете."

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid ""
"Expenses will be added to the Sales Order at their sales price (product "
"price, pricelist, etc.) when posted."
msgstr ""
"Расходы будут добавлены к заказу на продажу по их продажной цене (цена "
"продукта, прайс-лист и т.д.) при учете."

#. module: sale_expense
#: model:ir.model.fields,help:sale_expense.field_hr_expense__sale_order_id
msgid ""
"If the category has an expense policy, it will be reinvoiced on this sales "
"order"
msgstr ""
"Если для категории предусмотрена политика расходов, она будет повторно "
"указана в этом заказе на продажу"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.product_product_view_form_inherit_sale_expense
msgid "Invoicing"
msgstr "Выставление счетов"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move
msgid "Journal Entry"
msgstr "Запись в журнале"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move_line
msgid "Journal Item"
msgstr "Элемент журнала"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_product_template
msgid "Product"
msgstr "Товар"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/hr_expense_sheet.py:0
msgid "Reinvoiced Sales Orders"
msgstr "Заказы на продажу в повторными счетами"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_sheet__sale_order_count
msgid "Sale Order Count"
msgstr "Счетчик заказов на продажу"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.hr_expense_sheet_view_form
msgid "Sales Orders"
msgstr "Заказы на продажу"
