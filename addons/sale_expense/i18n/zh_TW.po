# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_expense
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:32+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_count
msgid "# of Expenses"
msgstr "開支數目"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__can_be_reinvoiced
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__can_be_reinvoiced
msgid "Can be reinvoiced"
msgstr "可以請求報銷"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__sale_order_id
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__sale_order_id
msgid "Customer to Reinvoice"
msgstr "待報銷費用客戶"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense
msgid "Expense"
msgstr "開支"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_product_product__expense_policy_tooltip
#: model:ir.model.fields,field_description:sale_expense.field_product_template__expense_policy_tooltip
msgid "Expense Policy Tooltip"
msgstr "報銷政策工具提示"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "開支報告"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "開支分攤"

#. module: sale_expense
#: model:ir.actions.act_window,name:sale_expense.hr_expense_action_from_sale_order
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_ids
#: model_terms:ir.ui.view,arch_db:sale_expense.sale_order_form_view_inherit
msgid "Expenses"
msgstr "開支"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid "Expenses of this category may not be added to a Sales Order."
msgstr "此類開支不可加至銷售訂單。"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid ""
"Expenses will be added to the Sales Order at their actual cost when posted."
msgstr "過賬時，開支將按實際成本加至銷售訂單。"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid ""
"Expenses will be added to the Sales Order at their sales price (product "
"price, pricelist, etc.) when posted."
msgstr "過賬時，開支將按其銷售價格（產品價格、價目表等）加至銷售訂單。"

#. module: sale_expense
#: model:ir.model.fields,help:sale_expense.field_hr_expense__sale_order_id
msgid ""
"If the category has an expense policy, it will be reinvoiced on this sales "
"order"
msgstr "如果該類別有費用政策，則將在此銷售訂單上處理費用報銷"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.product_product_view_form_inherit_sale_expense
msgid "Invoicing"
msgstr "發票"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move_line
msgid "Journal Item"
msgstr "日記賬項目"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_product_template
msgid "Product"
msgstr "產品"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/hr_expense_sheet.py:0
msgid "Reinvoiced Sales Orders"
msgstr "重開發票的銷售訂單"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_sheet__sale_order_count
msgid "Sale Order Count"
msgstr "銷售訂單數目"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_sale_order
msgid "Sales Order"
msgstr "銷售訂單"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.hr_expense_sheet_view_form
msgid "Sales Orders"
msgstr "銷售訂單"
