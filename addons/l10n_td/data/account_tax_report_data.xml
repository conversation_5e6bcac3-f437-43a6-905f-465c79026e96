<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_tax_report_td" model="account.report">
        <field name="name">VAT Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.td"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="account_tax_report_td_balance" model="account.report.column">
                <field name="name">Base</field>
                <field name="expression_label">base</field>
            </record>
            <record id="account_tax_report_td_tax" model="account.report.column">
                <field name="name">Tax</field>
                <field name="expression_label">tax</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_tax_report_line_td_sales" model="account.report.line">
                <field name="name">Outgoing</field>
                <field name="code">TD_SALES</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_tax_report_line_td_sales_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TD_TAXABLE_18.base + TD_TAXABLE_9.base + TD_EXPORT.base + TD_SALE_EXEMPT.base</field>
                    </record>
                    <record id="account_tax_report_line_td_sales_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TD_TAXABLE_18.tax + TD_TAXABLE_9.tax</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_tax_report_line_sales_18" model="account.report.line">
                        <field name="name">Taxable operations at 18%</field>
                        <field name="code">TD_TAXABLE_18</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_sales_18_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">base_18</field>
                            </record>
                            <record id="account_tax_report_line_sales_18_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">tax_18</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_sales_9" model="account.report.line">
                        <field name="name">Taxable operations at 9%</field>
                        <field name="code">TD_TAXABLE_9</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_sales_9_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">base_9</field>
                            </record>
                            <record id="account_tax_report_line_sales_9_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">tax_9</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_sales_export" model="account.report.line">
                        <field name="name">Export</field>
                        <field name="code">TD_EXPORT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_sales_export_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">export</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_sales_exempt" model="account.report.line">
                        <field name="name">Exempt</field>
                        <field name="code">TD_SALE_EXEMPT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_sales_exempt_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">sale_exempt</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_td_purchases" model="account.report.line">
                <field name="name">Incoming</field>
                <field name="code">TD_VAT_DEDUCT</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_tax_report_line_td_purchases_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TD_PURC_TAXABLE.base + TD_IMPORT.base + TD_PURC_EXEMPT.base</field>
                    </record>
                    <record id="account_tax_report_line_td_purchase_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TD_PURC_TAXABLE.tax</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_tax_report_line_td_purchases_taxable" model="account.report.line">
                        <field name="name">Taxable</field>
                        <field name="code">TD_PURC_TAXABLE</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_td_purchases_taxable_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">purc_base</field>
                            </record>
                            <record id="account_tax_report_line_td_purchases_taxable_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">purc_tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_td_purchases_import" model="account.report.line">
                        <field name="name">Import</field>
                        <field name="code">TD_IMPORT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_td_purchases_import_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">import_base</field>
                            </record>
                            <record id="account_tax_report_line_td_purchases_import_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">import_tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_td_purchases_exempt" model="account.report.line">
                        <field name="name">Exempt</field>
                        <field name="code">TD_PURC_EXEMPT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_td_purchases_exempt_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">purc_exempt</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <record id="account_tax_report_line_td_net" model="account.report.line">
                <field name="name">Net VAT</field>
                <field name="code">TD_NET</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_tax_report_line_td_net_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TD_VAT_CREDIT.tax + TD_VAT_TO_PAY.tax</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_tax_report_line_td_credit" model="account.report.line">
                        <field name="name">VAT Credit</field>
                        <field name="code">TD_VAT_CREDIT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_td_credit_tax" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TD_VAT_DEDUCT.tax - TD_SALES.tax</field>
                                <field name="subformula">if_above(EUR(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_td_to_pay" model="account.report.line">
                        <field name="name">VAT to pay</field>
                        <field name="code">TD_VAT_TO_PAY</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_td_to_pay_tax" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TD_SALES.tax - TD_VAT_DEDUCT.tax</field>
                                <field name="subformula">if_above(EUR(0))</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>