<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="res_partner_view_search" model="ir.ui.view">
        <field name="name">res.partner.search.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.res_partner_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='supplier']" position="after">
                <filter string="Exceeded Declaration of Intent"
                        name="l10n_it_edi_doi_declaration_of_intent_exceeded"
                        domain="[('l10n_it_edi_doi_ids','any', [('remaining', '&lt;', 0)])]"/>
            </xpath>
        </field>
    </record>

    <record id="view_partner_l10n_form" model="ir.ui.view">
        <field name="name">view_partner_l10n_form</field>
        <field name="inherit_id" ref="base_vat.view_partner_base_vat_form"/>
        <field name="model">res.partner</field>
        <field name="priority">100</field>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button groups="account.group_account_invoice"
                        type="object"
                        class="oe_stat_button"
                        name="l10n_it_edi_doi_action_open_declarations"
                        icon="fa-list"
                        invisible="'IT' not in fiscal_country_codes">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Declarations of Intent</span>
                    </div>
                </button>
            </div>
        </field>
    </record>

</odoo>
