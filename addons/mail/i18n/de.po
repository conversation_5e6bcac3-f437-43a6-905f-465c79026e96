# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# Wil O<PERSON>o, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Fügen Sie der Berechtigungsliste Adressen hinzu.\n"
"            </p><p>\n"
"                Um Sie vor Spam und Antwortschleifen zu schützen, blockiert Odoo automatisch E-Mails,\n"
"                die mit einer Häufigkeit von <b>%(threshold)i</b> E-Mails pro <b>%(minutes)i</b>\n"
"                Minuten an Ihrem Gateway ankommen. Wenn es Adressen gibt, von denen Sie sehr häufig Nachrichten erhalten müssen,\n"
"                können Sie diese unten hinzufügen, und Odoo wird sie dann nicht blockieren.\n"
"            </p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr "“ nicht mehr verfolgt"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr "„%(activity_name)s: %(summary)s“ an Sie zugewiesen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr "„%(hostname)s“ benötigt Zugriff auf Ihr Mikrofon"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "\"%(hostname)s\" requires microphone access"
msgstr "„%(hostname)s“ erfordert Zugriff aufs Mikrofon"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr "%(activity)s, zugewiesen an %(name)s, fällig am %(deadline)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "%(author name)s from %(channel name)s"
msgstr "%(author name)s von %(channel name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s wird nicht als gültige E-Mail erkannt. Dies ist erforderlich, um "
"einen neuen Kunden anzulegen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person)s"
msgstr "%(person)s hat mit %(emoji)s reagiert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s and %(person2)s"
msgstr "%(person1)s und %(person2)s haben mit %(emoji)s reagiert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and %(count)s "
"others"
msgstr ""
"%(person1)s, %(person2)s, %(person3)s und %(count)s weitere haben mit "
"%(emoji)s reagiert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and 1 other"
msgstr ""
"%(person1)s, %(person2)s, %(person3)s und 1 weitere Person haben mit "
"%(emoji)s reagiert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s, %(person2)s, and %(person3)s"
msgstr "%(person1)s, %(person2)s und %(person3)s haben mit %(emoji)s reagiert"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "%(model_name)s.%(field_path)s does not seem to be a valid field path"
msgstr ""
"„%(model_name)s.%(field_path)s“ scheint kein gültiger Feldpfad zu sein"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%(name)s: %(message)s)"
msgstr "%(name)s: %(message)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(new_line)s%(new_line)sType %(bold_start)s@username%(bold_end)s to mention "
"someone, and grab their attention.%(new_line)sType "
"%(bold_start)s#channel%(bold_end)s to mention a channel.%(new_line)sType "
"%(bold_start)s/command%(bold_end)s to execute a command.%(new_line)sType "
"%(bold_start)s:shortcut%(bold_end)s to insert a canned response in your "
"message."
msgstr ""
"%(new_line)s%(new_line)sTippen Sie %(bold_start)s@Benutzername%(bold_end)s, "
"um jemanden zu erwähnen und die Aufmerksamkeit zu erregen.%(new_line)sTippen"
" Sie %(bold_start)s#Kanal%(bold_end)s, um einen Kanal zu erwähnen. "
"%(new_line)sTippen Sie%(bold_start)s/Befehl%(bold_end)s, um einen Befehl "
"auszuführen.%(new_line)sGeben Sie ein "
"%(bold_start)s:Tastenkürzel%(bold_end)s ein, um eine vorformulierte Anwort "
"in Ihre Nachricht einzufügen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr ""
"%(open_button)s%(icon)s%(open_em)sBearbeitung "
"verwerfen%(close_em)s%(close_button)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sESC%(close_samp)s %(open_em)s zum "
"%(open_cancel)sAbbrechen%(close_cancel)s%(close_em)s, %(open_samp)sStrg-"
"Enter%(close_samp)s %(open_em)szum "
"%(open_save)sSpeichern%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sESC%(close_samp)s %(open_em)s "
"zum%(open_cancel)sAbbrechen%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)szum "
"%(open_save)sSpeichern%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.js:0
msgid "%(recipientCount)s more"
msgstr "%(recipientCount)s weitere"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr ""
"%(user)s verbunden. Dies ist ihre erste Verbindung. Wünschen Sie ihnen "
"Glück."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(user)s started a thread: %(goto)s%(thread_name)s%(goto_end)s. "
"%(goto_all)sSee all threads%(goto_all_end)s."
msgstr ""
"%(user)s hat einen Thread gestartet: %(goto)s%(thread_name)s%(goto_end)s. "
"%(goto_all)sAlle Threads ansehen%(goto_all_end)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s and %(user2)s are typing..."
msgstr "%(user1)s und %(user2)s schreiben ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s, %(user2)s and more are typing..."
msgstr "%(user1)s, %(user2)s und weitere schreiben ..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "%(user_name)s pinned a message to this channel."
msgstr "%(user_name)s hat eine Nachricht an diesen Kanal angeheftet."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
msgid "%s (Email Template)"
msgstr "%s(E-Mail-Vorlage)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan.py:0
#: code:addons/mail/models/mail_template.py:0
msgid "%s (copy)"
msgstr "%s (Kopie)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "%s created"
msgstr "%s erstellt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "%s days overdue"
msgstr "%s Tage überfällig"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%s is typing..."
msgstr "%s schreibt..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_result.js:0
msgid "%s messages found"
msgstr "%s Nachrichten gefunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "%s new messages"
msgstr "%s neue Nachrichten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s raised their hand"
msgstr "%s hat die Hand gehoben"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "%s started a live conference"
msgstr "%s hat eine Live-Konferenz begonnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"camera\" access"
msgstr "„%s“ erfordert Zugriff auf „Kamera“"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"screen recording\" access"
msgstr "„%s“ erfordert Zugriff auf „Bildschirmaufzeichnung“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_attachment_links
msgid "&amp;#128229;"
msgstr "&amp;#128229;"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translated from: %(language)s)"
msgstr "(Überstetzt aus: %(language)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translation Failure: %(error)s)"
msgstr "(Übersetzungsfehler: %(error)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "(edited)"
msgstr "(bearbeitet)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "(from"
msgstr "(von"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(ursprünglich zugewiesen an"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid ") added you as a follower of this"
msgstr ") hat Sie als Follower hinzugefügt für:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid ". Narrow your search to see more choices."
msgstr ""
". Schränken Sie Ihre Suche ein, um mehr Auswahlmöglichkeiten zu sehen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "1 new message"
msgstr "1 neue Nachricht"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr ""
"<b invisible=\"not no_record\" class=\"text-warning\">Keine Datensätze für "
"dieses Modell</b>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid ""
"<button>Change your preferences</button> to receive new notifications in "
"your inbox."
msgstr ""
"<button>Ändern Sie Ihre Präferenzen</button>, um neue Benachrichtigungen in "
"Ihrem Posteingang zu erhalten."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-check\"/> Done"
msgstr "<i class=\"fa fa-check\"/> Erledigt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_kanban
msgid ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"Steps count\" "
"title=\"Steps count\"/>"
msgstr ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"Steps count\" "
"title=\"Steps count\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-times\"/> Cancel"
msgstr "<i class=\"fa fa-times\"/> Abbrechen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Tauschen Sie sich</b> mithilfe von Direktnachrichten in Echtzeit <b> "
"mit Kollegen aus</b>.</p><p><i>Möglicherweise müssen Sie dafür zunächst die "
"Benutzer in den Einstellungen einladen.</i></p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Schreiben Sie hier eine Mitteilung</b> an die Mitglieder dieses "
"Kanals.</p> <p>Benachrichtigen Sie eine Person mit <i>„@“</i> oder "
"verknüpfen Sie einen anderen Kanal mit <i>„#“</i>. Beginnen Sie Ihre "
"Mitteilung mit <i>„/“</i>, um die Liste mit möglichen Befehlen "
"anzuzeigen.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Mit Kanälen können Informationen mühelos für verschiedene Themen und "
"Gruppen organisiert werden.</p> <p>Sie können <b>Ihren ersten Kanal "
"erstellen</b> (z. B. Verkauf, Marketing, Produkt XYZ, After-Work-Party und "
"mehr).</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a channel here.</p>"
msgstr "<p>Erstellen Sie hier einen Kanal.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>Erstellen Sie einen öffentlichen oder privaten Kanal.</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Farbe der Schaltflächen</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Farbe der Kopfzeile</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr "<span class=\"me-1 oe_inline\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr "<span class=\"me-1\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr "<span class=\"o_stat_text\">Dokument öffnen</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr "<span class=\"o_stat_text\">Übergeordnetes Dokument öffnen</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                Die Nachricht wird als E-Mail an die Empfänger der\n"
"                                Vorlage gesendet und erscheint nicht im Nachrichtenverlauf.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                Die Nachricht wird als interne Notiz hinterlassen, die für\n"
"                                interne Benutzer im Nachrichtenverlauf sichtbar ist.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                Die Nachricht wird als Nachricht im Datensatz hinterlassen\n"
"                                und alle Follower werden benachrichtigt. Sie erscheint im Nachrichtenverlauf.\n"
"                            </span>"

#. module: mail
#: model_terms:web_tour.tour,rainbow_man_message:mail.discuss_channel_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Gut gemacht!</b> Sie haben alle Schritte dieser Tour "
"absolviert.</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr "<span>Wenn Sie das waren:</span><br/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr "<span>Wenn Sie das nicht waren:</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr "<span>Datensatz öffnen</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr "<span>Beginnen Sie mit</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>Speichern</strong> Sie diese Seite und kommen Sie hierher zurück, um"
" die Funktion einzurichten."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr "<strong>Sie folgen dem Dokument nicht mehr:</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Ein Python-Dictionary, das Standardwerte zur Verfügung stellt, wenn neue "
"Datensätze für diesen Alias angelegt werden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A Scheduled Message cannot be scheduled in the past"
msgstr ""
"Eine geplante Nachricht kann nicht in der Vergangenheit geplant werden"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "Eine Bus-Anwesenheit erfordert einen Benutzer oder einen Gast."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "Ein Kanal-Mitglied muss ein Partner oder ein Gast sein."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "Ein Kanal vom Typ „Chat“ kann nicht mehr als zwei Benutzer haben."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr ""
"Ein Chat sollte nicht mit mehr als 2 Personen erstellt werden. Erstellen Sie"
" stattdessen eine Gruppe."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "A message can only be scheduled in monocomment mode"
msgstr "Eine Nachricht kann nur im Monokommentar-Modus geplant werden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"A message cannot be scheduled on a model that does not have a mail thread."
msgstr ""
"Eine Nachricht kann nicht für ein Modell geplant werden, das keinen E-Mail-"
"Thread hat."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr ""
"Eine Reaktion auf eine Nachricht muss von einem Partner oder einem Gast "
"kommen."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "A next activity can only be planned on models that use activities."
msgstr ""
"Eine nächste Aktivität kann nur in Modellen geplant werden, die Aktivitäten "
"verwenden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A scheduled message could not be sent"
msgstr "Eine geplante Nachricht konnte nicht versendet werden"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""
"Ein gültiger Google-API-Schlüssel ist erforderlich, um die Übersetzung von "
"Nachrichten zu aktivieren. https://cloud.google.com/translate/docs/setup"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "Eine Volumeneinstellung muss einen Partner oder einen Gast haben."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_chatgpt.js:0
msgid "AI"
msgstr "KI"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept"
msgstr "Akzeptieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept with camera"
msgstr "Mit Kamera annehmen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Access Denied"
msgstr "Zugriff verweigert"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Zugriffsgruppen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Zugriffstoken"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr "Zugriff auf die Gruppe „%(groupFullName)s“ beschränkt"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Account"
msgstr "Konto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Aktion"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Ansicht des Aktionsfensters"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Actions"
msgstr "Aktionen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Aktionen führen gegebenenfalls zu weiteren Funktionen wie zur Öffnung der "
"Kalenderansicht oder zur automatischen „Erledigt“-Markierung, wenn ein "
"Dokument hochgeladen wurde."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "Aktionen für eingehende Mails"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Standardmäßig aktiviert beim Abonnieren."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "Aktiv"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "Aktive Domain"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities_section
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activities"
msgstr "Aktivitäten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Create"
msgstr "Zu erstellende Aktivitäten"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr ""
"Aktivitäten müssen mit Datensätzen verknüpft werden, deren res_id nicht null"
" ist."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity"
msgstr "Aktivität"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Aktivitäts-Mixin"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.ui.menu,name:mail.menu_mail_activities
msgid "Activity Overview"
msgstr "Aktivitätsübersicht"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr "Aktivitätsplan"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "Aktivitätspläne"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "Aktivitätseinstellungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Aktivitätstyp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Activity Type Name"
msgstr "Name des Aktivitätstyps"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Aktivitätstypen"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Vorlage des Aktivitätsplans"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Prospect Follow-up\", \"Project Milestone Meeting\", ...)"
msgstr ""
"Aktivitätspläne werden verwendet, um eine Liste von Aktivitäten mit nur wenigen Klicks zuzuweisen\n"
"                    (z. B. „Einführung“, „Verfolgung potenzieller Kunden“, „Meeting für Projektmeilensteine“ ...)"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Assistent zur Aktivitätsplanung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Activity type"
msgstr "Aktivitätstyp"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Activity: %s"
msgstr "Aktivität: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr "Kontextaktion hinzufügen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Schwarze Liste für E-Mails hinzufügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add Followers"
msgstr "Follower hinzufügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.xml:0
msgid "Add Reaction"
msgstr "Reaktion hinzufügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Add a Reaction"
msgstr "Reaktion hinzufügen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr ""
"Fügen Sie einen Tenor-GIF-API-Schlüssel hinzu, um die Unterstützung von GIFs"
" zu aktivieren."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""
"Fügen Sie einen Tenor-GIF-API-Schlüssel hinzu, um die Unterstützung von GIFs"
" zu aktivieren. https://developers.google.com/tenor/guides/quickstart#setup"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Add a description"
msgstr "Beschreibung hinzufügen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr "Ihrer Aktivität eine Beschreibung hinzufügen ..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""
"Fügen Sie eine neues %(document)s hinzu oder senden Sie eine E-Mail an "
"%(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "Fügen Sie der schwarzen Liste eine E-Mail-Adresse hinzu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add and close"
msgstr "Hinzufügen und schließen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Add as recipient and follower (reason: %s)"
msgstr "Als Empfänger und Follower hinzufügen (Grund: %s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts"
msgstr "Kontakte hinzufügen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Add contacts to notify..."
msgstr "Kontakte hinzufügen, die benachrichtigt werden müssen ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
msgid "Add followers to this document"
msgstr "Diesem Dokument Follower hinzufügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Add or join a channel"
msgstr "Einen Kanal hinzufügen oder einem beitreten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr "Signatur hinzufügen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "Fügen Sie Ihre Twilio-Anmeldedaten für ICE-Server hinzu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""
"Das Hinzufügen von Followern zu Kanälen ist nicht möglich. Versuchen Sie "
"stattdessen, Mitglieder hinzuzufügen."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr ""
"Es ist nicht möglich, mehr Mitglieder zu diesem Chat hinzuzufügen; er ist "
"nur für zwei Personen ausgelegt."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Weitere Kontakte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Erweitert"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "Erweiterte Optionen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__after_plan_date
msgid "After Plan Date"
msgstr "Nach dem Plandatum"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__warning
msgid "Alert"
msgstr "Warnung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""
"Alias %(matching_name)s (%(current_id)s) ist schon mit %(alias_model_name)s "
"(%(matching_id)s) verknüpft und wird von %(parent_name)s "
"%(parent_model_name)s verwendet."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""
"Alias %(matching_name)s (%(current_id)s) ist schon mit %(alias_model_name)s "
"(%(matching_id)s) verknüpft."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "Aliaskontakt-Sicherheit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "Alias-Domain"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "Alias-Domainname"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr "Alias-Domains"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "Alias-E-Mail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "Alias-Name"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "Alias-Status"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr "Alias domain name"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""
"Alias-Status, der bei der letzten empfangenen Nachricht festgestellt wurde."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "Alias-Modell"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Aliasse"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr ""
"Aliasse %(alias_names)s wird/werden bereits für unzustellbare Nachrichten "
"oder als Catchall-Adresse verwenden. Bitte wählen Sie einen anderen Alias."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "All"
msgstr "Alle"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__all
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__all
msgid "All Messages"
msgstr "Alle Nachrichten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "All conversations have been muted"
msgstr "Alle Unterhaltungen wurden stummgeschaltet"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "All partners must belong to the same message"
msgstr "Alle Partner müssen zur selben Nachricht gehören"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr "Öffentliches Hochladen erlauben"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"Eine SSL-Ausnahme ist aufgetreten. Prüfen Sie die SSL/TLS-Konfiguration am Server-Port.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "An access token must be provided for each attachment."
msgstr "Für jeden Anhang muss ein Zugriffstoken angegeben werden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "An email is required for find_or_create to work"
msgstr "Eine E-Mail wird benötigt, damit find_or_create funktioniert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email"
msgstr "Beim Senden einer E-Mail ist ein Fehler aufgetreten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email on “%(record_name)s”"
msgstr ""
"Beim Senden einer E-Mail auf „%(record_name)s“ ist ein Fehler aufgetreten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "An error occurred while fetching messages."
msgstr "Beim Abrufen der Nachrichten ist ein Fehler aufgetreten."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "An unexpected error occurred during the creation of the chat."
msgstr ""
"Ein unerwarteter Fehler ist während der Erstellung des Chats aufgetreten."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And"
msgstr "Und"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And 1 other member."
msgstr "Und 1 anderes Mitglied."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Gilt für"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
msgid "Apply"
msgstr "Anwenden"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "Archiviert"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr ""
"Archiviert, weil %(user_name)s (#%(user_id)s) das Portalkonto gelöscht hat."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Are you sure you want to cancel the scheduled message?"
msgstr "Sind Sie sicher, dass Sie die geplante Nachricht stornieren möchten?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are you sure you want to delete \"%(template_name)s\"?"
msgstr "Sind Sie sicher, dass Sie „%(template_name)s” löschen möchten?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr "Sind Sie sicher, dass Sie diese E-Mail-Vorlage löschen möchten?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Are you sure you want to delete this message?"
msgstr "Sind Sie sicher, dass Sie diese Nachricht löschen möchten?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"Sind Sie sicher, dass Sie diese E-Mail-Vorlagen auf ihre ursprüngliche "
"Konfiguration zurücksetzen wollen? Änderungen und Übersetzungen gehen dann "
"verloren."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr ""
"Sind Sie sicher, dass Sie diese E-Mail-Adresse von der schwarzen Liste "
"streichen möchten?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Are you sure you want to unblacklist this email address?"
msgstr ""
"Sind Sie sicher, dass Sie diese E-Mail-Adresse von der schwarzen Liste "
"streichen möchten?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are your sure you want to update \"%(template_name)s\"?"
msgstr "Sind Sie sicher, dass Sie „%(template_name)s” aktualisieren möchten?"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr "Bei Start fragen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to ..."
msgstr "Zuweisen an ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to me"
msgstr "Mir zuweisen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Assigned To"
msgstr "Zugewiesen an"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
msgid "Assigned to"
msgstr "Zugewiesen an"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Zuweisung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "At this point lang should be correctly set"
msgstr "Zu diesem Zeitpunkt sollte die Sprache korrekt eingestellt sein"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Attach files"
msgstr "Dateien anhängen"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Dateianhang"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Attachment counter loading..."
msgstr "Anhangzähler lädt ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Attachments"
msgstr "Dateianhänge"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Audio player:"
msgstr "Audio-Player:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Authentifizierten Partnern"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Autor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Verfasser der Mitteilung. Wenn nicht gesetzt, kann email_from eine E-Mail-"
"Adresse enthalten, die keinem Partner zugeordnet werden kann."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar des Autors"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "Autorisierte Gruppe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__group_ids
msgid "Authorized Groups"
msgstr "Autorisierte Gruppen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Automatisch löschen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Gruppen automatisch abonnieren"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "Automatisches Abonnement"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Automatisches Abonnement"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr "Automatische Zielbenachrichtigung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Automatische Aktivität"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Automated message"
msgstr "Automatische Nachricht"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"Planen Sie diese Aktivität automatisch, sobald die aktuelle Aktivität als "
"erledigt markiert ist."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Available for all Companies"
msgstr "Für alle Unternehmen verfügbar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_cache_key
msgid "Avatar Cache Key"
msgstr "Cache-Schlüssel des Avatars"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Avatar of user"
msgstr "Avatar des Benutzers"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Away"
msgstr "Abwesend"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Background blur intensity"
msgstr "Intensität der Hintergrundschärfe"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Basis"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr "Basisvorlage"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr "Basisvorlagen"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr "Base64-codierter Schlüssel"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr "Sammelerstellung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr ""
"Sammelprotokoll kann keine Anhänge oder Nachverfolgungswerte für mehr als 1 "
"Dokument unterstützen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__before_plan_date
msgid "Before Plan Date"
msgstr "Vor dem Plandatum"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Schwarze Liste"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Datum der schwarzen Liste"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Adressen auf der schwarzen Liste"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "E-Mail-Adressen auf der schwarzen Liste"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"Blockiert durch Löschung des Portalkontos %(portal_user_name)s durch "
"%(user_name)s (#%(user_id)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Blur Background"
msgstr "Hintergrund unscharf machen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Blur video background"
msgstr "Video-Hintergrund trüben"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Nachrichtentext"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Textinhalt ist derselbe wie die Vorlage"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Bot"
msgstr "Bot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "Unzustellbar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr "Zuzustellbarkeitsalias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr "Unzustellbarekeitsmail"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""
"Der Unzustellbarkeitsalias %(bounce)s wird bereits für eine andere Domain "
"mit demselben Namen verwendet. Verwenden Sie einen anderen "
"Unzustellbarkeitsalias oder verwenden Sie einfach die andere Alias-Domain."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr "Unzustellbarkeitsmails sollten einzigartig sein"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""
"Unzustellbarkeits-/Catchall-Alias „%(matching_alias_name)s“ wird bereits von"
" %(document_name)s verwendet. Wählen Sie einen anderen Alias oder ändern Sie"
" ihn im anderen Dokument."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""
"Unzustellbarkeits-/Catchall-Alias „%(matching_alias_name)s“ wird bereits "
"verwendet. Wählen Sie einen anderen Alias oder ändern Sie ihn im verbundenen"
" Modell."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
msgid "Bounced"
msgstr "Unzustellbar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr ""
"Brave: Aktivieren Sie „Google-Services für Push-Benachrichtigungen“, um "
"Push-Benachrichtigungen zu aktivieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Browser default"
msgstr "Browser-Standardeinstellung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__endpoint
msgid "Browser endpoint"
msgstr "Browser-Endpunkt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__keys
msgid "Browser keys"
msgstr "Browser-Schlüssel"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
msgid "CC Email"
msgstr "E-mail-CC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "CTRL"
msgstr "STRG"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Anruf"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Call Settings"
msgstr "Anrufeinstellungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Camera is off"
msgstr "Kamera ist aus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr "Darf abbrechen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "Darf Inhalt bearbeiten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr "Darf erneut senden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "Darf schreiben"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Can not update the message or recipient of a notification."
msgstr ""
"Die Nachricht oder der Empfänger der Benachrichtigung kann nicht "
"aktualisiert werden."

#. module: mail
#: model:ir.model,name:mail.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "Kan Nachrichten über bus.bus senden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "E-Mail abbrechen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Cancel Message"
msgstr "Nachricht abbrechen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: mail
#: model:ir.model,name:mail.model_mail_canned_response
msgid "Canned Response"
msgstr "Vorformulierte Antwort"

#. module: mail
#: model:res.groups,name:mail.group_mail_canned_response_admin
msgid "Canned Response Administrator"
msgstr "Administrator für vorformulierte Antworten"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_canned_response_action
#: model:ir.module.category,name:mail.module_category_canned_response
#: model:ir.ui.menu,name:mail.menu_canned_responses
msgid "Canned Responses"
msgstr "Vorformulierte Antworten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Canned Responses Search"
msgstr "Suche in vorformulierten Antworten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_form
msgid "Canned response"
msgstr "Vorformulierte Antwort"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__source
msgid ""
"Canned response that will automatically be substituted with longer content "
"in your messages. Type ':' followed by the name of your shortcut (e.g. "
":hello) to use in your messages."
msgstr ""
"Vorformulierte Antwort, die automatisch durch längere Inhalte in Ihren "
"Nachrichten ersetzt wird. Geben Sie „:“ gefolgt von dem Namen des Kürzels "
"(z. B. :hallo) ein, das Sie in Ihren Nachrichten verwenden möchten."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_tree
msgid "Canned responses"
msgstr "Vorformulierte Antworten"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                    your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                    replaced directly in your message, so that you can still edit\n"
"                    it before sending."
msgstr ""
"Vorformulierte Antworten ermöglichen es Ihnen, vorgefertigte Antworten in\n"
"                    Ihre Nachrichten einzufügen, indem Sie <i>:shortcut</i>. eingeben. Das Kürzel wird\n"
"                    direkt in Ihrer Nachricht ersetzt, sodass Sie diese immer noch vor\n"
"                    dem Absenden ändern können."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change initial message nor parent channel of: %(channels)s."
msgstr ""
"Weder die erste Nachricht noch der übergeordnete Kanal kann geändert werden:"
" %(channels)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr "Folgender Kanaltyp kann nicht geändert werden: %(channel_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: initial message should belong to parent channel."
msgstr ""
"%(channels)s kann nicht erstellt werden: Die erste Nachricht sollte zum "
"übergeordneten Kanal gehören."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: parent should not be a sub-channel and should be"
" of type 'channel'."
msgstr ""
"%(channels)s kann nicht erstellt werden: Übergeordneter Kanal sollte kein "
"Unterkanal sein und sollte vom Typ „Kanal“ sein."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "CC-Empfänger"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "CC-Empfänger"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "CC-Empfänger (Platzhalter können verwendet werden)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "Catch-All"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr "Catchall-Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Catcha-All-E-Mail"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""
"Der Catchall-Alias %(catchall)s wird bereits für eine andere Domain mit "
"demselben Namen verwendet. Verwenden Sie einen anderen Catchall oder "
"verwenden Sie einfach die andere Alias-Domain."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr "Catchall-E-Mails sollten einzigartig sein"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "CC"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "Verkettungstyp"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr ""
"Ändern Sie die Hintergrundfarbe der zugehörigen Aktivitäten dieses Typs."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Channel"
msgstr "Kanal"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "Kanalmitglied"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
#: model:ir.model.fields,field_description:mail.field_res_users_settings__channel_notifications
msgid "Channel Notifications"
msgstr "Kanalbenachrichtigungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Kanaltyp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Channel full"
msgstr "Kanal voll"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "Channel members cannot include public users."
msgstr "Kanalmitglieder können keine öffentlichen Benutzer sein."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Channel settings"
msgstr "Kanal-Einstellungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
msgid "Channels"
msgstr "Kanäle"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr "Kanäle/Mitglieder"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
msgid "Chat"
msgstr "Chat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Chat Options"
msgstr "Chatoptionen"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Der Chat ist privat und nur zwischen 2 Personen. Die Gruppe ist privat für "
"die eingeladenen Personen. Einem Kanal kann frei beigetreten werden "
"(abhängig von seiner Konfiguration)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Chats"
msgstr "Chats"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr "Ausschlussliste prüfen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Untergeordnete Nachrichten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "Eine Vorlage auswählen ..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr "Einen Benutzer auswählen ..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Choose another value or change it on the other document."
msgstr ""
"Wählen Sie einen anderen Wert oder ändern Sie ihn im anderen Dokument."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr "Wählen Sie eine Zuweisung für Aktivitäten mit Bedarfszuweisung."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Clear quick search"
msgstr "Schnellsuche leeren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Click here to retry"
msgstr "Hier klicken, um es erneut zu versuchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Click to see the attachments"
msgstr "Klicken, um die Anhänge zu sehen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Close"
msgstr "Schließen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "Close Chat Bubble"
msgstr "Chatblase schließen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Chat Window (ESC)"
msgstr "Chatfenster schließen (ESC)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Search"
msgstr "Suche schließen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Close all conversations"
msgstr "Alle Unterhaltungen schließen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_ad_banner.xml:0
msgid "Close banner"
msgstr "Banner schließen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Close button"
msgstr "Schließen-Schaltfläche"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
msgid "Close panel"
msgstr "Panel schließen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
msgid "Close search"
msgstr "Suche schließen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "Geschlossen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Collapse panel"
msgstr "Panel einklappen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr "Antworten auf einer bestimmten E-Mail-Adresse sammeln"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Come here often? Install the app for quick and easy access!"
msgstr ""
"Sind Sie oft hier? Installieren Sie die App für schnellen und einfachen "
"Zugriff!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Durch Komma getrennte E-Mail-Adresse der Empfänger im CC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Durch Komma getrennte Empfänger-IDs der Partner"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Durch Komma getrennte Empfänger-IDs der Partner (Platzhalter können "
"verwendet werden)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Durch Komma getrennte E-Mail-Adresse der Empfänger"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Durch Komma getrennte E-Mail-Adresse der Empfänger (Platzhalter können "
"verwendet werden)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Kommentar"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "Unternehmen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr ""
"Unternehmen, die diese Domain standardmäßig zum Versenden von E-Mails "
"verwenden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "Unternehmen"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Compose Email"
msgstr "E-Mail verfassen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Schreibmodus"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model:ir.ui.menu,name:mail.menu_configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "Konfiguration"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "Konfigurieren Sie Ihre ICE-Serverliste für webRTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Konfigurieren Sie Ihre Aktivitätstypen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "Konfigurieren Sie Ihren eigenen E-Mail-Server"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
msgid "Confirm"
msgstr "Bestätigen"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Confirmation"
msgstr "Bestätigung"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "Bestätigt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Congratulations, you're done with your activities."
msgstr "Herzlichen Glückwunsch, Ihre Aktivitäten sind abgeschlossen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Congratulations, your inbox is empty"
msgstr "Herzlichen Glückwunsch. Ihr Posteingang ist leer."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_patch.js:0
msgid "Congratulations, your inbox is empty!"
msgstr "Herzlichen Glückwunsch. Ihr Posteingang ist leer."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Verbindung fehlgeschlagen (Problem mit dem Postausgangsserver)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Connection test failed: %s"
msgstr "Verbindungstest fehlgeschlagen: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid ""
"Connection to SFU server closed by the server, falling back to peer-to-peer"
msgstr ""
"Die Verbindung zum SFU-Server wurde vom Server getrennt und fällt auf Peer-"
"to-Peer zurück"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection type:"
msgstr "Verbindungstyp:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection:"
msgstr "Verbindung:"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"Verbindungen werden mit SSL/TLS verschlüsselt unter Verwendung bestimmter "
"Ports (Standard: IMAPS=993, POP3S=995)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "Behandelt Antworten als neuen Thread"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr "Kontaktieren Sie Ihren Administrator,"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Kontakte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr "Containermodell"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this user. This includes: creating, joining, pinning"
msgstr ""
"Enthält das Datum und die Uhrzeit des letzten interessanten Ereignisses, das"
" in diesem Kanal für diesen Benutzer stattgefunden hat. Dazu gehören: "
"Erstellen, Beitreten, Anheften"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel. This updates itself when new message posted."
msgstr ""
"Enthält das Datum und die Uhrzeit des letzten interessanten Ereignisses, das"
" in diesem Kanal stattgefunden hat. Aktualisiert sich selbst, wenn eine neue"
" Nachricht veröffentlicht wird."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__unpin_dt
msgid "Contains the date and time when the channel was unpinned by the user."
msgstr ""
"Enthält das Datum und die Uhrzeit, zu der der Kanal vom Benutzer losgeheftet"
" wurde."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Inhalt"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr ""
"Inhalt, der automatisch das von Ihnen gewählte Tastenkürzel ersetzt. Dieser "
"Inhalt kann vor dem Versenden Ihrer Nachricht noch angepasst werden."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__body
msgid "Contents"
msgstr "Inhalte"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "Unterhaltung in eingeklapptem Status"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Copy Link"
msgstr "Link kopieren"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Couldn't get your emails. Check out the error message below for more info:\n"
"%s"
msgstr ""
"Ihre E-Mails konnten nicht empfangen werden. Sehen Sie sich die Fehlermeldung unten an, um mehr zu erfahren:\n"
"%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Zähler der unzustellbaren E-Mails für diesen Kontakt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "Land"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Create"
msgstr "Erstellen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "Aktivität erstellen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "Erstellungsdatum"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Create Group Chat"
msgstr "Gruppenchat erstellen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Create Thread"
msgstr "Thread erstellen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "UId erstellen"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Create a Mail Template"
msgstr "Eine E-Mail-Vorlage erstellen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "Einen neuen Datensatz erstellen"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid "Create a new canned response"
msgstr "Eine neue vorformulierte Antwort erstellen"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr "Einen Aktivitätsplan erstellen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s"
msgstr "%(document)s neu erstellen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "%(document)s erstellen durch E-Mail an %(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
msgid "Create: #"
msgstr "Erstellen: #"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Created"
msgstr "Erstellt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Erstellt von"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/chatter.js:0
msgid "Creating a new record..."
msgstr "Einen neuen Datensatz erstellen ..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Erstellungsdatum"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "Ersteller"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "Anmeldedaten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Währung"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Der aktuelle Benutzer hat eine Markierung zu dieser Nachricht erstellt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Benutzerdefinierte unzustellbare Nachricht"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr "Benutzerdefinierte ICE-Serverliste"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr "Benutzerdefinierte Vorlage"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr "Benutzerdefinierte Vorlagen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "Benutzerdefinierter Kanalname"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "Kunde ist für Posteingang/E-Mail-Benachrichtigung erforderlich"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "Passen Sie das Aussehen der automatisierten E-Mails an"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr "Benutzerdefinierte Benachrichtigungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "DTLS:"
msgstr "DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Data channel:"
msgstr "Datenkanal:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Datum"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr "Datum und Uhrzeit, wann Benachrichtigung gesendet werden soll."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr "Datum/Zeit, wann die Nachricht angeheftet wurde"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Tage"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "Frist"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "Frist:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Deadline: %s"
msgstr "Frist: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Deafen"
msgstr "Stummschalten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Hallo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Dear Sender"
msgstr "Lieber Absender, liebe Absenderin,"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr "Lieber Absender, liebe Absenderin,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Dekorationstyp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
#: model_terms:ir.ui.view,arch_db:mail.mail_message_subtype_view_search
msgid "Default"
msgstr "Standard"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "Standard-Anzeigemodus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr "Standardabsender"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr "Standardabsenderalias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "Standard-Notiz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "Standard-Zusammenfassung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Default Summary:"
msgstr "Standard-Zusammenfassung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Standardbenutzer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "Standardwerte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Default deadline for the activities..."
msgstr "Standardfrist für die Aktivitäten ..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""
"Standardwert, wenn er nicht mit den Filtern des Ausgangsservers "
"übereinstimmt. Kann entweder ein lokaler Teil sein, z.B. "
"„Benachrichtigungen“ oder eine vollständige E-Mail-Adresse, z.B. "
"„<EMAIL>“, um alle ausgehenden E-Mails zu "
"überschreiben."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Standardempfänger"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Standardempfänger des Datensatzes:\n"
"- Partner (durch Verwendung der Felder id oder partner_id) oder\n"
"- E-Mail (durch Verwendung der Felder email_from oder email)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr "Standardbenutzer"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""
"Definiert die Reihenfolge der Verarbeitung, niedrige Nummern sind höherer "
"Priorität"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "Verzögerungsbezeichnung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Verzögerungstyp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Delay after releasing push-to-talk"
msgstr "Verzögerung nach dem Auslösen der Push-To-Talk-Funktion"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Verzögerungseinheiten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Delete"
msgstr "Löschen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "E-Mails löschen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Delete Template"
msgstr "Vorlage löschen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Delete all previews"
msgstr "Alle Vorschauen löschen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
msgid "Delivered"
msgstr "Zugestellt"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Zustellung fehlgeschlagen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Delivery failure"
msgstr "Zustellungsfehler"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr ""
"Veraltete Verwendung von „default_res_id“, sollte „default_res_ids“ "
"verwenden."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Beschreibung"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Beschreibung, die zur versendeten Nachricht hinzugefügt wird. Falls es hier "
"keinen Eintrag gibt, wird anstatt dessen die Bezeichnung genommen."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"Bestimmt, wie der Kanal standardmäßig angezeigt wird, wenn er über den "
"Einladungslink geöffnet wird. Kein Wert bedeutet, dass Text angezeigt wird "
"(keine Stimme/Video)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_editable
msgid "Determines if the canned response can be edited by the current user"
msgstr ""
"Legt fest, ob die vorformulierte Antwort vom aktuellen Benutzer bearbeitet "
"werden kann"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_shared
msgid "Determines if the canned_response is currently shared with other users"
msgstr ""
"Legt fest, ob die canned_response derzeit für andere Benutzer freigegeben "
"ist"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Direct messages"
msgstr "Direktnachricht"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Discard"
msgstr "Verwerfen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "Disconnect"
msgstr "Trennen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Disconnected from the RTC call by the server"
msgstr "Verbindung zum RTC-Call durch den Server unterbrochen"

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "Dialog"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "Dialog-Seitenleiste"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr "Dialog: Stummschaltung des Kanalmitglieds aufheben"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_users_settings_unmute_ir_actions_server
msgid "Discuss: users settings unmute"
msgstr "Dialog: Benutzereinstellungen - Stummschaltung aufheben"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "Diskussionskanal"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Diskussionen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Dismiss"
msgstr "Verwerfen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Anzeige einer Option für zugehörige Dokumente zum Öffnen eines Assistent für"
" die E-Mail-Erstellung mit dieser Vorlage"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
msgid "Display avatar name"
msgstr "Avatar-Name anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
msgid "Do you really want to delete \"%s\"?"
msgstr "Möchten Sie „%s“ wirklich löschen?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Do you really want to delete this preview?"
msgstr "Möchten Sie diese Vorschau wirklich löschen?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Dokument"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Dokument-Follower"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr "Dokument-IDs"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "Dokumentmodell"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Dokumenten Name"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr "Dokument: „"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "Domain"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done"
msgstr "Erledigt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "Erledigt & nächste Aktivität starten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Schedule Next"
msgstr "Erledigt & nächste Aktivität planen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done Date"
msgstr "Datum der Erledigung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Done and Schedule Next"
msgstr "Erledigt und nächste Aktivität planen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Download"
msgstr "Herunterladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Download Files"
msgstr "Dateien herunterladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Download logs"
msgstr "Protokolle herunterladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Download:"
msgstr "Herunterladen:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/mail_attachment_dropzone.xml:0
msgid "Drop Files here"
msgstr "Dateien hier ablegen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "Fälligkeitsdatum"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Fälligkeitsdatum in"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due in"
msgstr "Fällig in"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Due in %s days"
msgstr "In %s Tagen fällig"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due on"
msgstr "Fällig am"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Fälligkeitstyp"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Duplizierte E-Mail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "Dauer der Sprachaktivität in ms"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "Dynamische Berichte"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr "Dynamischer Benutzer (basierend auf Datensatz)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Edge blur intensity"
msgstr "Intensität der Randschärfe"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Edit"
msgstr "Bearbeiten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Partner bearbeiten"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Message"
msgstr "Geplante Nachricht bearbeiten"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Note"
msgstr "Geplante Notiz bearbeiten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "Edit Subscription of %(name)s"
msgstr "Abonnement von %(name)s bearbeiten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Edit subscription"
msgstr "Abonnement bearbeiten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
msgid "Edit: %s"
msgstr "Bearbeiten: %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "E-Mail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "E-Mail-Signatur hinzufügen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "E-Mail-Adresse"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "E-Mail-Alias"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "E-Mail-Aliasse"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "E-Mail-Aliasse-Mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr "E-Mail-Aliasse-Mixin (leicht)"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "Schwarze Liste für E-Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "Farbe der E-Mail-Schaltfläche"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "E-Mail-CC-Verwaltung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "E-Mail-Konfiguration"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr "E-Mail-Domain"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Email Failure: %(modelName)s"
msgstr "E-Mail-Fehler %(modelName)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "Farbe der E-Mail-Kopfzeile"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "E-Mail-Massenmailing"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "Layout der E-Mail-Benachrichtigung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "E-Mail-Vorschau"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "E-Mail-Suche"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "E-Mail-Vorlage"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "Vorschau der E-Mail-Vorlage"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "E-Mail-Vorlagen"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "E-Mail-Thread"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "Ihre E-Mail existiert bereits"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"E-Mail-Adresse des Absenders. Dieses Feld wird gesetzt, wenn kein passender "
"Partner gefunden wird und ersetzt das Feld author_id im Chatter."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""
"E-Mail-Adresse, an die Antworten beim Massenversand von E-Mails "
"weitergeleitet werden sollen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"E-Mail-Adresse, an die Antworten beim Massenversand von E-Mails "
"weitergeleitet werden sollen; wird nur verwendet, wenn die Antwort nicht im "
"ursprünglichen Diskussionsthread protokolliert wird."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr ""
"E-Mail-Adressen, die auf der schwarzen Liste stehen, erhalten keine "
"Nachrichten mehr."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""
"E-Mail-Aliasse %(alias_name)s können nicht für mehrere Datensätze "
"gleichzeitig verwendet werden. Bitte aktualisieren Sie die Datensätze "
"nacheinander."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "E-Mail-CC"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistent für die E-Mail-Erstellung"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "E-Mail-Domain z. B. 'beispiel.com' in '<EMAIL>'"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "E-Mail-Nachricht"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "Assistent zum erneuten Senden von E-Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "E-Mail-Vorlagen"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "E-Mails"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "Emoji"
msgstr "Emoji"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
msgid "Emojis"
msgstr "Emojis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "Nur Mitarbeiter"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Enable"
msgstr "Aktivieren"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "Auftragsverfolgung aktivieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Enter"
msgstr "Enter"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Enter Full Screen"
msgstr "Vollbildmodus aktivieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__danger
msgid "Error"
msgstr "Fehler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Fehlermeldung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
msgid "Error during communication with the publisher warranty server."
msgstr ""
"Es ist ein Fehler bei der Kommunikation mit dem Wartungsserver aufgetreten."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "Fehlermeldung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"Fehler ohne Ausnahme. Wahrscheinlich durch gleichzeitigen Zugriff auf die "
"Aktualisierung der Benachrichtigungsdaten. Bitte wenden Sie sich an einen "
"Administrator."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr ""
"Fehler ohne Ausnahme. Vermutlich sollte ein E-Mail versandt werden, bei der "
"keine Empfänger eingetragen sind."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Fehler, ein Partner kann nicht zweimal dem gleichen Objekt folgen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "Jedem"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Ausnahme"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Exit Fullscreen"
msgstr "Vollbildmodus beenden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Expand"
msgstr "Aufklappen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Expand panel"
msgstr "Panel aufklappen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__expiration_time
msgid "Expiration Token Date"
msgstr "Ablaufdatum des Tokens"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Erweiterte Filter ..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Extra Comments ..."
msgstr "Zusatzkommentare ..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Fehlgeschlagene E-Mails"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Fehlgeschlagen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid "Failed to enable push notifications"
msgstr "Push-Benachrichtigungen konnten nicht aktiviert werden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Failed to load gifs..."
msgstr "GIFs konnten nicht geladen werden ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr ""
"Der SFU-Server konnte nicht geladen werden und fällt auf Peer-to-Peer zurück"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Failed to post the message. Click to retry"
msgstr ""
"Nachricht konnte nicht gesendet werden. Klicken Sie hier, um es erneut zu "
"versuchen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""
"QWeb-Vorlage konnte nicht gerendert werden: %(template_src)s\n"
"\n"
"%(template_traceback)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr ""
"Die Vorlage inline_template konnte nicht gerendert werden: %(template_txt)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render template: %(view_ref)s"
msgstr "Vorlage konnte nicht gerendert werden: %(view_ref)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Fehlerursache"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Fehlergrund"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Fehlerursache. Dies ist üblicherweise eine Meldung des E-Mail-Servers, die "
"gespeichert wird, um die E-Mail-Fehlersuche zu erleichtern."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Fehlertyp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Failure: %(modelName)s"
msgstr "Fehler: %(modelName)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Favorisiert von"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Favorites"
msgstr "Favoriten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Feedback:"
msgstr "Feedback:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "Jetzt abrufen"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr "Rufen Sie eine bestimmte Anzahl GIFs ab."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "Feld"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "Das Feld „E-Mail-Aktivität“ kann nicht auf „Falsch“ geändert werden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr ""
"Das Feld „Schwarze Liste für E-Mails“ kann nicht auf „Falsch“ geändert "
"werden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Das Feld „E-Mail-Thread“ kann nicht in „Falsch“ geändert werden."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "Feld-Details"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Dieses Feld wird verwendet, um die zu verbindenden Modelle bei automatischen"
" Abonnements eines verbundenen Dokuments zu verknüpfen. Es dient zu "
"Berechnung von getattr(related_document.relation_field)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""
"Das Feld „%(field)s“ im Modell „%(model)s“ muss vom Typ Many2one sein und "
"für die Berechnung der Dauer den Wert tracking=True haben."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Felder"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr ""
"Datei, aus der die Vorlage stammt. Verwendet, um beschädigte Vorlagen "
"wiederherzustellen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "File too large"
msgstr "Datei zu groß"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is disabled for external users"
msgstr "Datei-Upload ist für externe Benutzer deaktiviert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is enabled for external users"
msgstr "Datei-Upload ist für externe Benutzer aktiviert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Files"
msgstr "Dateien"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Fold"
msgstr "Einklappen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "Eingeklappt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Follow"
msgstr "Folgen"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "Follower"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Follower-Formular"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Nur von Followern"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr "Hinzuzufügende Follower"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr "Zu entfernende Follower"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Following"
msgstr "Folgt"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""
"Für %(channels)s sollte channel_type „Kanal“ sein, um die gruppenbasierte "
"Autorisierung oder das automatische Gruppenabonnement zu haben."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 1 hour"
msgstr "Für 1 Stunde"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 15 minutes"
msgstr "Für 15 Minuten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 24 hours"
msgstr "Für 24 Stunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 3 hours"
msgstr "Für 3 Stunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 8 hours"
msgstr "Für 8 Stunden"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr "Versand erzwingen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr "Eine Sprache erzwingen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Formatierte Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "Von"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__from_message_id
msgid "From Message"
msgstr "Aus Nachricht"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "From peer:"
msgstr "Von Freund:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Full composer"
msgstr "Erweiterter Editor"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "Video im Vollbildmodus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Future"
msgstr "Zukünftig"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Anstehende Aktivitäten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF"
msgstr "GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Category"
msgstr "GIF-Kategorie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Favorites"
msgstr "GIF-Favoriten"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr "GIF-Favorit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr "GIF-ID von Tenor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "GIFs"
msgstr "GIFs"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Gateway"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Go to conversation"
msgstr "Zur Unterhaltung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr "Integration von Google Translate"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "Gruppe"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "Gruppenname"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr ""
"Gruppenautorisierung und automatische Gruppenabonnements werden nur für "
"Kanäle unterstützt."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Gruppieren nach ..."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_sub_channel_no_group_public_id
msgid ""
"Group public id should not be set on sub-channels as access is based on "
"parent channel"
msgstr ""
"Die öffentliche Gruppen-ID sollte nicht auf Unterkanälen eingestellt werden,"
" da der Zugriff auf dem übergeordneten Kanal basiert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Grouped Chat"
msgstr "Gruppenchat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "Gruppen"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
msgid "Guest"
msgstr "Gast"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name cannot be empty."
msgstr "Gastname kann nicht nicht leer bleiben."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name is too long."
msgstr "Der Name des Gastes ist zu lang."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "Gäste"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Per E-Mail bearbeiten"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "In Odoo bearbeiten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr "Hat Fehler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr "Hat E-Mail-Aktivität"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr "Hat Schwarze Liste für E-Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "Hat E-Mail-Thread"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Hat Erwähnungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "Hat eingehenden Ton deaktiviert"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Hat Fehler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "Hat Bedarfsverantwortlichen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Kopfzeilen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Hallo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Verborgen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr "Verborgene Vorlage"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
msgid "Hide Pinned Messages"
msgstr "Angeheftete Nachrichten ausblenden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Hide all conversations"
msgstr "Alle Unterhaltungen ausblenden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Hide sidebar"
msgstr "Seitenleiste verbergen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Subtyp in den Follower-Optionen ausblenden"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Ausblenden für öffentliche/Portalbenutzer, unabhängig von der Konfiguration "
"des Subtyps."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "Hoch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "History"
msgstr "Historie"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "Servername oder IP-Adresse des Mailservers"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Hover on your message and mark as todo"
msgstr ""
"Bewegen Sie den Mauszeiger über Ihre Nachricht und markieren Sie sie als To-"
"do"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"Wie lange die Audioübertragung nach Überschreiten der Lautstärkeschwelle "
"aktiv bleibt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "ICE-Server"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE connection:"
msgstr "ICE-Verbindung:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE gathering:"
msgstr "ICE-Erfassung:"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "ICE-Server"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr "ICE-Server"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE:"
msgstr "ICE:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_push__id
#: model:ir.model.fields,field_description:mail.field_mail_push_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID des übergeordneten Alias-Datensatz (Beispiel: Projekt welches den Alias "
"für die Erstellung von Aufgaben beinhaltet)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "Status der Sofortnachricht"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "IMAP-Server"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id der verfolgten Ressource"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "Identität"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Idle"
msgstr "Inaktiv"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "Wenn SSL notwendig."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr ""
"Wenn diese Option gesetzt ist, wird jede Änderung an diesem Feld verfolgt. "
"Der Wert wird verwendet, um die Nachverfolgungswerte zu ordnen."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr ""
"Wenn gesetzt, erhält das Mitglied bis zu diesem Datum keine "
"Benachrichtigungen aus dem Kanal."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"Wenn diese Option gesetzt ist, sendet der Warteschlangenmanager die E-Mail "
"nach diesem Datum. Wenn sie nicht gesetzt ist, wird die E-Mail so schnell "
"wie möglich versendet. Wenn keine Zeitzone angegeben ist, wird davon "
"ausgegangen, dass es sich um die UTC-Zeitzone handelt."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""
"Wenn diese Option gesetzt ist, sendet der Warteschlangenmanager die E-Mail "
"nach diesem Datum. Wenn sie nicht gesetzt ist, wird die E-Mail so schnell "
"wie möglich versendet. Sie können dynamische Ausdrücke verwenden."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__mute_until_dt
msgid ""
"If set, the user will not receive notifications from all the channels until "
"this date."
msgstr ""
"Wenn gesetzt, erhält der Benutzer bis zu diesem Datum keine "
"Benachrichtigungen aus allen Kanälen."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Wenn diese Option gesetzt ist, wird dieser Inhalt automatisch anstelle der "
"Standardnachricht an nichtautorisierte Benutzer gesendet."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""
"Wenn gesetzt, wird die Vorlage auf diesen speziellen Benutzer beschränkt."
"                                                  Wenn nicht gesetzt, mit "
"allen Benutzern geteilt."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Wenn die E-Mail-Adresse auf der schwarzen Liste steht, erhält der Kontakt "
"keine Massenmailings mehr."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_scheduled_message__is_note
msgid "If the message will be posted as a Note."
msgstr "Ob die Nachricht als Notiz hinterlassen wird."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"Wenn Wahr, werden die Antworten nicht in den ursprünglichen "
"Diskussionsthread des Dokuments gestellt. Stattdessen wird nach der reply_to"
" in der Verfolgungsnachricht-ID gesucht und entsprechend weitergeleitet. "
"Dies hat Auswirkungen auf die generierte Nachrichten-ID."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Wenn Sie eine Catch-All-E-Mail-Domain eingerichtet haben, geben Sie hier die"
" Domain an."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "Wenn Sie twilio als TURN/STUN-Server-Anbieter verwenden möchten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Alle ignorieren"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "Bild"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Bild 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Bild 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Bild 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Bild 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr "Bild MIME-Type"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "Bild ist ein Link"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""
"Im Kommentarmodus: falls eingestellt, verschieben Sie den Versand von "
"Benachrichtigungen. Im Massenmaiingmodus: falls eingestellt, senden Sie "
"E-Mails nach diesem Datum. Dieses Datum wird als in der UTC-Zeitzone liegend"
" betrachtet."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Inactive Alias"
msgstr "Inaktiver Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "Eingangsmailserver"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
msgid "Inbox"
msgstr "Posteingang"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Call..."
msgstr "Eingehender Anruf ..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr "Eingehende E-Mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "Posteingangsserver"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "Posteingangsserver"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "Posteingangsserver"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Video Call..."
msgstr "Eingehender Videoanruf ..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""
"Zeigt an, dass diese Aktivität automatisch und nicht von einem Benutzer "
"erstellt wurde."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Info"
msgstr "Information"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "Anfangsmodell"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "Vollständige Adresse inline"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Input device"
msgstr "Eingabegerät"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Insert Template"
msgstr "Vorlage einfügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Insert Templates"
msgstr "Vorlagen einfügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Install"
msgstr "Installieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Install Odoo"
msgstr "Odoo installieren"

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "Integrationen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Nur interne"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr "Interne Kommunikation:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_count
msgid "Interval"
msgstr "Intervall"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "Ungültig"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Invalid domain “%(domain)s” (type “%(domain_type)s”)"
msgstr "Ungültige Domain „%(domain)s“ (Typ %(domain_type)s)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Ungültige E-Mail-Adresse"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Invalid email address “%s”"
msgstr "Ungültige E-Mail-Adresse „%s“"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Ungültiger Ausdruck, es muss als literales Python-Dictionary definiert "
"werden, z. B. „{'field': 'value'}“"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr ""
"Ungültiges Feld „%(field_name)s“ beim Erstellen eines Kanals mit "
"Mitgliedern."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "Ungültige Absenderadresse"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Invalid primary email field on model %s"
msgstr "Ungültiges primäres E-Mail-Feld im Modell %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr "Ungültige res_ids %(res_ids_str)s (type %(res_ids_type)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""
"Ungültiger Servername!\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr ""
"Ungültige Vorlage oder Ansichtsquelle %(svalue)s (Typ %(stype)s), sollte ein"
" Datensatz oder eine XMLID sein"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr ""
"Ungültige Vorlage oder Ansichtsquelle Xml-ID %(source_ref)s existiert nicht "
"mehr"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr ""
"Ungültige Vorlagen- oder Ansichtsquellendatensatz %(svalue)s, ist "
"stattdessen %(model)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr ""
"Ungültige Vorlagen- oder Ansichtsquellenreferenz %(svalue)s, ist stattdessen"
" %(model)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"Ungültiger Wert beim Erstellen eines Kanals mit Mitgliedern, nur 4 oder 6 "
"sind erlaubt."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""
"Ungültiger Wert beim Erstellen eines Kanals mit Mitgliedschaften, nur 0 ist "
"erlaubt."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr "Einladungs-URL"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "Einladung, um %(document_model)s zu folgen: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite"
msgstr "Einladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Invite People"
msgstr "Leute einladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Invite a User"
msgstr "Einen Benutzer einladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Invite people"
msgstr "Personen einladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Channel"
msgstr "Zu Kanal einladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Group Chat"
msgstr "Zu Gruppenchat einladen"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Einladungsassistent"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "Ist aktiv"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "Ist aktueller Benutzer oder Gastautor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "Ist Editierbar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "Ist Editor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__is_hidden
msgid "Is Hidden"
msgstr "Ist ausgeblendet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "Ist Mitglied"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Ist gelesen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr "Ist selbst"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr "Ist Vorlagen-Editor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr "Ist ein Protokoll"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__is_note
msgid "Is a note"
msgstr "Ist eine Notiz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "Ist die Dialog-Seitenleistekategorie Kanal offen?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "Ist die Dialog-Seitenleistekategorie Chat offen?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "Ist Mikrofon stummgeschaltet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "Wurde an Oberfläche angeheftet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "Sendet Benutzer-Video"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "Teilt den Bildschirm"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr ""
"Es sieht so aus, als ob Sie versuchen, ein Kanalmitglied zu erstellen, aber "
"anscheinend haben Sie vergessen, den entsprechenden Kanal anzugeben. Um "
"fortzufahren, stellen Sie bitte sicher, dass Sie die erforderlichen "
"Kanalinformationen angeben."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_push_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""
"Bezieht sich auf die von der Benachrichtigung verwendeten Browserschlüssel: \n"
"- p256dh: Dies ist der öffentliche Schlüssel des Abonnements, der vom Browser erzeugt wurde. Der Browser\n"
"          hält den privaten Schlüssel geheim und verwendet ihn zum Entschlüsseln der Nutzdaten.\n"
"- auth: Der Auth-Wert sollte als Geheimnis behandelt werden und nicht außerhalb von Odoo weitergegeben werden."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""
"JSON, das IDs aus einem many2one-Feld den aufgewendeten Sekunden zuordnet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "Beitreten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Join Call"
msgstr "Anruf beitreten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Join Channel"
msgstr "Kanal beitreten"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "Gruppe beitreten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
msgid "Jump"
msgstr "Springen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Jump to Present"
msgstr "Zu heute springen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "Anhänge beibehalten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "Erledigte beibehalten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr "Nachrichtenkopie behalten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "Original behalten"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr ""
"Bewahren Sie eine Kopie des E-Mail-Inhalts auf, wenn E-Mails entfernt werden"
" (nur bei Massenversand)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr ""
"Behalten Sie Aktivitäten, die als erledigt markiert wurden, in der "
"Aktivitätsansicht"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "Schlüssel"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Kind Regards"
msgstr "Freundliche Grüße"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "LIVE"
msgstr "LIVE"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Sprache"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "Letztes Abrufdatum"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "Zuletzt abgerufen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__last_interest_dt
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "Letztes Interesse"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "Zuletzt gesehen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Zuletzt aktualisiert am"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__last_used
msgid "Last Used"
msgstr "Zuletzt verwendet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr "Letztes gesehenes Datum"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__last_used
msgid "Last time this canned_response was used"
msgstr "Letztes Mal, als diese canned_response verwendet wurde"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Late"
msgstr "Verspätet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Verspätete Aktivitäten"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Launch Plans"
msgstr "Pläne ausführen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Layout"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "Verlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Channel"
msgstr "Kanal verlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Conversation"
msgstr "Unterhaltung verlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Leave this channel"
msgstr "Kanal verlassen"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr "Linkvorschauen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Link copied!"
msgstr "Link kopiert!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "List Activity"
msgstr "Listenaktivität"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "List users in the current channel"
msgstr "Benutzer im aktuellen Kanal auflisten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Load More"
msgstr "Mehr laden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Load more"
msgstr "Mehr laden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/navigable_list.xml:0
#: code:addons/mail/static/src/core/public_web/messaging_menu.xml:0
msgid "Loading…"
msgstr "Lädt …"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "Lokaler Server"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Erkennung des Eingangs eines lokalen Elements"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr ""
"Lokalteil einer E-Mail, die für „Antwort an“ verwendet wird, um Antworten zu"
" empfangen, z. B. „Catchall“ in „<EMAIL>“"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""
"Lokalteil der E-Mail, der für den Return-Path verwendet wird, wenn E-Mails "
"unzustellbar sind, z. B. „Unzustellbar“ in „<EMAIL>“."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Log"
msgstr "Hinterlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Log Later"
msgstr "Später hinterlassen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Log Now"
msgstr "Jetzt hinterlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Log RTC events"
msgstr "RTC-Ereignisse protokollieren"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "Notiz hinterlassen …"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "Eine Aktivität erfassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Log an internal note…"
msgstr "Interne Notiz hinterlassen ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Log note"
msgstr "Notiz hinterlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Log step:"
msgstr "Schritt protokollieren:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
msgid "Logged in as %s"
msgstr "Angemeldet als %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "Anmeldeinformation"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "Niedrig"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Lower Hand"
msgstr "Hand herunternehmen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr "MIME-Type"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
msgid "Mail"
msgstr "E-Mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "E-Mail-Aktivität"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "E-Mail-Aktivitätstyp"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Schwarze Liste für E-Mails"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Mixin für schwarze Liste für Mails"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "Formular für E-Mail-Kanal"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "Mixin für Mail-Editor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mail Failures"
msgstr "E-Mail-Fehler"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr "E-Mail-Gateway erlaubt"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "Mail Layout"
msgstr "Maillayout"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr "Verwaltung von Hauptanlagen von Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr "E-Mail-Nachricht-ID Int."

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr "E-Mail-RTC-Sitzung"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mixin für Mail-Rendering"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "Mailserver"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "E-Mail-Vorlage"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "Mail-Vorlagen-Editor"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr "Zurücksetzung einer E-Mail-Vorlage"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "E-Mail-Thread"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Wert für E-Mail-Verfolgung"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""
"E-Mail-Editor im Kommentarmodus sollte auf mindestens einem Datensatz "
"laufen. Keine Datensätze gefunden (Modell %(model_name)s)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Es wurde eine E-Mail für eine existierende E-Mail-Benachrichtigung "
"versendet."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "Mail template model of %(action_name)s does not match action model."
msgstr ""
"E-Mail-Vorlagenmodell %(action_name)s entspricht nicht dem Aktionsmodell."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr "E-Mail-Vorlage, die diesen Mailserver verwendet"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "E-Mail: E-Mail-Manager für Warteschlange"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "Mail: Fetchmail Service"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_post_scheduled_message_ir_actions_server
msgid "Mail: Post scheduled messages"
msgstr "E-Mail: geplante Nachrichten veröffentlichen"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr "Mail: Web-Push-Benachrichtigung versenden"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Mailbox unavailable - %s"
msgstr "Mailbox nicht verfügbar –  %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mailboxes"
msgstr "Postfächer"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr ""
"Mailing oder Posting mit einer Quelle sollte nicht mit einer leeren "
"%(source_type)s sein"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hauptanhang"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""
"Verwalten Sie Antworten als neue eingehende E-Mails, nicht als Antworten, "
"die in denselben Thread gehen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Mark As Read"
msgstr "Als gelesen markieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Mark Done"
msgstr "Als erledigt markieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Mark all read"
msgstr "Alle als gelesen markieren"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Mark as Done"
msgstr "Als erledigt markieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Mark as Read"
msgstr "Als gelesen markieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Todo"
msgstr "Als To-do markieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Unread"
msgstr "Als ungelesen markieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Mark as done"
msgstr "Als erledigt markieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr ""
"Mediengeräte sind nicht verfügbar. SSL ist möglicherweise nicht richtig "
"eingerichtet."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "Medium"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Meeting"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "Anzahl Mitglieder"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "Mitglieder"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Mitglieder dieser Gruppe werden automatisch als Follower hinzugefügt. "
"Beachten Sie, dass diese Ihre Nachrichteneinstellungen auch selbständig "
"bearbeiten können."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Mentions"
msgstr "Erwähnungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
msgid "Mentions Only"
msgstr "Nur Erwähnungen"

#. module: mail
#: model:ir.model,name:mail.model_ir_ui_menu
msgid "Menu"
msgstr "Menü"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Assistent zum Zusammenführen von Partnern"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
msgid "Merged with the following partners: %s"
msgstr "Zusammengeführt mit diesen Partnern: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Message"
msgstr "Nachricht"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message \"%(subChannelName)s\""
msgstr "Nachricht an „%(subChannelName)s“"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message #%(threadName)s…"
msgstr "Nachricht an #%(threadName)s…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message %(thread name)s…"
msgstr "Nachricht %(thread name)s…"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "Nachrichten-ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copied!"
msgstr "Nachrichtenlink kopiert!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr "Kopie des Nachrichtenlinks fehlgeschlagen (Berechtigung verweigert?)!"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Benachrichtigungen über Nachrichten"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "Nachrichtenreaktion"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "Nachrichtenreaktionen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Name des Nachrichtendatensatzes"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr "Nachrichtenübersetzung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "API-Schlüssel der Nachrichtenübersetzung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Mitteilungstyp"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__new_message_separator
msgid "Message id before which the separator should be displayed"
msgstr "Nachrichten-ID, vor der das Trennzeichen angezeigt werden sollte"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message posted on \"%s\""
msgstr "Nachricht veröffentlicht auf „%s“"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Nachrichtenempfänger (E-Mails)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "Nachrichtenreferenzen, wie z. B. Kennungen von vorherigen Nachrichten"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Message should be a valid EmailMessage instance"
msgstr "Nachricht sollte eine gültige EmailMessage-Instanz sein"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Nachrichten-Subtyp ist ein spezieller Nachrichtentyp, vor allem für Systemmeldungen. Zum Beispiel kann es eine Benachrichtigung bezüglich eines neuen Datensatzes (Neu) oder einer geänderten Bearbeitungsphase (Phasenwechsel) geben.\n"
"Nachrichten-Subtypen ermöglichen demnach genau zu bestimmen, welche Meldungen Benutzer auf Ihrer Anzeigetafel sehen wollen."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Nachrichten-Subtyp"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Verfolgter Nachrichten-Subtyp, der auf der Anzeigetafel des Benutzers "
"angezeigt wird."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Nachrichtentyp: E-Mail für E-Mail-Nachricht, Mitteilung für Systemnachricht,"
" Kommentar für andere Mitteilungen wie Benutzerantworten."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Eindeutige Identifikation der Nachricht"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Nachricht-ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr "Nachrichten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Nachrichtensuche"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_from_message_id_unique
msgid "Messages can only be linked to one sub-channel"
msgstr "Nachrichten können nur mit einem Unterkanal verknüpft werden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Messages marked as read will appear in the history."
msgstr "Als gelesen markierte Nachrichten werden in der Historie angezeigt."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Interne Nachrichtensubtypen sind nur für Mitarbeiter sichtbar, d. h. "
"Mitglieder der base_user-Gruppe"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Messages with tracking values cannot be modified"
msgstr "Nachrichten mit Verfolgungswerten können nicht geändert werden"

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr "Metadaten für Audiodateien"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "Fehlende E-Mail"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Fehlende E-Mail-Adresse"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "Fehlt in Adresse"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr ""
"Mixin zum Berechnen der Zeit, die ein Datensatz in jedem Wert eines "
"many2one-Feldes verbracht hat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "Modell"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "Modell hat Änderung"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Modell der verfolgten Ressource"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Modell auf das der Subtyp angewendet wird. Wenn Falsch, wird dieser Subtyp "
"auf alle Modelle angewendet."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modelle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"Eine Änderung des Modells kann sich auf bestehende Aktivitäten auswirken, "
"die diese Aktivitätsart verwenden, seien Sie vorsichtig."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Modul deinstallieren"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Monate"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
msgid "More"
msgstr "Mehr"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Mute"
msgstr "Stumm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Mute Conversation"
msgstr "Unterhaltung stummschalten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute all conversations"
msgstr "Alle Unterhaltungen stummschalten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute duration"
msgstr "Dauer der Stummschaltung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
#: model:ir.model.fields,field_description:mail.field_res_users_settings__mute_until_dt
msgid "Mute notifications until"
msgstr "Benachrichtigungen stummschalten bis"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Muting prevents unread indicators and notifications from appearing."
msgstr ""
"Die Stummschaltung verhindert, dass ungelesene Hinweise und "
"Benachrichtigungen angezeigt werden."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action_my
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "My Activities"
msgstr "Meine Aktivitäten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "Meine Vorlagen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "My canned responses"
msgstr "Meine vorformulierten Antworten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "Name"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Erfordert Aktion"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "New"
msgstr "Neu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Channel"
msgstr "Neuer Kanal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Message"
msgstr "Neue Nachricht "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__new_message_separator
msgid "New Message Separator"
msgstr "Neuer Nachrichtentrenner"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "New Thread"
msgstr "Neues Thread"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Neuer Wert Zeichen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Neuer Wert Datum/Zeit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Neuer Wert Gleitkommazahl"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Neuer Wert Ganzzahl"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Neuer Wert Text"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "New message"
msgstr "Neue Nachricht"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "New messages appear here."
msgstr "Neue Nachrichten finden Sie hier."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "Neue Werte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Nächste Aktivitäten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "Nächste Aktivität"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Next Monday Morning"
msgstr "Nächsten Montagmorgen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Nächste verfügbare Aktivitäten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "No"
msgstr "Nein"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "No Error"
msgstr "Kein Fehler"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "No Followers"
msgstr "Keine Follower"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.js:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "No IM status available"
msgstr "Kein Status für Sofortnachrichten verfügbar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "Kein Datensatz"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr "Keine Aktivitäten."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No channel found"
msgstr "Kein Kanal gefunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "No conversation selected."
msgstr "Keine Unterhaltung ausgewählt."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No conversation yet..."
msgstr "Noch keine Unterhaltung ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No history messages"
msgstr "Kein Nachrichtenverlauf"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "No message_id found in context"
msgstr "Keine message_id im Kontext gefunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
msgid "No messages found"
msgstr "Keine Nachrichten gefunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "No recipient"
msgstr "Kein Empfänger"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "No recipient found."
msgstr "Kein Empfänger gefunden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"Keine Antwort erhalten. Prüfen Sie die Serverinformationen.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr ""
"Kein Verantwortlicher festgelegt für %(activity_type_name)s: "
"%(activity_summary)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "No results found"
msgstr "Keine Treffer gefunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "No saved templates"
msgstr "Keine gespeicherten Vorlagen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No starred messages"
msgstr "Keine mit Sternen versehenen Nachrichten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No thread found."
msgstr "Kein Thread gefunden."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.js:0
msgid "No thread named \"%(thread_name)s\""
msgstr "Kein Thread mit dem Namen „%(thread_name)s“"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "Kein Threading für Antworten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No user found"
msgstr "Keine Benutzer gefunden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "No user found that is not already a member of this channel."
msgstr ""
"Es wurde kein Benutzer gefunden, der nicht bereits Mitglied in diesem Kanal "
"ist."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "No users found"
msgstr "Keine Benutzer gefunden"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
msgid "Non existing record or wrong token."
msgstr "Kein vorhandener Datensatz oder falsches Token."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "None"
msgstr "Keine"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "Normalisierte E-Mail"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "Nicht bestätigt"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr "Nicht getestet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid "Not interested by this?"
msgstr "Nicht interessiert?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "Notiz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__no_notif
msgid "Nothing"
msgstr "Nichts"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Benachrichtigung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "Benachrichtigungs-E-Mail"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Notification Item Image"
msgstr "Bild des Benachrichtigungselements"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr "Benachrichtigungsparameter"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Notification Settings"
msgstr "Benachrichtigungseinstellungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Benachrichtigungstyp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__notification_parameters
msgid "Notification parameters"
msgstr "Benachrichtigungsparameter"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"Benachrichtigung sollte Anlagen als eine Liste von Tupeln erhalten (erhalten"
" %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr ""
"Benachrichtigung sollte Anlageneinträge als eine Liste von IDs erhalten "
"(erhalten %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr ""
"Benachrichtigung sollte Partner erhalten, die als Liste von IDs angegeben "
"sind (erhalten %(pids)s)"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr ""
"Benachrichtigung: Benachrichtigungen, die älter als 6 Monate sind, löschen"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Notify scheduled messages"
msgstr "Benachrichtigung: Über geplante Nachrichten benachrichtigen"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.actions.client,name:mail.discuss_notification_settings_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model:ir.ui.menu,name:mail.menu_notification_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Benachrichtigungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications allowed"
msgstr "Benachrichtigungen erlaubt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications blocked"
msgstr "Benachrichtigungen blockiert"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Notify Recipients"
msgstr "Empfänger benachrichtigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "Notify everyone"
msgstr "Alle benachrichtigen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_count
msgid ""
"Number of days/week/month before executing the action after or before the "
"scheduled plan date."
msgstr ""
"Anzahl der Tage/Woche/Monate vor der Ausführung der Aktion nach oder vor dem"
" festgelegten Plandatum."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Anzahl der Tage/Wochen/Monate vor Durchführung der Aktion. Ermöglicht die "
"Planung der Frist für die Durchführung der Aktion."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will not send notifications on this device."
msgstr "Odoo sendet keine Benachrichtigungen auf dieses Gerät."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will send notifications on this device!"
msgstr "Odoo sendet keine Benachrichtigungen auf dieses Gerät!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr "Aus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Offline"
msgstr "Offline"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Offline - %(offline_count)s"
msgstr "Offline - %(offline_count)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Alter Zeichenwert"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Alter Wert Datum/Zeit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Alter Wert Gleitkommazahl"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Alter Wert Ganzzahl"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Alter Wert Text"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "Alte Werte"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Wenn Sie eine Nachricht mit einem Sternchen versehen haben, können Sie "
"jederzeit hierher zurückkehren und sie ansehen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_indicator.xml:0
msgid "Ongoing call"
msgstr "Laufender Anruf"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Online"
msgstr "Online"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Online - %(online_count)s"
msgstr "Online - %(online_count)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators are allowed to export mail message"
msgstr "Nur Administratoren dürfen E-Mail-Nachrichten exportieren"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators can modify 'model' and 'res_id' fields."
msgstr ""
"Nur Administratoren können die Felder „model“ und „res_id“ bearbeiten."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Only custom models can be modified."
msgstr "Nur das Kundenmodell kann modifiziert werden."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr "Nur interne Benutzer können in Odoo Benachrichtigungen erhalten"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Only members of %(group_name)s group are allowed to edit templates "
"containing sensible placeholders"
msgstr ""
"Nur Mitglieder der Gruppe %(group_name)s dürfen Vorlagen mit sensiblen "
"Platzhaltern bearbeiten"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Only messages type comment can have their content updated"
msgstr ""
"Nur bei Nachrichten vom Typ Kommentar kann der Inhalt aktualisiert werden"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr ""
"Nur bei Nachrichten vom Typ Kommentar kann der Inhalt im Modell "
"„discuss.channel“ aktualisiert werden."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
msgid "Open"
msgstr "Offen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
msgid "Open Actions Menu"
msgstr "Aktionsmenü öffnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
msgid "Open Channel"
msgstr "Kanal öffnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Open Discuss App"
msgstr "Dialog-App öffnen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "Dokument öffnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Open Form View"
msgstr "Formularansicht öffnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Open Link"
msgstr "Link öffnen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr "Besitzer öffnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Open card"
msgstr "Karte öffnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
msgid "Open in Discuss"
msgstr "In Dialog öffnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_view.xml:0
msgid "Open preview in a separate window."
msgstr "Öffnen Sie die Vorschau in einem separaten Fenster."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Operation not supported"
msgstr "Vorgang nicht unterstützt"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "Abgemeldet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Optionale ID eines Threads (Datensatz), dem alle eingehenden Nachrichten "
"zugeordnet werden, auch wenn auf sie nicht geantwortet wird. Wenn gesetzt, "
"verhindert dies die Anlage neuer Datensätze vollständig."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr ""
"Optionale ID für mail_mail. Wird hauptsächlich zur Optimierung der Suche "
"verwendet."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Optional bevorzugter Server für ausgehende E-Mails. Wenn nicht gesetzt, wird"
" der Server mit der höchsten Priorität verwendet."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Optionale Übersetzung (ISO-Code) zur Auswahl beim E-Mail-Versand. Falls es "
"keinen Eintrag gibt, wird die englische Version verwendet. Es sollte sich "
"normalerweise um einen Platzhalterausdruck handeln, der die passende Sprache"
" enthält, z. B. {{ object.partner_id.lang }}."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Options"
msgstr "Optionen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"Ursprüngliche Diskussion: Die Antworten gehen in den Diskussionsthread zum Originaldokument. \n"
"Andere E-Mail-Adresse: Die Antworten gehen an die in der Tracking-ID der Nachricht genannte E-Mail-Adresse und nicht an den ursprünglichen Diskussionsthread zum Dokument. \n"
"Dies hat Auswirkungen auf die generierte Nachrichten-ID."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Original message was deleted"
msgstr "Originalnachricht wurde gelöscht"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Original note:"
msgstr "Ursprüngliche Notiz:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
msgid "Other activities"
msgstr "Andere Aktivitäten"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Ausgehend"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr "Ausgehende E-Mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "Postausgangsserver"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Postausgangsserver"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Ausgehende E-Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Postausgangsserver"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__overdue
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Overdue"
msgstr "Überfällig"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "E-Mail des Verfassers überschreiben"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Overwrite Template"
msgstr "Vorlage überschreiben"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "POP-Server"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "POP/IMAP-Server"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets received:"
msgstr "Datenpakete erhalten:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets sent:"
msgstr "Datenpakte gesendet:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Übergeordnet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__parent_channel_id
msgid "Parent Channel"
msgstr "Übergeordneter Kanal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Übergeordnete Nachricht"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "Übergeordnetes Modell"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Thread-ID des übergeordneten Datensatzes"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__parent_channel_id
msgid "Parent channel"
msgstr "Übergeordneter Kanal"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Das übergeordnete Modell des Alias. Dieses Modell, welches die Alias-"
"Referenz enthält, ist nicht zwangsläufig das Modell, das von alias_model_id "
"(Beispiel: project (parent_model) und task (model)) vorgegeben wird"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Übergeordneter Subtyp, der für automatische Abonnements genutzt wird. Das "
"Feld ist nicht korrekt bezeichnet. Z. B. verweist bei einem Projekt die "
"parent_id von Projekt-Subtypen auf aufgabenbezogene Subtypen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "Participant avatar"
msgstr "Avatar des Teilnehmers"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_push_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "Partner"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "Partner Profile"
msgstr "Partnerprofil"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "Partner schreibgeschützt"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr ""
"Partner mit zusätzlichen Informationen für den erneuten Versand von E-Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "Partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Partner mit Handlungsbedarf"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "Passwort"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr "API-Schlüssel einfügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Pause"
msgstr "Pause"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__payload
msgid "Payload"
msgstr "Nutzdaten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr "Diese Vorlage dauerhaft löschen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Telefon"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "Anruf"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Pick a specific time"
msgstr "Wählen Sie eine genaue Zeit aus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Pick an Activity Plan to launch"
msgstr "Wählen Sie einen Aktivitätsplan für den Start"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Pin"
msgstr "Anheften"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Pin It"
msgstr "Anheften"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr "Angeheftet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
msgid "Pinned Messages"
msgstr "Angeheftete Nachrichten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "Plan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr "Plan verfügbar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date
msgid "Plan Date"
msgstr "Plandatum"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "Planname"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_summary
msgid "Plan Summary"
msgstr "Planzusammenfassung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "Planzusammenfassung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__planned
msgid "Planned"
msgstr "Geplant"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Planned Activities"
msgstr "Geplante Aktivitäten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Geplant in"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "Planung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Play"
msgstr "Abspielen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Please complete customer's information"
msgstr "Bitte vervollständigen Sie die Kundeninformation"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Bitte kontaktieren Sie uns stattdessen unter Verwendung von"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Please wait while the file is uploading."
msgstr "Bitte warten Sie, während die Datei hochgeladen wird."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Umgang mit Chat-Benachrichtigungen:\n"
"- Per E-Mail bearbeiten: Benachrichtigungen werden an Ihre E-Mail-Adresse gesendet\n"
"- In Odoo abwicklen: Benachrichtigungen erscheinen in Ihrem Odoo-Posteingang"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Richtlinie zum Hinterlassen einer Mitteilung im Dokument über das E-Mail-Gateway.\n"
"- Jeder: jeder kann eine Nachricht hinterlassen\n"
"- Partner: nur bestätigte Partner\n"
"- Follower: nur Follower des entsprechenden Dokuments\n"
" oder Mitglieder der verfolgten Kanäle\n"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Pop out Attachments"
msgstr "Pop-out-Anhänge"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "Port"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Granted"
msgstr "Portalzugriff gewährt"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Revoked"
msgstr "Portalzugriff verweigert"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Portal users can only filter threads by themselves as followers."
msgstr "Portalbenutzer können Threads nur als Follower selbst filtern."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "In einem Dokument hinterlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Post your message on the thread"
msgstr "Posten Sie Ihre Nachricht in dem Thread"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"Das Hinterlassen einer Nachricht sollte auf einem Geschäftsdokument "
"erfolgen. Verwenden Sie message_notify, um eine Benachrichtigung an einen "
"Benutzer zu senden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"Das Senden einer Nachricht sollte Anlagen als eine Liste von Tupeln erhalten"
" (erhalten %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr ""
"Das Senden einer Nachricht sollte Anlageneinträge als eine Liste von IDs "
"erhalten (erhalten %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr ""
"Das Senden einer Nachricht sollte Partner als eine Liste von IDs erhalten "
"(erhalten %(pids)s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "Powered by"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Vorangehende Aktivitäten"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "Bevorzugte Antwortadresse"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "Press Enter to start"
msgstr "Zum starten Enter drücken"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr ""
"Drücken Sie eine Taste, um sie als Tastenkürzel für die Push-To-Talk-"
"Funktion zu speichern."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preview"
msgstr "Vorschau"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Preview my camera"
msgstr "Vorschau meiner Kamera"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Vorschau von"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Vorheriger Aktivitätstyp"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "Privatsphäre"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"Verarbeiten Sie jede eingehende E-Mail als Teil einer Unterhaltung, die "
"diesem Dokumenttyp entspricht. Dadurch werden neue Dokumente für neue "
"Unterhaltungen erstellt oder Folge-E-Mails an die bestehenden Unterhaltungen"
" (Dokumente) angehängt."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
msgid "Processing"
msgstr "Verarbeitung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Public Channel"
msgstr "Öffentlicher Kanal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Public Channels"
msgstr "Öffentliche Kanäle"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Garantievertrag des Veröffentlichers"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "Veröffentlicher: Benachrichtigung aktualisieren"

#. module: mail
#: model:ir.model,name:mail.model_mail_push_device
msgid "Push Notification Device"
msgstr "Push-Benachrichtigungsgerät"

#. module: mail
#: model:ir.model,name:mail.model_mail_push
msgid "Push Notifications"
msgstr "Push-Benachrichtigungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push to Talk"
msgstr "Push to Talk"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Push to talk"
msgstr "Push to talk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "Tastenkürzel für Push-To-Talk-Funktion"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push-to-talk key"
msgstr "Push-To-Talk-Taste"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search"
msgstr "Schnellsuche"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search…"
msgstr "Schnellsuche ..."

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "RTC-Sitzung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "RTC Session ID:"
msgstr "RTC-Sitzungs-ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr "RTC-Sitzungen"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "RTC-Sitzungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Raise Hand"
msgstr "Hand heben"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr ""
"Der Bereich liegt zwischen 0,0 und 1,0, die Skala hängt von der Browser-"
"Implementierung ab"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "Reagierender Gast"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "Reagierender Partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "Reaktionen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "Lesedatum"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read Less"
msgstr "Weniger anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read More"
msgstr "Mehr lesen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
msgid "Ready"
msgstr "Bereit"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Bereit zum Senden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/bus_connection_alert.xml:0
msgid "Real-time connection lost..."
msgstr "Echtzeitverbindung getrennt ..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Begründung"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr "Benachrichtigungen in Odoo erhalten"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Empfangen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Recent"
msgstr "Neueste"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Empfänger"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr "Empfängername"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Empfänger"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr "Empfohlene Aktivitäten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Empfohlener Aktivitätstyp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Datensatz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "Name des Datensatzes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Thread-ID des Datensatzes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Referenzen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Refuse"
msgstr "Ablehnen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Freundliche Grüße"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Register new key"
msgstr "Neuen Schlüssel registrieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Reject"
msgstr "Ablehnen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "Zugehöriges Unternehmen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "Zugehörige Dokument-ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr "Zugehörige Dokumenten-IDs"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__res_id
msgid "Related Document Id"
msgstr "Zugehörige Dokument-ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Zugehöriges Dokumentmodell"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Modellname des zugehörigen Dokuments"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "Zugehörige E-Mail-Vorlage"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Zugehörige Nachricht"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Zugehöriger Partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Beziehungsfeld"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
msgid "Remove"
msgstr "Entfernen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Remove Blur"
msgstr "Unschärfe entfernen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr "Kontextaktion entfernen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "Follower entfernen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Remove address from blacklist"
msgstr "Adresse aus schwarzer Liste entfernen"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "Assistent zur Entfernung von E-Mail-Adressen von der schwarzen Liste"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Entfernen Sie die Aktion, um diese Vorlage für bestimmte Dokumente zu "
"verwenden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Remove this follower"
msgstr "Diesen Follower löschen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr "Informationen zu entfernten Feldern"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Rename Thread"
msgstr "Thread umbenennen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "Rendering-Modell"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr ""
"Rendering von %(field_name)s ist nicht möglich, da kein Gegenstück auf "
"Vorlage gibt."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr ""
"Rendering von %(field_name)s ist nicht möglich, da nicht auf Vorlage "
"festgelegt."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Repeat"
msgstr "Wiederholen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "Antworten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Reply"
msgstr "Antworten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Antwort an"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Antwort-E-Mail-Adresse. Durch die Einstellung reply_to wird die automatische"
" Entstehung eines Threads verhindert."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Antwort an"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr "„Antwort an“-Adresse"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Replying to"
msgstr "Antwort an"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Report"
msgstr "Bericht"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "Anfordernder Partner"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "Erneut senden"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr "E-Mail erneut versenden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Assistent zum erneuten Senden"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "Bestätigung zurücksetzen"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr "E-Mail-Vorlage zurücksetzen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr "Vorlage zurücksetzen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr "Ihr Passwort zurücksetzen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "Verantwortlich"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "Rendering von Vorlagen einschränken"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""
"Schränken Sie die Verwendung von E-Mail-Vorlagen und QWEB-Platzhaltern ein."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr "Eingeschränkte Anhänge"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr "Ergebnis der Spracherkennung basierend auf dem Inhalt."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Wiederholen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Revert"
msgstr "Zurückgreifen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "Alle Vorlagen einsehen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "Rich-Text-Inhalte"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Rich-Text/HTML-Nachricht"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr "Klingelnde Sitzung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "Rtc-Sitzung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr "SFU-Server-URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr "SFU-Server-Schlüssel"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr "SFU-Server"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP-Server"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Vertriebsmitarbeiter"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Save"
msgstr "Speichern"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Save as Template"
msgstr "Als Vorlage speichern"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Save editing"
msgstr "Bearbeitung speichern"

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr "Bevorzugtes GIF aus Tenor-API speichern"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Planen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr "Planen & als erledigt markieren"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_model.js:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity"
msgstr "Aktivität planen"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity On Selected Records"
msgstr "Aktivität für ausgewählte Datensätze planen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Message"
msgstr "Nachricht planen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Note"
msgstr "Notiz planen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule activities to help you get things done."
msgstr "Planen Sie Aktivitäten für effizientes Arbeiten."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Schedule activity"
msgstr "Aktivität planen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Schedule an Activity"
msgstr "Eine Aktivität planen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity"
msgstr "Eine Aktivität planen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity on selected records"
msgstr "Eine Aktivität für ausgewählte Datensätze planen"

#. module: mail
#: model:ir.model,name:mail.model_ir_cron
msgid "Scheduled Actions"
msgstr "Geplante Aktionen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Geplantes Datum"

#. module: mail
#: model:ir.model,name:mail.model_mail_scheduled_message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Scheduled Message"
msgstr "Geplante Nachricht"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr "Geplante Nachrichten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Geplantes Sendedatum"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "Skript"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Search"
msgstr "Suchen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "Alias suchen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "Gruppen suchen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "Posteingangsserver suchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
msgid "Search Message"
msgstr "Nachricht suchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Search Messages"
msgstr "Nachrichten suchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Search More..."
msgstr "Mehr suchen ..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "RTC-Sitzung suchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search Sub Channels"
msgstr "Unterkanäle durchsuchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search button"
msgstr "Suchschaltfläche"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search by name"
msgstr "Nach Name suchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search for a GIF"
msgstr "Nach einem GIF suchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a channel..."
msgstr "Nach einem Kanal suchen ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a user..."
msgstr "Nach einem Benutzer suchen ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search in progress"
msgstr "Suche im Gange"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search..."
msgstr "Suchen ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
msgid "Search: %s"
msgstr "Suchen: %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Email Changed"
msgstr "Sicherheitsupdate: E-Mail geändert"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Login Changed"
msgstr "Sicherheitsupdate: Anmeldedaten geändert"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Password Changed"
msgstr "Sicherheitsupdate: Passwort geändert"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr "Fehlerdetails ansehen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "See all pinned messages."
msgstr "Alle angehefteten Nachrichten ansehen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user)s"
msgstr "Gesehen von %(user)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s and %(user2)s"
msgstr "Gesehen von %(user1)s und %(user2)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s and %(user3)s"
msgstr "Gesehen von %(user1)s, %(user2)s und %(user3)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and %(count)s others"
msgstr "Gesehen von %(user1)s, %(user2)s, %(user3)s und %(count)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and 1 other"
msgstr "Gesehen von %(user1)s, %(user2)s, %(user3)s und 1 weiterer"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by everyone"
msgstr "Gesehen von allen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.xml:0
msgid "Seen by:"
msgstr "Gesehen von:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr "Eine Sprache auswählen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Select a user..."
msgstr "Benutzer auswählen..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr "Wählen Sie den Inhaltsfilter, der zum Filtern von GIFs verwendet wird"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Send"
msgstr "Senden"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr "Senden & schließen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "E-Mail versenden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr "E-Mail senden als"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Send Later"
msgstr "Später senden"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Send Mail (%s)"
msgstr "Mail (%s) versenden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Send Now"
msgstr "Jetzt senden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Send a message to followers…"
msgstr "Eine Nachricht an Follower senden ..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr "Senden und empfangen Sie E-Mails mit Ihrem Gmail-Konto."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr "Senden und empfangen Sie E-Mails mit Ihrem Outlook-Konto."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "E-Mail versenden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr "Mailing oder Benachrichtigung direkt senden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Send message"
msgstr "Nachricht senden"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "Absenderadresse"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Absenderadresse (Platzhalter können eingesetzt werden). Wenn nicht gesetzt, "
"wird der E-Mail-Alias des Verfassers standardmäßig verwendet, sofern "
"konfiguriert, ansonsten dessen E-Mail-Adresse."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr "Sendefehler"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Sent"
msgstr "Gesendet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "Server & Login"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Serveraktion"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "Server-Informationen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "Servername"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "Server-Priorität"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Servertyp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr "Infos zum Servertyp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "Server error"
msgstr "Serverfehler"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"Der Server antwortete mit folgender Ausnahme:\n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "Servertyp IMAP."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "Servertyp POP."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr ""
"Setzen Sie Aktiv auf Falsch, um den Kanal auszublenden, ohne ihn zu "
"entfernen."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "Einstellungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr "SFU-Kanal-UUID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr "SFU-Server-URL"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Share Screen"
msgstr "Bildschirm teilen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Shared canned responses"
msgstr "Geteilte vorformulierte Antworten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Shared with all users."
msgstr "Mit allen Benutzern geteilt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__source
msgid "Shortcut"
msgstr "Abkürzung"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr ""
"Verkürzter Sprachcode, der als Ziel für die Übersetzungsanfrage verwendet "
"wird."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Show Followers"
msgstr "Follower anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Show a helper message"
msgstr "Hilfsmeldung anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
msgid "Show activities"
msgstr "Aktivitäten anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.xml:0
msgid "Show all recipients"
msgstr "Alle Empfänger anzeigen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Alle Datensätze mit vor heute geplanten Aktionen anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show less"
msgstr "Weniger anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show more"
msgstr "Mehr anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Show sidebar"
msgstr "Seitenleiste anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Show video participants only"
msgstr "Nur Video-Teilnehmer anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Showing"
msgstr "Anzeige von"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Seitenleistenaktion"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Aktion der Seitenleiste, um diese Vorlage für alle Datensätze des "
"zugehörigen Dokumentmodells verfügbar zu machen."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr "Name der Site"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Snooze 7d"
msgstr "7 T. schlummern"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "So uhh... maybe go favorite some GIFs?"
msgstr "Hmm ... legen Sie eventuell einige GIFs als Favoriten fest?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Herkunft"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr "Quellsprache"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Spezifischer Benutzer"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Spezifizieren Sie ein Modell, wenn die Aktivität für ein Modell spezifisch "
"sein und beim Verwalten von Tätigkeiten für andere Modelle nicht verfügbar "
"sein soll."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
msgid "Starred"
msgstr "Mit Sternchen versehen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr "Mit Sternchen versehene Nachricht"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Call"
msgstr "Einen Anruf beginnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Start a Conversation"
msgstr "Eine Unterhaltung beginnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Video Call"
msgstr "Einen Videoanruf beginnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a conversation"
msgstr "Eine Unterhaltung beginnen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_sidebar_patch.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a meeting"
msgstr "Ein Meeting beginnen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "Status"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Status"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivität ist für heute geplant\n"
"Geplant: zukünftige Aktivitäten."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "Statuszeit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
msgid "Status with time"
msgstr "Status mit Zeit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Stay tuned! Enable push notifications to never miss a message."
msgstr ""
"Bleiben Sie am Ball! Aktivieren Sie Push-Benachrichtigungen, um keine "
"Nachricht zu verpassen."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "Anzahl Schritte"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Stop Recording"
msgstr "Aufzeichnung stoppen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop Sharing Screen"
msgstr "Teilen des Bilschirms beenden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop camera"
msgstr "Kamera anhalten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Stop replying"
msgstr "Nicht mehr antworten"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr "E-Mail und Antworten im Chatter jedes Eintrags speichern"

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr "Link-Vorschaudaten speichern"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"Zeichenkette zur Darstellung eines Schlüssels mit Modifikatoren nach "
"folgendem Muster: shift.ctrl.alt.key, z. B.: truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr "Aus der Übersetzungsanfrage erhaltene Zeichenfolge."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sub_channel_ids
msgid "Sub Channels"
msgstr "Unterkanäle"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Betreff"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "Betreff (Platzhalter können verwendet werden)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Subject:"
msgstr "Betreff:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "Empfänger abonnieren"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__substitution
msgid "Substitution"
msgstr "Ersatz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Subtyp"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Subtypen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "Vorschlagen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "Nächste Aktivität vorschlagen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr ""
"Schlagen Sie diese Aktivitäten vor, sobald die aktuelle Aktivität als "
"erledigt markiert ist."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "Zusammenfassung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "Zusammenfassung:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr "Gmail-Authentifizierung unterstützen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr "Outlook-Authentifizierung unterstützen"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Systemparameter"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
msgid "System notification"
msgstr "Systembenachrichtigung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Tab to select"
msgstr "Zum Auswählen tippen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr "Zielsprache"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "Zielmodell"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "USt-IdNr."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Technical Settings"
msgstr "Technische Einstellungen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"Technisches Feld zur Verfolgung des Modells zu Beginn der Bearbeitung, um "
"Verhalten im Bezug auf das Benutzererlebnis zu unterstützen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "Vorlage"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr "Vorlagenkategorie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template Description"
msgstr "Beschreibung der Vorlage"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr "Dateiname der Vorlage"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_name
msgid "Template Name"
msgstr "Vorlagenname"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Vorlagenvorschau"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "Vorlagenvorschau Sprache"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr "Vorlage zurücksetzen Mixin"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Template creation from composer requires a valid model."
msgstr ""
"Die Erstellung von Vorlagen aus dem Editor erfordert ein gültiges Modell."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering should only be called with a list of IDs. Received "
"“%(res_ids)s” instead."
msgstr ""
"Das Rendering von Vorlagen sollte nur anhand einer Liste von IDs aufgerufen "
"werden; stattdessen „%(res_ids)s“ erhalten."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""
"Das Rendern von Vorlagen unterstützt nur inline_template, qweb oder "
"qweb_view (view oder raw); stattdessen %(engine)s erhalten."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Vorlagen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr "API-Schlüssel von Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr "GIF-API-Schlüssel von Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr "Tenor-GIF-Inhaltsfilter"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr "Tenor-GIF-Beschränkungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr "Tenor-GIF-Beschränkung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr "Tenor-Inhaltsfilter"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "Testen & Bestätigen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr "Testdatensatz:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr "Textinhalte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "Das"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "The 'Due Date In' value can't be negative."
msgstr "Der Wert „Fälligkeitsdatum in“ darf nicht negativ sein."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
msgid ""
"The 'To-Do' activity type is used to create reminders from the top bar menu "
"and the command palette. Consequently, it cannot be archived or deleted."
msgstr ""
"Der Aktivitätstyp „To-do“ wird verwendet, um Erinnerungen aus dem Menü der "
"oberen Leiste und der Befehlspalette zu erstellen. Er kann daher nicht "
"archiviert oder gelöscht werden."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
msgid "The Fullscreen mode was denied by the browser"
msgstr "Der Vollbildmodus wurde vom Browser abgelehnt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_extension_service.js:0
msgid ""
"The Push-to-Talk feature is only accessible within tab focus. To enable the "
"Push-to-Talk functionality outside of this tab, we recommend downloading our"
" %(anchor_start)sextension%(anchor_end)s."
msgstr ""
"Die Push-to-Talk-Funktion ist nur innerhalb dieses Reiter zugänglich. Um die"
" Push-to-Talk-Funktionalität außerhalb des Reiters zu aktivieren, empfehlen "
"wir Ihnen, unsere %(anchor_start)sErweiterung%(anchor_end)s herunterzuladen."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"Die Steueridentifikationsnummer. Die Werte hier werden auf der Grundlage des"
" Länderformats validiert. Sie können „/“ verwenden, um anzuzeigen, dass der "
"Partner nicht steuerpflichtig ist."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The activity cannot be launched:"
msgstr "Die Aktivität kann nicht gestartet werden:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""
"Der Aktivitätstyp „%(activity_type_name)s“ ist nicht mit dem Plan "
"„%(plan_name)s“ kompatibel, da er auf das Modell „%(activity_type_model)s“ "
"beschränkt ist."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "Der Anhang %s existiert nicht oder Sie besitzen keine Zugriffsrechte."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "The attachment %s does not exist."
msgstr "Der Dateianhang %s existiert nicht."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.js:0
msgid "The avatar has been updated!"
msgstr "Der Avatar wurde aktualisiert!"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "Die Channel-UUID muss eindeutig sein"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr "Der Kanaltyp darf nicht leer sein"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "The conversation is empty."
msgstr "Die Unterhaltung ist leer."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr "Der aktuelle Benutzer kann die Vorlage bearbeiten."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "The duration of voice messages is limited to 1 minute."
msgstr "Die Dauer von Sprachnachrichten ist auf 1 Minute begrenzt."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "Die E-Mail gesendet an"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
msgid "The email template(s) have been restored to their original settings."
msgstr ""
"Die E-Mail-Vorlage(n) wurde(n) auf ihre ursprünglichen Einstellungen "
"zurückgesetzt."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_push_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr "Der Endpunkt muss eindeutig sein!"

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""
"Die folgenden E-Mail-Vorlagen konnten nicht zurückgesetzt werden, da die zugehörigen Quelldateien nicht gefunden werden konnten:\n"
"- %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "Der interne Benutzer, der für diesen Kontakt verantwortlich ist."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr ""
"Die letzte unter diesem Alias empfangene Nachricht hat einen Fehler "
"verursacht."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr ""
"Die unten stehende Nachricht konnte nicht akzeptiert werden von der Adresse"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"Die unten stehende Nachricht konnte von der Adresse %(alias_display_name)s nicht akzeptiert werden.\n"
"                 Nur %(contact_description)s ist es gestattet, diese zu kontaktieren.<br /><br />\n"
"                 Bitte stellen Sie sicher, dass Sie die korrekte Adresse verwenden, oder kontaktieren Sie uns stattdessen unter %(default_email)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"Die unten stehende Nachricht konnte von der Adresse %(alias_display_name)s nicht aktzeptiert werden.\n"
"Bitte versuchen Sie es später erneut oder kontaktieren Sie stattdessen %(company_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"The message scheduled on %(model)s(%(id)s) with the following content could "
"not be sent:%(original_message)s"
msgstr ""
"Die Nachricht, die für %(model)s(%(id)s) mit dem folgenden Inhalte geplant "
"wurde, konnte nicht gesendet werden:%(original_message)s"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__from_message_id
msgid "The message the channel was created from."
msgstr "Die Nachricht, aus der der Kanal erstellt wurde."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Das Modell (Odoo-Dokumentart), auf das sich der Alias bezieht. Alle "
"eingehenden E-Mails ohne Bezug zu einer bereits vorhandenen E-Mail führen "
"üblicherweise zur Erstellung eines neuen Datensatz dieses Modells (z. B. "
"Projektaufgabe)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Die Bezeichnung des E-Mail-Alias, z. B. „Jobs“, falls Sie E-Mails für "
"<<EMAIL>> erhalten möchten"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr "Der Plan „%(plan_name)s“ kann nicht gestartet werden:"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" has been started"
msgstr "Der Plan „%(plan_name)s“ wurde gestartet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "Der Warteschlangenmanager sendet die E-Mail nach dem Datum"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The records must belong to the same company."
msgstr "Die Datensätze müssen zum selben Unternehmen gehören."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_message.py:0
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %(type)s, Operation: %(operation)s)\n"
"\n"
"Records: %(records)s, User: %(user)s"
msgstr ""
"Der gewünschte Vorgang kann aufgrund von Sicherheitsbestimmungen nicht ausgeführt werden. Bitte kontaktieren Sie den Systemadministrator.\n"
"\n"
"(Dokumenten Typ: %(type)s, Vorgang: %(operation)s)\n"
"\n"
"Datensätze: %(records)s, Benutzer: %(user)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "The server \"%s\" cannot be used because it is archived."
msgstr "Der Server „%s“ kann nicht verwendet werden, da er archiviert ist."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "The subscription preferences were successfully applied."
msgstr "Die Abonnementeinstellungen wurden erfolgreich übernommen."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr "Die Vorlage gehört zu diesem Benutzer"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr ""
"Der Nur-Text-Anfang des Textes, der als E-Mail-Vorschau verwendet wird."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr "Es kann nur eine RTC-Sitzung pro Kanalmitglied geben"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Diese(r)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action can only be done on a mail thread models"
msgstr ""
"Diese Aktion kann nur bei einem E-Mail-Thread-Modell ausgeführt werden"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action cannot be done on transient models."
msgstr "Diese Aktion kann bei Übergangsmodellen nicht durchgeführt werden."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "This action will send an email."
msgstr "Diese Aktion sendet eine E-Mail."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This channel doesn't have any attachments."
msgstr "Dieser Kanal hat keine Anhänge."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This channel doesn't have any pinned messages."
msgstr "Dieser Kanal hat keine angehefteten Nachrichten."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "This channel has no thread yet."
msgstr "Dieser Kanal hat noch keinen Thread."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This conversation doesn't have any attachments."
msgstr "Diese Unterhaltung hat keine Anhänge."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This conversation doesn't have any pinned messages."
msgstr "Diese Unterhaltung hat keine angehefteten Nachrichten."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Diese E-Mail-Adresse steht auf der schwarzen Liste für Massenmailings. "
"Klicken, um sie von der schwarzen Liste zu entfernen."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Das Feld unterscheidet nicht zwischen Groß- und Kleinschreibung"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr ""
"Dieses Feld wird zur internen Beschreibung der Vorlagenverwendung benutzt."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Dieses Feld wird für die Suche nach der E-Mail-Adresse verwendet, da das "
"primäre E-Mail-Feld mehr als nur eine E-Mail-Adresse enthalten kann."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "This layout seems to no longer exist."
msgstr "Das Layout scheint nicht mehr zu existieren."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message_model.js:0
msgid "This message has already been sent."
msgstr "Diese Nachricht wurde bereits gesendet."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"Mit dieser Option wird jede E-Mail nach dem Versand dauerhaft gelöscht, auch"
" aus dem technischen Menü in den Einstellungen, um Speicherplatz in Ihrer "
"Odoo-Datenbank zu sparen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
msgid "This record has an exception activity."
msgstr "Dieser Datensatz hat eine Ausnahmeaktivität."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid ""
"This setting will be applied to all channels using the default notification "
"settings."
msgstr ""
"Diese Einstellung wird auf alle Kanäle, die die "
"Standardbenachrichtigungseinstellen verwenden, angezeigt."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__channel_notifications
msgid ""
"This setting will only be applied to channels. Mentions only if not "
"specified."
msgstr ""
"Diese Einstellung wird nur auf Kanäle angewendet. Erwähnungen nur, wenn "
"nicht angegeben."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr ""
"Diese Werte werden beim Rendern nicht als Optionen unterstützt: "
"%(param_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr ""
"Diese Werte werden beim Posten oder Benachrichtigen unterstützt: "
"%(param_names)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Thread"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread Image"
msgstr "Thread-Bild"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread has unread messages"
msgstr "Der Thread enthält ungelesene Nachrichten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Thread image"
msgstr "Thread-Bild"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr "Thread-aktiviert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/thread_actions.js:0
msgid "Threads"
msgstr "Threads"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_tz
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Zeitzone"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "Titel"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "To"
msgstr "An"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "An (E-Mails)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "An (Partner)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
msgid "To :"
msgstr "An:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "To peer:"
msgstr "An Freund:"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr "To-do"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "To:"
msgstr "An:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__today
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Today"
msgstr "Heute"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Heutige Aktivitäten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Today at %(time)s"
msgstr "Heute um %(time)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Today:"
msgstr "Heute:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Tomorrow"
msgstr "Morgen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Afternoon"
msgstr "Morgen Nachmittag"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Morning"
msgstr "Morgen Vormittag"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Tomorrow:"
msgstr "Morgen:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "In dieser Gruppe besprochene Themen ..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr "Empfänger verfolgen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Nachverfolgte Werte werden in einem separaten Modell gespeichert. Mithilfe "
"dieses Felds kann die Nachverfolgung rekonstruiert und eine Statistik zum "
"Modell generiert werden."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "Nachverfolgung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Nachverfolgungswert"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Nachverfolgungswerte"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Nachverfolgungswerte"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Translate"
msgstr "Übersetzen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr "Übersetzungstext"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Translation Failure"
msgstr "Übersetzungsfehler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_from
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Auslöser"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "Nächste Aktivität auslösen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr "Erneut versuchen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Turn camera on"
msgstr "Kamera einschalten"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Turn on notifications"
msgstr "Benachrichtigungen aktivieren"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Auth-Token für Twilio-Konto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "Twilio-Konto-SID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Typ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Art der Verzögerung"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Art der Serveraktion. Die folgenden Werte sind verfügbar:\n"
"- „Einen Datensatz aktualieren“: Aktualisieren Sie die Werte eines Datensatzes.\n"
"- „Aktivität erstellen“: Erstellen Sie eine Aktivität (Dialog)\n"
"- „E-Mail versenden“: Hinterlassen Sie eine Nachricht, oder Notiz oder senden Sie eine E-Mail (Dialog)\n"
"- „SMS versenden“: Senden Sie eine SMS, protokollieren Sie diese in Dokumenten (SMS), log them on documents (SMS)\n"
"- „Follower hinzufügen/entfernen“: Fügen Sie einem Datensatz Follower hinzu oder entfernen Sie sie (Dialog)\n"
"- „Datensatz erstellen“: Erstellen Sie einen neuen Datensatz mit neuen Werten\n"
"- „Code ausführen“: ein Block Python-Code, der ausgeführt wird\n"
"- „Webhook-Benachrichtigung versenden“: Senden Sie eine Anfrage zur VERÖFFENTLICHUNG an ein externes System, auch bekannt als Webhook\n"
"- „Vorhandene Aktionen ausführen“: Legen Sie eine Aktion fest, die mehrere andere Serveraktionen auslöst\n"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Type the name of a person"
msgstr "Tippen Sie den Namen einer Person"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid "Unable to connect to SMTP Server"
msgstr "Verbindung mit SMTP-Server nicht möglich"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Die Nachricht kann nicht gepostet werden, bitte konfigurieren Sie die "
"E-Mail-Adresse des Absenders."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Unable to send message, please configure the sender's email address."
msgstr ""
"Die Nachricht kann nicht gesendet werden, bitte konfigurieren Sie die "
"E-Mail-Adresse des Absenders."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign"
msgstr "Zuweisung aufheben"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign from me"
msgstr "Mir nicht mehr zuweisen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "Von schwarzer Liste entfernen"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
msgid "Unblock Reason: %(reason)s"
msgstr "Entblockierungsgrund: %(reason)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Undeafen"
msgstr "Stummschaltung aufheben"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Unfollow"
msgstr "Nicht mehr folgen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Unit"
msgstr "Einheit"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Einheit der Verzögerung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
msgid "Unknown"
msgstr "Unbekannt"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
msgid "Unknown error"
msgstr "Unbekannter Fehler"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Unknown error: %(error)s"
msgstr "Unbekannter Fehler: %(error)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Unmute"
msgstr "Stummschaltung aufheben"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Unmute Conversation"
msgstr "Stummschaltung der Unterhaltung aufheben"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Unpin"
msgstr "Losheften"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Conversation"
msgstr "Unterhaltung loslösen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Unpin Message"
msgstr "Nachricht losheften"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Thread"
msgstr "Thread losheften"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__unpin_dt
msgid "Unpin date"
msgstr "Datum losheften"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Anzahl ungelesener Nachrichten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Ungelesene Nachrichten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr "Uneingeschränkte Anhänge"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Unselect person"
msgstr "Person abwählen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Unstar all"
msgstr "Alle Markierungen aufheben"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Unsupported report type %s found."
msgstr "Nicht unterstützter Berichtstyp %s gefunden."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until %s"
msgstr "Bis %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until I turn it back on"
msgstr "Bis ich es wieder einschalte"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Untitled"
msgstr "Ohne Titel"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr "E-Mail-Layout aktualisieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Update Template"
msgstr "Vorlage aktualisieren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Upload Avatar"
msgstr "Avatar hochladen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "Dokument hochladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload File"
msgstr "Datei hochladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload file"
msgstr "Datei hochladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Upload:"
msgstr "Hochladen:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploaded"
msgstr "Hochgeladen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploading"
msgstr "Lädt hoch"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""
"Verwenden Sie einen „Bestimmten Benutzer“, um bei der nächsten Aktivität "
"immer denselben Benutzer zugewiesen  zu bekommen. Verwenden Sie  "
"„Dynamischer Benutzer“, um den Feldnamen des Benutzers anzugeben, der im "
"Datensatz ausgewählt werden soll."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "Benutzerdefinierte E-Mail-Server verwenden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Use Default"
msgstr "Standard verwenden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "Twilio-ICE-Server verwenden"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "Einen Gmail-Server verwenden"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Use a local script to fetch your emails and create new records."
msgstr ""
"Verwenden Sie ein lokales Skript, um Ihre E-Mails abzurufen und neue "
"Datensätze zu erstellen."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr "Einen Outlook-Server verwenden"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid ""
"Use default from user settings if not specified. This setting will only be "
"applied to channels."
msgstr ""
"Verwenden Sie den Standardwert aus den Benutzereinstellungen, falls nicht "
"angegeben. Diese Einstellung wird nur auf Kanäle angewendet."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr "Verwenden Sie unterschiedliche Domains für Ihre E-Mail-Aliasse"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr "In Stapeln verwenden"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Use message_notify to send a notification to an user."
msgstr ""
"message_notify verwenden, um eine Benachrichtigung an Benutzer zu senden."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Vorlage verwenden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "Verwenden Sie die Push-to-talk-Funktion"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "Verwendet in"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr "Wird als Kontext verwendet, um die Verfasserdomain zu bewerten"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Wird verwendet, um den Nachrichtengenerator zu kategorisieren\n"
"„email“: generiert durch eine eingehende E-Mail, z. B. von mailgateway\n"
"„comment“: generiert durch Benutzereingaben, z. B. über Dialog oder Editor\n"
"„email_outgoing“: generiert durch ein Mailing\n"
"„notification“: vom System generiert, z. B. durch Verfolgungsnachrichten\n"
"„auto_comment“: generiert durch einen automatischen Benachrichtigungsmechanismus, z. B. eine Bestätigung\n"
"„user_notification“: für einen bestimmten Empfänger generiert"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "Dient zur Anzeige der Währung bei der Verfolgung von monetären Werten"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Wird zur Sortierung der Subtypen genutzt"

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Benutzer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr "Benutzerfeld"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Benutzer-Anwesenheit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "Benutzereinstellungen"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "Benutzereinstellungen"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "Benutzereinstellungen Volumen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "Benutzerspezifische Benachrichtigung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "Benutzertyp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is a bot"
msgstr "Benutzer ist ein Bot"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is idle"
msgstr "Benutzer ist inaktiv"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is offline"
msgstr "Benutzer ist offline"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is online"
msgstr "Benutzer ist online"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr "Benutzer sollte keine duplizierten GIFs in den Favoriten haben"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Benutzername"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Users in this channel: %(members)s."
msgstr "Benutzer in diesem Kanal: %(members)s."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"Die Benutzer werden weiterhin in der Lage sein, Vorlagen zu rendern.\n"
"Allerdings können nur Mail-Vorlagen-Editoren neue dynamische Vorlagen erstellen oder bestehende Vorlagen ändern."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"Bei der Community- und Enterprise-Version ist es erforderlich, zum Senden "
"und Empfangen von E-Mails Ihren eigenen E-Mail-Server zu verwenden. Online-"
"Benutzer können bereits von einem einsatzbereiten E-Mail-Server "
"(@meinunternehmen.odoo.com) profitieren."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "Gültig"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"Wert %(allowed_domains)s für `mail.catchall.domain.allowed` kann nicht validiert werden.\n"
"Es sollte eine durch Kommata getrennte Liste von Domains sein, z. B. beispiel.com,beispiel.org."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Video Settings"
msgstr "Videoeinstellungen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Video player:"
msgstr "Video-Player:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
msgid "View"
msgstr "Ansicht"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "View %s"
msgstr "%s ansehen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "View Profile"
msgstr "Profil anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "View Reactions"
msgstr "Reaktionen ansehen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
msgid "View Thread"
msgstr "Therad anzeigen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Ansichtstyp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "View all activities"
msgstr "Alle Aktivitäten anzeigen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "View or join channels"
msgstr "Kanal ansehen oder beitreten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "Stimme"

#. module: mail
#: model:ir.ui.menu,name:mail.menu_call_settings
msgid "Voice & Video"
msgstr "Sprach- & Videoanruf"

#. module: mail
#: model:ir.actions.client,name:mail.discuss_call_settings_action
msgid "Voice & Video Settings"
msgstr "Einstellungen für Sprach- & Videoanrufe"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice Detection"
msgstr "Spracherkennung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Voice Message"
msgstr "Sprachnachricht"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice detection threshold"
msgstr "Stimmerkennungsgrenze"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "Voice recording stopped"
msgstr "Sprachnachrichtaufzeichnung gestoppt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice settings"
msgstr "Spracheinstellungen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Volumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "Volumen pro Partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "Volumen anderer Partner"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr ""
"Möchten Sie Ihre Unterhaltungen mit GIFs aufpeppen? Aktivieren Sie die "
"Funktion in den Einstellungen!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "Warning"
msgstr "Warnung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""
"Wir konnten den Alias %(alias_name)s nicht erstellen, da die Domain "
"%(alias_domain_name)s zum Unternehmen %(alias_company_names)s gehört, "
"während das Besitzerdokument zum Unternehmen %(company_name)s gehört."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""
"Wir konnten den Alias %(alias_name)s nicht erstellen, da die Domain "
"%(alias_domain_name)s zum Unternehmen %(alias_company_names)s gehört, "
"während das Zieldokument zum Unternehmen %(company_name)s gehört."

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "We were not able to fetch value of field '%(field)s'"
msgstr "Wir konnten den Wert des Feldes „%(field)s“ nicht abrufen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Wochen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr "Herzlich willkommen bei MeinUnternehmen!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr ""
"Tja, nichts hält ewig, aber sind Sie sicher, dass Sie diese Nachricht "
"losheften möchten?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "What's your name?"
msgstr "Wie lautet Ihr Name?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__user_tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""
"Beim Drucken von Dokumenten und beim Exportieren/Importieren von Daten werden die Zeitwerte entsprechend dieser Zeitzone berechnet.\n"
"Ist die Zeitzone nicht eingestellt, wird UTC (Coordinated Universal Time) verwendet.\n"
"An allen anderen Stellen werden die Zeitwerte entsprechend dem Zeitversatz Ihres Web-Clients berechnet."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr ""
"Wenn Sie die Zuweisung „Standardbenutzer“ auswählen, müssen Sie einen "
"Verantwortlichen festlegen."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"Soll eine vollständige Originalkopie jeder E-Mail als Referenz aufbewahrt "
"und an jede verarbeitete Nachricht angehängt werden? Dies verdoppelt "
"normalerweise die Größe Ihrer Nachrichten-Datenbank."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"Ob Anhänge heruntergeladen werden sollen. Wenn nicht werden die eingehenden "
"Mails ohne Anhänge weiterverarbeitet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr "Ob alle Empfänger oder nur die wichtigen angezeigt werden sollen."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Write /field to insert dynamic content"
msgstr "Schreiben Sie /Feld, um dynamische Inhalte einzufügen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Write Feedback"
msgstr "Feedback schreiben"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Write your message here..."
msgstr "Verfassen Sie hier Ihre Nachricht ..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Wrong operation name (%s)"
msgstr "Falscher Vorgangsname (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "TT.MM.JJJJ HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yeah, pin it!"
msgstr "Yeah, anheften!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Yes"
msgstr "Ja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yes, remove it please"
msgstr "Ja, bitte entfernen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Yesterday"
msgstr "Gestern"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Yesterday at %(time)s"
msgstr "Gestern um %(time)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Yesterday:"
msgstr "Gestern:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"Sie sind im Begriff, diese Gruppenunterhaltung zu verlassen und haben keinen"
" Zugang mehr zu ihr, es sei denn, Sie werden erneut eingeladen. Sind Sie "
"sicher, dass Sie fortfahren möchten?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are alone in a private conversation."
msgstr "Sie sind alleine in einer privaten Unterhaltung."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are alone in this channel."
msgstr "Sie sind allein in diesem Kanal."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in a private conversation with %(member_names)s."
msgstr ""
"Sie befinden sich in einer privaten Unterhaltung mit %(member_names)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr ""
"Sie befinden sich im Kanal %(bold_start)s#%(channel_name)s%(bold_end)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "You are no longer following \"%(thread_name)s\"."
msgstr "Sie folgen „%(thread_name)s“ nicht mehr."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"You are not allowed to change the target record of a scheduled message."
msgstr "Sie dürfen den Zieldatensatz einer geplanten Nachricht nicht ändern."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "You are not allowed to send this scheduled message"
msgstr "Sie sind nicht berechtigt, diese geplante Nachricht zu versenden"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload an attachment here."
msgstr "Sie sind nicht berechtigt, hier einen Anhang hochzuladen."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload attachments on this channel."
msgstr "Sie sind nicht berechtigt, in diesem Kanal einen Anhang hochzuladen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr ""
"Sie sind der Administrator dieses Kanals. Sind Sie sicher, dass Sie ihn "
"verlassen möchten?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Sie können beliebig Nachrichten mit Sternchen versehen, um sie in diesem "
"Postfach anzeigen zu lassen."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "You can not write on %(field_name)s."
msgstr "Sie können in %(field_name)s nicht schreiben."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with existing users."
msgstr "Sie können nur mit bestehenden Benutzern chatten."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with partners that have a dedicated user."
msgstr ""
"Sie können nur mit Partnern chatten, die einen eigenen Benutzer haben."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr "Sie können diese Nachricht getrost ignorieren"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Sie können diese Gruppen nicht einfach löschen, da die Gruppe für das "
"Gesamtunternehmen in anderen Modulen benötigt wird."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr ""
"Sie können in der Alias-Adresse %(alias_name)s nur lateinische Zeichen ohne "
"Akzent verwenden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr ""
"Sie können in der Domaindresse %(domain_name)s nur lateinische Zeichen ohne "
"Akzent verwenden."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr "Sie haben keinen Zugriff auf"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"Sie haben keine Zugriffsrechte, um E-Mails von der schwarzen Liste zu "
"entfernen. Bitte wenden Sie sich an Ihren Administrator."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "You have been assigned to %s"
msgstr "Sie wurden %s zugewiesen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Sie wurden zugewiesen an"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_core_public_web_service.js:0
msgid "You have been invited to #%s"
msgstr "Sie wurden eingeladen zu #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Sie können Anhänge zu dieser Vorlage hinzufügen, die mit allen E-Mails, "
"basierend auf dieser Vorlage, versandt werden."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "You may not define a template on an abstract model: %s"
msgstr "Sie dürfen keine Vorlage für ein abstraktes Modell definieren: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr ""
"Sind Sie sicher, dass Sie diese Nachricht für immer und ewig an "
"%(conversation)s anheften möchten?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned %(conversation_name)s"
msgstr "%(conversation_name)s ist nicht länger angeheftet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned your conversation with %(user_name)s"
msgstr "Ihre Unterhaltung mit %(user_name)s ist nicht länger angeheftet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unsubscribed from %s."
msgstr "Sie wurden von %s abgemeldet."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a chat!"
msgstr "Sie wurden zu einem Chat eingeladen!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a meeting!"
msgstr "Sie wurden zu einem Meeting eingeladen!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "You:"
msgstr "Sie: "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "Ihr(e)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr "Ihre Konto-E-Mail wurde von %(old_email)s auf %(new_email)s geändert."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account login has been updated"
msgstr "Ihre Konto-Anmeldedaten wurden aktualisiert"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account password has been updated"
msgstr "Ihr Kontopasswort wurde aktualisiert"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your browser does not support videoconference"
msgstr "Videokonferenz wird von Ihrem Browser nicht unterstützt."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support voice activation"
msgstr "Sprachaktivierung wird von Ihrem Browser nicht unterstützt."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support webRTC."
msgstr "WebRTC wird von Ihrem Browser nicht unterstützt."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Your inbox is empty"
msgstr "Ihr Posteingang ist leer"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your name"
msgstr "Ihr Name"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
msgid "[%(name)s] (no email address)"
msgstr "[%(name)s] (keine E-Mail-Adresse)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "addresses linked to registered partners"
msgstr "Adressen, die mit registrierten Partnern verknüpft sind"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "nach Abschlussdatum"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "nach vorheriger Aktivitätsfrist"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "alias"
msgstr "Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "alias %(name)s: %(error)s"
msgstr "Alias %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr "Anhänge dieser E-Mail."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "available bitrate:"
msgstr "verfügbare Bitrate:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "back"
msgstr "zurück"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""
", da Sie sie\n"
"                in den letzten Minuten zu oft kontaktiert haben.\n"
"                <br/>\n"
"                Bitte versuchen Sie es später erneut."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "von"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "camera"
msgstr "Kamera"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"kann nicht verarbeitet werden. Diese Adresse\n"
"    wird verwendet, um Antworten zu sammeln und sollte nicht zur direkten Kontaktaufnahme verwendet werden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "channels"
msgstr "Kanäle"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "clock rate:"
msgstr "Schaltfrequenz:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "codec:"
msgstr "Codec:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "created this channel."
msgstr "hat diesen Kanal erstellt."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__days
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "Tage"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days overdue:"
msgstr "Tage überfällig:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days:"
msgstr "Tage:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "deaf"
msgstr "taub"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__mail_push_device_id
msgid "devices"
msgstr "Geräte"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "document"
msgstr "Dokumente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "erledigt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down DTLS:"
msgstr "DTLS runter:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down ICE:"
msgstr "ICE runter:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "e.g \"Asked to receive our next newsletters\""
msgstr "z. B. „Möchte unsere nächsten Newsletter erhalten“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr "z. B. „Vorschlag besprechen“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr "z. B. „Das Angebot durchgehen und Einzelheiten besprechen“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr "z. B. „Begrüßungs-E-Mail“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr ""
"z. B. „Willkommen bei MeinUnternehmen“ oder „Schön, Sie kennenzulernen, {{ "
"object.name }}“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr "z. B. „Unzustellbar“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr "z. B. „Catchall“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "z. B. „meinunternehmen.com“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr "z. B. „Benachrichtigungen“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "z. B. 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "z. B. ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr "z. B. Kontakt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Discuss Proposal"
msgstr "z. B. Vorschlag besprechen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "z. B. Angebotsbesprechung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
msgid "e.g. Log a note"
msgstr "z. B. Eine Notiz erfassen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "z. B. Einführung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "z. B. Ein Meeting planen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. mycompany.com"
msgstr "z. B. meinunternehmen.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "z. B. Support"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "z. B. true.true..f"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr "z. B. user_id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr "z. B.: „<EMAIL>“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g: Send order confirmation"
msgstr "z. B. Auftragsbestätigung senden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "for"
msgstr "für"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "wurde generiert aus:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "wurde geändert von:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr "hat Ihnen gerade die folgende Aktivität zugewiesen:"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr "https://developers.google.com/tenor/guides/content-filtering"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "in a few seconds"
msgstr "in einigen Sekunden"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias"
msgstr "falsch konfigurierter Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias (unknown reference record)"
msgstr "nicht richtig konfigurierter Alias (unbekannter Referenzdatensatz)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "invited %s to the channel"
msgstr "hat %s zum Kanal eingeladen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "joined the channel"
msgstr "ist dem Kanal beigetreten"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "left the channel"
msgstr "hat den Kanal verlassen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list"
msgstr "Liste"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list-item"
msgstr "list-item"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "live"
msgstr "live"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "media player Error"
msgstr "Media-Player-Fehler"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "microphone"
msgstr "Mikrofon"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "model %s does not accept document creation"
msgstr "Modell %s erlaubt keine Dokumenterstellung"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__months
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "Monate"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "more"
msgstr "mehr"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "ms"
msgstr "ms"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "muted"
msgstr "stummgeschaltet"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "new"
msgstr "neu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "no connection"
msgstr "keine Verbindung"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "now"
msgstr "jetzt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "on"
msgstr "auf"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "on:"
msgstr "am:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
msgid "or"
msgstr "oder"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "or press %(send_keybind)s"
msgstr "oder %(send_keybind)s drücken"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "other members."
msgstr "andere Mitglieder."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
msgid "props.action.title"
msgstr "props.action.title"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "raising hand"
msgstr "Hand heben"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"Antwort auf fehlendes Dokument (%(model)s, %(thread)s), Rückgriff auf "
"Dokumentenerstellung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"Antwort auf Modell %s, das die Dokumentaktualisierung nicht akzeptiert, "
"Rückgriff auf Dokumentenerstellung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to followers"
msgstr "beschränkt auf Follower"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to known authors"
msgstr "beschränkt auf unbekannte Autoren"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "results out of"
msgstr "Ergebnisse von"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "screen"
msgstr "Bildschirm"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "some specific addresses"
msgstr "einige spezifische Adressen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "Stun:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "target model unspecified"
msgstr "Zielmodell nicht spezifiziert"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "Team."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "template"
msgstr "Vorlage"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "Turn:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown error"
msgstr "unbekannter Fehler"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown target model %s"
msgstr "unbekanntes Zielmodell %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up DTLS:"
msgstr "DTLS hoch:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up ICE:"
msgstr "ICE hoch:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "users"
msgstr "Benutzer"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "view"
msgstr "ansehen"

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr "Websocket-Nachrichtbearbeitung"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__weeks
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "Wochen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "you"
msgstr "Ihnen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "your alias"
msgstr "Ihr Alias"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr "„%(member_name)s“ in „%(channel_name)s“"
