# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                허용 목록에 주소 추가\n"
"            </p><p>\n"
"               Odoo에서는 사용자가 스팸이나 회신 연락을 받지 않도록 보호하기 위해\n"
"                 임계값 <b>%(threshold)i</b>을 초과하여  <b>%(minutes)i</b>분마다 게이트웨이로 수신되는 이메일을\n"
"                자동으로 차단합니다. 매우 자주 받아야 하는 이메일이 있는 경우에는\n"
"                아래에 주소를 추가하시면 Odoo에서 해당 이메일 수신이 가능합니다.\n"
"            </p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr "\" 더 이상 팔로우하지 않음"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr "\"%(activity_name)s: %(summary)s\" 항목이 나에게 배정"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr "\"%(hostname)s\" 님이 귀하의 마이크에 액세스해야 합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "\"%(hostname)s\" requires microphone access"
msgstr "\"%(hostname)s\"에 마이크 액세스를 허용해야 합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr "%(activity)s,  %(name)s에게 할당, %(deadline)s에 마감"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "%(author name)s from %(channel name)s"
msgstr "%(channel name)s 에서 %(author name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr "%(email)s 항목은 유효한 이메일이 아닙니다. 새로운 고객을 생성하는데 필요한 항목입니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person)s"
msgstr "%(emoji)s이 %(person)s에게 반응했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s and %(person2)s"
msgstr "%(emoji)s이 %(person1)s과 %(person2)s에게 반응했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and %(count)s "
"others"
msgstr ""
"%(emoji)s이 %(person1)s, %(person2)s, %(person3)s 및 %(count)s의 다른 사람에게 "
"반응했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and 1 other"
msgstr "%(emoji)s이 %(person1)s, %(person2)s, %(person3)s 및 기타 1명에게 반응했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s, %(person2)s, and %(person3)s"
msgstr "%(emoji)s이 %(person1)s, %(person2)s에게 반응했습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "%(model_name)s.%(field_path)s does not seem to be a valid field path"
msgstr "%(model_name)s.%(field_path)s  항목은 유효한 필드 경로가 아닙니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%(name)s: %(message)s)"
msgstr "%(name)s: %(message)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(new_line)s%(new_line)sType %(bold_start)s@username%(bold_end)s to mention "
"someone, and grab their attention.%(new_line)sType "
"%(bold_start)s#channel%(bold_end)s to mention a channel.%(new_line)sType "
"%(bold_start)s/command%(bold_end)s to execute a command.%(new_line)sType "
"%(bold_start)s:shortcut%(bold_end)s to insert a canned response in your "
"message."
msgstr ""
"%(new_line)s%(new_line)s%(bold_start)s@username%(bold_end)s을 입력하여 누군가를 태그하고 "
"관심을 끌 수 있습니다. %(new_line)s%(bold_start)s#channel%(bold_end)s을 입력하여 채널을 "
"멘션합니다.%(new_line)s%(bold_start)s/command%(bold_end)s를 입력하여 명령을 실행할 수 있습니다. "
"%(new_line)s미리 준비된 응답을 메시지에 삽입하려면 %(bold_start)s:바로가기%(bold_end)s를 입력합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr "%(open_button)s%(icon)s%(open_em)s편집 취소%(close_em)s%(close_button)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sESC%(close_samp)s %(open_em)s를 눌러 "
"%(open_cancel)s취소하거나%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"엔터%(close_samp)s %(open_em)s버튼을 눌러 "
"%(open_save)s저장합니다.%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sESC%(close_samp)s %(open_em)s를 눌러 "
"%(open_cancel)s취소하거나%(close_cancel)s%(close_em)s, "
"%(open_samp)s엔터%(close_samp)s %(open_em)s버튼을 눌러 "
"%(open_save)s저장합니다.%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.js:0
msgid "%(recipientCount)s more"
msgstr "%(recipientCount)s 더 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr "%(user)s 사용자 접속 완료. 첫 접속이며, 앞으로 유용하게 사용하시기를 바랍니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(user)s started a thread: %(goto)s%(thread_name)s%(goto_end)s. "
"%(goto_all)sSee all threads%(goto_all_end)s."
msgstr ""
"%(user)s가 스레드를 시작했습니다: %(goto)s%(thread_name)s%(goto_end)s. %(goto_all)s모든 "
"스레드 보기%(goto_all_end)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s and %(user2)s are typing..."
msgstr "%(user1)s님과 %(user2)s님이 입력 중입니다..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s, %(user2)s and more are typing..."
msgstr "%(user1)s, %(user2)s님 등이 입력 중입니다..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "%(user_name)s pinned a message to this channel."
msgstr "%(user_name)s 님이 메시지를 채널에 고정했습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
msgid "%s (Email Template)"
msgstr "%s (이메일 서식)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan.py:0
#: code:addons/mail/models/mail_template.py:0
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "%s created"
msgstr "%s 항목이 생성되었습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "%s days overdue"
msgstr "%s 일 기한 초과"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%s is typing..."
msgstr "%s 입력중..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_result.js:0
msgid "%s messages found"
msgstr "%s 메시지를 찾을 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "%s new messages"
msgstr "%s 새 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s raised their hand"
msgstr "%s님들이 손을 들었습니다"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "%s started a live conference"
msgstr "%s 님이 실시간 회의를 시작했습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"camera\" access"
msgstr "%s\"에 \"카메라\" 접근 권한이 필요합니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"screen recording\" access"
msgstr "%s\" 에 \"화면 녹화\" 접근 권한이 필요합니다"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_attachment_links
msgid "&amp;#128229;"
msgstr "&amp;#128229;"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translated from: %(language)s)"
msgstr "(번역 출발 언어: %(language)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translation Failure: %(error)s)"
msgstr "(번역 실패: %(error)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "(edited)"
msgstr "(편집됨)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "(from"
msgstr "(보낸 사람"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(원래 할당"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid ") added you as a follower of this"
msgstr ") 님을 팔로워로 추가했습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid ". Narrow your search to see more choices."
msgstr ". 검색 범위를 좁히시면 더 많은 선택 항목을 확인하실 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "1 new message"
msgstr "1 새 메시지"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr "<b invisible=\"not no_record\" class=\"text-warning\">이 모델에 대한 기록이 없습니다.</b>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid ""
"<button>Change your preferences</button> to receive new notifications in "
"your inbox."
msgstr "받은 편지함에서 새 알림을 받으려면 <button>환경설정</button>을 변경하세요."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-check\"/> Done"
msgstr "<i class=\"fa fa-check\"/> 완료"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_kanban
msgid ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"Steps count\" "
"title=\"Steps count\"/>"
msgstr "<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"단계 수\" title=\"단계 수\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-times\"/> Cancel"
msgstr "<i class=\"fa fa-times\"/> 취소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"직접 메시지를 사용하여 <p><b>동료와 실시간으로 채팅</b> 할 수 있습니다. </p><p><i>먼저 설정 앱에서 사용자를 초대해야 "
"할 수도 있습니다.</i></p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p>여기 채널의 참여자에게<b>메시지를 쓰세요.</b></p> <p><i>'@'</i>를 사용하여 다른 사용자에게 알리거나 "
"<i>'#'</i>를 사용하여 다른 채널을 연결할 수 있습니다. 가능한 명령 목록을 보려면 <i>'/'</i>를 사용하여 메시지를 "
"시작하십시오.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>채널을 통해 서로 다른 주제와 그룹에서 정보를 쉽게 구성할 수 있습니다.</p> <p><b>첫 번째 채널을 만드십시오</b>(예 :"
" 영업, 마케팅, 제품 XYZ, 업무용 파티 등).</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a channel here.</p>"
msgstr "<p>여기서 채널을 만듭니다.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>공개 또는 비공개 채널을 만듭니다.</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">버튼 색상</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">머리글 색상</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr "<span class=\"me-1 oe_inline\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr "<span class=\"me-1\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr "<span class=\"o_stat_text\">문서 열기</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr "<span class=\"o_stat_text\">상위 문서 열기</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                메시지는 템플릿을 수신하는 대상에게 이메일 형태로 전송되며\n"
"                                메시지 이력에는 표시되지 않습니다.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                메시지는 내부 사용자가 볼 수 있도록 메모로 게시되어\n"
"                                메시지 이력에 표시됩니다.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                메시지는 레코드에 메시지로 게시되어서 팔로워에게 알림이 전달되며\n"
"                                해당 메시지는 메시지 이력에 표시됩니다.\n"
"                            </span>"

#. module: mail
#: model_terms:web_tour.tour,rainbow_man_message:mail.discuss_channel_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>잘하셨습니다!</b>  이번 여정의 모든 단계를 마쳤습니다.</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr "<span>회원님이 로그인을 시도하셨다면:</span><br/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr "<span>회원님이 로그인을 시도하신 것이 아니라면:</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr "<span>기록 열기</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr "<span>지금 바로 비밀번호를 변경해 주세요.</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "이 페이지를 <strong>저장</strong>하고 여기로 돌아와 기능을 설정합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr "<strong>더 이상 해당 문서를 팔로우하지 않습니다:</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "이 별칭에 대한 새 레코드를 만들 때 기본값을 제공하도록 평가되는 Python 사전입니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A Scheduled Message cannot be scheduled in the past"
msgstr "지난 시간에는 예약된 메시지를 설정할 수 없습니다."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "Bus Presence 에는 반드시 사용자나 게스트가 있어야 합니다."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "협력사나 게스트만 채널 구성원이 될 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "'채팅' 채널에서는 사용자 수가 두 명을 넘을 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "채팅 사용자 수는 두 명을 넘을 수 없습니다. 대신 그룹을 생성하세요."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "A message can only be scheduled in monocomment mode"
msgstr "단일 쪽지 모드에서만 메시지를 예약할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"A message cannot be scheduled on a model that does not have a mail thread."
msgstr "메일 스레드가 없는 모델에 대해서는 메시지를 예약할 수 없습니다."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "메시지 응답은 반드시 협력사나 게스트가 해야만 합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "A next activity can only be planned on models that use activities."
msgstr "활동을 사용하는 모델에서만 다음 활동을 계획할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A scheduled message could not be sent"
msgstr "예약된 메시지를 보낼 수 없습니다"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""
"메시지 번역을 활성화시키려면 유효한 Google API 키가 있어야 합니다.  "
"https://cloud.google.com/translate/docs/setup"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "볼륨 설정을 하려면 반드시 협력사나 게스트가 있어야 합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_chatgpt.js:0
msgid "AI"
msgstr "AI"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept"
msgstr "수락"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept with camera"
msgstr "카메라로 승인하기"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Access Denied"
msgstr "접근이 거부되었습니다"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "사용 가능 그룹"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "사용 권한 토큰"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr "\"%(groupFullName)s\" 그룹에 접근 제한"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Account"
msgstr "계정"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "추가 작업"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "작업 윈도우 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Actions"
msgstr "활동"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr "캘린더 화면 열기와 같은 특정한 작업이 활동에서 진행되도록 하거나 문서 업로드가 되면 자동 완료로 표시되게 합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "받는 메일에 대해 수행할 행동"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "가입시 기본적으로 활성화 됩니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "활성화"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "도메인 활성"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities_section
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activities"
msgstr "활동"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Create"
msgstr "생성할 작업"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr "활동은 null이 아니라 res_id 값을 가진 레코드로 연결되어야 합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity"
msgstr "활동"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "활동 혼합"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.ui.menu,name:mail.menu_mail_activities
msgid "Activity Overview"
msgstr "작업 현황"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr "활동 계획"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "활동 계획"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "활동 설정"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "활동 유형"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Activity Type Name"
msgstr "작업 유형 이름"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "활동 유형"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "활동 계획 서식"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Prospect Follow-up\", \"Project Milestone Meeting\", ...)"
msgstr ""
"작업 계획은 몇 번의 클릭만으로 작업 목록을 빠르게 할당하는 데 활용됩니다.\n"
"                    (예: “온보딩”, “잠재 고객 후속 조치”, “프로젝트 마일스톤 회의”, ...)"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "활동 계획 마법사"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Activity type"
msgstr "업무 유형"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Activity: %s"
msgstr "활동: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr "컨텍스트 활동 추가"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "이메일 수신 거부 추가"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add Followers"
msgstr "팔로워 추가하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.xml:0
msgid "Add Reaction"
msgstr "반응 추가"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Add a Reaction"
msgstr "리액션 추가"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr "GIF 지원을 활성화하려면 Tenor GIF API 키를 추가하세요."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""
"GIF 지원을 활성화하려면 Tenor GIF API 키를 추가하세요. "
"https://developers.google.com/tenor/guides/quickstart#setup"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Add a description"
msgstr "설명 추가"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr "활동에 설명을 추가합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "새 %(document)s를 추가하거나 %(email_link)s에 이메일 보내기"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "블랙리스트에 이메일 주소 추가"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add and close"
msgstr "추가 및 닫기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Add as recipient and follower (reason: %s)"
msgstr "수신자 및 팔로워로 추가 (사유: %s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts"
msgstr "연락처 추가"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Add contacts to notify..."
msgstr "알림을 보낼 연락처 추가"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
msgid "Add followers to this document"
msgstr "이 문서에 팔로워 추가하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Add or join a channel"
msgstr "채널에 추가 또는 가입"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr "서명 추가"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "ICE 서버용 twilio 자격증명 추가"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr "채널에 팔로워를 추가할 수 없습니다. 대신 구성원을 추가하세요."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr "해당 채팅에는 멤버를 추가할 수 없습니다. 2인 채팅용입니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "추가 연락처"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "고급"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "고급 옵션"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__after_plan_date
msgid "After Plan Date"
msgstr "계획일 이후"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__warning
msgid "Alert"
msgstr "주의"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "별칭"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""
"%(matching_name)s (%(current_id)s) 별칭은 이미 %(alias_model_name)s "
"(%(matching_id)s)에 연결되어 있으며 %(parent_name)s %(parent_model_name)s 항목에서 사용 "
"중입니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""
"%(matching_name)s (%(current_id)s) 별칭은 이미 %(alias_model_name)s "
"(%(matching_id)s)에 연결되어 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "별칭 연락처 보안"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "별칭 도메인"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "별칭 도메인 이름"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr "별칭 도메인"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "이메일 별칭"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "별칭 이름"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "별칭 상태"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr "별칭 도메인 이름"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr "최근 받은메일에서 확인한 별칭 상태입니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "별칭 모델"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "별칭"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr "%(alias_names)s 별칭은 이미 바운스 또는 캐치올 주소로 사용 중이빈다. 다른 별칭을 선택하세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "All"
msgstr "전체"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__all
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__all
msgid "All Messages"
msgstr "모든 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "All conversations have been muted"
msgstr "모든 대화가 음소거되었습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "All partners must belong to the same message"
msgstr "모든 파트너는 동일한 메시지에 속해 있어야 합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr "일반 업로드 허용"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"SSL 예외 사항이 발생하였습니다. 서버 포트에서 SSL/TLS 구성 내용을 확인하십시오.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "An access token must be provided for each attachment."
msgstr "각 첨부 파일에 액세스 토큰이 제공되어야 합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "An email is required for find_or_create to work"
msgstr "find_or_create가 작동하려면 이메일이 필요합니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email"
msgstr "이메일 발송 중 오류가 발생했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email on “%(record_name)s”"
msgstr " “%(record_name)s ”에 관한 이메일을 보내는 중 오류가 발생했습니다. "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "An error occurred while fetching messages."
msgstr "메시지를 가져오는 중 오류가 발생했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "An unexpected error occurred during the creation of the chat."
msgstr "채팅을 생성하는 동안 예상하지 못한 오류가 발생했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And"
msgstr "및"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And 1 other member."
msgstr "및 다른 멤버 1인"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "적용 대상"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
msgid "Apply"
msgstr "적용"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "보관됨"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr "%(user_name)s (# %(user_id)s) 사용자가 포털 계정을 삭제하였으므로 보관 처리되었습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Are you sure you want to cancel the scheduled message?"
msgstr "예약된 메시지를 취소하시겠습니까?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are you sure you want to delete \"%(template_name)s\"?"
msgstr " \"%(template_name)s\"를 삭제하시겠습니까?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr "이 메일 서식을 정말로 삭제하시겠습니까?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Are you sure you want to delete this message?"
msgstr "이 메시지를 삭제하시겠습니까?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr "이 메일 서식을 원래의 구성으로 재설정하시겠습니까? 변경 사항 및 번역한 내용이 삭제됩니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "이 메일 주소를 차단 해제하시겠습니까?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Are you sure you want to unblacklist this email address?"
msgstr "이 이메일 주소의 차단을 해제하시겠습니까?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are your sure you want to update \"%(template_name)s\"?"
msgstr " \"%(template_name)s\"를 업데이트하시겠습니까?"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr "출시 시 질문"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to ..."
msgstr "다음 사람에게 배정하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to me"
msgstr "자신에게 할당"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Assigned To"
msgstr "담당자"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
msgid "Assigned to"
msgstr "담당자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "배정"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "At this point lang should be correctly set"
msgstr "여기에서 알맞게 언어 설정을 해야 합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Attach files"
msgstr "파일 첨부"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "첨부 파일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Attachment counter loading..."
msgstr "첨부파일 카운터 로딩 중"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Attachments"
msgstr "첨부 파일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Audio player:"
msgstr "오디오 플레이어:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "인증된 협력사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "작성자"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"메시지 작성자입니다. 설정하지 않으면 이메일의 보낸 사람 항목에 해당 협력사가 아닌 다른 이메일 주소가 선택될 수 있습니다. "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "작성자 아바타"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "그룹 권한"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__group_ids
msgid "Authorized Groups"
msgstr "인증된 그룹"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "자동 삭제"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "자동 가입 그룹"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "자동 구독"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "자동 구독"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr "대상에게 자동 알림"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "자동화된 활동"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Automated message"
msgstr "자동 메시지"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr "현재 활동을 완료로 표시하면, 이 활동이 자동으로 예약됩니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Available for all Companies"
msgstr "모든 회사에서 사용 가능"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
msgid "Avatar"
msgstr "아바타"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "아바타 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "아바타 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "아바타 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "아바타 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_cache_key
msgid "Avatar Cache Key"
msgstr "아바타 캐시 키"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Avatar of user"
msgstr "사용자 아바타"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Away"
msgstr "자리 비움"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Background blur intensity"
msgstr "배경 흐림도"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "기준액"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr "기본 서식"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr "기본 서식"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr "Base64 인코딩 키"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr "일괄 구성"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr "두 개 이상의 문서를 첨부하거나 조회해야 하는 경우에는 일괄 기록이 지원되지 않습니다. "

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__before_plan_date
msgid "Before Plan Date"
msgstr "계획일 이전"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "수신 거부"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "수신 거부일"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "수신 거부 주소"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "수신 거부 이메일 주소"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"%(user_name)s (#%(user_id)s) 사용자가 포털 계정 %(portal_user_name)s 삭제로 인해 차단됨"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Blur Background"
msgstr "흐린 배경"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Blur video background"
msgstr "동영상 배경 흐리게 처리"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "본문"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr "본문 내용은 서식과 동일합니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Bot"
msgstr "봇"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "반송"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr "반송 별칭"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr "반송 이메일"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""
"%(bounce)s 반송 별칭은 같은 이름으로 이미 다른 도메인에서 사용 중입니다. 다른 반송 항목을 사용하시거나 다른 별칭 도메인을 "
"사용하세요."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr "반송 이메일은 고유해야 합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""
"'%(matching_alias_name)s' 반송/캐치올은 이미%(document_name)s에서 사용 중입니다. 다른 별칭을 "
"선택하거나 다른 문서에서 변경하세요."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""
"'%(matching_alias_name)s' 반송/캐치올이 이미 사용 중입니다. 다른 별칭을 선택하거나 다른 문서에서 변경하세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
msgid "Bounced"
msgstr "반송됨"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr "Brave: 푸시 알림을 활성화하려면 '푸시 메시징용 Google 서비스'를 사용 설정하세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Browser default"
msgstr "브라우저 기본값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__endpoint
msgid "Browser endpoint"
msgstr "브라우저 엔드포인트"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__keys
msgid "Browser keys"
msgstr "브라우저 키"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
msgid "CC Email"
msgstr "참조 이메일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "CTRL"
msgstr "CTRL"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "전화"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Call Settings"
msgstr "통화 설정"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Camera is off"
msgstr "카메라가 꺼져 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr "취소 가능"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "본문 편집 가능"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr "재발송 가능"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "쓸 수 있음"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Can not update the message or recipient of a notification."
msgstr "메시지 또는 통지 수신인을 업데이트할 수 없습니다."

#. module: mail
#: model:ir.model,name:mail.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "bus.bus를 통해 메시지 전송 가능"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Cancel"
msgstr "취소"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "이메일 취소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Cancel Message"
msgstr "메시지 취소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
msgid "Cancelled"
msgstr "취소됨"

#. module: mail
#: model:ir.model,name:mail.model_mail_canned_response
msgid "Canned Response"
msgstr "미리 준비된 답변"

#. module: mail
#: model:res.groups,name:mail.group_mail_canned_response_admin
msgid "Canned Response Administrator"
msgstr "미리 준비된 응답 관리자"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_canned_response_action
#: model:ir.module.category,name:mail.module_category_canned_response
#: model:ir.ui.menu,name:mail.menu_canned_responses
msgid "Canned Responses"
msgstr "미리 준비된 답변"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Canned Responses Search"
msgstr "미리 준비된 답변 검색"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_form
msgid "Canned response"
msgstr "미리 준비된 응답"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__source
msgid ""
"Canned response that will automatically be substituted with longer content "
"in your messages. Type ':' followed by the name of your shortcut (e.g. "
":hello) to use in your messages."
msgstr ""
"미리 준비된 응답은 메시지에서 더 긴 내용으로 자동 대체될 수 있습니다. ':'를 입력한 다음 단축키 이름(예: :hello)을 입력하면"
" 메시지를 사용할 수 있습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_tree
msgid "Canned responses"
msgstr "미리 준비된 답변"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                    your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                    replaced directly in your message, so that you can still edit\n"
"                    it before sending."
msgstr ""
"미리 준비된 응답을 사용하면\n"
"                    <i>:shortcut</i>를 입력하여 미리 작성된 내용을 메시지에 삽입할 수 있습니다.\n"
"                    바로 가기는 메시지에서 직접 대체되므로 보내기 전에\n"
"                    수정할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change initial message nor parent channel of: %(channels)s."
msgstr "다음 초기 메시지 또는 상위 채널을 변경할 수 없습니다: %(channels)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr "다음 채널 유형을 변경할 수 없습니다: %(channel_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: initial message should belong to parent channel."
msgstr "%(channels)s을 만들 수 없습니다: 초기 메시지는 상위 채널에 속해야 합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: parent should not be a sub-channel and should be"
" of type 'channel'."
msgstr "%(channels)s을 만들 수 없습니다: 부모는 하위 채널이 아니어야 하며 '채널' 유형이어야 합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "참조 메시지를 받는 사람"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "참조 수신인"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "참조 대상자 (사전 표시 문자를 여기에 사용할 수 있습니다)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "일반"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr "캐치올 별칭"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "캣치올 이메일"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""
"%(catchall)s 캐치올 별칭은 같은 이름으로 이미 다른 도메인에서 사용 중입니다. 다른 캐치올 항목을 사용하시거나 다른 별칭 "
"도메인을 사용하세요."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr "캐치올 이메일은 고유해야 합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "참조"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "연결 방식"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "이 유형과 관련된 활동의 배경색을 변경합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Channel"
msgstr "채널"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "채널 회원"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
#: model:ir.model.fields,field_description:mail.field_res_users_settings__channel_notifications
msgid "Channel Notifications"
msgstr "채널 알림"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "채널 유형"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Channel full"
msgstr "채널 최대 인원 초과"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "Channel members cannot include public users."
msgstr "일반 사용자는 채널 구성원이 될 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Channel settings"
msgstr "채널 설정"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
msgid "Channels"
msgstr "채널"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr "채널/구성원"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
msgid "Chat"
msgstr "채팅"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Chat Options"
msgstr "채팅 옵션"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"채팅은 두 사람 사이에 비공개로 고유하게 진행됩니다. 그룹은 초대받은 사람들간에 비공개로 진행됩니다. 채널은 자유롭게 가입할 수 있습니다"
" (설정에 따라 다름)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Chats"
msgstr "채팅"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr "제외 목록 확인"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "하위 메시지"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "서식을 선택하세요."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr "사용자를 선택하세요."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Choose another value or change it on the other document."
msgstr "다른 값을 선택하거나 다른 문서에서 변경하세요."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr "배정이 필요한 활동에 대해 배정 항목을 선택하세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Clear quick search"
msgstr "빠른 검색 지우기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Click here to retry"
msgstr "다시 시도하려면 여기를 클릭하세요"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Click to see the attachments"
msgstr "첨부 파일을 확인하려면 여기를 클릭하세요"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Close"
msgstr "닫기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "Close Chat Bubble"
msgstr "말풍선 닫기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Chat Window (ESC)"
msgstr "채팅창 닫기 (ESC)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Search"
msgstr "검색창 닫기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Close all conversations"
msgstr "모든 대화 닫기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_ad_banner.xml:0
msgid "Close banner"
msgstr "배너 닫기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Close button"
msgstr "닫기 버튼"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
msgid "Close panel"
msgstr "창 닫기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
msgid "Close search"
msgstr "검색창 닫기"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "마감됨"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Collapse panel"
msgstr "패널 축소"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr "특정 이메일 주소에서 답신 수집"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Come here often? Install the app for quick and easy access!"
msgstr "자주 방문하시나요? 앱을 설치하면 더 빠르고 쉽게 액세스할 수 있습니다!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "쉼표로 구분한 참조 대상자 주소"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "쉼표로 수신인의 id 구분"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr "쉼표로 구분된 수신 협력사 ID (사전 표시 문자를 여기에 사용할 수 있습니다)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "쉼표로 수신인의 이메일 주소 구분"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "쉼표로 구분된 수신자 주소 (사전 표시 문자를 여기에 사용할 수 있습니다)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "댓글"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "회사"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr "이 도메인을 메일 발송에 기본값으로 사용하는 회사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "회사"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Compose Email"
msgstr "이메일 작성"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "작성 모드"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "환경설정"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model:ir.ui.menu,name:mail.menu_configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "설정"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "webRTC용 ICE 서버 목록을 구성합니다"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "활동 유형 환경 설정"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "고객님만의 이메일 서버를 구성합니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
msgid "Confirm"
msgstr "승인"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Confirmation"
msgstr "확인"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "확인됨"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Congratulations, you're done with your activities."
msgstr "축하합니다, 활동을 완료하셨습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Congratulations, your inbox is empty"
msgstr "축하합니다, 받은편지함이 비어 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_patch.js:0
msgid "Congratulations, your inbox is empty!"
msgstr "축하합니다, 받은편지함이 비어 있습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "연결 실패 (발신 메일 서버 문제 발생)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Connection test failed: %s"
msgstr "연결 테스트에 실패했습니다 : %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid ""
"Connection to SFU server closed by the server, falling back to peer-to-peer"
msgstr "SFU 서버 연결이 서버 측에서 해제되어 P2P 모드로 전환되었습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection type:"
msgstr "연결 유형:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection:"
msgstr "연결:"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr "연결은 전용 포트 (기본값 : IMAPS = 993, POP3S = 995)를 통해 SSL/TLS로 암호화됩니다"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "답변을 새로운 스레드로 처리"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr "관리자에게 문의하여 관련 내용을 확인해 주세요."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "연락처"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr "컨테이너 모델"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this user. This includes: creating, joining, pinning"
msgstr ""
"이 사용자에 대해 이 채널에서 가장 최근에 발생한 주목할 만한 이벤트의 날짜와 시간을 포함합니다. 여기에는 만들기, 참여하기, 고정하기 "
"등의 활동이 포함됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel. This updates itself when new message posted."
msgstr ""
"이 채널에서 가장 최근에 발생한 주목할 만한 이벤트의 날짜와 시간을 포함합니다. 새 메시지가 게시될 때마다 자동으로 업데이트됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__unpin_dt
msgid "Contains the date and time when the channel was unpinned by the user."
msgstr "사용자가 채널의 고정을 해제한 날짜와 시간을 포함합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "내용"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr "선택한 바로가기를 자동으로 대체할 콘텐츠입니다. 메시지를 보내기 전까지 계속 콘텐츠를 조정할 수 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__body
msgid "Contents"
msgstr "콘텐츠"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "대화 접기 상태"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Copy Link"
msgstr "링크 복사"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Couldn't get your emails. Check out the error message below for more info:\n"
"%s"
msgstr ""
"이메일을 수신할 수 없습니다. 자세한 내용은 아래 오류 메시지를 확인하세요:\n"
"%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "이 연락처로 반송된  이메일 수 계산"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "국가"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Create"
msgstr "작성"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "활동 생성"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "작성일자"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Create Group Chat"
msgstr "그룹 채팅 생성"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Create Thread"
msgstr "스레드 만들기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "Uid 만들기"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Create a Mail Template"
msgstr "메일 템플릿 만들기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "새 레코드 만들기"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid "Create a new canned response"
msgstr "새로운 미리 준비된 응답 만들기"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr "작업 계획 만들기"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s"
msgstr "새로운 %(document)s 생성"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "%(email_link)s로 이메일을 전송하여 새로운 %(document)s 생성"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
msgid "Create: #"
msgstr "작성: #"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Created"
msgstr "작성"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "작성자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "작성자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "작성일자"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/chatter.js:0
msgid "Creating a new record..."
msgstr "새로운 레코드를 생성했습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "작성일자"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "작성자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "자격 증명"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "통화"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "현재 사용자에게 이 메시지와 연결된 별표가 표시된 알림이 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "고객 반송 메시지"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr "사용자 지정 ICE 서버 목록"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr "맞춤형 서식"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr "맞춤형 서식"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "맞춤 채널 이름"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "받은 편지함 / 이메일 알림을 받으려면 고객이 필요합니다"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "자동 생성 이메일의 형식과 분위기를 설정할 수 있습니다"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr "사용자 지정 알림"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "DTLS:"
msgstr "DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Data channel:"
msgstr "데이터 채널:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "날짜"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr "알림을 보내야 할 날짜와 시간입니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr "메시지가 핀고정되어 있는 날짜와 시간입니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "일"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "마감 시한"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "마감일 :"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Deadline: %s"
msgstr "마감일: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Deafen"
msgstr "청력 상실"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "안녕하세요,"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Dear Sender"
msgstr "안녕하세요"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr "안녕하세요,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "장식 유형"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
#: model_terms:ir.ui.view,arch_db:mail.mail_message_subtype_view_search
msgid "Default"
msgstr "기본"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "기본 화면 모드"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr "보낸 사람 기본값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr "보내는 별칭 기본값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "기본 메모"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "기본 요약"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Default Summary:"
msgstr "기본 요약:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "기본 사용자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "기본값"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Default deadline for the activities..."
msgstr "작업의 기본 마감일 ..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""
"발신 서버 필터와 일치하지 않는 경우 기본값입니다. 로컬 부분 (예: '알림')이나 전체 이메일 주소 (예: "
"'<EMAIL>')로 모든 발신 이메일을 재설정할 수 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "기본 수신인"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"레코드의 기본 수신인 :\n"
"- 협력사 (협력사 ID 또는 partner_id 필드 사용) 또는\n"
"- 이메일 (email_from 또는 email field 사용)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr "기본 사용자"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr "프로세스 순서를 정의하고, 낮은값은 높은 우선 순위를 의미합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "지연 라벨"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "지연 유형"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Delay after releasing push-to-talk"
msgstr "PTT (push-to-talk) 사용 중 딜레이"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "지연 단위"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Delete"
msgstr "삭제"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "이메일 삭제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Delete Template"
msgstr "템플릿 삭제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Delete all previews"
msgstr "미리보기 전체를 삭제합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
msgid "Delivered"
msgstr "배송완료"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "전송 실패"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Delivery failure"
msgstr "전송 실패"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr "'default_res_id' 사용을 중단하고, 'default_res_ids' 를 사용해야만 합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "설명"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr "이 하위 유형에 대해 게시된 메시지에 추가할 설명입니다. 비워두면 이름이 대신 추가됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"초대 링크를 통해서 열 경우 기본값으로 채널을 어떻게 표시할 것인지를 결정합니다. 값을 지정하지 않는 경우 텍스트를 표시합니다 (사운드나"
" 동영상 없음)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_editable
msgid "Determines if the canned response can be edited by the current user"
msgstr "미리 준비된 응답을 현재 사용자가 편집할 수 있는지 결정합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_shared
msgid "Determines if the canned_response is currently shared with other users"
msgstr "canned_response가 현재 다른 사용자와 공유되고 있는지 확인합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Direct messages"
msgstr "다이렉트 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Discard"
msgstr "취소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "Disconnect"
msgstr "연결 해제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Disconnected from the RTC call by the server"
msgstr "서버에서 RTC 호출 연결이 해제되었습니다."

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "메일 및 채팅"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "메일및채팅 사이드바"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr "메일및채팅: 채널 멤버 음소거 해제"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_users_settings_unmute_ir_actions_server
msgid "Discuss: users settings unmute"
msgstr "메일 및 채팅: 사용자 설정 음소거 해제"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "메일 및 채팅 채널"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "메일 및 채팅"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Dismiss"
msgstr "해산"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "표시명"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr "관련 문서에 옵션을 표시하여 이 서식을 사용하여 구성 마법사를 열도록 합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
msgid "Display avatar name"
msgstr "아바타 이름 표시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
msgid "Do you really want to delete \"%s\"?"
msgstr "정말로 \"%s\" 항목을 삭제하시겠습니까?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Do you really want to delete this preview?"
msgstr "정말로 미리보기를 삭제하시겠습니까?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "문서"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "문서 팔로워"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr "문서 ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "문서 모델"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "문서명"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr "문서: \""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "도메인"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done"
msgstr "완료"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "완료 및 다음 실행"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Schedule Next"
msgstr "완료 및 다음 일정 예약"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done Date"
msgstr "완료 날짜"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Done and Schedule Next"
msgstr "완료 및 다음 일정 예약"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Download"
msgstr "다운로드"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Download Files"
msgstr "파일 다운로드"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Download logs"
msgstr "다운로드 로그"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Download:"
msgstr "다운로드:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/mail_attachment_dropzone.xml:0
msgid "Drop Files here"
msgstr "여기로 파일 끌어오기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "마감 기한"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "만기일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due in"
msgstr "마감일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Due in %s days"
msgstr "%s 일 후 만기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due on"
msgstr "마감일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "만기 유형"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "복사한 이메일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "음성 활동 지속 시간 (ms)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "동적 보고서"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr "동적 사용자 (레코드 기준)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Edge blur intensity"
msgstr "가장자리 흐림도"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Edit"
msgstr "편집"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "협력사 편집하기"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Message"
msgstr "예약된 메시지 편집"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Note"
msgstr "예약된 노트 편집"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "Edit Subscription of %(name)s"
msgstr "%(name)s의 설명 편집"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Edit subscription"
msgstr "구독 편집"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
msgid "Edit: %s"
msgstr "편집: %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "이메일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "이메일 서명 추가"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "이메일 주소"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "이메일 별칭"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "이메일 별칭"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "이메일 별칭 혼합"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr "이메일 별칭 믹스인 (라이트)"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "이메일 수신 거부"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "이메일 버튼 색상"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "이메일 참조 관리"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "이메일 환경 설정"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr "이메일 도메인"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Email Failure: %(modelName)s"
msgstr "이메일 실패: %(modelName)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "이메일 머리글 색상"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "이메일 대량 발송"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "이메일 알림 레이아웃"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "이메일 미리보기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "이메일 검색"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "이메일 서식"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "이메일 서식 미리보기"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "이메일 서식"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "이메일 스레드"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "이메일 주소가 이미 존재합니다!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr "보낸 사람의 이메일 주소입니다. 이 필드는 일치하는 협력사가 없는 경우에 설정되어 메시지 창에서 작성자 필드를 대체합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr "대량 메일에 대한 회신이 전송될 이메일 주소"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr "대량 메일에 대한 회신이 전송될 이메일 주소입니다. 회신 메일이 원본 대화 스레드에 기록되어 있지 않은 경우에만 사용됩니다."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr "블랙리스트에 올라 있는 이메일인 경우에는 더 이상 메일링을 받지 않습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""
"%(alias_name)s 이메일 별칭은 여러 개의 레코드에서 동시에 사용할 수 없습니다. 레코드를 하나씩 업데이트하시기 바랍니다. "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "이메일 참조"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "이메일 구성 마법사"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "이메일 도메인 예: '<EMAIL>'의 'example.com'"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "이메일 메시지"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "이메일 재전송 마법사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "이메일 서식"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "이메일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "Emoji"
msgstr "이모티콘"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
msgid "Emojis"
msgstr "이모지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "직원 전용"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Enable"
msgstr "활성화"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "주문 추적 사용"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Enter"
msgstr "Enter"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Enter Full Screen"
msgstr "전체 화면 시작"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__danger
msgid "Error"
msgstr "오류"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "오류 메시지"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
msgid "Error during communication with the publisher warranty server."
msgstr "게시자 보증 서버와의 통신 중 오류가 발생했습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "오류 메시지"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"전체에서 오류가 발생했습니다. 알림 레코드에 대한 동시 접속 업데이트에서 발생했을 가능성이 있습니다. 관리자에게 문의하시기 바랍니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr "전체에서 오류가 발생했습니다. 지정된 수신자 없이 이메일을 발송하여 발생했을 가능성이 있습니다. "

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "오류, 협력사가 동일한 객체를 두 번 팔로우할 수 없습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "전체"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "예외"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Exit Fullscreen"
msgstr "전체 화면에서 나가기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Expand"
msgstr "확장"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Expand panel"
msgstr "패널 펼치기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__expiration_time
msgid "Expiration Token Date"
msgstr "토큰 만료일"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "확장 필터..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Extra Comments ..."
msgstr "추가 댓글 ..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "전송 실패"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "실패함"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid "Failed to enable push notifications"
msgstr "푸시 알림을 사용 설정하지 못함"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Failed to load gifs..."
msgstr "gif를 불러오지 못했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr "SFU 서버를 불러오지 못하여 P2P 모드로 전환되었습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Failed to post the message. Click to retry"
msgstr "메시지를 게시하지 못했습니다. 다시 시도하려면 클릭하세요."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""
"QWeb 서식 렌더링에 실패했습니다: %(template_src)s\n"
"\n"
"%(template_traceback)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr "inline_template을 렌더링하지 못했습니다: %(template_txt)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render template: %(view_ref)s"
msgstr "서식 렌더링에 실패하였습니다: %(view_ref)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "실패 이유"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "실패 이유"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr "실패 이유. 이메일 서버가 보낸 예외는 일반적으로 메일 문제의 디버깅을 쉽게 하기 위해 저장됩니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "실패 유형"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Failure: %(modelName)s"
msgstr "실패: %(modelName)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "즐겨찾기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Favorites"
msgstr "즐겨찾기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Feedback:"
msgstr "피드백:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "지금 가져오기"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr "지정된 개수의 GIF를 불러옵니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "필드"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "\"Mail Activity\" 필드는 \"False\"로 변경할 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "\"Mail Blacklist\" 필드는 \"False\"로 변경할 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "필드 \"Mail Thread\"는 \"False\"로 변경할 수 없습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "필드 세부사항"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"관련 문서에서 자동 가입을 사용할 때 관련 모델을 하위 유형 모델에 연결하는 데 사용되는 필드입니다. 이 필드는 getattr "
"(related_document.relation_field)을 계산하는 데 사용됩니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""
"“%(field)s” 필드에 포함되어 있는 “%(model)s” 모델은 반드시 Many2one 유형이여야 하며, 기간을 계산하기 위해 "
"tracking=True으로 설정되어 있어야 합니다."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "필드"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr "서식이 만들어진 원래의 파일입니다. 서식이 손상된 경우 다시 제작하는데 사용합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "File too large"
msgstr "파일이 너무 큽니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is disabled for external users"
msgstr "외부 사용자는 파일 업로드를 할 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is enabled for external users"
msgstr "외부 사용자가 파일 업로드를 할 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Files"
msgstr "파일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Fold"
msgstr "접기"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "접음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Follow"
msgstr "팔로우"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "팔로워"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "팔로워 양식"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "팔로워 전용"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr "팔로워 추가"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr "팔로워 삭제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Following"
msgstr "팔로잉"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예: fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""
"%(channels)s의 경우, channel_type을 '채널'로 설정해야 그룹 기준으로 승인을 하거나 그룹 자동 구독을 할 수 "
"있습니다. "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 1 hour"
msgstr "1시간 동안"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 15 minutes"
msgstr "15분 동안"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 24 hours"
msgstr "24시간 동안"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 3 hours"
msgstr "3시간 동안"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 8 hours"
msgstr "8시간 동안"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr "강제 발송"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr "강제 언어 선택:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "서식있는 이메일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "발신인"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__from_message_id
msgid "From Message"
msgstr "메시지에서"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "From peer:"
msgstr "발신 동료:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Full composer"
msgstr "전체 작성자"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "전체 화면 동영상"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Future"
msgstr "향후"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "향후 활동"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF"
msgstr "GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Category"
msgstr "GIF 카테고리"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Favorites"
msgstr "GIF 즐겨찾기"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr "GIF 즐겨찾기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr "Tenor의 GIF id"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "GIFs"
msgstr "GIFs"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "게이트웨이"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Go to conversation"
msgstr "대화로 이동"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr "Google Translate 통합"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "그룹"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "그룹별"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "그룹명"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr "그룹 승인이나 그룹 자동 구독은 채널에서만 지원되는 기능입니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "그룹별..."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_sub_channel_no_group_public_id
msgid ""
"Group public id should not be set on sub-channels as access is based on "
"parent channel"
msgstr "상위 채널을 통해 액세스가 관리되므로 하위 채널에 그룹 공개 ID를 할당해서는 안 됩니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Grouped Chat"
msgstr "그룹 채팅"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "그룹"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
msgid "Guest"
msgstr "손님"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name cannot be empty."
msgstr "게스트 이름은 비워둘 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name is too long."
msgstr "게스트 이름이 너무 깁니다."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "손님"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "이메일"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Odoo 수신함"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr "오류가 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr "메일 활동 있음"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr "메일 수신 거부 있음"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "메일 스레드 있음"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "언급함"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "수신음이 꺼져 있음"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "오류 있음"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "주문형 응답이 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "머리글"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "안녕하세요"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "숨김"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr "숨김 처리된 서식"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
msgid "Hide Pinned Messages"
msgstr "고정 메시지 숨기기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Hide all conversations"
msgstr "모든 대화 숨기기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Hide sidebar"
msgstr "사이드바 숨기기"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "팔로워 옵션에서 하위 유형 숨기기"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr "일반 및 포털 사용자에게 숨기며, 하위 환경 설정과는 무관합니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "높음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "History"
msgstr "이전 기록"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "메일 서버의 호스트 이름 또는 IP"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Hover on your message and mark as todo"
msgstr "메시지 위에 마우스를 가져가서 할 일로 표시하세요."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr "볼륨에서 임계값 통과 후 오디오 송출이 활성화된 상태로 유지되는 시간"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "ICE 서버"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE connection:"
msgstr "ICE 연결:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE gathering:"
msgstr "ICE 수집:"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "ICE 서버"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr "ICE 서버"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE:"
msgstr "ICE:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_push__id
#: model:ir.model.fields,field_description:mail.field_mail_push_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "별칭을 담고 있는 상위 레코드의 ID (예: 별칭을 생성한 작업을 담고 있는 프로젝트)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "메신저 상태"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "IMAP 서버"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "팔로우된 자원의 ID"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "신원"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Idle"
msgstr "대기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "SSL이 필요할 경우."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr "설정할 경우, 이 필드에 대한 모든 수정 내역을 추적합니다. 설정값은 주문 추적값으로 사용합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr "설정할 경우, 멤버는 해당 날짜까지 채널에서 알림을 받지 않습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"설정할 경우, 대기열 관리자가 해당 날짜 이후에 이메일을 전송합니다. 설정하지 않을 경우, 이메일은 최대한 빠른 시간 안에 전송됩니다. "
"시간대를 지정하지 않으면, UTC 시간대에 있는 것으로 간주합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""
"설정할 경우, 대기열 관리자가 해당 날짜 이후에 이메일을 전송합니다. 설정하지 않을 경우, 이메일은 최대한 빠른 시간 안에 전송됩니다. "
"동적 표현식을 사용할 수 있습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__mute_until_dt
msgid ""
"If set, the user will not receive notifications from all the channels until "
"this date."
msgstr "설정 시 해당 날짜까지 모든 채널에서 알림을 받지 않습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "설정할 경우, 권한이 없는 사용자에게 기본 메시지 대신 이 내용을 전송합니다. "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""
"설정할 경우, 서식 사용을 특정 사용자로 제한합니다."
"                                                   설정하지 않으면, 모든 사용자와 공유합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr "이메일 주소가 블랙리스트에 있는 경우, 해당 연락처는 더 이상 일괄 메일을 수신하지 않습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_scheduled_message__is_note
msgid "If the message will be posted as a Note."
msgstr "메시지가 메모로 게시되는 경우."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"참인 경우, 응답 내용이 원본 문서의 토론 스레드로 포함되지 않습니다. 대신 message-id를 추적하여 reply_to 내용을 "
"확인하고 그에 따라서 리디렉트합니다. 이는 생성된 message-id 항목에 영향을 미치게 됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr "Odoo 서버로 리디렉션되는 도메인에 모든 이메일을 수집하도록 설정한 경우 여기에 도메인 이름을 입력합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "twilio를 TURN/STUN 서버 공급업체로 사용하려는 경우"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr "전체 무시"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "이미지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "1024 이미지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "128 이미지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "256 이미지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "512 이미지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr "MIME 타입 이미지"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "이미지는 링크입니다"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""
"댓글 모드에서 설정할 경우, 알림 전송을 연기합니다. 대량 메일 모드에서 설정할 경우, 해당 날짜 이후에 이메일을 전송합니다. 이 날짜는"
" UTC 시간대를 사용합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Inactive Alias"
msgstr "별칭 비활성"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "받는 메일 서버"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
msgid "Inbox"
msgstr "수신함"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Call..."
msgstr "수신 전화"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr "수신 이메일"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "이메일 수신 서버"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "수신 메일 서버"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "수신 메일 서버"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Video Call..."
msgstr "비디오 통화 수신 중..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr "이 활동은 사용자가 생성하지 않고 자동으로 생성되었음을 나타냅니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Info"
msgstr "정보"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "초기 모델"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "인라인된 전체 주소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Input device"
msgstr "입력 장치"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Insert Template"
msgstr "템플릿 삽입"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Insert Templates"
msgstr "템플릿 삽입"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Install"
msgstr "설치"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Install Odoo"
msgstr "Odoo 설치"

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "연동"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "내부 전용"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr "내부 커뮤니케이션"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_count
msgid "Interval"
msgstr "간격"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "유효하지 않음"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Invalid domain “%(domain)s” (type “%(domain_type)s”)"
msgstr "잘못된 도메인 “%(domain)s” (“%(domain_type)s” 유형)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "잘못된 이메일 주소"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Invalid email address “%s”"
msgstr "잘못된 이메일 주소입니다. “%s”"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr "잘못된 표현, 그것은 문자 그대로의 파이썬 사전 정의여야 합니다. 예: \"{'field': 'value'}\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr "구성원이 있는 채널을 생성할 때 “%(field_name)s” 필드가 유효하지 않았습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "주소가 잘못되었습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Invalid primary email field on model %s"
msgstr "%s 모델에 대한 기본 전자 메일 필드가 잘못되었습니다"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr "잘못된 res_ids %(res_ids_str)s (%(res_ids_type)s 유형)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""
"서버 이름이 잘못되었습니다!\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr "잘못된 도메인이나 화면 소스 %(svalue)s (%(stype)s 유형), 레코드 또는 XMLID 여야 합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr "잘못된 서식이나 화면 소스 Xml ID %(source_ref)s 항목은 더 이상 존재하지 않습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr "잘못된 서식이나 화면 소스 레코드 %(svalue)s, 대신 %(model)s입니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr "잘못된 서식이나 화면 소스 참조 %(svalue)s, 대신 %(model)s 입니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr "채널을 생성할 때 구성원 값이 잘못되었습니다. 4나 6만 사용할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr "채널을 생성할 때 구성원 값이 잘못되었습니다. 0만 사용할 수 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr "초대 URL"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "%(document_model)s 팔로우 초대: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite"
msgstr "초대"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Invite People"
msgstr "다른 사람 초대하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Invite a User"
msgstr "사용자 초대"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Invite people"
msgstr "초대하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Channel"
msgstr "채널 초대"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Group Chat"
msgstr "그룹 채팅 초대"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "초대 마법사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "활성화"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "현재 사용자 또는 게스트 작성자입니다"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "편집 가능 여부"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "편집자입니다 "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__is_hidden
msgid "Is Hidden"
msgstr "숨김"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "회원 여부"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "읽기 전용 여부"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr "본인입니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr "서식 편집기입니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr "기록"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__is_note
msgid "Is a note"
msgstr "노트"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "메일및채팅 사이드바의 카테고리 채널이 열려 있습니까?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "메일및채팅 사이드바의 카테고리 채팅창이 열려 있습니까?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "마이크 음소거"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "초기 화면 노출 여부"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "사용자 동영상 전송 중입니다"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "화면 공유 중입니다"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr "채널 멤버 생성과 관련된 채널을 지정하지 않았습니다. 계속 진행하려면 필요한 채널 정보를 입력해 주세요."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_push_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""
"알림에서 사용하는 브라우저 키를 참조합니다: \n"
"- p256dh: 브라우저에서 생성한 subscription 공개 키입니다. 브라우저는 \n"
"개인 키를 보안으로 유지하고 페이로드를 해독하는 데 사용합니다.\n"
"- 인증: 인증 값은 보안으로 취급되어야 하며 Odoo 외부에서 공유해서는 안 됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "many2one 필드의 ID를 초 단위의 소요 시간으로 매핑하는 JSON"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "참여"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Join Call"
msgstr "통화 참여"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Join Channel"
msgstr "참여 채널"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "그룹에 참여"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
msgid "Jump"
msgstr "이동"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Jump to Present"
msgstr "현재 메시지로 이동"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "첨부파일 보관"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "완료 상태 유지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr "메시지 사본 보관"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "원본 유지"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr "이메일이 삭제된 경우 이메일 본문 사본을 보관하십시오 (대량 메일만 해당)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr "활동 보기에서 완료 상태로 유지"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "키"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Kind Regards"
msgstr "감사합니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "LIVE"
msgstr "실시간"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "사용 언어"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "최근에 가져온 날짜"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "최근에 가져옴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__last_interest_dt
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "최근 관심"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "최근 확인"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "최근 업데이트"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__last_used
msgid "Last Used"
msgstr "최근 사용"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr "최근 확인일"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__last_used
msgid "Last time this canned_response was used"
msgstr "마지막으로 이 canned_response가 사용된 시간"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Late"
msgstr "지연"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "지연된 활동"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Launch Plans"
msgstr "출시 계획"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "배치"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "나가기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Channel"
msgstr "채널 나오기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Conversation"
msgstr "대화 나가기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Leave this channel"
msgstr "이 채널에서 나가기"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr "미리보기 링크"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Link copied!"
msgstr "링크를 복사하였습니다!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "List Activity"
msgstr "작업 목록"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "List users in the current channel"
msgstr "현재 채널 사용자 목록 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Load More"
msgstr "더 불러오기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Load more"
msgstr "추가 불러오기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/navigable_list.xml:0
#: code:addons/mail/static/src/core/public_web/messaging_menu.xml:0
msgid "Loading…"
msgstr "불러오는 중..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "로컬 서버"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "로컬 부품 기반 입고 감지"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr "답장 수신에 사용되는 이메일의 로컬 부분 (예: '<EMAIL>'의 'catchall')"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""
"반송된 이메일을 처리할 때 반환 경로에 사용되는 이메일의 로컬 부분 (예: '<EMAIL>'의 'bounce')."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Log"
msgstr "로그"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Log Later"
msgstr "나중에 로그"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Log Now"
msgstr "지금 로그"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Log RTC events"
msgstr "RTC 행사 불러오기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "메모 기록"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "활동 기록"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Log an internal note…"
msgstr "내부용 메모를 기록합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Log note"
msgstr "로그 기록"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Log step:"
msgstr "로드 단계:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
msgid "Logged in as %s"
msgstr "%s으로 로그인됨"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "로그인 정보"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "낮음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Lower Hand"
msgstr "손내리기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr "MIME 유형"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
msgid "Mail"
msgstr "메일"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "메일 활동"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "메일 활동 유형"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "메일 수신 거부"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "수신 거부 메일 혼합"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "메일 채널 양식"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "메일 쓰기 혼합"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mail Failures"
msgstr "메일 실패"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr "메일 게이트웨이 허용"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "Mail Layout"
msgstr "메일 레이아웃"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr "메일 주요 첨부 파일 관리"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr "메일 메시지 내부 아이디"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr "메일 RTC 세션"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "메일 렌더링 혼합"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "메일 서버"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "메일 서식"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "메일 서식 편집기"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr "메일 서식 재설정"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "메일 스레드"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "메일 추척 값"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""
"댓글 모드의 메일 쓰기는 반드시 하나 이상의 레코드에서 실행되어야 합니다. 레코드를 찾을 수 없습니다 (%(model_name)s "
"모델)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "기존 mail.message를 알리기 위해 메일이 생성되었습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "Mail template model of %(action_name)s does not match action model."
msgstr "%(action_name)s의 메일 서식 모델이 작업 모델과 일치하지 않습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr "이 메일 서버를 사용하는 메일 서식"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "메일 : 이메일 대기열 관리자"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "메일 : 메일 서비스 가져 오기"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_post_scheduled_message_ir_actions_server
msgid "Mail: Post scheduled messages"
msgstr "메일: 예약된 메시지 게시"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr "메일: 웹 푸시 알림 보내기"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Mailbox unavailable - %s"
msgstr "메일함을 사용할 수 없음 - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mailboxes"
msgstr "메일함"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr "소스가 포함되어 있는 메일이나 포스팅은 %(source_type)s 항목을 비운 채로 호출해서는 안됩니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "메일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "주요 첨부 파일"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr "같은 스레드를 유지하여 답신하는 대신 새로운 수신 이메일로 답변을 관리합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Mark As Read"
msgstr "읽음 표시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Mark Done"
msgstr "완료"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Mark all read"
msgstr "모두 읽음 표시"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Mark as Done"
msgstr "완료"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Mark as Read"
msgstr "읽음 표시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Todo"
msgstr "할 일로 표시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Unread"
msgstr "안읽음 표시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Mark as done"
msgstr "완료"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr "미디어 장치를 가져올 수 없습니다. SSL 항목이 제대로 설치되지 않았습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "중간"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "회의"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "구성원 수"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "참여자"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr "해당 그룹의 참여자는 자동으로 팔로워로 추가됩니다. 필요한 경우 수동으로 구독을 관리할 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Mentions"
msgstr "언급"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
msgid "Mentions Only"
msgstr "언급 전용"

#. module: mail
#: model:ir.model,name:mail.model_ir_ui_menu
msgid "Menu"
msgstr "메뉴"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "협력사 병합 마법사"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
msgid "Merged with the following partners: %s"
msgstr "다음 협력사와 병합되었습니다: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Message"
msgstr "메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message \"%(subChannelName)s\""
msgstr "메시지 \"%(subChannelName)s\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message #%(threadName)s…"
msgstr "메시지 #%(threadName)s…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message %(thread name)s…"
msgstr "메시지 %(thread name)s…"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "메시지 ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copied!"
msgstr "메시지 링크가 복사되었습니다!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr "메시지 링크 복사에 실패했습니다! (권한이 거부되었습니다?)"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "메시지 알림"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "메시지 응답"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "메시지 응답"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "메세지 레코드 이름"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr "메시지 번역"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "메시지 번역 API 키"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "메시지 유형"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__new_message_separator
msgid "Message id before which the separator should be displayed"
msgstr "구분 기호가 표시되어야 하는 메시지 ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message posted on \"%s\""
msgstr "\"%s\"에 게시된 메시지"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "메세지 수신자 (이메일)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "이전 메시지의 식별 기호와 같은 메시지 참조"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Message should be a valid EmailMessage instance"
msgstr "메시지는 유효한 이메일메시지 인스턴스여야만 합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"메시지 하위유형은 특히 시스템 알림 메시지에 대해 보다 정확한 유형을 제공합니다. 예를 들어 새로운 레코드(신규)에 관련해서 통지하거나 "
"진행 중에 단계가 변경되는 것(단계 변경) 등입니다. 메시지 하위유형을 통해서 사용자가 받기 원하는 알림을 정확하게 조정할 수 있습니다."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "메시지 하위유형"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr "팔로우하는 메시지 하위유형. 하위유형의 의미는 사용자에게 푸쉬해 주는 것입니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr "메시지 유형: 이메일 메시지를 위한 이메일, 시스템 메시지를 위한 알림, 사용자 댓글과 같이 기타 메시지에 대한 코멘트"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "메시지 고유 식별 기호"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "메시지-ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr "메시지"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "메시지 검색"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_from_message_id_unique
msgid "Messages can only be linked to one sub-channel"
msgstr "메시지는 하나의 하위 채널에만 연결할 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Messages marked as read will appear in the history."
msgstr "읽은 것으로 표시된 메시지가 이전 기록에 표시됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr "내부 하위 유형이 있는 메시지는 직원, 즉 base_user 그룹 구성원만 볼 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Messages with tracking values cannot be modified"
msgstr "추적값이 있는 메시지는 수정할 수 없습니다."

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr "음성 첨부 파일의 메타데이터"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "누락된 이메일"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "누락된 이메일 주소"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "주소 누락"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr "혼합을 사용하여 각 many2one 필드 값의 레코드에 소요된 시간을 계산합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "모델"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "모델이 변경됨"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "다음 자원의 모델"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr "모델에 적용되는 하위 유형. False인 경우 이 하위 유형의 모든 모델에 적용됩니다."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "모델"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr "모델을 수정하면 이 활동 유형을 사용한 기존 활동에 영향을 미칠 수 있습니다. 주의하십시오."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "모듈 제거"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "월"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
msgid "More"
msgstr "더 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Mute"
msgstr "소리 끄기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Mute Conversation"
msgstr "대화 음소거"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute all conversations"
msgstr "모든 대화 무음 처리"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute duration"
msgstr "음소거 지속 시간"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
#: model:ir.model.fields,field_description:mail.field_res_users_settings__mute_until_dt
msgid "Mute notifications until"
msgstr "다음까지 알림 음소거"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Muting prevents unread indicators and notifications from appearing."
msgstr "음소거 설정하면 읽지 않은 표시기와 알림이 표시되지 않습니다."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action_my
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "My Activities"
msgstr "내 작업"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "나의 서식"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "My canned responses"
msgstr "내 미리 준비된 답변"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "이름"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "작업 필요"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "New"
msgstr "신규"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Channel"
msgstr "신규 채널"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Message"
msgstr "신규 메시지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__new_message_separator
msgid "New Message Separator"
msgstr "새 메시지 구분 기호"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "New Thread"
msgstr "새 스레드"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "새 Char 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "새 Datetime 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "새 Float 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "새 Integer 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "새 Text 값"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "New message"
msgstr "새 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "New messages appear here."
msgstr "여기에 새 메시지가 표시됩니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "새 값"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "다음 활동"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "다음 활동"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Next Monday Morning"
msgstr "다음 주 월요일 아침"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "다음 활동 가능"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "No"
msgstr "아니오"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "No Error"
msgstr "오류 없음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "No Followers"
msgstr "팔로워 없음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.js:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "No IM status available"
msgstr "사용할 수 있는 IM 상태 없음"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "기록 없음"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr "활동이 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No channel found"
msgstr "채널을 찾을 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "No conversation selected."
msgstr "선택한 대화가 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No conversation yet..."
msgstr "아직 대화가 없습니다 ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No history messages"
msgstr "이전 메시지 내역이 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "No message_id found in context"
msgstr "컨텍스트에 message_id가 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
msgid "No messages found"
msgstr "메시지를 찾을 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "No recipient"
msgstr "수신인 없음"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "No recipient found."
msgstr "수신자가 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"응답이 없습니다. 서버 정보를 확인하십시오.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr "%(activity_type_name)s: %(activity_summary)s에 대한 책임이 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "No results found"
msgstr "결과를 찾을 수 없습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "No saved templates"
msgstr "저장된 템플릿 없음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No starred messages"
msgstr "별표 표시된 메일이 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No thread found."
msgstr "스레드를 찾을 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.js:0
msgid "No thread named \"%(thread_name)s\""
msgstr "\"%(thread_name)s\"라는 스레드가 없습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "답변을 위한 threading(스레딩) 없음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No user found"
msgstr "사용자를 찾을 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "No user found that is not already a member of this channel."
msgstr "채널에서 사용자를 찾을 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "No users found"
msgstr "사용자를 찾을 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
msgid "Non existing record or wrong token."
msgstr "존재하지 않는 레코드이거나 잘못된 토큰입니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "None"
msgstr "없음"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "정규화된 이메일"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "확인 안됨"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr "테스트되지 않았습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid "Not interested by this?"
msgstr "관심이 없으신가요?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "노트"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__no_notif
msgid "Nothing"
msgstr "없음"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "알림"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "알림 이메일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Notification Item Image"
msgstr "알림 항목 이메일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr "알림 매개변수"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Notification Settings"
msgstr "알림 설정"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "알림 유형"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__notification_parameters
msgid "Notification parameters"
msgstr "알림 매개변수"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr "알림은 목록이나 집합으로 된 첨부 파일을 수신해야 합니다 (수신된 %(aids)s)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr "알림은 아이디 목록으로 된 첨부 파일 레코드를 수신해야 합니다 (수신된 %(aids)s)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr "알림은 주어진 협력사 정보를 아이디 목록으로 수신해야 합니다 (수신된 %(pids)s)."

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "알림 : 6개월이 지난 알림 삭제"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Notify scheduled messages"
msgstr "알림: 예약된 메시지 알림"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.actions.client,name:mail.discuss_notification_settings_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model:ir.ui.menu,name:mail.menu_notification_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "알림"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications allowed"
msgstr "알림 확인"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications blocked"
msgstr "알림 차단됨"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Notify Recipients"
msgstr "수신자 알림"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "Notify everyone"
msgstr "모두에게 알림"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_count
msgid ""
"Number of days/week/month before executing the action after or before the "
"scheduled plan date."
msgstr "작업을 실행하기 위한 예정된 계획 날짜 전후의 일/주/월 수입니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr "활동 실행 전까지의 일, 주 및 월 기간입니다. 활동 마감일을 계획할 수 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will not send notifications on this device."
msgstr "Odoo에서는 이 장치로 알림을 보내지 않습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will send notifications on this device!"
msgstr "Odoo에서 이 장치로 알림을 보냅니다!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr "끄기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Offline"
msgstr "오프라인"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Offline - %(offline_count)s"
msgstr "오프라인 - %(offline_count)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "예전 Char 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "예전 Datetime 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "예전 Float 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "예전 Integer 값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "예전 Text 값"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "예전 값"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr "별표가 표시된 메일은 언제든지 여기에서 다시 볼 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_indicator.xml:0
msgid "Ongoing call"
msgstr "진행 중인 통화"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Online"
msgstr "온라인"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Online - %(online_count)s"
msgstr "온라인 - %(online_count)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators are allowed to export mail message"
msgstr "관리자만 메일 메시지를 내보낼 수 있습니다"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators can modify 'model' and 'res_id' fields."
msgstr "관리자만 'model' 및 'res_id' 필드를 수정할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Only custom models can be modified."
msgstr "사용자 정의 모델만 수정할 수 있습니다."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr "내부 사용자만 Odoo 알림을 받을 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Only members of %(group_name)s group are allowed to edit templates "
"containing sensible placeholders"
msgstr "%(group_name)s의 그룹 구성원만 적절한 자리 표시자가 포함된 템플릿을 편집할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Only messages type comment can have their content updated"
msgstr "메시지 유형 코멘트에서만 내용을 업데이트할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr "댓글 종류가 메시지인 경우에만 '메일 및 채팅 채널' 모델에서 업데이트됩니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
msgid "Open"
msgstr "열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
msgid "Open Actions Menu"
msgstr "작업 메뉴 열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
msgid "Open Channel"
msgstr "채널 열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Open Discuss App"
msgstr "메일 및 채팅 앱 열기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "문서 열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Open Form View"
msgstr "양식 화면 열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Open Link"
msgstr "링크 열기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr "소유자 열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Open card"
msgstr "카드 열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
msgid "Open in Discuss"
msgstr "업무 협의에서 열기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_view.xml:0
msgid "Open preview in a separate window."
msgstr "별도의 창에서 미리 보기 열기."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Operation not supported"
msgstr "지원되지 않는 작업"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "선택하지 않음"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"회신하지 않은 것까지 모든 받는 메시지를 첨부할 스레드(레코드)의 선택사항 ID. 설정하면 완벽하게 새로운 레코드의 생성을 억제합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "선택 사항인 mail_mail ID입니다. 주로 검색을 최적화하기 위해 사용합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr "발신 메일에 대해 선호하는 서버를 설정합니다. 설정되어 있지 않은 경우 가장 높은 우선 순위의 서버가 사용됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"이메일 전송 시 번역할 언어 (ISO code)를 선택할 수 있습니다. 설정하지 않을 경우, 영어가 기본 언어로 사용됩니다. 적절한 "
"언어를 선택할 수 있도록 다음과 같은 예시용 메시지를 사용합니다. {{ object.partner_id.lang }}."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Options"
msgstr "옵션"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"원본 논의 내용: 답변 내용은 원래 진행되고 있던 문서의 논의 사항 스레드로 이동됩니다. \n"
"다른 이메일 주소: 답변 내용은 논의 스레드가 아니라 추적용 메시지 ID에서 지정된 이메일 주소로 이동됩니다.\n"
"이는 생성한 메시지 ID에 영향을 주게 됩니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Original message was deleted"
msgstr "원본 메시지가 삭제되었습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Original note:"
msgstr "원본 메모:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
msgid "Other activities"
msgstr "기타 작업"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "출고"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr "발신 이메일"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "발신 메일 서버"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "발신 메일 서버"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "발신 메일"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "보내는 메일 서버"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__overdue
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Overdue"
msgstr "기한이 지난"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "작성자 이메일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Overwrite Template"
msgstr "템플릿 덮어쓰기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "POP 서버"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "POP/IMAP 서버"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets received:"
msgstr "수신된 패킷:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets sent:"
msgstr "전송한 패킷:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "상위"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__parent_channel_id
msgid "Parent Channel"
msgstr "상위 채널"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "상위 메시지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "상위 모델"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "상위 레코드 스레드 ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__parent_channel_id
msgid "Parent channel"
msgstr "상위 채널"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"별칭이 있는 상위 모델입니다. 별칭 참조를 포함하는 모델은 반드시 alias_model_id(예: 프로젝트(parent_model) 및 "
"태스크(model)에서 제공하는 모델일 필요는 없습니다.)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"자동 구독에 사용되는 상위 하위 유형입니다.이 필드의 이름이 올바르지 않습니다. 예를 들어 프로젝트의 경우 프로젝트 하위 유형의 "
"parent_id는 작업 관련 하위유형을 나타냅니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "Participant avatar"
msgstr "참가자 아바타"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_push_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "협력사"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "Partner Profile"
msgstr "협력사 프로필"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "협력사 읽기 전용"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "추가 정보와 함께 협력사에게 메일 재전송"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "협력사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "작업이 필요한 협력사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "비밀번호"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr "API 키 붙여넣기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Pause"
msgstr "일시 정지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__payload
msgid "Payload"
msgstr "페이로드"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr "이 템플릿을 영구적으로 삭제합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "전화번호"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "전화 통화"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Pick a specific time"
msgstr "특정 시간 지정하기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Pick an Activity Plan to launch"
msgstr "시작할 작업 일정 선택"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Pin"
msgstr "핀 고정"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Pin It"
msgstr "핀고정하기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr "핀고정"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
msgid "Pinned Messages"
msgstr "핀 고정한 메시지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "계획"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr "사용 가능한 플랜"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date
msgid "Plan Date"
msgstr "플랜 날짜"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "계획명"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_summary
msgid "Plan Summary"
msgstr "플랜 요약"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "플랜 요약"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__planned
msgid "Planned"
msgstr "계획됨"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Planned Activities"
msgstr "계획한 활동"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "다음으로 계획됨"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "일정 관리"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Play"
msgstr "재생"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Please complete customer's information"
msgstr "고객 정보를 입력해주시기 바랍니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "대신 연락 주십시오."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Please wait while the file is uploading."
msgstr "파일이 업로드 될 동안 잠시 기다려 주십시오."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"대화 알림 처리 방법을 설정합니다:\n"
"- 이메일: 설정하신 이메일 주소로 알림이 전송됩니다.\n"
"- Odoo 수신함: Odoo 수신함에 알림이 표시됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mailgateway를 사용하여 문서에 메시지를 게시하는 정책.\n"
"- 모든 사용자: 모든 사용자가 게시할 수 있습니다\n"
"- 협력사: 인증된 협력사만 게시할 수 있습니다\n"
"- 팔로워: 관련 문서의 팔로워 또는 팔로잉 중인 채널의 사용자만 게시할 수 있습니다\n"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Pop out Attachments"
msgstr "첨부 파일 팝업"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "포트"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Granted"
msgstr "포털 접근 권한 부여"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Revoked"
msgstr "포털 접근 권한 취소"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Portal users can only filter threads by themselves as followers."
msgstr "포털 사용자는 자신이 팔로우하는 스레드만 필터링할 수 있습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "문서에 게시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Post your message on the thread"
msgstr "스레드에 메시지 게시"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr "메시지는 비즈니스 문서에서 게시되어야 합니다. message_notify 를 통하여 사용자에게 알림을 전송합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr "메시지를 게시하면 목록이나 집합으로 된 첨부 파일을 수신해야 합니다 (수신된 %(aids)s)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr "메시지를 게시하면 아이디 목록으로 된 첨부 파일 레코드를 수신해야 합니다 (수신된 %(aids)s)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr "메시지를 게시하면 아이디 목록으로 된 협력사 정보를 수신해야 합니다 (수신된 %(pids)s)."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "저작권자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "선행 활동"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "선호하는 응답 주소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "Press Enter to start"
msgstr "시작하려면 엔터키를 누르세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr "PTT (push-to-talk) 바로가기를 등록할 키를 누르세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preview"
msgstr "미리보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Preview my camera"
msgstr "내 카메라 미리보기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "다음 미리보기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "이전 활동 유형"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "비공개"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"이 문서 유형에 해당하는 대화의 일부로 각 받는 메일을 처리합니다. 이렇게하면 새 대화를 위한 새 문서를 만들거나 기존 대화(문서)에 "
"후속 이메일을 첨부할 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
msgid "Processing"
msgstr "처리 중"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Public Channel"
msgstr "공개 채널"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Public Channels"
msgstr "공개 채널"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "퍼블리셔 워런티 계약"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "퍼블리셔: 업데이트 알림"

#. module: mail
#: model:ir.model,name:mail.model_mail_push_device
msgid "Push Notification Device"
msgstr "푸시 알림 디바이스"

#. module: mail
#: model:ir.model,name:mail.model_mail_push
msgid "Push Notifications"
msgstr "푸시 알림"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push to Talk"
msgstr "1:1 대화하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Push to talk"
msgstr "1:1 대화하기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "PTT (Push-To-Talk) 바로가기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push-to-talk key"
msgstr "PTT (Push-To-Talk) 키"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search"
msgstr "빠른 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search…"
msgstr "빠른 검색…"

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "RTC 세션"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "RTC Session ID:"
msgstr "RTC 세션 ID:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr "RTC 세션"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "RTC 세션"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Raise Hand"
msgstr "손들기"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr "0.0에서 1.0 사이의 범위, 크기는 브라우저 구현 상황에 따라 다름"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "게스트 반응"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "협력사 반응"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "반응"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "읽은 날짜"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read Less"
msgstr "적게 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read More"
msgstr "더 읽기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
msgid "Ready"
msgstr "준비"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "전송 준비 완료"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/bus_connection_alert.xml:0
msgid "Real-time connection lost..."
msgstr "실시간 연결이 끊어졌습니다..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "사유"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr "Odoo에서 알림 수신"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "수령함"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Recent"
msgstr "최근"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "대상자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr "수신인 이름"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "수신인"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr "추천 활동"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "권장하는 행동 유형"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "기록"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "레코드 이름"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "레코드 스레드 ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "참조"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Refuse"
msgstr "반려"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "감사합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Register new key"
msgstr "새로운 키 등록"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Reject"
msgstr "거부"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "관련 회사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "관련 문서 ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr "관련 문서 ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__res_id
msgid "Related Document Id"
msgstr "관련 문서 ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "관련 문서 모델"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "관련 문서 모델 이름"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "관련 메일 서식"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "관련 메시지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "관련된 협력사"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "관계 필드"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
msgid "Remove"
msgstr "제거"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Remove Blur"
msgstr "흐린 효과 제거"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr "컨텍스트 활동 제거"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "팔로워 삭제"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Remove address from blacklist"
msgstr "수신 거부 목록에서 주소 삭제하기"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "수신 거부 마법사에서 이메일 제거"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr "관련 문서에서 이 서식을 사용하기 위해 상황별 작업 제거"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Remove this follower"
msgstr "이 팔로워 삭제"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr "필드 정보 삭제됨"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Rename Thread"
msgstr "스레드 이름 바꾸기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "렌더링 모델"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr "서식에 대응 내용이 없으므로 %(field_name)s 항목을 렌더링할 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr "서식에 정의된 내용이 없으므로 %(field_name)s 항목을 렌더링할 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Repeat"
msgstr "반복"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "답글"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Reply"
msgstr "답장"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "답장하기: "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr "회신 이메일 주소. reply_to를 설정하면 자동 스레드 생성을 무시합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "답장하다"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr "회신 주소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Replying to"
msgstr "다음 사용자에게 답장합니다:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Report"
msgstr "보고서"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "요청 협력사"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "재전송"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr "이메일 재전송"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "재전송 마법사"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "재설정 확인"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr "이메일 재전송 서식"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr "서식 재설정"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr "비밀번호 변경하기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "담당자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "템플릿 렌더링 제한"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr "메일 템플릿을 편집하거나 QWEB 텍스트 예문을 사용하는 것을 제한합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr "제한된 첨부 파일"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr "콘텐츠를 기반으로 한 언어 탐지 결과입니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "재시도"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Revert"
msgstr "되돌리기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "전체 양식 보기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "서식있는 텍스트 내용"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "서식있는 텍스트/HTML 메시지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr "통화 세션"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "RTC 세션"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr "SFU 서버 URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr "SFU 서버 키"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr "SFU 서버"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP 서버"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "영업 담당자"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Save"
msgstr "저장"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Save as Template"
msgstr "서식으로 저장"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Save editing"
msgstr "편집 내용 저장"

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr "Tenor API에 즐겨찾는 GIF 저장"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "예약"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr "예약 및 완료로 표시"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_model.js:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity"
msgstr "활동 계획표"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity On Selected Records"
msgstr "선택한 레코드에서 활동 예약하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Message"
msgstr "스케줄 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Note"
msgstr "스케줄 메모"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule activities to help you get things done."
msgstr "작업을 완료하기 위한 활동을 예약하세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Schedule activity"
msgstr "활동 계획표"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Schedule an Activity"
msgstr "활동 계획표"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity"
msgstr "활동 계획표"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity on selected records"
msgstr "선택한 레코드에서 활동 예약하기"

#. module: mail
#: model:ir.model,name:mail.model_ir_cron
msgid "Scheduled Actions"
msgstr "예약 작업"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "예정일"

#. module: mail
#: model:ir.model,name:mail.model_mail_scheduled_message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Scheduled Message"
msgstr "예약된 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr "예약된 메시지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "전송 날짜 예약"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "스크립트"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Search"
msgstr "검색"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "별칭 검색"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "그룹 검색"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "수신 메일 서버 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
msgid "Search Message"
msgstr "메시지 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Search Messages"
msgstr "메시지 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Search More..."
msgstr "추가 검색..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "RTC 세션 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search Sub Channels"
msgstr "하위 채널 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search button"
msgstr "버튼 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search by name"
msgstr "이름으로 검색하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search for a GIF"
msgstr "GIF 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a channel..."
msgstr "채널 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a user..."
msgstr "사용자 검색"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search in progress"
msgstr "검색 진행 중"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search..."
msgstr "검색..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
msgid "Search: %s"
msgstr "검색 : %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Email Changed"
msgstr "보안 업데이트: 이메일이 변경됨"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Login Changed"
msgstr "보안 업데이트: 로그인이 변경됨"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Password Changed"
msgstr "보안 업데이트: 비밀번호가 변경됨"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr "오류 세부 정보 보기"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "See all pinned messages."
msgstr "전체 핀 고정한 메시지를 확인합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user)s"
msgstr "%(user)s님이 읽음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s and %(user2)s"
msgstr "%(user1)s, %(user2)s님이 읽음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s and %(user3)s"
msgstr "%(user1)s, %(user2)s, %(user3)s님이 읽음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and %(count)s others"
msgstr "%(user1)s, %(user2)s, %(user3)s,%(count)s님이 읽음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and 1 other"
msgstr "%(user1)s, %(user2)s, %(user3)s님과 기타 1명이 읽음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by everyone"
msgstr "모두 읽음"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.xml:0
msgid "Seen by:"
msgstr "조회 :"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr "언어 선택"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Select a user..."
msgstr "사용자 선택"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr "GIF를 필터링하는 데 사용되는 콘텐츠 필터를 선택합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Send"
msgstr "보내기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr "전송 및 닫기"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "이메일 보내기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr "다른 이름으로 이메일 보내기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Send Later"
msgstr "나중에 보내기"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Send Mail (%s)"
msgstr "(%s) 메일 보내기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Send Now"
msgstr "지금 보내기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Send a message to followers…"
msgstr "팔로워에게 메시지 전송하기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr "Gmail 계정을 통해 이메일을 수발신합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr "Outlook 계정을 통해 이메일을 수발신합니다."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "이메일 전송"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr "메일이나 알림 직접 전송하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Send message"
msgstr "메시지 보내기"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "발신자 주소"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"발신자 주소(자리 표시자를 여기에 사용할 수 있습니다). 설정하지 않으면 기본값으로 설정된 작성자의 이메일 별칭 또는 이메일 주소를 "
"사용합니다."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr "전송 실패"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Sent"
msgstr "전송됨"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "순서"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "서버 & 로그인"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "서버작업"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "서버 정보"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "서버 이름"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "서버 우선 순위"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "서버 유형"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr "서버 유형 정보"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "Server error"
msgstr "서버 오류"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"서버가 다음 예외와 함께 응답했습니다 : \n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "서버유형 IMAP."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "서버 유형 POP."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "채널을 삭제하지 않고 숨기려면 false로 설정합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "설정"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr "Sfu 채널 Uuid"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr "Sfu 서버 Url"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Share Screen"
msgstr "화면 공유"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Shared canned responses"
msgstr "미리 준비된 답변 공유"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Shared with all users."
msgstr "모든 사용자와 공유"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__source
msgid "Shortcut"
msgstr "바로가기"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr "번역을 요청할 대상으로 사용되는 단축 언어 코드입니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Show Followers"
msgstr "팔로워 표시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Show a helper message"
msgstr "도움말 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
msgid "Show activities"
msgstr "활동 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.xml:0
msgid "Show all recipients"
msgstr "모든 수신자 보기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "다음 행동 날짜가 오늘 이전 인 모든 기록보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show less"
msgstr "간단히 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show more"
msgstr "자세히 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Show sidebar"
msgstr "사이드바 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Show video participants only"
msgstr "동영상 참가자만 표시"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Showing"
msgstr "전시"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "사이드바 동작"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr "관련 문서 모델의 레코드에서 이 서식을 사용할 수 있도록 하는 사이드바 동작"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr "사이트 이름"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Snooze 7d"
msgstr "7일 후 다시 알림"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "So uhh... maybe go favorite some GIFs?"
msgstr "그럼... GIF를 즐겨찾기에 추가해 볼까요?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "원본"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr "원본 언어"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "특정 사용자"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr "활동이 모델에 고유해야 하고 다른 모델에 대한 활동을 관리 할 때 사용할 수 없는 경우 모델을 지정하십시오."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
msgid "Starred"
msgstr "별표"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr "별표 표시된 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Call"
msgstr "통화 시작"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Start a Conversation"
msgstr "대화 시작하기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Video Call"
msgstr "화상통화 시작"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a conversation"
msgstr "대화 시작"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_sidebar_patch.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a meeting"
msgstr "회의 시작하기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "시/도"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "상태"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동 기준 상태\n"
"기한 초과: 기한이 이미 지났습니다.\n"
"오늘: 활동 날짜가 오늘입니다.\n"
"예정: 향후 계획된 활동입니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "상태 시간"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
msgid "Status with time"
msgstr "시간별 상태"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Stay tuned! Enable push notifications to never miss a message."
msgstr "최신 정보를 놓치지 마세요! 푸시 알림을 활성화하면 메시지를 놓치지 않을 수 있습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "단계"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Stop Recording"
msgstr "기록 중지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop Sharing Screen"
msgstr "화면 공유 중지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop camera"
msgstr "카메라 중지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Stop replying"
msgstr "답변 중지"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr "각 레코드의 채팅장에 이메일 및 답글 저장"

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr "링크 미리보기 데이터 저장하기"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"다음의 패턴에 따라 수정할 수 있는 키를 표시하도록 형식이 지정된 문자열: shift.ctrl.alt.key, 예: "
"truthy.1.true.b "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr "번역 요청에서 수신된 문자열입니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sub_channel_ids
msgid "Sub Channels"
msgstr "하위 채널"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "제목"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "제목 (자리 표시자를 여기에 사용할 수 있습니다)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Subject:"
msgstr "제목:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "구독 수신자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__substitution
msgid "Substitution"
msgstr "대체"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "하위 유형"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "하위 유형"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "제안"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "다음 활동 제안"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr "현재 활동이 완료된 것으로 표시되면 이러한 활동을 제안하십시오."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "요약"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "요약 :"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr "Gmail 인증 지원"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr "Outlook 인증 지원"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "시스템 매개 변수"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
msgid "System notification"
msgstr "시스템 알림"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Tab to select"
msgstr "탭하여 선택"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr "대상 언어"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "대상 모델"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "사업자등록번호"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Technical Settings"
msgstr "기술 설정"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr "편집을 시작할 경우 UX 관련 동작을 지원할 수 있도록 모델을 추적하는 기술 필드"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "서식"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr "서식 카테고리"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template Description"
msgstr "서식 상세"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr "서식 파일명"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_name
msgid "Template Name"
msgstr "서식명"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "서식 미리보기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "서식 미리보기 언어"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr "서식 재설정 혼합"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Template creation from composer requires a valid model."
msgstr "컴포저에서 서식을 작성하려면 유효한 모델이 필요합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering should only be called with a list of IDs. Received "
"“%(res_ids)s” instead."
msgstr "서식 렌더링은 반드시 ID 목록에서만 호출해야 합니다. 대신 “%(res_ids)s ” 항목을 수신합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""
"서식 렌더링은 inline_template, qweb, 또는 qweb_view (보기 또는 원본 화면)에서만 지원됩니다. 대신 "
"%(engine)s 항목을 수신합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "템플릿(서식)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr "Tenor API 키"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr "Tenor GIF API 키"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr "Tenor GIF 콘텐츠 필터"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr "Tenor GIF 제한"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr "Tenor GIF 제한"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr "Tenor 콘텐츠 필터"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "시험 & 확인"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr "테스트 기록:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr "텍스트 본문"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "The"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "The 'Due Date In' value can't be negative."
msgstr "'만기일'은 음수일 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
msgid ""
"The 'To-Do' activity type is used to create reminders from the top bar menu "
"and the command palette. Consequently, it cannot be archived or deleted."
msgstr ""
"'To-Do' 작업 유형은 상단 표시줄 메뉴와 명령 팔레트에서 미리 알림을 생성하는 데 사용됩니다. 따라서 보관하거나 삭제할 수 "
"없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
msgid "The Fullscreen mode was denied by the browser"
msgstr "브라우저에서 전체 화면 모드가 거부되었습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_extension_service.js:0
msgid ""
"The Push-to-Talk feature is only accessible within tab focus. To enable the "
"Push-to-Talk functionality outside of this tab, we recommend downloading our"
" %(anchor_start)sextension%(anchor_end)s."
msgstr ""
"푸시 투 토크 기능은 탭 포커스 내에서만 액세스할 수 있습니다. 이 탭 외부에서 푸시 투 토크 기능을 사용하려면  "
"%(anchor_start)s확장%(anchor_end)s을 다운로드하는 것이 좋습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"세무 신고용 등록 번호입니다. 국가별로 지정된 형식을 기준으로 값을 검증합니다. 협력사가 과세 대상이 아닌 경우에는 '/'를 입력하세요."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The activity cannot be launched:"
msgstr "활동을 시작할 수 없습니다:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""
"\"%(activity_type_name)s\"의 활동 유형은 \"%(plan_name)s\"과 호환될 수 없습니다. 이 활동 유형은 "
"현재 \"%(activity_type_model)s\"의 모델로 제한되어 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "첨부 파일인 %s가 존재하지 않거나 사용자가 접근 권한이 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "The attachment %s does not exist."
msgstr "%s 첨부 파일이 존재하지 않습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.js:0
msgid "The avatar has been updated!"
msgstr "아바타가 업데이트되었습니다!"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "채널 UUID는 고유해야 합니다."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr "채널 유형은 공란으로 둘 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "The conversation is empty."
msgstr "대화가 비어 있습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr "현재 사용자는 서식을 편집할 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "The duration of voice messages is limited to 1 minute."
msgstr "음성 메시지의 길이는 1분으로 제한됩니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "다음에게 보낸 이메일"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
msgid "The email template(s) have been restored to their original settings."
msgstr "이메일 템플릿이 기본 설정으로 복원되었습니다."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_push_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr "엔드포인트는 고유해야 합니다!"

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""
"관련 소스 파일이 없으므로 다음의 이메일 서식을 재설정할 수 없습니다:\n"
"- %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "해당 연락처를 담당하는 내부 사용자입니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr "이 별칭에서 받은 최근 메시지로 인하여 오류가 발생했습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr "아래의 메시지는 주소에서 받을 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"아래의 메시지는 %(alias_display_name)s 주소에서 받을 수 없습니다.\n"
"                 %(contact_description)s 주소로만 연락할 수 있습니다.<br /><br />\n"
"                  정확한 주소인지 확인하시거나 %(default_email)s 주소로 대신 문의주시기 바랍니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"아래의 메시지는 %(alias_display_name)s 주소에서 받을 수 없습니다.\n"
"나중에 다시 시도하시거나 %(company_name)s 주소로 대신 문의주시기 바랍니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"The message scheduled on %(model)s(%(id)s) with the following content could "
"not be sent:%(original_message)s"
msgstr "%(model)s(%(id)s)에 %(original_message)s 내용으로 예약된 메시지가 전송되지 못했습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__from_message_id
msgid "The message the channel was created from."
msgstr "채널이 생성된 메시지입니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"이 별칭에 해당하는 모델(Odoo 문서 종류)입니다. 모든 받는 메일은 기존 레코드로 기록되지 않고 이 모델의 새 레코드로 작성됩니다. "
"(예. 프로젝트 작업)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"<<EMAIL>>에 대한 이메일을 수신하도록 하려면 이메일의 별칭 이름을 적어주세요. 예: 'jobs'"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr "\"%(plan_name)s\" 계획을 시작할 수 없습니다:"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" has been started"
msgstr "\"%(plan_name)s\" 계획이 시작되었습니다"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "대기열 관리자가 해당일 이후에 이메일을 전송합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The records must belong to the same company."
msgstr "레코드는 같은 회사에 속해야 합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_message.py:0
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %(type)s, Operation: %(operation)s)\n"
"\n"
"Records: %(records)s, User: %(user)s"
msgstr ""
"보안 제한으로 인해 요청하신 작업을 완료할 수 없습니다. 시스템 관리자에게 문의하세요.\n"
"\n"
"(문서 유형: %(type)s, 작업: %(operation)s)\n"
"\n"
"레코드: %(records)s, 사용자: %(user)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "The server \"%s\" cannot be used because it is archived."
msgstr "\"%s\" 서버는 보관 처리되었기 때문에 사용할 수 없습니다. "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "The subscription preferences were successfully applied."
msgstr "구독 추가 설정이 성공적으로 적용되었습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr "이 서식은 이 사용자에게 속합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr "본문의 텍스트 전용 시작 부분을 이메일 미리보기에 사용합니다."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr "채널 구성원 당 RTC 세션은 하나만 허용합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "해당"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action can only be done on a mail thread models"
msgstr "이 작업은 메일 스레드 모델에서만 수행할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action cannot be done on transient models."
msgstr "임시 모델에서는 이 작업을 할 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "This action will send an email."
msgstr "이 작업은 전자 메일을 보냅니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This channel doesn't have any attachments."
msgstr "이 채널에는 첨부파일이 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This channel doesn't have any pinned messages."
msgstr "이 채널에는 핀고정된 메시지가 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "This channel has no thread yet."
msgstr "아직 이 채널에는 스레드가 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This conversation doesn't have any attachments."
msgstr "이 대화에는 첨부파일이 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This conversation doesn't have any pinned messages."
msgstr "이 대화에는 핀 고정한 메시지가 없습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "이 메일은 대량메일에서 블랙리스트에 올라있습니다. 블랙리스트를 해제하세요."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "이 필드는 대소문자를 구분하지 않습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr "이 필드는 서식 사용에 대한 내부 설명용으로 사용합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr "이 필드는 기본 이메일 필드에 이메일 주소 이상을 포함할 수 있으므로 이메일 주소를 검색하는 데 사용됩니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "This layout seems to no longer exist."
msgstr "이 레이아웃은 더 이상 존재하지 않습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message_model.js:0
msgid "This message has already been sent."
msgstr "이 메시지는 이미 전송되었습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"이 옵션은 Odoo 데이터베이스의 저장 공간을 확보하기 위해 설정에 있는 기술 메뉴에서 발신된 것을 포함하여 보낸 메일 기록 전체를 "
"영구적으로 삭제합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
msgid "This record has an exception activity."
msgstr "이 레코드에는 예외 활동이 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid ""
"This setting will be applied to all channels using the default notification "
"settings."
msgstr "이 설정은 기본 알림 설정을 사용하는 모든 채널에 적용됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__channel_notifications
msgid ""
"This setting will only be applied to channels. Mentions only if not "
"specified."
msgstr "이 설정은 채널에만 적용됩니다. 지정하지 않은 경우에만 멘션됩니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr "해당 값은 렌더링에 옵션으로 지원되지 않습니다: %(param_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr "해당 값은 게시 또는 알림 시 지원되지 않습니다: %(param_names)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "스레드"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread Image"
msgstr "스레드 이미지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread has unread messages"
msgstr "스레드에 읽지 않은 메시지가 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Thread image"
msgstr "스레드 이미지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr "스레드 사용 가능"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/thread_actions.js:0
msgid "Threads"
msgstr "스레드"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_tz
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "시간대"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "제목"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "To"
msgstr "종료 시간"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "받는 사람 (이메일)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "받는 사람 (협력사)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
msgid "To :"
msgstr "받는 사람:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "To peer:"
msgstr "수신 동료:"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr "해야 할 일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "To:"
msgstr "받는 사람 :"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__today
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Today"
msgstr "오늘"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "오늘 활동"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Today at %(time)s"
msgstr "오늘 %(time)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Today:"
msgstr "오늘:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Tomorrow"
msgstr "내일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Afternoon"
msgstr "내일 오후"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Morning"
msgstr "내일 아침"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Tomorrow:"
msgstr "내일:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "그룹에서 논의할 주제 등을 기록합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr "수신자 추적"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr "추적된 값은 별도의 모델에 저장됩니다. 이 필드를 사용하면 추적을 재구성하고 모델에 대한 통계를 생성할 수 있습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "추적"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "추적값"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "추적값"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "추적값"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Translate"
msgstr "번역"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr "번역 본문"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Translation Failure"
msgstr "번역 실패"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_from
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "자동 실행"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "다음 활동 트리거"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr "다시 시도하세요"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Turn camera on"
msgstr "카메라 켜기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Turn on notifications"
msgstr "알림 켜기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Twilio 계정 인증용 토큰"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "Twilio 계정 SID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "유형"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "지연 유형"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"서버 작업 유형입니다. 다음 값을 사용할 수 있습니다:\n"
"- '레코드 업데이트': 레코드 값을 업데이트합니다.\n"
"- '활동 만들기': 활동을 생성합니다. (토론)\n"
"- '이메일 보내기': 메시지, 메모를 게시하거나 이메일을 전송합니다. (토론)\n"
"- 'SMS 보내기': SMS를 보내고, 문서에 기록합니다. (SMS)- '팔로워 추가/제거': 레코드에 팔로워를 추가하거나 제거합니다 .(토론)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Type the name of a person"
msgstr "이름을 입력하세요."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid "Unable to connect to SMTP Server"
msgstr "SMTP 서버에 연결할 수 없습니다"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr "메시지를 게시할 수 없습니다. 보낸 사람의 이메일 주소를 구성하십시오."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Unable to send message, please configure the sender's email address."
msgstr "메시지를 보낼 수 없습니다. 이메일 발신 주소를 설정하시기 바랍니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign"
msgstr "할당 취소"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign from me"
msgstr "나에게서 할당 취소"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "수신 거부 해제"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
msgid "Unblock Reason: %(reason)s"
msgstr "차단 해제 사유: %(reason)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Undeafen"
msgstr "음소거 해제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Unfollow"
msgstr "팔로우 취소"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Unit"
msgstr "단위"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "지연 단위"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
msgid "Unknown"
msgstr "알 수 없음"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
msgid "Unknown error"
msgstr "알 수 없는 오류"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Unknown error: %(error)s"
msgstr "알 수 없는 오류: %(error)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Unmute"
msgstr "소리 켜기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Unmute Conversation"
msgstr "대화 음소거 해제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Unpin"
msgstr "핀 고정 해제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Conversation"
msgstr "대화 핀고정 해제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Unpin Message"
msgstr "메시지 핀고정 해제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Thread"
msgstr "스레드 고정 해제"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__unpin_dt
msgid "Unpin date"
msgstr "핀 해제 날짜"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "읽지 않은 메세지 수"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "읽지 않은 메시지"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr "첨부파일 제한 해제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Unselect person"
msgstr "선택 취소된 사람"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Unstar all"
msgstr "별표 전체 취소"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Unsupported report type %s found."
msgstr "지원되지 않는 보고서 %s 유형이 발견되었습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until %s"
msgstr "%s까지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until I turn it back on"
msgstr "다시 켤 때까지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Untitled"
msgstr "제목 없음"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr "메일 레이아웃 업데이트"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Update Template"
msgstr "템플릿 업데이트"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Upload Avatar"
msgstr "아바타 업로드"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "문서 업로드"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload File"
msgstr "파일 업로드"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload file"
msgstr "파일 업로드"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Upload:"
msgstr "업로드:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploaded"
msgstr "업로드함"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploading"
msgstr "업로드 중"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""
"다음 작업에서 항상 동일한 사용자를 할당하려면 '특정 사용자'를 사용합니다. '동적 사용자'를 사용하여 레코드에서 선택할 사용자의 필드 "
"이름을 지정합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "사용자 지정 이메일 서버 사용"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Use Default"
msgstr "기본값 사용"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "Twilio ICE 서버 사용"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "Gmail 서버 사용"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Use a local script to fetch your emails and create new records."
msgstr "로컬 스크립트를 사용하여 이메일을 가져오고 새로운 레코드를 생성합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr "Outlook 서버 사용"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid ""
"Use default from user settings if not specified. This setting will only be "
"applied to channels."
msgstr "지정하지 않으면 사용자 설정에서 기본값을 사용합니다. 이 설정은 채널에만 적용됩니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr "메일 별칭용으로 다른 도메인 사용"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr "일괄 사용"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Use message_notify to send a notification to an user."
msgstr "message_notify를 사용하여 사용자에게 알림을 전송합니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "서식 사용"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "PTT (push-to-talk:길게 눌러 말하기) 기능 사용"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "다음에서 사용"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr "메일쓰기 도메인을 평가하는 컨텍스트로 사용됨"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"메세지 작성을 분류하는 데 사용됩니다.\n"
"'email': 수신 이메일에 의해 작성됩니다. (예: 메일 게이트웨이)\n"
"'comment': 토론 또는 작성란을 통해 사용자가 입력할 수 있습니다.\n"
"'email_outgoing': 메일링에 의해 작성됩니다.\n"
"'notification': 시스템에서 생성됩니다. (예: 메시지 추적)\n"
"'auto_comment': 자동화된 알림 메커니즘에 의해 생성됩니다. (예: 승인)\n"
"'user_notification': 특정 수신자를 위해 생성됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "금전적인 가치를 추적할 때 통화를 표시하기 위해 사용됨"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "하위 유형 정리시 사용"

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "사용자"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr "사용자 필드"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "사용자 출석"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "사용자 설정"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "사용자 설정"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "사용자 볼륨 설정"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "사용자별 알림"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "사용자 유형"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is a bot"
msgstr "봇 사용자입니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is idle"
msgstr "사용자가 유휴 상태입니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is offline"
msgstr "사용자가 오프라인 상태입니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is online"
msgstr "사용자 온라인"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr "사용자가 즐겨찾는 GIF는 중복될 수 없습니다."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "사용자 이름"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Users in this channel: %(members)s."
msgstr "이 채널의 사용자: %(members)s."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"사용자는 서식 렌더링을 계속 사용할 수 있습니다.\n"
"다만 메일 서식 편집기에서만 새로운 동적 서식을 생성하거나 기존 서식을 수정할 수 있습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"커뮤니티 및 유료버전에서 이메일을 보내고 받으려면 전자 메일 서버를 사용해야 합니다. 온라인 사용자는 이미 가입하면 바로 사용가능한 "
"이메일 서버(@ mycompany.odoo.com)를 이미 활용하고 있습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "유효"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"`mail.catchall.domain.allowed`에 대한 %(allowed_domains)s의 값을 확인할 수 없습니다.\n"
"도메인 목록은 쉼표로 구분되어야 합니다. (예: example.com,example.org.)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Video Settings"
msgstr "동영상 설정"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Video player:"
msgstr "동영상 플레이어"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
msgid "View"
msgstr "화면"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "View %s"
msgstr "%s 화면"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "View Profile"
msgstr "프로필 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "View Reactions"
msgstr "반응 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
msgid "View Thread"
msgstr "스레드 보기"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "화면 유형"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "View all activities"
msgstr "전체 활동 보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "View or join channels"
msgstr "채널 보기 또는 가입"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "음성"

#. module: mail
#: model:ir.ui.menu,name:mail.menu_call_settings
msgid "Voice & Video"
msgstr "음성 및 비디오"

#. module: mail
#: model:ir.actions.client,name:mail.discuss_call_settings_action
msgid "Voice & Video Settings"
msgstr "음성 및 비디오 설정"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice Detection"
msgstr "음성 인식"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Voice Message"
msgstr "음성 메시지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice detection threshold"
msgstr "음성 인식 임계값"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "Voice recording stopped"
msgstr "음성 녹음 중지"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice settings"
msgstr "음성 설정"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "부피"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "협력사 당 볼륨"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "다른 협력사 볼륨"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr "GIF로 대화에 재미를 불어넣고 싶으신가요? 설정에서 기능을 활성화하세요!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "Warning"
msgstr "주의"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""
"별칭 %(alias_name)s을 만들 수 없습니다. 도메인 %(alias_domain_name)s이 "
"%(alias_company_names)s에 포함되어 있으며, 소유자의 문서가 %(company_name)s에 포함되어 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""
"별칭 %(alias_name)s을 만들 수 없습니다. 도메인 %(alias_domain_name)s이 "
"회사%(alias_company_names)s에 포함되어 있고, 대상 문서가 %(company_name)s에 포함되어 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "We were not able to fetch value of field '%(field)s'"
msgstr "'%(field)s' 필드값을 가져올 수 없습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "주"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr "MyCompany에 오신 것을 환영합니다!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr "메시지 핀 고정을 해제할까요?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "What's your name?"
msgstr "이름이 무엇입니까?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__user_tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""
"문서를 인쇄하고 데이터를 내보내거나 가져올 때 기준이 되는 시간값을 설정합니다.\n"
"시간대를 따로 설정하지 않은 경우 UTC (협정세계시)가 기본으로 적용됩니다.\n"
"기타 상황에서는 웹 클라이언트의 시간 오프셋에 따라 시간을 계산합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr "'기본 사용자'에게 할당을 선택할 때는 담당자를 지정해야 합니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"각 이메일의 전체 원본을 참조용으로 보관하고 처리된 각 메시지에 첨부할지 여부. 이것은 대개 메시지 데이터베이스의 크기를 두 배로 "
"늘립니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr "첨부 파일이 다운로드 될지 여부를 나타냅니다. 활성화하지 않으면 수신 이메일이 처리되기 전에 첨부 파일이 제거됩니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr "모든 수신자를 표시할지 중요한 수신자만 표시할지 여부를 설정합니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Write /field to insert dynamic content"
msgstr "동적 콘텐츠를 삽입하려면 /field를 입력하세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Write Feedback"
msgstr "피드백 작성"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Write your message here..."
msgstr "여기에 메시지를 작성하십시오."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Wrong operation name (%s)"
msgstr "잘못된 작업명 (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yeah, pin it!"
msgstr "네, 핀 고정합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Yes"
msgstr "예"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yes, remove it please"
msgstr "네, 해제합니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Yesterday"
msgstr "어제"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Yesterday at %(time)s"
msgstr "어제 %(time)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Yesterday:"
msgstr "어제:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"대화창에서 나가시면 더 이상 그룹 대화에 참여할 수 없으며, 참여를 원하실 경우 다시 초대를 받으셔야 합니다. 계속하시겠습니까?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are alone in a private conversation."
msgstr "비공개 대화에 혼자 참여 중입니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are alone in this channel."
msgstr "채널에 아무도 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in a private conversation with %(member_names)s."
msgstr "%(member_names)s님과 비공개 대화 중입니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr "채널 %(bold_start)s#%(channel_name)s%(bold_end)s에 참여 중입니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "You are no longer following \"%(thread_name)s\"."
msgstr "더이상 \"%(thread_name)s\"를 팔로우하고 있지 않습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"You are not allowed to change the target record of a scheduled message."
msgstr "예약된 메시지의 대상 레코드를 수정할 수 있는 권한이 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "You are not allowed to send this scheduled message"
msgstr "이 예약 메시지를 보낼 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload an attachment here."
msgstr "여기에 첨부 파일을 업로드할 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload attachments on this channel."
msgstr "이 채널에는 첨부 파일을 업로드할 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "현재 이 채널의 관리자입니다. 정말로 탈퇴하시겠습니까?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr "'별표' 표시된 메시지가 이 메일함에 표시됩니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "You can not write on %(field_name)s."
msgstr "%(field_name)s에는 쓸 수 없습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with existing users."
msgstr "기존 사용자와만 채팅하실 수 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with partners that have a dedicated user."
msgstr "지정 사용자가 있는 협력사와만 채팅하실 수 있습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr "이 메시지는 무시하셔도 됩니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr "해당 그룹을 삭제할 수 없습니다. 전체 회사 그룹은 다른 모듈에서 사용합니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr "별칭 주소%(alias_name)s에는 악센트가 제거된 라틴 문자 외에 다른 문자는 사용할 수 없습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr "도메인 이름 %(domain_name)s에는 악센트가 제거된 라틴 문자 외에 다른 문자는 사용할 수 없습니다."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr "다음에 대한 액세스 권한이 없습니다"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr "이메일 차단을 해제할 수 있는 권한이 없습니다. 관리자에게 문의해 주세요."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "You have been assigned to %s"
msgstr "%s에 할당되었습니다"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "다음에 할당되었습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_core_public_web_service.js:0
msgid "You have been invited to #%s"
msgstr "#%s에 초대되었습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr "이 서식에 파일을 첨부할 수 있습니다. 이 서식으로 작성된 모든 이메일에 추가할 수 있습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "You may not define a template on an abstract model: %s"
msgstr "추상 모델에서 템플릿을 정의할 수 없습니다: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr "이 메시지를 %(conversation)s로 핀 고정할까요?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned %(conversation_name)s"
msgstr "%(conversation_name)s의 고정을 해제했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned your conversation with %(user_name)s"
msgstr "%(user_name)s과의 대화를 고정 해제했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unsubscribed from %s."
msgstr "%s의 구독을 취소했습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a chat!"
msgstr "채팅에 초대되었습니다!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a meeting!"
msgstr "미팅에 초대되었습니다!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "You:"
msgstr "나:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "귀하"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr "계정 이메일이 %(old_email)s에서 %(new_email)s로 변경되었습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account login has been updated"
msgstr "계정 로그인 정보가 업데이트되었습니다"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account password has been updated"
msgstr "계정 비밀번호가 변경되었습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your browser does not support videoconference"
msgstr "이 브라우저는 화상 회의를 지원하지 않습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support voice activation"
msgstr "이 브라우저는 음성 활성화 기능을 지원하지 않습니다"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support webRTC."
msgstr "이 브라우저는 webRTC를 지원하지 않습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Your inbox is empty"
msgstr "받은 편지함이 비어 있습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your name"
msgstr "귀하의 성명"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
msgid "[%(name)s] (no email address)"
msgstr "[%(name)s] (이메일 주소 없음)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "addresses linked to registered partners"
msgstr "등록된 파트너에 연결된 주소"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "완료일 이후"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "이전 활동 마감 후"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "alias"
msgstr "별칭"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "alias %(name)s: %(error)s"
msgstr "별칭 %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr "이 이메일의 첨부 파일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "available bitrate:"
msgstr "사용 가능한 비트 전송률:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "back"
msgstr "뒤로"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""
"지난 몇 분 동안\n"
"                너무 많은 연락을 시도하셨습니다.\n"
"                <br/>\n"
"                나중에 다시 시도해 주십시오."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "작성자"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "camera"
msgstr "카메라"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"항목을 처리할 수 없습니다.\n"
"          답장을 수신하기 위한 것이며 직접적인 연락을 위해 사용해서는 안 됩니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "channels"
msgstr "채널"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "clock rate:"
msgstr "클록 속도:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "codec:"
msgstr "코덱:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "created this channel."
msgstr "이 채널을 생성했습니다."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__days
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "일"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days overdue:"
msgstr "일 연체됨:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days:"
msgstr "일:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "deaf"
msgstr "청각 장애가 있는 사람"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__mail_push_device_id
msgid "devices"
msgstr "장치"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "document"
msgstr "문서"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "완료"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down DTLS:"
msgstr "다운 DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down ICE:"
msgstr "다운 ICE:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "e.g \"Asked to receive our next newsletters\""
msgstr "예: \"다음 뉴스레터 수신 요청\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr "예: 업무 협의 제안"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr "예. \"제안을 검토하고 세부 사항을 논의하세요\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr "예. \"환영 이메일\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr "예. \"MyCompany에 오신 것을 환영합니다\" 또는 \"반갑습니다, {{ object.name }}님\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr "e.g. \"bounce\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr "예: \"catchall\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "예: \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr "예: \"알림\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "예: 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "예: ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr "예. 연락처"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Discuss Proposal"
msgstr "예: 업무 협의 제안"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "예: 업무 협의 제안"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
msgid "e.g. Log a note"
msgstr "예: 노트 기록"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "예: 신입 사원 연수 프로그램"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "예. 회의 예약하기"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. mycompany.com"
msgstr "예: mycompany.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "예: 고객 지원"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "예. true.true..f"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr "예: user_id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr "예: \"<EMAIL>\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g: Send order confirmation"
msgstr "예: 주문 확인서 보내기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "for"
msgstr "for"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "항목은 다음의 작업에서 생성되었습니다:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "다음에서 변경되었습니다:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr "을 통해 다음 활동이 배정되었습니다."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr "https://developers.google.com/tenor/guides/content-filtering"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "in a few seconds"
msgstr "몇 초 후"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias"
msgstr "잘못 구성된 별칭"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias (unknown reference record)"
msgstr "잘못 구성된 별칭 (알 수 없는 참조 레코드)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "invited %s to the channel"
msgstr "%s 사용자를 채널에 초대했습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "joined the channel"
msgstr "사용자가 채널에 들어왔습니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "left the channel"
msgstr "사용자가 채널에서 나갔습니다."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list"
msgstr "목록"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list-item"
msgstr "리스트-항목"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "live"
msgstr "실시간"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "media player Error"
msgstr "미디어 플레이어 오류"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "microphone"
msgstr "마이크"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "model %s does not accept document creation"
msgstr "%s 모델이 문서 생성을 허용하지 않습니다"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__months
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "월"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "more"
msgstr "더보기"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "ms"
msgstr "ms"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "muted"
msgstr "무음"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "new"
msgstr "신규"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "no connection"
msgstr "무선"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "now"
msgstr "지금"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "on"
msgstr "대상: "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "on:"
msgstr ", 관련:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
msgid "or"
msgstr "또는"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "or press %(send_keybind)s"
msgstr "또는 %(send_keybind)s를 누르세요."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "other members."
msgstr "다른 회원."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
msgid "props.action.title"
msgstr "props.action.title"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "raising hand"
msgstr "손바닥"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr "누락된 문서(%(model)s,%(thread)s)에 대한 회신, 문서 작성 시점으로 돌아갑니다"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr "%s 모델에 회신이 문서 업데이트를 수락하지 않습니다. 문서 작성 시점으로 돌아갑니다."

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to followers"
msgstr "팔로워로 제한"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to known authors"
msgstr "알려진 소유자로 제한"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "results out of"
msgstr "결과 중"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "screen"
msgstr "화면"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "some specific addresses"
msgstr "일부 특정 주소"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "stun:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "target model unspecified"
msgstr "지정되지 않은 대상 모델"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "팀."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "template"
msgstr "서식"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "turn:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown error"
msgstr "알 수 없는 오류"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown target model %s"
msgstr "알 수없는 대상 모델 %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up DTLS:"
msgstr "업 DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up ICE:"
msgstr "업 ICE:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "users"
msgstr "사용자"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "view"
msgstr "화면"

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr "웹소켓 메시지 처리"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__weeks
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "주"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "you"
msgstr "귀하"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "your alias"
msgstr "별칭"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr "“%(member_name)s”의 “%(channel_name)s”"
