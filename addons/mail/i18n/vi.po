# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON>, 2024
# W<PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Thêm địa chỉ vào Danh sách được phép\n"
"            </p><p>\n"
"                Để bảo vệ bạn khỏi thư rác và nhóm trả lời, Odoo tự động chặn các email\n"
"                đến cổng email của bạn vượt quá ngưỡng <b>%(threshold)i</b> email sau mỗi <b>%(minutes)i</b>\n"
"                phút. Tuy nhiên, nếu có một số địa chỉ mà bạn cần nhận cập nhật thường xuyên,\n"
"                thì bạn có thể thêm chúng dưới đây và Odoo sẽ cho phép chúng đi tới hộp thư của bạn.\n"
"            </p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr "\" không còn được theo dõi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr "\"%(activity_name)s: %(summary)s\" được phân công cho bạn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr "\"%(hostname)s\" cần truy cập vào mic của bạn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "\"%(hostname)s\" requires microphone access"
msgstr "\"%(hostname)s\" yêu cầu quyền truy cập micrô"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr ""
"%(activity)s, được phân công cho %(name)s, có thời hạn vào %(deadline)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "%(author name)s from %(channel name)s"
msgstr "%(author name)s từ %(channel name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s không được công nhận là email hợp lệ. Đây là điều kiện bắt buộc để"
" tạo một khách hàng mới."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person)s"
msgstr "%(emoji)s được bày tỏ cảm xúc bởi %(person)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s and %(person2)s"
msgstr "%(emoji)s được bày tỏ cảm xúc bởi %(person1)s và %(person2)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and %(count)s "
"others"
msgstr ""
"%(emoji)s được bày tỏ cảm xúc bởi %(person1)s, %(person2)s, %(person3)s, và "
"%(count)s người khác"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid ""
"%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and 1 other"
msgstr ""
"%(emoji)s được bày tỏ cảm xúc bởi %(person1)s, %(person2)s, %(person3)s, và "
"1 người khác"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_list.js:0
msgid "%(emoji)s reacted by %(person1)s, %(person2)s, and %(person3)s"
msgstr ""
"%(emoji)s được bày tỏ cảm xúc bởi %(person1)s, %(person2)s, và %(person3)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "%(model_name)s.%(field_path)s does not seem to be a valid field path"
msgstr ""
"%(model_name)s.%(field_path)s có vẻ không phải là đường dẫn trường hợp lệ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%(name)s: %(message)s)"
msgstr "%(name)s: %(message)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(new_line)s%(new_line)sType %(bold_start)s@username%(bold_end)s to mention "
"someone, and grab their attention.%(new_line)sType "
"%(bold_start)s#channel%(bold_end)s to mention a channel.%(new_line)sType "
"%(bold_start)s/command%(bold_end)s to execute a command.%(new_line)sType "
"%(bold_start)s:shortcut%(bold_end)s to insert a canned response in your "
"message."
msgstr ""
"%(new_line)s%(new_line)sNhập %(bold_start)s@username%(bold_end)s để nhắc đến"
" ai đó và thu hút sự chú ý của họ.%(new_line)sNhập "
"%(bold_start)s#channel%(bold_end)s để nhắc đến một kênh.%(new_line)sNhập "
"%(bold_start)s/command%(bold_end)s để thực thi một lệnh. %(new_line)sNhập "
"%(bold_start)s:shortcut%(bold_end)s để chèn một câu trả lời soạn sẵn trong "
"nội dung của bạn."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr ""
"%(open_button)s%(icon)s%(open_em)sHuỷ chỉnh sửa%(close_em)s%(close_button)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sThoát%(close_samp)s %(open_em)sđể "
"%(open_cancel)shuỷ%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sđể %(open_save)slưu%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sThoát%(close_samp)s %(open_em)sđể "
"%(open_cancel)shuỷ%(close_cancel)s%(close_em)s, "
"%(open_samp)sNhập%(close_samp)s %(open_em)sđể "
"%(open_save)slưu%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.js:0
msgid "%(recipientCount)s more"
msgstr "Thêm %(recipientCount)s "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr ""
"%(user)s đã được kết nối. Đây là lần kết nối đầu tiên của họ. Hãy chúc họ "
"may mắn nào!"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"%(user)s started a thread: %(goto)s%(thread_name)s%(goto_end)s. "
"%(goto_all)sSee all threads%(goto_all_end)s."
msgstr ""
"%(user)s đã bắt đầu một chủ đề: %(goto)s%(thread_name)s%(goto_end)s. "
"%(goto_all)sXem tất cả chủ đề %(goto_all_end)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s and %(user2)s are typing..."
msgstr "%(user1)s và %(user2)s đang nhập..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%(user1)s, %(user2)s and more are typing..."
msgstr "%(user1)s, %(user2)s và những người dùng khác đang nhập..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "%(user_name)s pinned a message to this channel."
msgstr "%(user_name)s đã ghim một tin nhắn vào kênh này."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
msgid "%s (Email Template)"
msgstr "%s (Mẫu email)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan.py:0
#: code:addons/mail/models/mail_template.py:0
msgid "%s (copy)"
msgstr "%s (sao chép)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "%s created"
msgstr "%s được tạo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "%s days overdue"
msgstr "%s ngày quá hạn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
msgid "%s is typing..."
msgstr "%s đang gõ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_result.js:0
msgid "%s messages found"
msgstr "%s tin nhắn được tìm thấy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "%s new messages"
msgstr "%s tin nhắn mới"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s raised their hand"
msgstr "%s đã giơ tay"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "%s started a live conference"
msgstr "%s đã bắt đầu cuộc họp online"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"camera\" access"
msgstr "%s\" yêu cầu quyền truy cập \"camera\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "%s\" requires \"screen recording\" access"
msgstr "%s\" yêu cầu quyền truy cập tính năng \"quay màn hình\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_attachment_links
msgid "&amp;#128229;"
msgstr "&amp;#128229;"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translated from: %(language)s)"
msgstr "(Được dịch từ: %(language)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "(Translation Failure: %(error)s)"
msgstr "(Lỗi dịch: %(error)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "(edited)"
msgstr "(đã chỉnh sửa)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "(from"
msgstr "(từ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(chỉ định ban đầu cho"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid ") added you as a follower of this"
msgstr ") đã thêm bạn làm người theo dõi của"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid ". Narrow your search to see more choices."
msgstr ". Thu hẹp tìm kiếm để có nhiều lựa chọn hơn."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid "1 new message"
msgstr "1 tin nhắn mới"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr ""
"<b invisible=\"not no_record\" class=\"text-warning\">Không có dữ liệu cho "
"mô hình này</b>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.js:0
msgid ""
"<button>Change your preferences</button> to receive new notifications in "
"your inbox."
msgstr ""
"<button>Thay đổi tùy chọn của bạn</button> để nhận thông báo mới trong hộp "
"thư đến."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-check\"/> Done"
msgstr "<i class=\"fa fa-check\"/> Hoàn tất"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_kanban
msgid ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"Steps count\" "
"title=\"Steps count\"/>"
msgstr ""
"<i class=\"fa fa-cogs fa-fw me-2\" role=\"img\" aria-label=\"Số bước\" "
"title=\"Số bước\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Url dữ liệu\"/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
msgid "<i class=\"fa fa-times\"/> Cancel"
msgstr "<i class=\"fa fa-times\"/> Huỷ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Chat với đồng nghiệp</b> theo thời gian thực sử dụng thông điệp trực "
"tiếp.</p><p><i>Bạn có thể cần phải mời người dùng từ ứng dụng Thiết lập "
"trước để họ có tài khoản người dùng trong Hệ thống.</i></p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Viết một thông điệp</b> đến thành viên của kênh ở đây.</p> <p>Bạn có "
"thể \"chộp\" ai đó vào thảo luận bằng cách gõ <i>'@'</i> hoặc liên kết một "
"kênh khác vào bằng cách gõ <i>'#'</i>. Bắt đầu thông điệp của bạn bằng "
"<i>'/'</i> để nhận danh sách các lệnh khả dụng.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Các kênh cho phép bạn đơn giản hoá việc tổ chức thông tin trải qua các "
"chủ để và các nhóm khác nhau.</p> <p>Hãy thử <b>tạo kênh đầu tiên của "
"bạn</b> (vd: bán hàng, marketing, sản phẩm XYZ, liên hoan, ăn chơi nhảy múa,"
" etc).</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a channel here.</p>"
msgstr "<p>Tạo kênh mới.</p>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>Tạo kênh công cộng hoặc riêng tư.</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Màu nút</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Màu header</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr "<span class=\"me-1 oe_inline\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr "<span class=\"me-1\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr "<span class=\"o_stat_text\">Mở tài liệu</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr "<span class=\"o_stat_text\">Mở tài liệu chính</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                Tin nhắn sẽ được gửi dưới dạng email cho người nhận mẫu\n"
"                                và sẽ không xuất hiện trong lịch sử nhắn tin.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                Tin nhắn sẽ được đăng dưới dạng ghi chú nội bộ mà người dùng\n"
"                                nội bộ có thể nhìn thấy trong lịch sử nhắn tin.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                Tin nhắn sẽ được đăng dưới dạng tin nhắn trong bản ghi để thông báo\n"
"                                cho tất cả người theo dõi. Nó sẽ xuất hiện trong lịch sử nhắn tin.\n"
"                            </span>"

#. module: mail
#: model_terms:web_tour.tour,rainbow_man_message:mail.discuss_channel_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Tốt lắm!</b> Bạn đã xem qua tất cả các bước của tour hướng dẫn "
"này.</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr "<span>Nếu việc này do bạn thực hiện:</span><br/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr "<span>Nếu việc này không phải bạn thực hiện:</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr "<span>Mở bản ghi</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr "<span>Chúng tôi khuyên bạn nên bắt đầu bằng</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>Lưu</strong> trang này và quay lại đây để cài đặt tính năng này."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr "<strong>Bạn không còn theo dõi tài liệu:</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Một từ điển Python sẽ được đánh giá để cung cấp giá trị mặc định khi tạo bản"
" ghi mới cho bí danh này. "

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A Scheduled Message cannot be scheduled in the past"
msgstr ""
"Không thể lên lịch Tin nhắn đã lên lịch vào một thời điểm trong quá khứ"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "Sự hiện diện bus phải có người dùng hoặc khách."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "Thành viên kênh phải là đối tác hoặc khách."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "Kênh thuộc kiểu 'chat' không thể có quá hai người dùng."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "Không nên tạo chat cho hơn 2 người. Hãy tạo nhóm."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "A message can only be scheduled in monocomment mode"
msgstr "Một tin nhắn chỉ có thể được lên lịch ở chế độ bình luận đơn"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"A message cannot be scheduled on a model that does not have a mail thread."
msgstr ""
"Không thể lên lịch gửi tin nhắn trên một mô hình không có chủ đề email."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "Phản ứng với tin nhắn phải từ đối tác hoặc khách."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "A next activity can only be planned on models that use activities."
msgstr ""
"Hoạt động tiếp theo chỉ có thể được lên kế hoạch trên các mô hình sử dụng "
"hoạt động."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "A scheduled message could not be sent"
msgstr "Không thể gửi tin nhắn đã lên lịch"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""
"Cần có mã khóa Google API hợp lệ để bật tính năng dịch tin nhắn. "
"https://cloud.google.com/translate/docs/setup"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "Cài đặt âm lượng phải có đối tác hoặc khách."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_chatgpt.js:0
msgid "AI"
msgstr "AI"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept"
msgstr "Chấp nhận"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Accept with camera"
msgstr "Chấp nhận với camera"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Access Denied"
msgstr "Truy cập bị từ chối"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Nhóm truy cập"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Token truy cập"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr "Quyền truy cập được giới hạn cho nhóm \"%(groupFullName)s\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Account"
msgstr "Tài khoản"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Tác vụ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Chế độ xem cửa sổ tác vụ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Actions"
msgstr "Tác vụ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Tác vụ có thể kích hoạt thao tác cụ thể như mở chế độ xem lịch hoặc tự động "
"đánh dấu là hoàn thành khi tài liệu được tải lên"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "Tác vụ cần Thực hiện khi có Email gửi đến"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Được kích hoạt mặc định khi đăng ký (subscribing)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "Đang hoạt động"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "Miền hiệu lực"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities_section
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activities"
msgstr "Hoạt động"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Create"
msgstr "Hoạt động cần tạo"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr "Hoạt động phải được liên kết với các bản ghi có res_id không rỗng."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_kanban_open_target
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity"
msgstr "Hoạt động"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hoạt động ngoại lệ"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Hoạt động Mixin"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.ui.menu,name:mail.menu_mail_activities
msgid "Activity Overview"
msgstr "Tổng quan hoạt động"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr "Kế hoạch hoạt động"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "Kế hoạch hoạt động"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "Cài đặt hoạt động"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Loại hoạt động"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Activity Type Name"
msgstr "Tên loại hoạt động"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Loại hoạt động"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Mẫu kế hoạch hoạt động"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Prospect Follow-up\", \"Project Milestone Meeting\", ...)"
msgstr ""
"Kế hoạch hoạt động được sử dụng để chỉ định danh sách hoạt động chỉ trong vài cú nhấp chuột\n"
"                 (VD: \"Onboarding\", \"Follow-up khách hàng tiềm năng\", \"Họp mốc dự án\",...)"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Tính năng kế hoạch lên lịch hoạt động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Activity type"
msgstr "Kiểu hoạt động"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Activity: %s"
msgstr "Hoạt động: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr "Thêm tác vụ theo ngữ cảnh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Thêm email ở danh sách đen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add Followers"
msgstr "Thêm Người theo dõi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.xml:0
msgid "Add Reaction"
msgstr "Thêm biểu tượng cảm xúc"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Add a Reaction"
msgstr "Thêm phản ứng"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr "Thêm mã khóa API Tenor GIF để bật hỗ trợ GIF."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""
"Thêm mã khóa API Tenor GIF để bật hỗ trợ GIF. "
"https://developers.google.com/tenor/guides/quickstart#setup"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Add a description"
msgstr "Thêm mô tả"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr "Thêm mô tả cho hoạt động của bạn..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "Bấm để thêm mới  %(document)s hoặc gửi email tới: %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "Thêm một địa chỉ email vào danh sách đen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add and close"
msgstr "Thêm và đóng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Add as recipient and follower (reason: %s)"
msgstr "Thêm làm người nhận và người theo dõi (lý do: %s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts"
msgstr "Thêm liên hệ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Add contacts to notify..."
msgstr "Thêm các liên hệ để thông báo..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
msgid "Add followers to this document"
msgstr "Thêm người theo dõi vào tài liệu này"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Add or join a channel"
msgstr "Thêm hoặc tham gia kênh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr "Thêm chữ ký"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "Thêm thông tin đăng nhập twilio của bạn cho máy chủ ICE"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""
"Không thể thêm người theo dõi kênh. Thay vào đó, hãy cân nhắc thêm thành "
"viên."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr ""
"Không thể thêm nhiều thành viên hơn vào cuộc trò chuyện này; nó chỉ cho phép"
" hai người."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Các liên hệ bổ sung"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Nâng cao"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "Tùy chọn nâng cao"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__after_plan_date
msgid "After Plan Date"
msgstr "Sau ngày kế hoạch"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__warning
msgid "Alert"
msgstr "Cảnh báo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "Bí danh"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""
"Bí danh %(matching_name)s (%(current_id)s) đã được liên kết với "
"%(alias_model_name)s (%(matching_id)s) và được sử dụng bởi %(parent_name)s "
"%(parent_model_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""
"Bí danh %(matching_name)s (%(current_id)s) đã được liên kết với "
"%(alias_model_name)s (%(matching_id)s)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "Bảo mật bí danh liên hệ "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "Tên miền bí danh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "Tên miền bí danh"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr "Miền bí danh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "Email bí danh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "Tên bí danh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "Trạng thái bí danh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr "Tên miền bí danh"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Trạng thái bí danh được ước tính trong tin nhắn đã nhận gần đây nhất."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "Mô hình bí danh"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Bí danh"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr ""
"Bí danh %(alias_names)s đã được sử dụng làm địa chỉ trả về hoặc nhận tất cả."
" Vui lòng chọn bí danh khác."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "All"
msgstr "Tất cả"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__all
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__all
msgid "All Messages"
msgstr "Tất cả tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "All conversations have been muted"
msgstr "Đã tắt thông báo tất cả cuộc trò chuyện"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "All partners must belong to the same message"
msgstr "Tất cả đối tác phải thuộc cùng một tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr "Cho phép tải lên công khai"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"Xuất hiện ngoại lệ SSL. Kiểm tra cấu hình SSL/TLS trong cổng máy chủ.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "An access token must be provided for each attachment."
msgstr "Token truy cập phải được cung cấp cho mỗi tệp đính kèm."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "An email is required for find_or_create to work"
msgstr "Cần có email để find_or_create hoạt động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email"
msgstr "Đã xảy ra lỗi khi gửi email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
msgid "An error occurred when sending an email on “%(record_name)s”"
msgstr "Có lỗi khi gửi email trên “%(record_name)s”"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "An error occurred while fetching messages."
msgstr "Đã xảy ra lỗi khi tìm nạp tin nhắn."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "An unexpected error occurred during the creation of the chat."
msgstr "Đã xảy ra lỗi không mong muốn trong quá trình tạo chat."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And"
msgstr "Và"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "And 1 other member."
msgstr "Thêm 1 thành viên khác."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Áp dụng cho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
msgid "Apply"
msgstr "Áp dụng"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr ""
"Đã được lưu trữ vì %(user_name)s (#%(user_id)s) đã xoá tài khoản cổng thông "
"tin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Are you sure you want to cancel the scheduled message?"
msgstr "Bạn có chắc chắn muốn huỷ tin nhắn đã lên lịch không?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are you sure you want to delete \"%(template_name)s\"?"
msgstr "Bạn có chắc chắn muốn xóa \"%(template_name)s\" không?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr "Bạn có chắc chắn muốn xóa Mẫu email này không?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Are you sure you want to delete this message?"
msgstr "Bạn có chắc muốn xóa tin nhắn này không?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"Bạn có chắc chắn muốn đặt lại các mẫu email này về cấu hình ban đầu không? "
"Các thay đổi và bản dịch sẽ bị mất."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "Bạn có chắc chắn muốn bỏ hạn chế Địa chỉ email này không?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Are you sure you want to unblacklist this email address?"
msgstr "Bạn có chắc chắn muốn bỏ hạn chế địa chỉ email này không?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Are your sure you want to update \"%(template_name)s\"?"
msgstr "Bạn có chắc chắn muốn cập nhật \"%(template_name)s\" không?"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr "Hỏi khi triển khai"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to ..."
msgstr "Phân công cho ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Assign to me"
msgstr "Phân công cho tôi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Assigned To"
msgstr "Đã phân công cho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
msgid "Assigned to"
msgstr "Phân công cho"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Phân công"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "At this point lang should be correctly set"
msgstr "Tại thời điểm này lang phải được thiết lập chính xác"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Attach files"
msgstr "Đính kèm tệp"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Tệp đính kèm"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Attachment counter loading..."
msgstr "Đang tải bộ đếm tệp đính kèm..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Attachments"
msgstr "Tệp đính kèm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Audio player:"
msgstr "Trình phát âm thanh:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Đối tác đã xác thực"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Tác giả"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Tác giả của thông điệp. Nếu không đặt, trường email_from có thể chứa địa chỉ"
" email mà không khớp với bất kỳ đối tác nào."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar của tác giả"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "Nhóm có thẩm quyền"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__group_ids
msgid "Authorized Groups"
msgstr "Nhóm được ủy quyền"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Tự động xóa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Tự động đăng ký nhóm"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "Tự động đăng ký nhận tin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Tự động đăng ký nhận tin"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr "Thông báo được nhắm mục tiêu tự động"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Hoạt động tự động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Automated message"
msgstr "Tin nhắn tự động"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"Tự động lên lịch trình cho hoạt động này ngay khi hoạt động hiện tại được "
"đánh dấu hoàn thành."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Available for all Companies"
msgstr "Khả dụng cho mọi công ty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
msgid "Avatar"
msgstr "Ảnh đại diện"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "Hình đại diện 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "Hình đại diện 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "Hình đại diện 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "Hình đại diện 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_cache_key
msgid "Avatar Cache Key"
msgstr "Khoá cache avatar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Avatar of user"
msgstr "Ảnh đại diện người dùng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Away"
msgstr "Vắng mặt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Background blur intensity"
msgstr "Cường độ làm mờ nền"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Cơ sở"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr "Mẫu cơ sở"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr "Mẫu cơ sở"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr "Mã khóa được mã hóa Base64"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr "Soạn thảo theo lô"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr ""
"Nhật ký theo lô không thể hỗ trợ tệp đính kèm hoặc giá trị theo dõi trên "
"nhiều tài liệu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_from__before_plan_date
msgid "Before Plan Date"
msgstr "Trước ngày kế hoạch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Danh sách hạn chế"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Ngày vào danh sách hạn chế"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Địa chỉ bị hạn chế"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "Địa chỉ email bị hạn chế"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"Bị chặn do việc xóa tài khoản cổng thông tin %(portal_user_name)s bởi "
"%(user_name)s (#%(user_id)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Blur Background"
msgstr "Nền mờ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Blur video background"
msgstr "Làm mờ nền video"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Thân"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Nội dung chính giống với mẫu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Bot"
msgstr "Bot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "Trả về"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr "Bí danh trả về"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr "Email trả về"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""
"Bí danh trả về %(bounce)s đã được sử dụng cho một miền khác có cùng tên. Sử "
"dụng bí danh trả về khác hoặc đơn giản là sử dụng một miền bí danh khác."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr "Email trả về phải là duy nhất"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""
"Bí danh trả về/nhận tất cả '%(matching_alias_name)s' đã được "
"%(document_name)s sử dụng. Chọn bí danh khác hoặc thay đổi bí danh này trong"
" tài liệu kia."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""
"Bí danh trả về/nhận tất cả '%(matching_alias_name)s' đã được sử dụng. Chọn "
"bí danh khác hoặc thay đổi bí danh này mô hình liên kết."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
msgid "Bounced"
msgstr "Bị trả về"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr "Brave: bật 'Dịch vụ Google cho Tin nhắn đẩy' để bật thông báo đẩy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Browser default"
msgstr "Mặc định trình duyệt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__endpoint
msgid "Browser endpoint"
msgstr "Endpoint của trình duyệt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__keys
msgid "Browser keys"
msgstr "Khoá trình duyệt"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
msgid "CC Email"
msgstr "CC Email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "CTRL"
msgstr "CTRL"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Gọi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Call Settings"
msgstr "Cài đặt cuộc gọi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Camera is off"
msgstr "Camera đã tắt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr "Có thể huỷ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "Có thể sửa nội dung chính"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr "Có thể gửi lại"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "Có thể viết"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Can not update the message or recipient of a notification."
msgstr "Không thể cập nhật tin nhắn hoặc người nhận của thông báo."

#. module: mail
#: model:ir.model,name:mail.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "Có thể gửi tin nhắn qua bus.bus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Cancel"
msgstr "Hủy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Hủy Email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
msgid "Cancel Message"
msgstr "Huỷ tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
msgid "Cancelled"
msgstr "Đã hủy"

#. module: mail
#: model:ir.model,name:mail.model_mail_canned_response
msgid "Canned Response"
msgstr "Câu trả lời soạn sẵn"

#. module: mail
#: model:res.groups,name:mail.group_mail_canned_response_admin
msgid "Canned Response Administrator"
msgstr "Quản trị viên câu trả lời soạn sẵn"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_canned_response_action
#: model:ir.module.category,name:mail.module_category_canned_response
#: model:ir.ui.menu,name:mail.menu_canned_responses
msgid "Canned Responses"
msgstr "Câu trả lời soạn sẵn"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Canned Responses Search"
msgstr "Tìm kiếm câu trả lời soạn sẵn"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_form
msgid "Canned response"
msgstr "Câu trả lời soạn sẵn"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__source
msgid ""
"Canned response that will automatically be substituted with longer content "
"in your messages. Type ':' followed by the name of your shortcut (e.g. "
":hello) to use in your messages."
msgstr ""
"Câu trả lời soạn sẵn sẽ tự động được thay thế bằng văn bản dài hơn trong "
"phần nội dung của bạn. Nhập ':' rồi đến tên phím tắt (VD: :hello) để sử dụng"
" trong phần nội dung."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_tree
msgid "Canned responses"
msgstr "Câu trả lời soạn sẵn"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                    your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                    replaced directly in your message, so that you can still edit\n"
"                    it before sending."
msgstr ""
"Câu trả lời soạn sẵn cho phép bạn chèn câu trả lời đã viết trước \n"
"                trong nội dung bằng cách nhập <i>:shortcut</i>. Phím tắt được\n"
"                thay thế trực tiếp trong nội dung, nên bạn vẫn có thể chỉnh sửa\n"
"                nội dung trước khi gửi."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change initial message nor parent channel of: %(channels)s."
msgstr ""
"Không thể thay đổi tin nhắn ban đầu hoặc kênh chính của: %(channels)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr "Không thể thay đổi loại kênh của: %(channel_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: initial message should belong to parent channel."
msgstr ""
"Không thể tạo %(channels)s: tin nhắn ban đầu phải thuộc về kênh chính."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Cannot create %(channels)s: parent should not be a sub-channel and should be"
" of type 'channel'."
msgstr ""
"Không thể tạo %(channels)s: kênh chính không được là kênh phụ và phải có "
"loại là 'kênh'."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Người nhận bản sao (Cc)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "Người nhận bản sao cc"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Người nhận bản sao (có thể dùng phần giữ chỗ ở đây)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "Nhận tất cả"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr "Bí danh nhận tất cả"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Email nhận tất cả"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""
"Bí danh nhận tất cả %(catchall)s đã được sử dụng cho một miền khác có cùng "
"tên. Sử dụng bí danh nhận tất cả khác hoặc đơn giản là sử dụng một miền bí "
"danh khác."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr "Email nhận tất cả phải là duy nhất"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "Loại chuỗi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "Thay đổi màu nền của các hoạt động liên quan của loại này."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Channel"
msgstr "Kênh"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "Thành viên kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
#: model:ir.model.fields,field_description:mail.field_res_users_settings__channel_notifications
msgid "Channel Notifications"
msgstr "Thông báo kênh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Loại kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Channel full"
msgstr "Kênh đã đủ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "Channel members cannot include public users."
msgstr "Thành viên kênh không thể bao gồm người dùng công cộng."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Channel settings"
msgstr "Kênh thiết lập"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
msgid "Channels"
msgstr "Kênh"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr "Kênh/Thành viên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
msgid "Chat"
msgstr "Nhắn tin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Chat Options"
msgstr "Tuỳ chọn chat"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Chat là riêng tư và chỉ giữa 2 người. Nhóm là riêng tư cho một nhóm người "
"được mời. Kênh có thể tham dự tự do (tùy theo cấu hình)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Chats"
msgstr "Chat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr "Kiểm tra danh sách loại trừ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Tin nhắn phụ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "Chọn một mẫu..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr "Chọn một người dùng..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Choose another value or change it on the other document."
msgstr "Chọn một giá trị khác hoặc thay đổi nó trong tài liệu kia."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr "Chọn phân công cho các hoạt động có phân công theo yêu cầu."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Clear quick search"
msgstr "Xoá tìm kiếm nhanh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Click here to retry"
msgstr "Bấm vào đây để thử lại"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Click to see the attachments"
msgstr "Bấm để xem tệp đính kèm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: code:addons/mail/static/src/chatter/web/scheduled_message.js:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Close"
msgstr "Đóng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "Close Chat Bubble"
msgstr "Đóng bong bóng chat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Chat Window (ESC)"
msgstr "Đóng cửa sổ chat (ESC)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Close Search"
msgstr "Đóng tìm kiếm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Close all conversations"
msgstr "Đóng tất cả cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_ad_banner.xml:0
msgid "Close banner"
msgstr "Đóng banner"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Close button"
msgstr "Nút đóng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
msgid "Close panel"
msgstr "Đóng bảng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
msgid "Close search"
msgstr "Đóng tìm kiếm"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "Đã chốt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Collapse panel"
msgstr "Thu gọn bảng điều khiển"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr "Thu thập thư trả lời trên một địa chỉ email cụ thể"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Come here often? Install the app for quick and easy access!"
msgstr ""
"Bạn thường xuyên truy cập? Hãy cài đặt ứng dụng để truy cập dễ dàng và nhanh"
" chóng!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Các địa chỉ người nhận cách nhau bằng dấu phảy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Các ID của Đối tác nhận cách nhau bằng dấu phảy"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"ID của đối tác nhận cách nhau bằng dấu phảy (có thể dùng phần giữ chỗ ở đây)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Các địa chỉ người nhận cách nhau bằng dấu phảy"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Địa chỉ người nhận cách nhau bằng dấu phảy (có thể dùng phần giữ chỗ ở đây)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Bình luận"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "Công ty"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr "Các công ty sử dụng miền này làm mặc định để gửi thư"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "Công ty"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Compose Email"
msgstr "Soạn email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Chế độ soạn thảo"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model:ir.ui.menu,name:mail.menu_configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "Cấu hình"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "Cấu hình danh sách máy chủ ICE của bạn cho webRTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Định cấu hình các loại hoạt động của bạn"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "Cấu hình tài khoản email server của bạn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
msgid "Confirm"
msgstr "Xác nhận"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Confirmation"
msgstr "Xác nhận"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "Đã xác nhận"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Congratulations, you're done with your activities."
msgstr "Xin chúc mừng, bạn đã hoàn thành các hoạt động."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Congratulations, your inbox is empty"
msgstr "Thật tuyệt vời, hộp thư đến của bạn đã không còn mail nào để xử lý"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_patch.js:0
msgid "Congratulations, your inbox is empty!"
msgstr "Thật tuyệt vời, hộp thư đến của bạn đã không còn mail nào để xử lý!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Kết nối thất bại (vấn đề máy chủ gửi email)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Connection test failed: %s"
msgstr "Kiểm thử kết nối thất bại: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid ""
"Connection to SFU server closed by the server, falling back to peer-to-peer"
msgstr ""
"Kết nối đến máy chủ SFU đã bị máy chủ đóng lại, chuyển sang chế độ peer-to-"
"peer"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection type:"
msgstr "Loại kết nối:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Connection:"
msgstr "Kết nối:"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"Kết nối được mã hoá với SSL/TLS thông qua một cổng chuyên dụng (mặc định: "
"IMAPS=993, POP3S=995)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "Cân nhắc tạo câu trả lời thành chủ đề mới"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr "Liên hệ quản trị viên"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Liên hệ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr "Mô hình container"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this user. This includes: creating, joining, pinning"
msgstr ""
"Chứa ngày và giờ của sự kiện được quan tâm cuối cùng đã xảy ra trong kênh "
"này dành cho người dùng này. Bao gồm: tạo, tham gia, ghim"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel. This updates itself when new message posted."
msgstr ""
"Chứa ngày và giờ của sự kiện được quan tâm cuối cùng đã xảy ra trong kênh "
"này. Tự cập nhật khi có tin nhắn mới được đăng."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__unpin_dt
msgid "Contains the date and time when the channel was unpinned by the user."
msgstr "Chứa ngày và giờ khi người dùng bỏ ghim kênh."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Nội dung"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr ""
"Nội dung sẽ tự động thay thế phím tắt bạn chọn. Nội dung này vẫn có thể được"
" điều chỉnh trước khi bạn gửi đi."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__body
msgid "Contents"
msgstr "Nội dung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "Trạng thái thu hẹp cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Copy Link"
msgstr "Sao chép liên kết"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Couldn't get your emails. Check out the error message below for more info:\n"
"%s"
msgstr ""
"Không thể lấy email của bạn. Hãy xem thông báo lỗi dưới đây để biết thêm thông tin:\n"
"%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Bộ đếm các email trả về đối với liên hệ này"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "Quốc gia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Create"
msgstr "Tạo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "Tạo hoạt động"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "Tạo ngày"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Create Group Chat"
msgstr "Tạo nhóm trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Create Thread"
msgstr "Tạo chủ đề"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "Tạo Uid"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Create a Mail Template"
msgstr "Tạo một chủ đề email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "Tạo mới một Bản ghi"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_canned_response_action
msgid "Create a new canned response"
msgstr "Tạo câu trả lời soạn sẵn mới"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr "Tạo một kế hoạch hoạt động"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s"
msgstr "Tạo mới %(document)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "Tạo mới %(document)s bằng cách gửi một email đến %(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
msgid "Create: #"
msgstr "Tạo: #"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Created"
msgstr "Đã tạo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Tạo bởi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/chatter.js:0
msgid "Creating a new record..."
msgstr "Đang tạo hồ sơ mới..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Ngày tạo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "Người tạo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "Thông tin đăng nhập"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Người dùng hiện tại có một thông báo được đánh dấu sao và liên kết tới thông"
" điệp này"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Tin nhắn bị trả lại tuỳ chỉnh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr "Danh sách máy chủ ICE tùy chỉnh"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr "Mẫu tuỳ chỉnh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr "Mẫu tuỳ chỉnh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "Tên kênh tùy chỉnh"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "Khách hàng là bắt buộc đối với thông báo hộp thư đến/email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "Tùy chỉnh giao diện của email tự động"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr "Thông báo tuỳ chỉnh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "DTLS:"
msgstr "DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Data channel:"
msgstr "Kênh dữ liệu:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Ngày"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr "Ngày mà thông báo sẽ được gửi đi."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr "Ngày mà tin nhắn được ghim."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Ngày"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "Thời hạn"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "Thời hạn:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Deadline: %s"
msgstr "Thời hạn: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Deafen"
msgstr "Tắt tiếng"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Thân gửi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Dear Sender"
msgstr "Xin chào Người gửi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr "Xin chào Người gửi,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Loại"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
#: model_terms:ir.ui.view,arch_db:mail.mail_message_subtype_view_search
msgid "Default"
msgstr "Mặc định"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "Chế độ hiển thị mặc định"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr "Mặc định từ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr "Bí danh mặc định từ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "Ghi chú mặc định"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "Tóm tắt mặc định"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_kanban
msgid "Default Summary:"
msgstr "Tóm tắt mặc định"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Người dùng mặc định"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "Giá trị Mặc định"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Default deadline for the activities..."
msgstr "Thời hạn mặc định cho hoạt động..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""
"Mặc định từ khi nó không khớp với bộ lọc máy chủ thư đi. Có thể là một phần "
"cục bộ, ví dụ: 'thông báo' hoặc địa chỉ email đầy đủ, ví dụ: "
"'<EMAIL>' để ghi đè tất cả các email gửi đi."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Người nhận mặc định"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Người nhận mặc định của bản ghi:\n"
"- đối tác (sử dụng id của đối tác hoặc trường partner_id) HOẶC\n"
"- email (sử dụng trường email_from hoặc trường email)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr "Người dùng mặc định"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""
"Xác định trình tự xử lý, giá trị thấp hơn nghĩa là mức ưu tiên cao hơn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "Delay Label"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Loại trễ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Delay after releasing push-to-talk"
msgstr "Độ trễ sau khi nhả phím bấm để nói"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Đơn vị trễ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Delete"
msgstr "Xoá"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Xoá email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Delete Template"
msgstr "Xoá mẫu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Delete all previews"
msgstr "Xoá tất cả bản xem trước"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
msgid "Delivered"
msgstr "Đã giao"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Giao thư đã lỗi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Delivery failure"
msgstr "Lỗi gửi"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr ""
"Việc sử dụng 'default_res_id' không được chấp nhận, nên sử dụng "
"'default_res_ids'."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Mô tả"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Mô tả mã sẽ được thêm vào thông điệp cho kiểu phụ này. Nếu để trống, tên sẽ "
"được thêm vào để thay thế."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"Xác định cách mặc định hiển thị kênh khi mở kênh từ link lời mời. Không có "
"giá trị nghĩa là hiển thị chữ (không âm thanh/video)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_editable
msgid "Determines if the canned response can be edited by the current user"
msgstr ""
"Xác định xem người dùng hiện tại có thể chỉnh sửa câu trả lời soạn sẵn hay "
"không"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__is_shared
msgid "Determines if the canned_response is currently shared with other users"
msgstr ""
"Xác định xem canned_response hiện có được chia sẻ với người dùng khác hay "
"không"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
msgid "Direct messages"
msgstr "Tin nhắn trực tiếp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "Disconnect"
msgstr "Ngắt kết nối"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Disconnected from the RTC call by the server"
msgstr "Máy chủ đã ngắt kết nối khỏi cuộc gọi RTC"

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "Thảo luận"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "Thanh bên thảo luận"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr "Thảo luận: thành viên kênh bật tiếng"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_users_settings_unmute_ir_actions_server
msgid "Discuss: users settings unmute"
msgstr "Thảo luận: Bỏ tắt cài đặt người dùng"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "Kênh thảo luận"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Thảo luận"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Dismiss"
msgstr "Bỏ qua"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_push_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Hiển thị một tuỳ chọn trên các tài liệu liên quan để mở một đồ thoại soạn "
"thảo cho mẫu này"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
msgid "Display avatar name"
msgstr "Hiển thị tên avatar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
msgid "Do you really want to delete \"%s\"?"
msgstr "Bạn có thực sự muốn xóa \"%s\" không?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
msgid "Do you really want to delete this preview?"
msgstr "Bạn có thực sự muốn xóa bản xem trước này không?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Tài liệu"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Người theo dõi Tài liệu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr "ID tài liệu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "Mô hình tài liệu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Tên tài liệu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr "Tài liệu: \""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "Miền"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done"
msgstr "Hoàn tất"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "Hoàn thành & tiếp tục thực hiện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Schedule Next"
msgstr "Hoàn tất & lên lịch tiếp theo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Done Date"
msgstr "Ngày hoàn tất"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Done and Schedule Next"
msgstr "Hoàn tất và lên lịch tiếp theo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Download"
msgstr "Tải xuống"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Download Files"
msgstr "Tải tệp xuống"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Download logs"
msgstr "Tải xuống nhật ký"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Download:"
msgstr "Tải xuống"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/mail_attachment_dropzone.xml:0
msgid "Drop Files here"
msgstr "Thả file vào đây"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "Ngày đến hạn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Ngày đến hạn vào"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due in"
msgstr "Đến hạn trong"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Due in %s days"
msgstr "Đến hạn trong %s ngày"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Due on"
msgstr "Đến hạn vào"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Loại thời hạn"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Email trùng lặp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "Thời lượng nói trong tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "Báo cáo động"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr "Người dùng không cố định (dựa trên dữ liệu)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Edge blur intensity"
msgstr "Cường độ làm mờ cạnh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Edit"
msgstr "Chỉnh sửa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Sửa đối tác"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Message"
msgstr "Chỉnh sửa tin nhắn đã lên lịch"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "Edit Scheduled Note"
msgstr "Chỉnh sửa ghi chú đã lên lịch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "Edit Subscription of %(name)s"
msgstr "Chỉnh sửa gói đăng ký của %(name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Edit subscription"
msgstr "Sửa đổi các đăng ký nhận tin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
msgid "Edit: %s"
msgstr "Sửa: %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "Email Thêm chữ ký"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "Địa chỉ Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "Bí danh email"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Bí danh Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "Email Mixin bí danh"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr "Mixin bí danh email (sáng)"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "Email Danh sách hạn chế "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "Email Màu nút"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "Quản lý Email CC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Cấu hình Thư điện tử"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr "Miền email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Email Failure: %(modelName)s"
msgstr "Lỗi email: %(modelName)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "Màu header email"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "Email Gửi thư hàng loạt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "Bố cục email thông báo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "Xem trước Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Tìm kiếm Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "Mẫu email"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "Xem trước Mẫu Email"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "Mẫu email"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Chủ đề email"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "Địa chỉ email đã tồn tại!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Địa chỉ email của người gửi. Trường này được thiết lập khi không có đối tác "
"tương ứng được tìm thấy và thay thế trường author_id ở chatter."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""
"Địa chỉ email mà thư trả lời sẽ được chuyển hướng đến khi gửi email hàng "
"loạt"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"Địa chỉ email mà thư trả lời sẽ được chuyển hướng đến khi gửi email hàng "
"loạt; chỉ được sử dụng khi trả lời không được ghi lại trong chủ đề thảo luận"
" ban đầu."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr "Địa chỉ email trong danh sách đen sẽ không nhận được email nữa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""
"Không thể sử dụng bí danh email %(alias_name)s trên nhiều bản ghi cùng một "
"lúc. Vui lòng cập nhật từng bản ghi một."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Trình soạn thảo email"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Ví dụ miền email 'example.com' trong '<EMAIL>'"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Nội dung email"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "Tính năng gửi lại email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "Mẫu Email"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "Emoji"
msgstr "Emoji"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
msgid "Emojis"
msgstr "Emoji"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "Chỉ nhân viên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Enable"
msgstr "Bật"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "Bật theo dõi có thứ tự"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Enter"
msgstr "Vào"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Enter Full Screen"
msgstr "Vào chế độ toàn màn hình"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__danger
msgid "Error"
msgstr "Lỗi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Thông điệp lỗi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
msgid "Error during communication with the publisher warranty server."
msgstr ""
"Có lỗi xảy ra khi thông tin liên lạc với máy chủ bảo hành của nhà phát hành."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "Thông báo lỗi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"Lỗi không có ngoại lệ. Có thể là do đồng thời cập nhật truy cập các bản ghi "
"thông báo. Vui lòng liên hệ quản trị viên."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr "Lỗi không có ngoại lệ. Có thể là do gửi email mà không có người nhận."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Lỗi, một đối tác không thể theo dõi hai lần cùng một đối tượng."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "Mọi người"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Ngoại lệ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Exit Fullscreen"
msgstr "Thoát chế độ toàn màn hình"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
msgid "Expand"
msgstr "Mở rộng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Expand panel"
msgstr "Mở rộng bảng điều khiển"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push_device__expiration_time
msgid "Expiration Token Date"
msgstr "Ngày hết hạn token"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Các bộ lọc Mở rộng..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Extra Comments ..."
msgstr "Bình luận bổ sung..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Thư thất bại"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Không đạt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
msgid "Failed to enable push notifications"
msgstr "Không thể bật thông báo đẩy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Failed to load gifs..."
msgstr "Tải gif không thành công..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr "Không tải được máy chủ SFU, quay lại peer-to-peer"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Failed to post the message. Click to retry"
msgstr "Đăng nội dung không thành công. Nhấp để thử lại"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""
"Không thể kết xuất mẫu QWeb: %(template_src)s\n"
"\n"
"%(template_traceback)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr "Không thể kết xuất mẫu inline_template: %(template_txt)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid "Failed to render template: %(view_ref)s"
msgstr "Không thể kết xuất mẫu: %(view_ref)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Lý do thất bại"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Lý do thất bại"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Lý do thất bại. Cái này thường là một ngoại lệ (exception) được ném ra bởi "
"máy chủ email, được lưu trữ để thuận tiện cho việc dò lỗi các phát sinh liên"
" quan đến gửi nhận mail."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Loại thất bại"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Failure: %(modelName)s"
msgstr "Lỗi: %(modelName)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Ưa thích bởi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Favorites"
msgstr "Danh sách yêu thích"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Feedback:"
msgstr "Phản hồi:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "Lấy về Ngay"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr "Tìm nạp đến số lượng GIF xác định."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "Trường"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "Không thể đổi trường \"Hoạt động thư\" thành \"Sai\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "Không thể đổi trường \"Danh hạn chế thư\" thành \"Sai\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Không thể thay đổi trường \"Chủ đề email\" thành \"Sai\"."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "Chi tiết trường"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Trường được sử dụng để liên kết với một mô hình liên quan đến một mô hình "
"kiểu con khi sử dụng đăng ký tự động trên một tài liệu liên quan. Trường này"
" được dùng để tính toán getattr(related_document.relation_field)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""
"Trường “%(field)s” trên mô hình “%(model)s” phải thuộc loại Many2one và có "
"track=True để tính toán thời lượng."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Trường "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr "Tệp từ nơi hình thành mẫu. Được sử dụng để thiết lập lại mẫu bị hỏng."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "File too large"
msgstr "Tệp quá lớn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is disabled for external users"
msgstr "Tính năng tải tệp lên bị vô hiệu hóa đối với người dùng bên ngoài"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "File upload is enabled for external users"
msgstr "Tính năng tải tệp lên được bật cho người dùng bên ngoài"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Files"
msgstr "Tập tin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Fold"
msgstr "Thu gọn"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "Thu gọn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Follow"
msgstr "Theo dõi"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "Người theo dõi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Form Người theo dõi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Chỉ những người dõi theo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr "Người theo dõi cần thêm"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr "Người theo dõi cần xoá"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Following"
msgstr "Theo dõi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng, ví dụ: fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""
"Đối với %(channels)s, channel_type phải là 'kênh' để có ủy quyền theo nhóm "
"hoặc đăng ký tự động theo nhóm."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 1 hour"
msgstr "Trong 1 giờ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 15 minutes"
msgstr "Trong 15 phút"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 24 hours"
msgstr "Trong 24 giờ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 3 hours"
msgstr "Trong 3 giờ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "For 8 hours"
msgstr "Trong 8 giờ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr "Bắt buộc gửi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr "Bắt buộc một loại ngôn ngữ:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Email được Định dạng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "Từ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__from_message_id
msgid "From Message"
msgstr "Từ tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "From peer:"
msgstr "Từ mạng ngang hàng:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Full composer"
msgstr "Trình soạn thảo toàn màn hình"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "Video toàn màn hình"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Future"
msgstr "Tương lai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Hoạt động trong tương lai"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF"
msgstr "GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Category"
msgstr "Danh mục GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "GIF Favorites"
msgstr "GIF yêu thích"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr "GIF yêu thích"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr "ID GIF từ Tenor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid "GIFs"
msgstr "GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Cổng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Go to conversation"
msgstr "Đi đến cuộc trò chuyện"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr "Tích hợp Google Dịch"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "Nhóm"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "Tên Nhóm"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr ""
"Ủy quyền theo nhóm và tự động đăng ký theo nhóm chỉ được hỗ trợ trên các "
"kênh."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Nhóm theo..."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_sub_channel_no_group_public_id
msgid ""
"Group public id should not be set on sub-channels as access is based on "
"parent channel"
msgstr ""
"Không nên đặt ID công khai của nhóm trên các kênh phụ vì quyền truy cập dựa "
"trên kênh chính"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Grouped Chat"
msgstr "Cuộc trò chuyện được nhóm"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "Nhóm"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
msgid "Guest"
msgstr "Khách"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name cannot be empty."
msgstr "Tên khách không thể để trống."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
msgid "Guest's name is too long."
msgstr "Tên khách quá dài."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "Khách"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "Định tuyến HTTP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Xử lý bằng email"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Xử lý trong hệ thống"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr "Có lỗi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr "Có hoạt động email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr "Có danh sách hạn chế email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "Có chủ đề email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Có đề cập"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "Có cuộc gọi đến bị tắt tiếng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Có lỗi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "Có người phụ trách theo yêu cầu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Header "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Xin chào"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Ẩn"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr "Mẫu bị ẩn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
msgid "Hide Pinned Messages"
msgstr "Ẩn tin nhắn đã ghim"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Hide all conversations"
msgstr "Ẩn tất cả cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Hide sidebar"
msgstr "Ẩn thanh bên"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Ẩn các trường phụ trong tùy chọn người theo dõi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr "Ẩn người dùng chung / cổng thông tin, độc lập với cấu hình kiểu phụ."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "Cao"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "History"
msgstr "Lịch sử"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "Hostname hoặc IP của máy chủ email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Hover on your message and mark as todo"
msgstr "Di chuột vào tin nhắn của bạn và đánh dấu là việc cần làm"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"Trình phát âm thanh sẽ duy trì hoạt động trong bao lâu sau khi vượt qua "
"ngưỡng âm lượng"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "Máy chủ ICE"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE connection:"
msgstr "Kết nối ICE:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE gathering:"
msgstr "Tập trung ICE:"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "Máy chủ ICE"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr "Máy chủ ICE"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "ICE:"
msgstr "ICE:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_push__id
#: model:ir.model.fields,field_description:mail.field_mail_push_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID của bản ghi chính chứa bí danh (ví dụ: dự án chứa bí danh tạo nhiệm vụ)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "Tình trạng IM"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "Địa chỉ máy chủ IMAP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng cho thấy một hoạt động ngoại lệ."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id của tài nguyên đã theo dõi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "Định danh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Idle"
msgstr "Rảnh rỗi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "Nếu yêu cầu SSL"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr ""
"Nếu chọn, mọi sửa đổi thực hiện trong trường này sẽ được theo dõi. Giá trị "
"được sử dụng để yêu cầu giá trị theo dõi."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr ""
"Nếu thiết lập, thành viên sẽ không nhận được thông báo từ kênh cho đến ngày "
"này."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"Nếu được thiết lập, trình quản lý danh sách chờ sẽ gửi email sau ngày đó. "
"Nếu không được thiết lập, email sẽ được gửi sớm nhất có thể. Nếu không xác "
"định rõ múi giờ, thì nó được coi là nằm trong múi giờ UTC."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""
"Nếu được thiết lập, trình quản lý danh sách chờ sẽ gửi email sau ngày đó. "
"Nếu không được thiết lập, email sẽ được gửi sớm nhất có thể. Bạn có thể sử "
"dụng biểu thức động."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__mute_until_dt
msgid ""
"If set, the user will not receive notifications from all the channels until "
"this date."
msgstr ""
"Nếu thiết lập, người dùng sẽ không nhận được thông báo từ kênh cho đến ngày "
"này."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Nếu được cài đặt, nội dung này sẽ tự động được gửi đến người dùng chưa được "
"cấp quyền thay vì tin nhắn mặc định."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""
"Nếu chọn, sẽ hạn chế mẫu cho người dùng cụ thể này."
"                                             Nếu không chọn, được chia sẻ "
"với tất cả người dùng."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Nếu địa chỉ email nằm trong danh sách hạn chế, liên hệ sẽ không nhận được "
"thư hàng loạt từ bất kỳ danh sách nào nữa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_scheduled_message__is_note
msgid "If the message will be posted as a Note."
msgstr "Nếu tin nhắn sẽ được đăng dưới dạng Ghi chú."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"Nếu đúng, câu trả lời sẽ không đi vào chủ đề thảo luận tài liệu gốc. Thay "
"vào đó, nó sẽ kiểm tra reply_to trong id tin nhắn theo dõi và chuyển hướng "
"tương ứng với kết quả. Điều này có tác động đến id tin nhắn được tạo."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Nếu bạn đã thiết lập một miền email catch-all chuyển hướng đến Hệ thống, hãy"
" nhập tên miền vào đây."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "Nếu bạn muốn sử dụng twilio làm nhà cung cấp máy chủ TURN/STUN"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Bỏ qua tất cả"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "Hình ảnh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Hình ảnh 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Hình ảnh 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Hình ảnh 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Hình ảnh 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr "Loại MIME hình ảnh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "Hình ảnh là một liên kết"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""
"Ở chế độ bình luận: nếu được thiết lập, hãy hoãn gửi thông báo. Ở chế độ gửi"
" thư hàng loạt: nếu gửi, thì gửi email sau ngày đó. Ngày này được coi là nằm"
" trong múi giờ UTC."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "Inactive Alias"
msgstr "Bí danh vô hoạt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "Máy chủ thư đến"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
msgid "Inbox"
msgstr "Hộp thư đến"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Call..."
msgstr "Cuộc gọi đến..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr "Email đến"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "Máy chủ thư đến"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "Máy chủ thư đến"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "Máy chủ thư đến"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Incoming Video Call..."
msgstr "Cuộc gọi video đến..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""
"Cho biết hoạt động này đã được tạo tự động chứ không phải bởi bất kỳ người "
"dùng nào."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Info"
msgstr "Thông tin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "Đối tượng ban đầu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "Địa chỉ đầy đủ nội tuyến"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Input device"
msgstr "Thiết bị đầu vào"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Insert Template"
msgstr "Chèn mẫu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Insert Templates"
msgstr "Chèn mẫu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "Install"
msgstr "Cài đặt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Install Odoo"
msgstr "Cài đặt Odoo"

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "Tích hợp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Chỉ nội bộ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr "Trao đổi nội bộ:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_count
msgid "Interval"
msgstr "Khoảng thời gian"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "Không hợp lệ"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Invalid domain “%(domain)s” (type “%(domain_type)s”)"
msgstr "Miền “%(domain)s” không hợp lệ (loại “%(domain_type)s”)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Địa chỉ email không hợp lệ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
msgid "Invalid email address “%s”"
msgstr "Địa chỉ email “%s” không hợp lệ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Biểu thức không hợp lệ, not phải là một python dictionary theo đúng nghĩa "
"đen, ví dụ \"{'field': 'value'}\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr "Trường “%(field_name)s” không hợp lệ khi tạo kênh với các thành viên."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "Địa chỉ gửi từ không hợp lệ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid "Invalid primary email field on model %s"
msgstr "Trường email chính không hợp lệ trên đối tượng %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr "res_ids %(res_ids_str)s không hợp lệ (loại %(res_ids_type)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""
"Tên máy chủ không hợp lệ!\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr ""
"Mẫu hoặc nguồn chế độ xem %(svalue)s không hợp lệ (loại %(stype)s), phải là "
"một bản ghi hoặc XMLID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr ""
"Mẫu hoặc nguồn chế độ xem Xml ID %(source_ref)s không hợp lệ không còn tồn "
"tại"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr ""
"Mẫu hoặc bản ghi nguồn chế độ xem %(svalue)s không hợp lệ, thay vào đó là "
"%(model)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr ""
"Mẫu hoặc tham chiếu liệu nguồn chế độ xem %(svalue)s không hợp lệ, thay vào "
"đó là %(model)s "

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"Giá trị không hợp lệ khi tạo kênh có thành viên, chỉ được phép 4 hoặc 6 "
"người."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""
"Giá trị không hợp lệ khi tạo kênh có chương trình thành viên, chỉ được phép "
"nhập 0."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr "URL lời mời"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "Lời mời theo dõi %(document_model)s: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite"
msgstr "Mời"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Invite People"
msgstr "Mời mọi người"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Invite a User"
msgstr "Mời một người dùng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Invite people"
msgstr "Mời mọi người"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Channel"
msgstr "Mời tham gia kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Invite to Group Chat"
msgstr "Mời vào nhóm trò chuyện"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Đồ thuật Mời"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "Có hiệu lực"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "Là người dùng hiện tại hoặc tác giả khách mời"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "Cho chỉnh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "Là trình chỉnh sửa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__is_hidden
msgid "Is Hidden"
msgstr "Bị ẩn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "Là một thành viên"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Đã được đọc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr "Là bản thân"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr "Là trình chỉnh sửa mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr "Là một nhật ký"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__is_note
msgid "Is a note"
msgstr "Là ghi chú"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "Kênh danh mục thanh bên thảo luận có mở không?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "Hộp thoại trò chuyện danh mục thanh bên thảo luận có mở không?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "Micro bị tắt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "Được chốt vào giao diện"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "Đang gửi video của người dùng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "Đang chia sẻ màn hình"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr ""
"Dường như bạn đang cố gắng tạo một thành viên kênh, nhưng có vẻ bạn đã quên "
"chỉ định kênh liên quan. Để tiếp tục, hãy đảm bảo cung cấp thông tin kênh "
"cần thiết."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_push_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""
"Nó đề cập đến các mã khóa trình duyệt được thông báo sử dụng:\n"
"- p256dh: Là mã khóa đăng ký công khai do trình duyệt tạo ra. Trình duyệt sẽ\n"
"          giữ lại bí mật mã khóa riêng tư và sử dụng nó để giải mã payload\n"
"- auth: Giá trị xác thực phải được coi là bí mật và không được chia sẻ ra ngoài Odoo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON ánh xạ id từ trường many2one tới số giây đã sử dụng"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "Gia nhập"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Join Call"
msgstr "Tham gia cuộc gọi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Join Channel"
msgstr "Gia nhập Kênh"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "Tham gia một nhóm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
msgid "Jump"
msgstr "Chuyển đến"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Jump to Present"
msgstr "Chuyển đến hiện tại"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "Giữ lại Đính kèm"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "Giữ hoạt động hoàn tất"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr "Giữ bản sao nội dung"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "Giữ lại Bản gốc"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr ""
"Giữ một bản sao nội dung email nếu email bị xóa (chỉ dành cho gửi thư hàng "
"loạt)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr ""
"Giữ các hoạt động được đánh dấu là hoàn tất trong chế độ xem hoạt động"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "Khoá"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Kind Regards"
msgstr "Trân trọng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "LIVE"
msgstr "TRỰC TIẾP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Ngôn ngữ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "Ngày lấy email Gần nhất"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "Tìm nạp lần cuối"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__last_interest_dt
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "Lần quan tâm cuối cùng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "Thấy lần cuối"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Cập nhật lần cuối vào"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_push_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__last_used
msgid "Last Used"
msgstr "Được sử dụng cuối cùng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr "Ngày xem cuối cùng"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_canned_response__last_used
msgid "Last time this canned_response was used"
msgstr "Lần cuối cùng canned_response này được sử dụng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "Late"
msgstr "Trễ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Hoạt động chậm trễ"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Launch Plans"
msgstr "Kích hoạt cv vào/ra"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Bố cục"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "Rời khỏi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Channel"
msgstr "Rời kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Leave Conversation"
msgstr "Rời cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Leave this channel"
msgstr "Rời kênh này"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr "Xem trước liên kết"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Link copied!"
msgstr "Liên kết đã được sao chép!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "List Activity"
msgstr "Hoạt động theo danh sách"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "List users in the current channel"
msgstr "Liệt kê người dùng ở Kênh hiện hành"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Load More"
msgstr "Tải thêm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "Load more"
msgstr "Tải thêm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/navigable_list.xml:0
#: code:addons/mail/static/src/core/public_web/messaging_menu.xml:0
msgid "Loading…"
msgstr "Đang tải..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "Máy chủ cục bộ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Phát hiện gửi đến dựa trên phần cục bộ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr ""
"Phần cục bộ của email được sử dụng cho Trả lời để nhận thư trả lời, ví dụ: "
"'catchall' trong '<EMAIL>'"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""
"Phần cục bộ của email được sử dụng cho Đường dẫn trả về khi email bị trả "
"lại, ví dụ: 'bounce' trong '<EMAIL>'"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Log"
msgstr "Ghi nhận"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Log Later"
msgstr "Lưu lại sau"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Log Now"
msgstr "Lưu lại ngay"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Log RTC events"
msgstr "Ghi nhận sự kiện RTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "Ghi chú ..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "Ghi 1 hoạt động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Log an internal note…"
msgstr "Thêm một ghi chú nội bộ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Log note"
msgstr "Ghi chú"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Log step:"
msgstr "Bước ghi nhận:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
msgid "Logged in as %s"
msgstr "Đã đăng nhập với tư cách %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "Thông tin đăng nhập"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "Thấp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Lower Hand"
msgstr "Hạ tay xuống"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr "Loại MIME"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
msgid "Mail"
msgstr "Mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "Kiểu hoạt động mail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Kiểu hoạt động mail"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Danh sách hạn chế thư"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Mixin danh sách hạn chế thư"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "Kênh mail từ"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "Mixin trình soạn thảo email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mail Failures"
msgstr "Lỗi email"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr "Cổng email được cho phép"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "Mail Layout"
msgstr "Bố cục email"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr "Quản lý tệp đính kèm chính của email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr "Int id nội dung email"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr "Phiên RTC email"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mixin kết xuất email"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "Máy chủ gửi thư"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "Mẫu mail"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "Trình chỉnh sửa mẫu email"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr "Đặt lại mẫu email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Chủ đề email"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Giá trị Theo vết Thư"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""
"Trình soạn thảo email ở chế độ bình luận phải chạy trên ít nhất một bản ghi."
" Không tìm thấy bản ghi nào (mô hình %(model_name)s)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "Thư đã được tạo để báo cho biết một mail.message đang tồn tại"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "Mail template model of %(action_name)s does not match action model."
msgstr "Mô hình mẫu email của %(action_name)s không khớp với mô hình tác vụ."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr "Mẫu email sử dụng máy chủ email này"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "Thư: Trình quản lý hàng đợi email"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "Thư: Làm mới hộp thư đến"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_post_scheduled_message_ir_actions_server
msgid "Mail: Post scheduled messages"
msgstr "Email: Đăng tin nhắn đã lên lịch"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr "Email: gửi thông báo đẩy trên web"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Mailbox unavailable - %s"
msgstr "Hộp thư không khả dụng - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Mailboxes"
msgstr "Hộp thư"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr ""
"Không nên gọi hành động gửi thư hoặc đăng nội dung có nguồn với "
"%(source_type)s  trống"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "Mails"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Tệp đính kèm chính"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""
"Quản lý câu trả lời dưới dạng email đến thay vì câu trả lời trong cùng một "
"chủ đề."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Mark As Read"
msgstr "Đánh dấu là đã đọc"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Mark Done"
msgstr "Hoàn tất"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Mark all read"
msgstr "Đánh dấu Đã đọc tất cả"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Mark as Done"
msgstr "Đánh dấu là hoàn tất"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "Mark as Read"
msgstr "Đánh dấu đã đọc"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Todo"
msgstr "Đánh dấu việc cần làm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Mark as Unread"
msgstr "Đánh dấu là chưa đọc"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Mark as done"
msgstr "Đánh dấu xong"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr ""
"Không thể tiếp cận được các thiết bị truyền thông. SSL có thể không được "
"thiết lập đúng cách."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "Phương tiện"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Cuộc họp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "Số lượng thành viên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "Thành viên"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Thành viên của nhóm sẽ được tự động thêm vào danh sách người theo dõi. Lưu ý"
" rằng họ có thể tự thay đổi việc theo dõi này một cách thủ công, khi cần "
"thiết."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Mentions"
msgstr "Đề cập"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
msgid "Mentions Only"
msgstr "Chỉ đề cập"

#. module: mail
#: model:ir.model,name:mail.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Tính năng gộp đối tác"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
msgid "Merged with the following partners: %s"
msgstr "Đã gộp với các đối tác sau: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Message"
msgstr "Tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message \"%(subChannelName)s\""
msgstr "Tin nhắn \"%(subChannelName)s\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message #%(threadName)s…"
msgstr "Tin nhắn #%(threadName)s…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message %(thread name)s…"
msgstr "Tin nhắn %(thread name)s…"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID Tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copied!"
msgstr "Liên kết tin nhắn đã được sao chép!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr "Sao chép liên kết tin nhắn không thành công (Quyền bị từ chối?)!"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Thông báo tin nhắn"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "Phản ứng tin nhắn"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "Phản ứng tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Tên bản ghi tin nhắn"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr "Dịch tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "Khoá API dịch tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Kiểu tin nhắn"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__new_message_separator
msgid "Message id before which the separator should be displayed"
msgstr "Id tin nhắn mà dấu phân cách sẽ được hiển thị trước đó"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Message posted on \"%s\""
msgstr "Tin nhắn đã được đăng vào \"%s\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Người nhận tin nhắn (email)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "ID tin nhắn, ví dụ như ID của tin nhắn trước"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Message should be a valid EmailMessage instance"
msgstr "Tin nhắn phải là phiên bản EmailMessage hợp lệ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Kiểu thư phụ cung cấp loại chính xác hơn trên thư, đặc biệt là đối với thông"
" báo hệ thống. Ví dụ, nó có thể là một thông báo liên quan đến một bản ghi "
"mới (Mới) hoặc thay đổi giai đoạn trong một quy trình (Thay đổi giai đoạn). "
"Các kiểu con tin nhắn cho phép điều chỉnh chính xác các thông báo mà người "
"dùng muốn nhận trên tường của nó."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Kiểu phụ của tin nhắn"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Các kiểu Thông điệp phụ theo dõi, có nghĩa là các kiểu con sẽ được đẩy lên "
"Tường của người dùng."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Kiểu thông điệp: email đối với thông điệp bằng email, thông báo đối với các "
"thông điệp hệ thống, bình luận đối với các loại thông điệp khác (ví dụ: phản"
" hồi của người dùng)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "ID duy nhất của tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Message-Id"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr "Tin nhắn"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Tìm kiếm tin nhắn"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_from_message_id_unique
msgid "Messages can only be linked to one sub-channel"
msgstr "Tin nhắn chỉ có thể được liên kết với một kênh phụ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Messages marked as read will appear in the history."
msgstr "Tin nhắn được đánh dấu đã đọc sẽ xuất hiện trong lịch sử."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Thông điệp với kiểu phụ nội bộ sẽ chỉ được thấy bởi cán bộ nhân viên trong "
"công ty, (thành viên của nhóm base_user)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Messages with tracking values cannot be modified"
msgstr "Tin nhắn có giá trị theo dõi không thể sửa đổi"

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr "Siêu dữ liệu cho tệp đính kèm dạng giọng nói"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "Thiếu email"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Thiếu địa chỉ email"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "Thiếu địa chỉ gửi từ"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr ""
"Mixin để tính thời gian một bản ghi sử dụng cho mỗi giá trị mà trường "
"many2one có thể lấy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "Mô hình"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "Đối tượng đã đổi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Mô hình của tài nguyên được theo dõi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Mô hình loại phụ áp dụng cho. Nếu sai, loại phụ này áp dụng cho tất cả các "
"kiểu máy."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Mô hình"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"Việc điều chỉnh mô hình có thể gây ảnh hưởng tới các hoạt động hiện có đang "
"sử dụng loại hoạt động này. Hãy cẩn thận."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Gỡ cài đặt phân hệ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Tháng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
msgid "More"
msgstr "Thêm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Mute"
msgstr "Tắt tiếng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Mute Conversation"
msgstr "Tắt thông báo cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute all conversations"
msgstr "Tắt thông báo tất cả cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Mute duration"
msgstr "Thời gian tắt thông báo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
#: model:ir.model.fields,field_description:mail.field_res_users_settings__mute_until_dt
msgid "Mute notifications until"
msgstr "Tắt thông báo cho đến"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid "Muting prevents unread indicators and notifications from appearing."
msgstr ""
"Việc tắt thông báo sẽ ngăn các chỉ báo và thông báo chưa đọc xuất hiện."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_action_my
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "My Activities"
msgstr "Hoạt động của tôi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Thời hạn hoạt động của tôi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "Mẫu của tôi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "My canned responses"
msgstr "Câu trả lời soạn sẵn của tôi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "Tên"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Cần có tác vụ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "New"
msgstr "Mới"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Channel"
msgstr "Kênh mới"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "New Message"
msgstr "Thông điệp mới"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__new_message_separator
msgid "New Message Separator"
msgstr "Dấu phân cách tin nhắn mới"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "New Thread"
msgstr "Chủ đề mới"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Giá trị Char mới"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Giá trị ngày giờ mới"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Giá trị Float mới"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Giá trị Integer mới"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Giá trị văn bản mới"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
msgid "New message"
msgstr "Gửi tin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "New messages appear here."
msgstr "Thông điệp mới xuất hiện ở đây."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "Giá trị mới"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Hoạt động tiếp theo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "Hoạt động tiếp theo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Thời hạn cho hoạt động tiếp theo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Next Monday Morning"
msgstr "Sáng thứ 2 tuần sau"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Hành động đang có kế tiêp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "No"
msgstr "Không"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "No Error"
msgstr "Không gặp lỗi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "No Followers"
msgstr "Không có người theo dõi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.js:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "No IM status available"
msgstr "Không có trạng thái IM khả dụng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "Không có dữ liệu"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr "Không có hoạt động nào."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No channel found"
msgstr "Không tìm thấy kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "No conversation selected."
msgstr "Không có trò chuyện được chọn."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No conversation yet..."
msgstr "Chưa có cuộc trò chuyện nào..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No history messages"
msgstr "Không có lịch sử tin nhắn"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
msgid "No message_id found in context"
msgstr "No message_id found in context"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
msgid "No messages found"
msgstr "Không tìm thấy tin nhắn nào"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "No recipient"
msgstr "Không có người nhận"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "No recipient found."
msgstr "Không tìm thấy người nhận."

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"Không nhận được phản hồi. Kiểm tra thông tin máy chủ.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr ""
"Không có người phụ trách nào được chỉ định cho %(activity_type_name)s: "
"%(activity_summary)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "No results found"
msgstr "Không có kết quả"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "No saved templates"
msgstr "Không có mẫu đã lưu nào"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "No starred messages"
msgstr "Không có tin nhắn gắn sao"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
msgid "No thread found."
msgstr "Không tìm thấy chủ đề nào."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.js:0
msgid "No thread named \"%(thread_name)s\""
msgstr "Không có chủ đề nào tên là \"%(thread_name)s\""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "Không có chủ đề cho câu trả lời"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "No user found"
msgstr "Không tìm thấy người dùng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "No user found that is not already a member of this channel."
msgstr "Không tìm thấy người dùng không phải là thành viên của kênh này."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "No users found"
msgstr "Không tìm thấy người dùng"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
msgid "Non existing record or wrong token."
msgstr "Bản ghi không tồn tại hoặc token không chính xác."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "None"
msgstr "Không"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "Email chuẩn hóa"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "Chưa được xác nhận"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr "Chưa được kiểm thử"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
msgid "Not interested by this?"
msgstr "Bạn không có hứng thú?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "Ghi chú"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#: model:ir.model.fields.selection,name:mail.selection__res_users_settings__channel_notifications__no_notif
msgid "Nothing"
msgstr "Không có gì"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Thông báo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "Email thông báo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/notification_item.xml:0
msgid "Notification Item Image"
msgstr "Hình ảnh mục thông báo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr "Thông số thông báo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
msgid "Notification Settings"
msgstr "Cài đặt thông báo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Loại thông báo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__notification_parameters
msgid "Notification parameters"
msgstr "Tham số thông báo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"Thông báo sẽ nhận tệp đính kèm dưới dạng danh sách hoặc bộ dữ liệu (%(aids)s"
" đã nhận)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr ""
"Thông báo sẽ nhận các bản ghi đính kèm dưới dạng danh sách ID (%(aids)s đã "
"nhận)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr ""
"Thông báo sẽ nhận các đối tác được cung cấp dưới dạng danh sách ID (%(pids)s"
" đã nhận)"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "Thông báo: Xoá thông báo quá 6 tháng"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Notify scheduled messages"
msgstr "Thông báo: Thông báo tin nhắn đã lên lịch"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.actions.client,name:mail.discuss_notification_settings_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model:ir.ui.menu,name:mail.menu_notification_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Thông báo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications allowed"
msgstr "Thông báo được phép"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Notifications blocked"
msgstr "Thông báo đã khóa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Notify Recipients"
msgstr "Thông báo cho người nhận"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "Notify everyone"
msgstr "Thông báo cho mọi người"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_count
msgid ""
"Number of days/week/month before executing the action after or before the "
"scheduled plan date."
msgstr ""
"Số ngày/tuần/tháng trước khi thực hiện tác vụ này trước hoặc sau ngày đã lên"
" lịch đã lên lịch."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Số ngày/tuần/tháng trước khi thực hiện công việc này. Nó cho phép lên kế "
"hoạch thời hạn thực hiện công việc."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will not send notifications on this device."
msgstr "Odoo sẽ không gửi thông báo trên thiết bị này."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
msgid "Odoo will send notifications on this device!"
msgstr "Odoo sẽ gửi thông báo trên thiết bị này!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr "Tắt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Offline"
msgstr "Offline"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Offline - %(offline_count)s"
msgstr "Offline - %(offline_count)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Char giá trị cũ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Giá trị ngày giờ cũ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Giá trị Float cũ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Giá trị Integer cũ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Giá trị văn bản cũ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "Giá trị cũ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Một khi một thông điệp được gắn sao, bạn có thể quay lại và xem lại nó bất "
"kỳ lúc nào."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_indicator.xml:0
msgid "Ongoing call"
msgstr "Cuộc gọi đang diễn ra"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Online"
msgstr "Online"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
msgid "Online - %(online_count)s"
msgstr "Online - %(online_count)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators are allowed to export mail message"
msgstr "Chỉ quản trị viên mới được phép xuất nội dung thư"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Only administrators can modify 'model' and 'res_id' fields."
msgstr "Chỉ quản trị viên mới có thể sửa đổi các trường 'model' và 'res_id'."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
msgid "Only custom models can be modified."
msgstr "Chỉ có thể sửa đổi các mô hình tùy chỉnh."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr "Chỉ người dùng nội bộ mới có thể nhận được thông báo trong Odoo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Only members of %(group_name)s group are allowed to edit templates "
"containing sensible placeholders"
msgstr ""
"Chỉ những thành viên của nhóm %(group_name)s mới được phép chỉnh sửa các mẫu"
" có chứa phần giữ chỗ hợp lý"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Only messages type comment can have their content updated"
msgstr "Chỉ bình luận thuộc loại tin nhắn mới có thể cập nhật nội dung"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr ""
"Chỉ bình luân loại tin nhắn mới có thể cập nhật nội dung của chúng trên mô "
"hình 'discuss.channel'"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
msgid "Open"
msgstr "Mở"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
msgid "Open Actions Menu"
msgstr "Mở menu tác vụ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
msgid "Open Channel"
msgstr "Mở kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Open Discuss App"
msgstr "Mở ứng dụng Thảo luận"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "Mở Tài liệu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Open Form View"
msgstr "Mở chế độ xem biểu mẫu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Open Link"
msgstr "Mở liên kết"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr "Mở người sở hữu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Open card"
msgstr "Mở thẻ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
msgid "Open in Discuss"
msgstr "Mở trong Thảo luận"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_view.xml:0
msgid "Open preview in a separate window."
msgstr "Mở bản xem trước trong một cửa sổ riêng."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Operation not supported"
msgstr "Hoạt động không được hỗ trợ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "Hủy tham gia"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID tùy chọn của một chủ đề (bản ghi) tập hợp tất cả tin nhắn nhận được, thậm"
" chí nếu đó là tin nhắn không có phản hồi. Nếu cài đặt, điều này sẽ tắt hoàn"
" toàn việc tạo các bản ghi mới. "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "ID mail_mail tuỳ chỉnh. Được sử dụng chủ yếu để tối ưu hoá tìm kiếm."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Máy chủ gửi thư ưa thích. Nếu không thiết lập, máy chủ gửi thư có mức ưu "
"tiên cao nhất sẽ được sử dụng."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Ngôn ngữ dịch tuỳ chọn (mã ISO) để chọn khi gửi email. Nếu không đặt, phiên "
"bản tiếng Anh sẽ được sử dụng. Đây luôn là biểu thức phần giữ chỗ cung cấp "
"ngôn ngữ thích hợp, VD: {{ object.partner_id.lang }}."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_sidebar.xml:0
msgid "Options"
msgstr "Tùy chọn"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"Thảo luận gốc: Thư trả lời gửi tới chủ đề thảo luận tài liệu gốc.\n"
" Địa chỉ email khác: Thư trả lời gửi tới địa chỉ email được nhắc tới trong message-id theo dõi thay vì chủ đề thảo luận tài liệu gốc.\n"
" Việc này có ảnh hưởng tới message-id được tạo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
msgid "Original message was deleted"
msgstr "Tin nhắn gốc đã bị xóa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "Original note:"
msgstr "Ghi chú gốc:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
msgid "Other activities"
msgstr "Hoạt động khác"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Xuất đi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr "Email đi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "Máy chủ gửi Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Máy chủ gửi email"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Thư gửi đi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Máy chủ gửi email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__overdue
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Overdue"
msgstr "Quá hạn"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Ghi đè email của tác giả"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Overwrite Template"
msgstr "Ghi đè mẫu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "Địa chỉ máy chủ POP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "Máy chủ POP/IMAP"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets received:"
msgstr "Gói đã nhận:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Packets sent:"
msgstr "Gói đã gửi:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Chính"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__parent_channel_id
msgid "Parent Channel"
msgstr "Kênh chính"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Tin nhắn chính"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "Mô hình chính"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID chủ đề bản ghi chính"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__parent_channel_id
msgid "Parent channel"
msgstr "Kênh chính"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Mô hình chính chứa bí danh này. Mô hình chứa tham chiếu bí danh không nhất "
"thiết phải là mô hình được đưa ra bởi alias_model_id (Ví dụ: dự án "
"(parent_model) và nhiệm vụ (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Kiểu phụ của phần tử chính, được sử dụng để đăng ký tự động. Trường này "
"không được đặt tên chính xác. Ví dụ về một dự án, parent_id của các kiểu phụ"
" dự án là các kiểu phụ liên quan đến nhiệm vụ."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.xml:0
msgid "Participant avatar"
msgstr "Avatar của người tham gia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_push_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "Đối tác"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
msgid "Partner Profile"
msgstr "Hồ sơ Đối tác"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "Đối tác chỉ xem"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "Đối tác có thông tin bổ sung để gửi lại thư"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "Đối tác"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Đối tác cần có tác vụ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "Mật khẩu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr "Dán khoá API của bạn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Pause"
msgstr "Tạm ngừng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__payload
msgid "Payload"
msgstr "Payload"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr "Xóa vĩnh viễn mẫu này"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Điện thoại"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "Cuộc gọi điện thoại"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Pick a specific time"
msgstr "Chọn khoảng thời gian cụ thể"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Pick an Activity Plan to launch"
msgstr "Chọn một Kế hoạch hoạt động để khởi chạy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Pin"
msgstr "Ghim"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Pin It"
msgstr "Ghim tin nhắn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr "Đã ghim"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
msgid "Pinned Messages"
msgstr "Tin nhắn đã ghim"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "Kế hoạch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr "Kế hoạch khả dụng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date
msgid "Plan Date"
msgstr "Ngày kế hoạch"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "Tên kế hoạch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_summary
msgid "Plan Summary"
msgstr "Tóm tắt kế hoạch"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "Tóm tắt kế hoạch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__planned
msgid "Planned"
msgstr "Đã lên kế hoạch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Planned Activities"
msgstr "Hoạt động đã lên kế hoạch"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Đã lên kế hoạch trong"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "Kế hoạch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Play"
msgstr "Mở"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
msgid "Please complete customer's information"
msgstr "Vui lòng hoàn thành thông tin khách hàng"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Vui lòng liên lạc với chúng tôi thay vì sử dụng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Please wait while the file is uploading."
msgstr "Hãy chờ khi tệp được tải lên."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Chính sách xử lý thông báo của cửa sổ trò chuyện:\n"
"- Xử lý bằng email: thông báo được gửi tới địa chỉ email của bạn\n"
"- Xử lý trong Odoo: thông báo xuất hiện trong hộp thư đến Odoo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Chính sách cho phép đăng tin nhắn lên tài liệu sử dụng cổng email.\n"
"- mọi người: mọi người có thể đăng\n"
"- đối tác: chỉ các đối tác đã xác thực\n"
"- người theo dõi: chỉ những người theo dõi của tài liệu liên quan hoặc thành viên của kênh đang theo dõi\n"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "Pop out Attachments"
msgstr "Tệp đính kèm bật lên"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "Cổng"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Granted"
msgstr "Truy cập cổng đã được phép"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Portal Access Revoked"
msgstr "Truy cập cổng bị thu hồi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Portal users can only filter threads by themselves as followers."
msgstr ""
"Người dùng cổng thông tin chỉ có thể lọc chủ đề theo chính họ với tư cách là"
" người theo dõi."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "Đưa vào tài liệu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tours/discuss_channel_tour.js:0
msgid "Post your message on the thread"
msgstr "Gửi tin nhắn của bạn lên kênh"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"Đăng tin nhắn phải được thực hiện trong tài liệu kinh doanh. Sử dụng "
"message_notify để gửi thông báo tới một người dùng."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"Việc đăng một tin nhắn sẽ nhận tệp đính kèm dưới dạng danh sách hoặc bộ dữ "
"liệu (%(aids)s đã nhận)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr ""
"Việc đăng một tin nhắn sẽ nhận các bản ghi đính kèm dưới dạng danh sách ID "
"(%(aids)s đã nhận)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr ""
"Việc đăng một tin nhắn sẽ nhận các đối tác được cung cấp dưới dạng danh sách"
" ID (%(pids)s đã nhận)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "Cung cấp bởi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Hoạt động trước"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "Địa chỉ trả lời ưa thích"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
msgid "Press Enter to start"
msgstr "Nhấn Enter để bắt đầu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr "Nhấn một phím để đặt làm phím tắt bấm để nói."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preview"
msgstr "Xem trước"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Preview my camera"
msgstr "Xem trước camera của tôi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Xem trước của"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Kiểu hoạt động trước"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "Tính riêng tư"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"Xử lý từng email đến như là một phần của một cuộc trò chuyện ứng với kiểu "
"tài liệu này. Việc này sẽ tạo các tài liệu mới cho các cuộc trò chuyện mới, "
"hoặc đính kèm các follow-up email và cuộc trò chuyện (tài liệu) hiện có."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
msgid "Processing"
msgstr "Đang xử lý"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
msgid "Public Channel"
msgstr "Kênh công khai"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "Public Channels"
msgstr "Kênh Công cộng"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Hợp đồng bảo hành nhà phát hành"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "Nhà xuất bản: Cập nhật thông báo"

#. module: mail
#: model:ir.model,name:mail.model_mail_push_device
msgid "Push Notification Device"
msgstr "Thiết bị nhận thông báo đẩy"

#. module: mail
#: model:ir.model,name:mail.model_mail_push
msgid "Push Notifications"
msgstr "Thông báo đẩy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push to Talk"
msgstr "Đẩy để nói"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Push to talk"
msgstr "Đẩy để nói"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "Phím tắt bấm để nói"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Push-to-talk key"
msgstr "Phím bấm để nói"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search"
msgstr "Tìm kiếm nhanh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_quick_search.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Quick search…"
msgstr "Tìm kiếm nhanh..."

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "Phiên RTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "RTC Session ID:"
msgstr "ID phiên RTC:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr "Phiên RTC"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "Phiên RTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Raise Hand"
msgstr "Dơ tay"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr ""
"Phạm vi từ 0.0 tới 1.0, quy mô tùy thuộc vào việc triển khai trình duyệt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "Khách phản ứng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "Đối tác phản ứng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "Phản ứng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "Ngày đọc"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read Less"
msgstr "Đọc ít hơn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web_portal/message_patch.js:0
msgid "Read More"
msgstr "Tìm hiểu thêm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
msgid "Ready"
msgstr "Sẵn sàng"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Sẵn sàng gửi đi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/bus_connection_alert.xml:0
msgid "Real-time connection lost..."
msgstr "Mất kết nối theo thời gian thực..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Lý do"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr "Nhận thông báo trong Odoo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Đã nhận"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
msgid "Recent"
msgstr "Gần đây"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Người nhận"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr "Tên người nhận"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Người nhận"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr "Hoạt động được đề xuất"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Kiểu Hoạt động được Khuyến nghị"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Bản ghi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "Tên bản ghi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID chủ đề bản ghi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Các tham chiếu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
msgid "Refuse"
msgstr "Từ chối"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Trân trọng,"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Register new key"
msgstr "Đăng ký khóa mới"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
msgid "Reject"
msgstr "Từ chối"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "Công ty liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "ID tài liệu liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr "ID tài liệu liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__res_id
msgid "Related Document Id"
msgstr "ID tài liệu liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Mô hình tài liệu liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Tên Mô hình tài liệu liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "Mẫu thư liên quan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Thông điệp liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Đối tác liên quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Trường quan hệ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
msgid "Remove"
msgstr "Xóa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Remove Blur"
msgstr "Xoá hiệu ứng mờ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr "Xoá tác vụ theo ngữ cảnh"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "Xoá người theo dõi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Remove address from blacklist"
msgstr "Xoá địa chỉ khỏi danh sách hạn chế"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "Công cụ xoá email khỏi danh sách hạn chế"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Xóa tác vụ theo ngữ cảnh để sử dụng mẫu này trên các tài liệu liên quan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
msgid "Remove this follower"
msgstr "Gỡ người theo dõi này"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr "Thông tin trường đã bị xóa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Rename Thread"
msgstr "Đặt lại tên chủ đề"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "Kết xuất mô hình"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr "Không thể kết xuất %(field_name)s vì không có bản sao nào trên mẫu."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr "Không thể kết xuất %(field_name)s vì không thể xác định trên mẫu."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
msgid "Repeat"
msgstr "Lặp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "Trả lời"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Reply"
msgstr "Trả lời"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Trả lời Đến"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Địa chỉ email trả lời. Thiết lập reply_to sẽ bỏ qua việc tạo chủ đề (thread)"
" tự động."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Trả lời đến"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr "Địa chỉ trả lời"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Replying to"
msgstr "Trả lời tới"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Report"
msgstr "Báo cáo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "Yêu cầu đối tác"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "Gửi lại"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr "Gửi lại email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Tính năng gửi lại"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "Đặt lại Chưa Xác nhận"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr "Đặt lại mẫu email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr "Đặt lại mẫu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr "Đặt lại mật khẩu của bạn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "Người phụ trách"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "Giới hạn kết xuất mẫu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr "Hạn chế chỉnh sửa mẫu email và việc sử dụng phần giữ chỗ QWEB."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr "Tệp đính kèm bị hạn chế"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr "Kết quả phát hiện ngôn ngữ dựa trên nội dung của nó."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Thử lại"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Revert"
msgstr "Đảo ngược"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "Xem lại tất cả mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "Nội dung Rich-text"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Thông điệp Rich-text/HTML"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr "Ringing session"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "Phiên Rtc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr "URL máy chủ SFU"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr "Khoá máy chủ SFU"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr "Máy chủ SFU"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "Máy chủ SMTP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Chuyên viên sales"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Save"
msgstr "Lưu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Save as Template"
msgstr "Lưu dưới dạng Mẫu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Save editing"
msgstr "Lưu chỉnh sửa"

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr "Lưu GIF yêu thích từ API Tenor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Lịch trình"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr "Lên lịch & đánh dấu là hoàn tất"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_model.js:0
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity"
msgstr "Lên lịch hoạt động"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "Schedule Activity On Selected Records"
msgstr "Lên lịch hoạt động trên các bản ghi đã chọn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Message"
msgstr "Lên lịch tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Schedule Note"
msgstr "Lên lịch ghi chú"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule activities to help you get things done."
msgstr "Lên lịch hoạt động để giúp bạn hoàn thành công việc."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "Schedule activity"
msgstr "Lên lịch hoạt động"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
msgid "Schedule an Activity"
msgstr "Lên lịch một hoạt động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity"
msgstr "Lên lịch một hoạt động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
msgid "Schedule an activity on selected records"
msgstr "Lên lịch hoạt động trên các bản ghi đã chọn"

#. module: mail
#: model:ir.model,name:mail.model_ir_cron
msgid "Scheduled Actions"
msgstr "Tác vụ đã lên lịch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Ngày đã lên lịch"

#. module: mail
#: model:ir.model,name:mail.model_mail_scheduled_message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Scheduled Message"
msgstr "Tin nhắn đã lên lịch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr "Tin nhắn đã lên lịch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Ngày gửi đã lên lịch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "Kịch bản"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
msgid "Search"
msgstr "Tìm kiếm"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "Tìm kiếm Bí danh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "Tìm kiếm Nhóm"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "Tìm máy chủ thư đến"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
msgid "Search Message"
msgstr "Tìm kiếm tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
msgid "Search Messages"
msgstr "Tìm kiếm tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Search More..."
msgstr "Tìm kiếm thêm..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "Tìm kiếm phiên RTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search Sub Channels"
msgstr "Tìm kiếm kênh phụ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search button"
msgstr "Nút tìm kiếm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search by name"
msgstr "Tìm kiếm theo tên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search for a GIF"
msgstr "Tìm kiếm một GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a channel..."
msgstr "Tìm kiếm một kênh..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "Search for a user..."
msgstr "Tìm kiếm một người dùng..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_message_input.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "Search in progress"
msgstr "Đang tìm kiếm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "Search..."
msgstr "Tìm kiếm..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
msgid "Search: %s"
msgstr "Tìm: %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Email Changed"
msgstr "Cập nhật bảo mật: Email đã thay đổi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Login Changed"
msgstr "Cập nhật bảo mật: Thông tin đăng nhập đã thay đổi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Security Update: Password Changed"
msgstr "Cập nhật bảo mật: Mật khẩu đã thay đổi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr "Xem thông tin lỗi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "See all pinned messages."
msgstr "Xem tất cả tin nhắn đã ghim."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user)s"
msgstr "%(user)s đã xem"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s and %(user2)s"
msgstr "%(user1)s và %(user2)s đã xem"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s and %(user3)s"
msgstr "%(user1)s, %(user2)s và %(user3)s đã xem"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and %(count)s others"
msgstr "%(user1)s, %(user2)s, %(user3)s và %(count)s người khác đã xem"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by %(user1)s, %(user2)s, %(user3)s and 1 other"
msgstr "%(user1)s, %(user2)s, %(user3)s và một người khác đã xem"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
msgid "Seen by everyone"
msgstr "Mọi người đã xem"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.xml:0
msgid "Seen by:"
msgstr "Được xem bởi:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr "Chọn ngôn ngữ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Select a user..."
msgstr "Chọn một người dùng..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr "Chọn bộ lọc nội dung được sử dụng để lọc GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Send"
msgstr "Gửi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr "Gửi & đóng"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "Gửi email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr "Gửi email dưới dạng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_send_dropdown.xml:0
msgid "Send Later"
msgstr "Gửi sau"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Send Mail (%s)"
msgstr "Gửi email (%s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Send Now"
msgstr "Gửi ngay"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web_portal/composer_patch.js:0
msgid "Send a message to followers…"
msgstr "Gửi tin nhắn cho người theo dõi..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr "Gửi và nhận email bằng tài khoản Gmail của bạn."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr "Gửi và nhận email bằng tài khoản Outlook của bạn."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "Gửi email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr "Gửi thư hoặc thông báo trực tiếp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "Send message"
msgstr "Gửi tin"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "Địa chỉ email người gửi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Địa chỉ người gửi (có thể dùng phần giữ chỗ ở đây). Nếu không thiết lập, giá"
" trị mặc định sẽ là bí danh email - nếu được cấu hình, hoặc một địa chỉ "
"email."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr "Lỗi gửi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_seen_indicator.js:0
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Sent"
msgstr "Đã gửi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "Máy chủ & Đăng nhập"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Tác vụ phía máy chủ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "Thông tin máy chủ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "Tên máy chủ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "Mức ưu tiên Máy chủ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Kiểu máy chủ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr "Thông tin loại máy chủ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
msgid "Server error"
msgstr "Lỗi máy chủ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"Máy chủ đã trả lời với ngoại lệ sau:\n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "Kiểu máy chủ IMAP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "Kiểu máy chủ POP"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "Đặt đang hoạt động thành sai để ẩn kênh và không cần xóa bỏ kênh. "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "Cài đặt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr "UUID kênh SFU"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr "URL máy chủ SFU"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Share Screen"
msgstr "Chia sẻ màn hình"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_canned_response_view_search
msgid "Shared canned responses"
msgstr "Câu trả lời soạn sẵn đã chia sẻ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Shared with all users."
msgstr "Đã chia sẻ với tất cả người dùng."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__source
msgid "Shortcut"
msgstr "Phím tắt"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr "Mã ngôn ngữ viết tắt được sử dụng làm ngôn ngữ đích cho yêu cầu dịch."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
msgid "Show Followers"
msgstr "Hiện người theo dõi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
msgid "Show a helper message"
msgstr "Hiển thị một thông điệp trợ giúp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
msgid "Show activities"
msgstr "Hiển thị hoạt động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/base_recipients_list.xml:0
msgid "Show all recipients"
msgstr "Hiển thị tất cả người nhận"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Hiển thị tất cả bản ghi có ngày xử lý tiếp theo trước ngày hôm nay"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show less"
msgstr "Hiển thị ít"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
msgid "Show more"
msgstr "Hiển thị thêm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
msgid "Show sidebar"
msgstr "Hiển thị thanh bên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Show video participants only"
msgstr "Chỉ hiển thị người tham gia video"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Showing"
msgstr "Hiển thị"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Thanh tác vụ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Tác vụ thanh bên để mẫu này khả dụng trong bản ghi của mô hình tài liệu liên"
" quan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr "Tên trang web"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Snooze 7d"
msgstr "Tạm tắt trong 7 ngày"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "So uhh... maybe go favorite some GIFs?"
msgstr "Vậy, uhh... bạn có thể đưa một số GIF vào mục yêu thích?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Nguồn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr "Ngôn ngữ nguồn"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Người dùng cụ thể"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Chỉ định một mô hình nếu hoạt động phải cụ thể cho một mô hình và không có "
"sẵn khi quản lý hoạt động cho các mô hình khác."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/store_service_patch.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
msgid "Starred"
msgstr "Được gắn sao"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr "Nội dung được gắn sao"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Call"
msgstr "Bắt đầu cuộc gọi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
msgid "Start a Conversation"
msgstr "Bắt đầu một cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
msgid "Start a Video Call"
msgstr "Bắt đầu cuộc gọi video"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a conversation"
msgstr "Bắt đầu cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/discuss_sidebar_patch.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
msgid "Start a meeting"
msgstr "Bắt đầu cuộc họp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "Trạng thái"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Trạng thái"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Hạn chót hạn đã qua\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Cần thực hiện trong tương lai."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "Thời gian của trạng thái"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
msgid "Status with time"
msgstr "Trạng thái kèm thời gian"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Stay tuned! Enable push notifications to never miss a message."
msgstr ""
"Luôn cập nhật thông tin! Bật thông báo đẩy để không bao giờ bỏ lỡ tin nhắn."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "Số bước"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Stop Recording"
msgstr "Ngừng ghi âm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop Sharing Screen"
msgstr "Ngừng chia sẻ màn hình"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Stop camera"
msgstr "Dừng camera"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "Stop replying"
msgstr "Dừng trả lời"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr ""
"Lưu trữ email và email trả lời trong cửa sổ trò chuyện của từng bản ghi"

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr "Lưu dữ liệu xem trước liên kết"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"Chuỗi được định dạng để đại diện mã khóa có từ khóa theo mẫu sau: "
"shift.ctrl.alt.key, VD: truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr "Chuỗi nhận được từ yêu cầu dịch."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sub_channel_ids
msgid "Sub Channels"
msgstr "Kênh phụ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_scheduled_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Tiêu đề"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "Chủ đề (có thể sử dụng phần giữ chỗ)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message.xml:0
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Subject:"
msgstr "Chủ đề:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "Đăng ký người nhận"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_canned_response__substitution
msgid "Substitution"
msgstr "Thay thế"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Kiểu phụ"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Kiểu phụ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "Gợi ý"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "Gợi ý hoạt động kế tiếp"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr ""
"Gợi ý những hoạt động này khi hoạt động hiện tại được đánh dấu hoàn thành."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "Tóm tắt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "Tóm tắt:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr "Hỗ trợ xác thực Gmail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr "Hỗ trợ xác thực Outlook"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Tham số hệ thống"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
msgid "System notification"
msgstr "Thông báo của hệ thống"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "Tab to select"
msgstr "Tab để chọn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr "Ngôn ngữ đích"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "Mô hình mục tiêu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "Mã số thuế"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Technical Settings"
msgstr "Cài đặt kỹ thuật"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"Trường kỹ thuật để theo dõi mô hình khi bắt đầu chỉnh sửa nhằm hỗ trợ hành "
"vi liên quan đến UX"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "Mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr "Danh mục mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template Description"
msgstr "Mô tả mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr "Tên tệp mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_name
msgid "Template Name"
msgstr "Tên mẫu"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Xem trước Mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "Ngôn ngữ mẫu xem trước"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr "Mixin đặt lại mẫu"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
msgid "Template creation from composer requires a valid model."
msgstr "Việc tạo mẫu từ trình soạn thảo yêu cầu một mô hình hợp lệ."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering should only be called with a list of IDs. Received "
"“%(res_ids)s” instead."
msgstr ""
"Chỉ nên gọi kết xuất mẫu bằng danh sách ID. Thay vào đó đã nhận được "
"“%(res_ids)s”."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""
"Kết xuất mẫu chỉ hỗ trợ inline_template, qweb, hoặc qweb_view (chế độ xem "
"hoặc thô); thay vào đó đã nhận %(engine)s."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr "Mã khóa API Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr "Khoá API Tenor GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr "Bộ lọc nội dung Tenor GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr "Giới hạn Tenor GIF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr "Giới hạn Tenor GIF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr "Bộ lọc nội dung Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "Kiểm thử & Xác nhận"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr "Bản ghi kiểm thử:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr "Nội dung văn bản"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "The"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "The 'Due Date In' value can't be negative."
msgstr "Giá trị 'Ngày đến hạn vào' phải lớn hơn 0."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
msgid ""
"The 'To-Do' activity type is used to create reminders from the top bar menu "
"and the command palette. Consequently, it cannot be archived or deleted."
msgstr ""
"Loại hoạt động 'Việc cần làm' được sử dụng để tạo nhắc nhở từ thanh menu "
"trên cùng và bảng lệnh. Do đó, không thể được lưu trữ hoặc xóa loại hoạt "
"động này."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
msgid "The Fullscreen mode was denied by the browser"
msgstr "Chế độ Toàn màn hình bị trình duyệt từ chối"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/ptt_extension_service.js:0
msgid ""
"The Push-to-Talk feature is only accessible within tab focus. To enable the "
"Push-to-Talk functionality outside of this tab, we recommend downloading our"
" %(anchor_start)sextension%(anchor_end)s."
msgstr ""
"Tính năng Push-to-Talk chỉ có thể truy cập được trong tiêu điểm tab. Để bật "
"tính năng Push-to-Talk bên ngoài tab này, bạn nên tải xuống %(anchor_start)s"
" extension %(anchor_end)scủa chúng tôi."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"Mã số thuế. Các giá trị ở đây sẽ được xác thực dựa trên định dạng quốc gia. "
"Bạn có thể sử dụng dấu '/' để chỉ một đối tác không phải chịu thuế."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The activity cannot be launched:"
msgstr "Không thể khởi chạy hoạt động:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""
"Loại hoạt động \"%(activity_type_name)s\" không tương thích với kế hoạch "
"\"%(plan_name)s\" bởi vì nó bị giới hạn trong mô hình "
"\"%(activity_type_model)s\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr ""
"Tệp đính kèm %s không tồn tại hoặc bạn không có quyền truy cập vào tệp đó."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
msgid "The attachment %s does not exist."
msgstr "Tệp đính kèm %s không tồn tại."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.js:0
msgid "The avatar has been updated!"
msgstr "Avatar đã được cập nhật!"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "Kênh UUID phải là duy nhất"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr "Loại kênh không được để trống"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
msgid "The conversation is empty."
msgstr "Cuộc trò chuyện trống."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr "Người dùng hiện tại có thể chỉnh sửa mẫu."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "The duration of voice messages is limited to 1 minute."
msgstr "Thời lượng của tin nhắn thoại được giới hạn trong 1 phút."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "Thư  đã được gửi đến"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
msgid "The email template(s) have been restored to their original settings."
msgstr "(Các) mẫu email đã được khôi phục về cài đặt ban đầu."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_push_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr "Endpoint phải là duy nhất!"

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""
"Không thể đặt lại các mẫu email sau vì không tìm thấy tệp nguồn liên quan của chúng:\n"
"- %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "Người dùng nội bộ phụ trách liên lạc này."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr "Nội dung cuối cùng nhận được bằng bí danh này đã gây ra lỗi."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr "Không thể chấp nhận nội dung dưới đây bằng địa chỉ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"Không thể chấp nhận nội dung dưới đây bằng địa chỉ %(alias_display_name)s.\n"
"                 Chỉ %(contact_description)s được phép liên hệ với địa chỉ này.<br /><br />\n"
"                 Hãy đảm bảo rằng bạn đang sử dụng đúng địa chỉ hoặc liên hệ với chúng tôi qua %(default_email)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"Không thể chấp nhận nội dung dưới đây bằng địa chỉ %(alias_display_name)s.\n"
"Vui lòng thử lại sau hoặc liên hệ %(company_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"The message scheduled on %(model)s(%(id)s) with the following content could "
"not be sent:%(original_message)s"
msgstr ""
"Không thể gửi tin nhắn đã lên lịch vào %(model)s(%(id)s) với nội dung sau: "
"%(original_message)s"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__from_message_id
msgid "The message the channel was created from."
msgstr "Tin nhắn mà từ đó kênh được tạo ra."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Mô hình (Loại tài liệu Odoo) mà bí danh này tương tác. Mọi email đến mà "
"không phải là email trả lời một bản ghi hiện có sẽ tạo ra một bản ghi mới "
"trong mô hình này. (ví dụ: Nhiệm vụ dự án) "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Tên của bí danh email, ví dụ: 'jobs' nếu bạn muốn nhận email gửi đến địa chỉ"
" <<EMAIL>>"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr "Không thể khởi chạy kế hoạch \"%(plan_name)s\":"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The plan \"%(plan_name)s\" has been started"
msgstr "Kế hoạch \"%(plan_name)s\" đã bắt đầu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "Quản lý hàng đợi sẽ gửi email sau ngày"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
msgid "The records must belong to the same company."
msgstr "Các bản ghi phải thuộc về cùng một công ty."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_message.py:0
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %(type)s, Operation: %(operation)s)\n"
"\n"
"Records: %(records)s, User: %(user)s"
msgstr ""
"Không thể hoàn thành thao tác được yêu cầu vì hạn chế bảo mật. Vui lòng liên hệ với quản trị viên hệ thống của bạn.\n"
"\n"
"(Loại tài liệu: %(type)s, Thao tác: %(operation)s)\n"
"\n"
"Bản ghi: %(records)s, Người dùng: %(user)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "The server \"%s\" cannot be used because it is archived."
msgstr "Không thể sử dụng máy chủ \"%s\" vì nó đã được lưu trữ."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
msgid "The subscription preferences were successfully applied."
msgstr "Các tùy chọn đăng ký đã được áp dụng thành công."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr "Mẫu thuộc về người dùng này"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr ""
"Phần đầu chỉ chứa văn bản của nội dung email được sử dụng làm bản xem trước "
"email."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr "Chỉ có thể có một phiên rtc cho mỗi thành viên kênh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Đây"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action can only be done on a mail thread models"
msgstr "Tác vụ này chỉ có thể được thực hiện trên mô hình chủ đề email"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
msgid "This action cannot be done on transient models."
msgstr "Không thể thực hiện tác vụ này trên các mô hình tạm thời."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
msgid "This action will send an email."
msgstr "Tác vụ này sẽ gửi một email."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This channel doesn't have any attachments."
msgstr "Kênh này không có bất kỳ tệp đính kèm nào."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This channel doesn't have any pinned messages."
msgstr "Kênh này không có bất kỳ tin nhắn nào được ghim."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
msgid "This channel has no thread yet."
msgstr "Kênh này chưa có chủ đề nào."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
msgid "This conversation doesn't have any attachments."
msgstr "Cuộc trò chuyện này không có bất kỳ tệp đính kèm nào."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
msgid "This conversation doesn't have any pinned messages."
msgstr "Cuộc trò chuyện này không có bất kỳ tin nhắn nào được ghim."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Email này bị hạn chế đối với gửi thư hàng loạt. Bấm để loại khỏi danh sách "
"hạn chế."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Trường này không phân biệt chữ hoa chữ thường."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr "Trường này được sử dụng trong mô tả nội bộ về cách sử dụng mẫu."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Trường này được sử dụng để tìm kiếm trên địa chỉ email vì trường email chính"
" có thể chứa nhiều hơn chỉ một địa chỉ email."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
msgid "This layout seems to no longer exist."
msgstr "Bố cục này có vẻ không còn tồn tại."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/scheduled_message_model.js:0
msgid "This message has already been sent."
msgstr "Tin nhắn này đã được gửi đi."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"Tùy chọn này sẽ xóa vĩnh viễn mọi dấu vết của email sau khi nó được gửi đi, "
"bao gồm cả từ menu Kỹ thuật trong Cài đặt, để duy trì không gian lưu trữ "
"trong cơ sở dữ liệu của bạn."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
msgid "This record has an exception activity."
msgstr "Dữ liệu này có một hoạt động ngoại lệ."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_notification_settings.xml:0
msgid ""
"This setting will be applied to all channels using the default notification "
"settings."
msgstr ""
"Cài đặt này sẽ được áp dụng cho tất cả các kênh sử dụng cài đặt thông báo "
"mặc định."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__channel_notifications
msgid ""
"This setting will only be applied to channels. Mentions only if not "
"specified."
msgstr ""
"This setting will only be applied to channels. Mentions only if not "
"specified."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr ""
"Những giá trị đó không được hỗ trợ dưới dạng tùy chọn khi kết xuất: "
"%(param_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr ""
"Những giá trị đó không được hỗ trợ khi đăng nội dung hoặc thông báo: "
"%(param_names)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Chủ đề"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread Image"
msgstr "Ảnh chủ đề"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.xml:0
msgid "Thread has unread messages"
msgstr "Chủ đề có tin nhắn chưa đọc"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
#: code:addons/mail/static/src/core/common/chat_hub.xml:0
msgid "Thread image"
msgstr "Ảnh chủ đề"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr "Đã bật chủ đề"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/sub_channel_list.xml:0
#: code:addons/mail/static/src/discuss/core/public_web/thread_actions.js:0
msgid "Threads"
msgstr "Chủ đề"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_tz
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Múi giờ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "Tiêu đề"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "To"
msgstr "Đến"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Đến (Email)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Đến (Đối tác)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
msgid "To :"
msgstr "Đến :"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "To peer:"
msgstr "Đến mạng ngang hàng:"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr "Việc cần làm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter.xml:0
msgid "To:"
msgstr "Đến:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__card_campaign__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_model__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_candidate__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_department__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_routing_workcenter__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__today
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Today"
msgstr "Hôm nay"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Today at %(time)s"
msgstr "Hôm nay lúc %(time)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Today:"
msgstr "Hôm nay:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Tomorrow"
msgstr "Ngày mai"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Afternoon"
msgstr "Chiều mai"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/mail_composer_schedule_dialog.xml:0
msgid "Tomorrow Morning"
msgstr "Sáng mai"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Tomorrow:"
msgstr "Ngày mai:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Các chủ đề được thảo luận trong nhóm này..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr "Theo dõi người nhận"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Các giá trị được theo dõi được lưu trữ trong một mô hình riêng biệt. Trường "
"này cho phép tạo lại theo dõi và tạo thống kê trên mô hình."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "Theo dõi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Kiểm soát thay đổi"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Giá trị theo dõi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Giá trị truy vết"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "Translate"
msgstr "Dịch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr "Nội dung bản dịch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
msgid "Translation Failure"
msgstr "Lỗi dịch"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__delay_from
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Kích hoạt"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "Điều kiện cho tác vụ kế tiếp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr "Thử lại"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Turn camera on"
msgstr "Bật camera"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu_patch.js:0
msgid "Turn on notifications"
msgstr "Bật thông báo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Token xác thực tài khoản Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "SID tài khoản Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Loại"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Loại trễ"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Loại tác vụ máy chủ. Các giá trị sau đây khả dụng:\n"
"- 'Cập nhật bản ghi': cập nhật giá trị của bản ghi\n"
"- 'Tạo hoạt động': tạo một hoạt động (Thảo luận)\n"
"- 'Gửi email': gửi tin nhắn, ghi chú hoặc email (Thảo luận)\n"
"- 'Gửi SMS': gửi SMS, thêm chúng vào tài liệu (SMS)- 'Thêm/Xóa người theo dõi': thêm hoặc xóa người theo dõi vào/khỏi một bản ghi (Thảo luận)\n"
"- 'Tạo bản ghi': tạo một bản ghi mới với các giá trị mới\n"
"- 'Thực thi mã': một khối mã Python sẽ được thực thi\n"
"- 'Gửi thông báo Webhook': gửi yêu cầu POST tới hệ thống bên ngoài, hay còn gọi là Webhook\n"
"- 'Thực thi tác vụ hiện có': xác định một tác vụ kích hoạt một số tác vụ máy chủ khác\n"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trong bản ghi."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Type the name of a person"
msgstr "Gõ tên của một người"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
msgid "Unable to connect to SMTP Server"
msgstr "Không thể kết nối với Máy chủ SMTP"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Không thể gửi tin nhắn, vui lòng định cấu hình địa chỉ email của người gửi "
"tin."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Unable to send message, please configure the sender's email address."
msgstr ""
"Không thể gửi nội dung, vui lòng cấu hình địa chỉ email của người gửi."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign"
msgstr "Ngừng chỉ định"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
msgid "Unassign from me"
msgstr "Bỏ phân công cho tôi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "Bỏ khỏi danh sách đen"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
msgid "Unblock Reason: %(reason)s"
msgstr "Lý do bỏ chặn: %(reason)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Undeafen"
msgstr "Bỏ tắt tiếng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/chatter/web/chatter_patch.js:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_invite
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Unfollow"
msgstr "Ngừng theo dõi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Unit"
msgstr "Đơn vị"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__delay_unit
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Đơn vị trễ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
msgid "Unknown"
msgstr "Không xác định"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
msgid "Unknown error"
msgstr "Lỗi không xác định"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
msgid "Unknown error: %(error)s"
msgstr "Lỗi không xác định: %(error)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_actions.js:0
msgid "Unmute"
msgstr "Bỏ tắt tiếng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Unmute Conversation"
msgstr "Bật lại thông báo cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_actions.js:0
msgid "Unpin"
msgstr "Bỏ ghim"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Conversation"
msgstr "Hủy ghim cuộc trò chuyện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Unpin Message"
msgstr "Bỏ ghim tin nhắn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid "Unpin Thread"
msgstr "Bỏ ghim chủ đề"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__unpin_dt
msgid "Unpin date"
msgstr "Ngày bỏ ghim"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Bộ đếm tin chưa đọc"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Thông điệp chưa đọc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr "Tệp đính kèm không bị hạn chế"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "Unselect person"
msgstr "Bỏ chọn người"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
msgid "Unstar all"
msgstr "Thôi Gắn sao Tất cả"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "Unsupported report type %s found."
msgstr "Tìm thấy loại báo cáo %s không được hỗ trợ."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until %s"
msgstr "Cho đến %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/settings_model.js:0
msgid "Until I turn it back on"
msgstr "Cho đến khi tôi bật lại"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.xml:0
msgid "Untitled"
msgstr "Không có tiêu đề"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr "Cập nhật bố cục email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mail_composer_template_selector.js:0
msgid "Update Template"
msgstr "Cập nhật mẫu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/public_web/discuss.xml:0
msgid "Upload Avatar"
msgstr "Tải lên avatar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "Tải lên tài liệu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload File"
msgstr "Tải lên tệp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
msgid "Upload file"
msgstr "Tải tập tin lên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Upload:"
msgstr "Tải lên:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploaded"
msgstr "Đã tải lên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
msgid "Uploading"
msgstr "Đang tải lên"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""
"Dùng 'Người dùng cụ thể' để luôn phân công cùng một người dùng cho hoạt động"
" tiếp theo. Sử dụng 'Người dùng không cố định' để chỉ định tên trường của "
"người dùng cần chọn trong bản ghi. "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "Sử dụng Máy chủ email tùy chỉnh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
msgid "Use Default"
msgstr "Sử dụng mặc định"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "Sử dụng máy chủ Twilio ICE"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "Sử dụng máy chủ Gmail"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
msgid "Use a local script to fetch your emails and create new records."
msgstr ""
"Sử dụng ngôn ngữ kịch bản cục bộ để tìm nạp email của bạn và tạo bản ghi "
"mới."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr "Sử dụng máy chủ Outlook"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid ""
"Use default from user settings if not specified. This setting will only be "
"applied to channels."
msgstr ""
"Sử dụng mặc định từ cài đặt người dùng nếu không xác định. Cài đặt này sẽ "
"chỉ được áp dụng cho các kênh."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr "Sử dụng miền khác cho bí danh email của bạn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr "Sử dụng theo lô"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "Use message_notify to send a notification to an user."
msgstr "Sử dụng message_notify để gửi thông báo cho người dùng."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Sử dụng mẫu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "Sử dụng tính năng push to talk"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "Dùng trong"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr "Được sử dụng làm bối cảnh dùng để đánh giá miền trình soạn thảo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Được sử dụng để phân loại trình tạo tin nhắn\n"
"'email': được tạo bởi một email đến, VD: cổng thư\n"
"'bình luận': được tạo bởi thông tin người dùng nhập vào, VD: thông qua thảo luận hoặc trình soạn thảo\n"
"'email_outgoing': được tạo bằng cách gửi thư\n"
"'thông báo': được tạo bởi hệ thống, VD: theo dõi tin nhắn\n"
"'auto_comment': được tạo bởi cơ chế thông báo tự động, VD: báo nhận\n"
"'user_notification': được tạo cho một người nhận cụ thể"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "Được dùng để hiển thị tiền tệ khi theo dõi giá trị tiền"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Được sử dụng để đặt các subtypes."

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Người dùng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr "Trường người dùng"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Người dùng hiện diện"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "Cài đặt người dùng"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "Cài đặt người dùng"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "Cài đặt người dùng Dung lượng"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "Thông báo cụ thể cho người dùng"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "Loại người dùng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is a bot"
msgstr "Người dùng là máy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is idle"
msgstr "Người dùng đang nghỉ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is offline"
msgstr "Người dùng đã offline"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "User is online"
msgstr "Người dùng đang online"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr "Người dùng không được có GIF yêu thích trùng lặp"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Tên đăng nhập"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "Users in this channel: %(members)s."
msgstr "Người dùng trong kênh này: %(members)s."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"Người dùng sẽ có thể kết xuất mẫu.\n"
"Tuy nhiên chỉ Biên tập viên mẫu email mới có thể tạo mẫu động mới hoặc sửa đổi mẫu hiện có."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"Sử dụng máy chủ email của riêng bạn là bắt buộc để gửi/nhận email trong các "
"phiên bản Cộng đồng và Doanh nghiệp. Người dùng online đã được hưởng lợi từ "
"máy chủ email sẵn sàng sử dụng (@tencongty.com)."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "Hợp lệ"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"Không thể xác thực giá trị %(allowed_domains)s cho `mail.catchall.domain.allowed`.\n"
"Nó phải là một danh sách các tên miền được phân tách bằng dấu phẩy. VD: example.com,example.org."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Video Settings"
msgstr "Cài đặt video"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "Video player:"
msgstr "Trình phát video:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
msgid "View"
msgstr "Chế độ xem"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "View %s"
msgstr "Xem %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
msgid "View Profile"
msgstr "Xem trang cá nhân"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
msgid "View Reactions"
msgstr "Xem phản ứng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/message_actions.js:0
msgid "View Thread"
msgstr "Xem chủ đề"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Dạng hiển thị"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
msgid "View all activities"
msgstr "Xem tất cả hoạt động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories_patch.js:0
msgid "View or join channels"
msgstr "Xem hoặc tham gia kênh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "Giọng nói"

#. module: mail
#: model:ir.ui.menu,name:mail.menu_call_settings
msgid "Voice & Video"
msgstr "Giọng nói & video"

#. module: mail
#: model:ir.actions.client,name:mail.discuss_call_settings_action
msgid "Voice & Video Settings"
msgstr "Cài đặt giọng nói & video"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice Detection"
msgstr "Phát hiện giọng nói"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
msgid "Voice Message"
msgstr "Tin nhắn thoại"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice detection threshold"
msgstr "Ngưỡng phát hiện giọng nói"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
msgid "Voice recording stopped"
msgstr "Ghi âm đã ngừng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "Voice settings"
msgstr "Cài đặt giọng nói"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Thể tích"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "Dung lượng theo mỗi đối tác"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "Dung lượng của các đối tác khác"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr ""
"Bạn muốn thêm gia vị cho cuộc trò chuyện của mình bằng ảnh GIF? Hãy kích "
"hoạt tính năng này trong cài đặt!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
msgid "Warning"
msgstr "Cảnh báo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""
"Chúng tôi không thể tạo bí danh %(alias_name)s vì miền %(alias_domain_name)s"
" thuộc về công ty %(alias_company_names)s trong khi tài liệu sở hữu thuộc về"
" công ty %(company_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""
"Chúng tôi không thể tạo bí danh %(alias_name)s vì miền %(alias_domain_name)s"
" thuộc về công ty %(alias_company_names)s trong khi tài liệu đích thuộc về "
"công ty %(company_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "We were not able to fetch value of field '%(field)s'"
msgstr "Chúng tôi không thể tìm nạp giá trị của trường '%(field)s'"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Tuần"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr "Chào mừng đến với MyCompany!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr ""
"Chà, không có gì là tồn tại mãi mãi, nhưng bạn có chắc chắn muốn bỏ ghim tin"
" nhắn này không?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "What's your name?"
msgstr "Bạn tên là gì?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__user_tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""
"Khi in tài liệu và xuất/nhập dữ liệu, các giá trị thời gian được tính theo múi giờ này.\n"
"Nếu múi giờ không được thiết lập, UTC (Giờ phối hợp quốc tế) sẽ được sử dụng.\n"
"Với các thao tác khác, giá trị thời gian được tính theo chênh lệch múi giờ của trang web máy khách."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr ""
"Khi chọn phân công là \"Người dùng mặc định\", bạn phải chỉ định người phụ "
"trách."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"Có giữ lại một bản gốc của mỗi email để dẫn chiếu và đính kèm vào các thông "
"điệp. Việc này sẽ làm tăng gấp đôi dung lượng lưu trữ các thông điệp trong "
"cơ sở dữ liệu."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"Có nên tải xuống tệp đính kèm hay không. Nếu không được bật, email đến sẽ bị"
" xóa bất kỳ tệp đính kèm nào trước khi được xử lý"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr ""
"Liệu nên hiển thị tất cả người nhận hay chỉ những người nhận quan trọng."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Write /field to insert dynamic content"
msgstr "Viết /field để chèn nội dung động"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
msgid "Write Feedback"
msgstr "Viết phản hồi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_scheduled_message_view_form
msgid "Write your message here..."
msgstr "Viết lời nhắn của bạn ở đây..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
msgid "Wrong operation name (%s)"
msgstr "Tên hoạt động sai (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yeah, pin it!"
msgstr "Yayyy! Ghim tin nhắn!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
msgid "Yes"
msgstr "Có"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid "Yes, remove it please"
msgstr "Đúng, hãy xoá đi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
msgid "Yesterday"
msgstr "Hôm qua"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "Yesterday at %(time)s"
msgstr "Hôm qua lúc %(time)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "Yesterday:"
msgstr "Hôm qua:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"Bạn sắp rời khỏi nhóm thảo luận này và sẽ không còn truy cập nhóm được nữa "
"trừ khi bạn được mời lại. Bạn có chắc chắn muốn tiếp tục không?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are alone in a private conversation."
msgstr "Chỉ có mình bạn trong một cuộc trò chuyện riêng tư."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are alone in this channel."
msgstr "Chỉ có mình bạn trong kênh này."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in a private conversation with %(member_names)s."
msgstr "Bạn đang tham gia cuộc trò chuyện riêng với %(member_names)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr "Bạn đang tham gia kênh %(bold_start)s#%(channel_name)s%(bold_end)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
msgid "You are no longer following \"%(thread_name)s\"."
msgstr "Bạn không còn theo dõi \"%(thread_name)s\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid ""
"You are not allowed to change the target record of a scheduled message."
msgstr ""
"Bạn không được phép thay đổi bản ghi mục tiêu của tin nhắn đã lên lịch."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_scheduled_message.py:0
msgid "You are not allowed to send this scheduled message"
msgstr "Bạn không được phép gửi tin nhắn đã lên lịch này"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload an attachment here."
msgstr "Bạn không có quyền tải lên file đính kèm ở đây."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
msgid "You are not allowed to upload attachments on this channel."
msgstr "Bạn không được phép tải lên tệp đính kèm trên kênh này."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js:0
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "Bạn là quản trị viên của kênh này. Bạn có muốn tiếp tục không?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Bạn có thể đánh dấu bất kỳ tin nhắn nào là \"được đánh dấu sao\", nó sẽ xuất"
" hiện trong hộp thư."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "You can not write on %(field_name)s."
msgstr "Bạn không thể viết trong %(field_name)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with existing users."
msgstr "Bạn chỉ có thể chat với những người dùng đã tồn tại."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/store_service.js:0
msgid "You can only chat with partners that have a dedicated user."
msgstr "Bạn chỉ có thể chat với đối tác có tài khoản người dùng chuyên dụng."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr "Bạn có thể yên tâm bỏ qua thông báo này."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Bạn không thể xóa (những) nhóm này vì nó/chúng còn được sử dụng bởi các phân"
" hệ khác."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr ""
"Bạn chỉ có thể dùng các ký tự latin không dấu cho địa chỉ bí danh "
"%(alias_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr ""
"Bạn chỉ có thể dùng các ký tự latin không dấu cho tên miền %(domain_name)s."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr "Bạn không có quyền truy cập vào"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"Bạn không có quyền truy cập để bỏ hạn chế email. Vui lòng liên hệ với quản "
"trị viên."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "You have been assigned to %s"
msgstr "Bạn đã được phân công cho %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Bạn đã được phân công cho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public_web/discuss_core_public_web_service.js:0
msgid "You have been invited to #%s"
msgstr "Bạn đã được mời tới #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Bạn có thể đính kèm các tập tin vào mẫu này để các tập tin đính kèm được gửi"
" khi bạn gửi email dùng mẫu này"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
msgid "You may not define a template on an abstract model: %s"
msgstr "Bạn không được xác định mẫu trên mô hình trừu tượng:%s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_model_patch.js:0
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr ""
"Bạn có chắc chắn muốn ghim tin nhắn này trong %(conversation)s mãi mãi "
"không?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned %(conversation_name)s"
msgstr "Bạn đã bỏ ghim %(conversation_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unpinned your conversation with %(user_name)s"
msgstr "Bạn đã bỏ ghim cuộc trò chuyện với %(user_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
msgid "You unsubscribed from %s."
msgstr "Bạn đã hủy đăng ký %s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a chat!"
msgstr "Bạn đã được mời tới một mục chat!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "You've been invited to a meeting!"
msgstr "Bạn đã được mời tới một cuộc họp!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_bubble.xml:0
msgid "You:"
msgstr "Bạn:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "Của bạn"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr ""
"Email tài khoản của bạn đã được thay đổi từ %(old_email)s thành "
"%(new_email)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account login has been updated"
msgstr "Thông tin đăng nhập tài khoản của bạn đã được cập nhật"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
msgid "Your account password has been updated"
msgstr "Mật khẩu tài khoản của bạn đã được cập nhật"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your browser does not support videoconference"
msgstr "Trình duyệt của bạn không hỗ trợ họp qua video"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support voice activation"
msgstr "Trình duyệt của bạn không hỗ trợ kích hoạt giọng nói"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
msgid "Your browser does not support webRTC."
msgstr "Trình duyệt của bạn không hỗ trợ webRTC."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_patch.xml:0
msgid "Your inbox is empty"
msgstr "Hộp thư đến của bạn đang trống"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
msgid "Your name"
msgstr "Tên của bạn"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
msgid "[%(name)s] (no email address)"
msgstr "[%(name)s] (không có địa chỉ email)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "addresses linked to registered partners"
msgstr "địa chỉ liên kết với các đối tác đã đăng ký"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "sau ngày xác nhận"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "sau thời hạn hoạt động trước"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "alias"
msgstr "bí danh"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "alias %(name)s: %(error)s"
msgstr "alias %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr "(các) tệp đính kèm của email này."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "available bitrate:"
msgstr "tốc độ bit khả dụng:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "back"
msgstr "trở lại"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""
"bởi vì bạn đã\n"
"                liên hệ quá nhiều lần trong vài phút qua.\n"
"                <br/>\n"
"                Vui lòng thử lại sau."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "bởi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "camera"
msgstr "máy ảnh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"không thể xử lý. Địa chỉ này\n"
"    được dùng để thu thập các phản hồi và không nên dùng cho liên hệ trực tiếp"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "channels"
msgstr "kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "clock rate:"
msgstr "Tần số xung đồng hồ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "codec:"
msgstr "codec:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "created this channel."
msgstr "đã tạo kênh này."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__days
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "ngày"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days overdue:"
msgstr "ngày quá hạn:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "days:"
msgstr "ngày:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "deaf"
msgstr "điếc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_push__mail_push_device_id
msgid "devices"
msgstr "thiết bị"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "document"
msgstr "tài liệu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "hoàn thành"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down DTLS:"
msgstr "down DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "down ICE:"
msgstr "down ICE:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "e.g \"Asked to receive our next newsletters\""
msgstr "VD: \"Đã yêu cầu nhận bản tin email sắp tới của chúng ta\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr "VD: \"Đề xuất thảo luận\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr "VD: \"Xem qua đề nghị và thảo luận chi tiết\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr "VD: \"Email chào mừng\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr ""
"VD: \"Chào mừng đến với MyCompany\" hoặc \"Rất vui được gặp gỡ bạn, {{ "
"object.name }}\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr "VD: \"bounce\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr "VD: \"catchall\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "VD: \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr "VD: \"notifications\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "VD: 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "VD: ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr "VD: Liên hệ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Discuss Proposal"
msgstr "VD: Thảo luận về đề xuất"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "vd: Thảo luận về đề xuất"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
msgid "e.g. Log a note"
msgstr "VD: Lưu ghi chú"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "VD: Onboarding"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "VD: Lên lịch một cuộc họp"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. mycompany.com"
msgstr "VD: mycompany.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "VD: hỗ trợ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "VD: true.true..f"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr "VD: user_id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr "VD: \"<EMAIL>\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g: Send order confirmation"
msgstr "VD: Gửi xác nhận đơn hàng"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
msgid "for"
msgstr "cho"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "đã được tạo ra từ:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "đã được sửa đổi từ:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr "vừa phân công cho bạn hoạt động sau:"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr "https://developers.google.com/tenor/guides/content-filtering"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "in a few seconds"
msgstr "trong vài giây"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias"
msgstr "bí danh được định cấu hình sai"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "incorrectly configured alias (unknown reference record)"
msgstr ""
"bí danh được định cấu hình không chính xác (bản ghi tham chiếu không xác "
"định)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "invited %s to the channel"
msgstr "đã mời %s vào kênh"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "joined the channel"
msgstr "đã tham gia kênh"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "left the channel"
msgstr "đã khỏi rời kênh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list"
msgstr "danh sách"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
msgid "list-item"
msgstr "list-item"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "live"
msgstr "trực tiếp"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "media player Error"
msgstr "Lỗi trình phát phương tiện"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "microphone"
msgstr "micro"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "model %s does not accept document creation"
msgstr "mô hình %s không chấp nhận tạo tài liệu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__months
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "tháng"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "more"
msgstr "thêm"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
msgid "ms"
msgstr "ms"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "muted"
msgstr "tắt tiếng"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "new"
msgstr "mới"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
msgid "no connection"
msgstr "không có kết nối"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
msgid "now"
msgstr "bây giờ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "on"
msgstr "trên"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
msgid "on:"
msgstr "trên:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
msgid "or"
msgstr "hay"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
msgid "or press %(send_keybind)s"
msgstr "hoặc nhấn %(send_keybind)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
msgid "other members."
msgstr "thành viên khác."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
msgid "props.action.title"
msgstr "props.action.title"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
msgid "raising hand"
msgstr "dơ tay"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"phản hồi tài liệu bị thiếu (%(model)s,%(thread)s), có thể sử dụng tạo tài "
"liệu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"mô hình %s không chấp nhận cập nhật tài liệu, hãy quay lại tạo tài liệu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to followers"
msgstr "giới hạn người theo dõi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
msgid "restricted to known authors"
msgstr "giới hạn tác giả đã biết"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
msgid "results out of"
msgstr "kết quả trong số"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "screen"
msgstr "màn hình"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "some specific addresses"
msgstr "một số địa chỉ cụ thể"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "stun:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "target model unspecified"
msgstr "đối tượng mục tiêu không xác định"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "nhóm."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "template"
msgstr "mẫu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "turn:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown error"
msgstr "lỗi không xác định"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "unknown target model %s"
msgstr "mô hình mục tiêu không xác định %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up DTLS:"
msgstr "up DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
msgid "up ICE:"
msgstr "up ICE:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
msgid "users"
msgstr "người dùng"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
msgid "view"
msgstr "chế độ xem"

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr "xử lý thông báo websocket"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__delay_unit__weeks
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "tuần"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
msgid "you"
msgstr "bạn"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
msgid "your alias"
msgstr "bí danh của bạn"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr "“%(member_name)s” trong “%(channel_name)s”"
