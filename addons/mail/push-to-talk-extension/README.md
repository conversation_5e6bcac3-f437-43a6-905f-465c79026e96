# Discuss Push-to-Talk Extension

This extension enhances your communication experience by providing system-wide
Push-to-Talk functionality. Say goodbye to tab-specific limitations, as this
extension allows you to effortlessly utilize Push-to-Talk across your entire
system. Whether Chrome is in focus or not, on the same tab or not, enjoy
seamless communication in Odoo Discuss without any constraints.

> Please note that this extension only works for Odoo 17 and higher and is not
> compatible with ChromeOS.

## Installing the extension

- Go to the chrome extension panel: `chrome://extensions/`.
- Click on `Load unpacked`.
- Select the `push-to-talk-extension` folder.
- Your extension is active!

## Extension State

You can check whether your voice transmission is activated at anytime by looking
at the extension icon.
- <img src="assets/icons/active_icon.png" alt="active_icon" width="24"/> Indicates the voice transmission is active.
- <img src="assets/icons/inactive_icon.png" alt="inactive_icon" width="24"/>
  Indicates the voice transmission is inactive.

## Prerequisites

- Before installing the Discuss Push-to-Talk Extension, ensure that the `Repeat
  key` feature is enabled on your system.
- Command scope must be set to `global`. You can click the extension icon to get
  to `Keyboard shortcuts` settings or access `chrome://extensions/shortcuts`
  directly.
- Push-to-Talk should be enabled on Odoo Discuss voice settings.

## Commands

- `Push-to-Talk`: Voice is activated as long as the key is held.
- `Voice Toggle`: Switch your voice transmission on or off.
