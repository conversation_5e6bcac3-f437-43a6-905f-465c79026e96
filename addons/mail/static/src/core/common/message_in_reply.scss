.o-mail-MessageInReply-avatar {
    width: $o-mail-Avatar-size / 2;
    height: $o-mail-Avatar-size / 2;
}

.o-mail-MessageInReply-core {
    border-left: 3px solid transparent !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    background-color: rgba($o-view-background-color, .5) !important;

    &.o-blue {
        border-left-color: mix($o-view-background-color, $info, 50%) !important;
    }
    &.o-green {
        border-left-color: mix($o-view-background-color, $success, 50%) !important;
    }
    &.o-orange {
        border-left-color: mix($o-view-background-color, $warning, 40%) !important;
    }
}

.o-mail-MessageInReply-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.o-mail-MessageInReply-message {
    // Make the body single line when possible
    p, div {
        display: inline;
        margin: 0;
    }

    br {
        display: none;
    }
}

