# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "Línea analítica"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
msgid "Analytic Lines"
msgstr "Líneas analíticas"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off %s. If you don't want a timesheet, you should leave the "
"internal project and task empty."
msgstr ""
"Para crear una hoja de horas para el tiempo personal %s debe configurar el "
"proyecto interno y la tarea. Deje los campos anteriores vacíos si no desea "
"crearla."

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_employee
msgid "Employee"
msgstr "Empleado"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheets"
msgstr "Generar hojas de horas"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Generate timesheets when validating time off requests of this type"
msgstr ""
"Genere hojas de horas al validar solicitudes con este tipo de tiempo "
"personal."

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""
"Si se encuentra seleccionado, generará una hoja de horas en el proyecto de "
"vacaciones de la empresa al validar un permiso."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
msgid "Internal"
msgstr "Interno"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Internal Project"
msgstr "Proyecto interno"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/project_task.py:0
msgid "Operation not supported"
msgstr "Operación incompatible"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "Proyecto"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detalle de la pausa del recurso"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_project_task
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "Tarea"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid ""
"The default project used when automatically generating timesheets via time "
"off requests. You can specify another project on each time off type "
"individually."
msgstr ""
"El proyecto predeterminado utilizado al generar hojas de horas en automático"
" desde las solicitudes de tiempo personal. Puede especificar otro proyecto "
"en cada tipo de tiempo personal."

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid ""
"The default task used when automatically generating timesheets via time off "
"requests. You can specify another task on each time off type individually."
msgstr ""
"La tarea predeterminada utilizada al generar una hoja de horas en automático"
" desde las solicitudes de tiempo personal. Puede especificar otra tarea en "
"cada tipo de tiempo personal."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
msgid "Time Off"
msgstr "Tiempo personal"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#: code:addons/project_timesheet_holidays/models/resource_calendar_leaves.py:0
msgid "Time Off (%(index)s/%(total)s)"
msgstr "Tiempo personal (%(index)s/%(total)s)"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "Tarea de tiempo personal"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "Tipo de tiempo personal"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_project_task__leave_types_count
msgid "Time Off Types Count"
msgstr "Número de tipos de tiempo personal"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheets"
msgstr "Hojas de horas"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid "View Time Off"
msgstr "Ver tiempo personal "

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot create timesheets for a task that is linked to a time off type. "
"Please use the Time Off application to request new time off instead."
msgstr ""
"No puede crear hojas de horas para una tarea vinculada a un tipo de tiempo "
"personal. Use la aplicación Tiempo personal para solicitar uno nuevo."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid "You cannot delete timesheets that are linked to global time off."
msgstr ""
"No puede eliminar las hojas de horas que están vinculadas al tiempo personal"
" global."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot delete timesheets that are linked to time off requests. Please "
"cancel your time off request from the Time Off application instead."
msgstr ""
"No puede eliminar hojas de horas vinculadas a solicitudes de tiempo "
"personal. Cancele la solicitud desde la aplicación Tiempo personal."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot modify timesheets that are linked to time off requests. Please "
"use the Time Off application to modify your time off requests instead."
msgstr ""
"No puede modificar las hojas de horas vinculadas a solicitudes de tiempo "
"personal. Use la aplicación Tiempo personal para modificar sus solicitudes."
