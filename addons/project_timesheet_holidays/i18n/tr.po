# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# emre oktem, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>_<PERSON>do<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitik Satırı"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
msgid "Analytic Lines"
msgstr "Analitik Satırları"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off %s. If you don't want a timesheet, you should leave the "
"internal project and task empty."
msgstr ""
"İzin %s için bir çalışma çizelgesi oluşturmak üzere hem dahili proje hem de "
"görev gereklidir. Çalışma çizelgesi istemiyorsanız, dahili proje ve görev "
"alanlarını boş bırakmalısınız."

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_employee
msgid "Employee"
msgstr "Çalışan"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheets"
msgstr "Çalışma Çizelgeleri Oluştur"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Generate timesheets when validating time off requests of this type"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""
"Eğer işaretli ise, bir izin onaylandığında, şirketin İzin Projesi içinde "
"çalışma çizelgesi oluşturulacak."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
msgid "Internal"
msgstr "İç"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Internal Project"
msgstr "İç Proje"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/project_task.py:0
msgid "Operation not supported"
msgstr "Operasyon desteklenmiyor"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "Proje"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Kaynak İzin Detayı"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_project_task
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "Görev"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid ""
"The default project used when automatically generating timesheets via time "
"off requests. You can specify another project on each time off type "
"individually."
msgstr ""
"İzin istekleri aracılığıyla çalışma çizelgelerini otomatik olarak "
"oluştururken kullanılan varsayılan proje. Her izin türünde ayrı ayrı başka "
"bir proje belirtebilirsiniz."

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid ""
"The default task used when automatically generating timesheets via time off "
"requests. You can specify another task on each time off type individually."
msgstr ""
"İzin istekleri aracılığıyla çalışma çizelgelerini otomatik olarak "
"oluştururken kullanılan varsayılan görev. Her izin türünde ayrı ayrı başka "
"bir görev belirleyebilirsiniz."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
msgid "Time Off"
msgstr "İzin"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#: code:addons/project_timesheet_holidays/models/resource_calendar_leaves.py:0
msgid "Time Off (%(index)s/%(total)s)"
msgstr "İzin (%(index)s/%(total)s)"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "İzin Görevi"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "İzin Türü"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_project_task__leave_types_count
msgid "Time Off Types Count"
msgstr ""

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheets"
msgstr "Çalışma Çizelgeleri"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid "View Time Off"
msgstr ""

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot create timesheets for a task that is linked to a time off type. "
"Please use the Time Off application to request new time off instead."
msgstr ""
"Bir izin türüne bağlı bir görev için çalışma çizelgeleri oluşturamazsınız. "
"Bunun yerine yeni izin talep etmek için lütfen İzin uygulamasını kullanın."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid "You cannot delete timesheets that are linked to global time off."
msgstr ""

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot delete timesheets that are linked to time off requests. Please "
"cancel your time off request from the Time Off application instead."
msgstr ""
"İzin istekleriyle bağlantılı çalışma çizelgelerini silemezsiniz. Lütfen "
"bunun yerine İzin uygulamasından izin talebinizi iptal edin."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
msgid ""
"You cannot modify timesheets that are linked to time off requests. Please "
"use the Time Off application to modify your time off requests instead."
msgstr ""
"İzin isteklerine bağlı çalışma çizelgelerini değiştiremezsiniz. Bunun yerine"
" izin taleplerinizi değiştirmek için lütfen İzin uygulamasını kullanın."
