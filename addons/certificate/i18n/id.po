# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* certificate
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "A private key is required to decrypt data."
msgstr "Private key diperlukan untuk decrypt data."

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__active
#: model:ir.model.fields,field_description:certificate.field_certificate_key__active
msgid "Active"
msgstr "Aktif"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Add, edit and delete certificates."
msgstr "Tambah, edit, dan hapus sertifikat."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Add, edit and delete keys."
msgstr "Tambah, edit, dan hapus keys."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Archived certificates"
msgstr "Sertifikat yang diarsip"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Archived keys"
msgstr "Key yang diarsip"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__date_start
msgid "Available date"
msgstr "Tanggal tersedia"

#. module: certificate
#: model:ir.model,name:certificate.model_certificate_certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__content
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Certificate"
msgstr "Sertifikat"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__pkcs12_password
msgid "Certificate Password"
msgstr "Password Sertifikat"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__pem_certificate
msgid "Certificate in PEM format"
msgstr "Sertifikat di format PEM"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr "Cakupan sertifikat"

#. module: certificate
#: model:ir.actions.act_window,name:certificate.certificate_certificate_action_view_list
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Certificates"
msgstr "Sertifikat-Sertifikat"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Certificates and Keys"
msgstr "Sertifikat-Sertifikat dan Keys"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__company_id
#: model:ir.model.fields,field_description:certificate.field_certificate_key__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__country_code
msgid "Country Code"
msgstr "Kode Negara"

#. module: certificate
#: model_terms:ir.actions.act_window,help:certificate.certificate_certificate_action_view_list
msgid "Create a first certificate"
msgstr "Buat sertifikat pertama"

#. module: certificate
#: model_terms:ir.actions.act_window,help:certificate.certificate_key_action_view_list
msgid "Create a first key"
msgstr "Buat key pertama"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__create_uid
#: model:ir.model.fields,field_description:certificate.field_certificate_key__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__create_date
#: model:ir.model.fields,field_description:certificate.field_certificate_key__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: certificate
#: model:ir.model,name:certificate.model_certificate_key
msgid "Cryptographic Keys"
msgstr "Kunci-Kunci Kriptografi"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__der
msgid "DER"
msgstr "DER"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:certificate.field_certificate_key__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__date_end
msgid "Expiration date"
msgstr "Tanggal kadaluwarsa"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__scope__general
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "General"
msgstr "Umum"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "General certificates"
msgstr "Sertifikat-sertifikat umum"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__id
#: model:ir.model.fields,field_description:certificate.field_certificate_key__id
msgid "ID"
msgstr "ID"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Invalid"
msgstr "Tidak valid"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_list
msgid "Key"
msgstr "Kunci"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__pem_key
msgid "Key bytes in PEM format"
msgstr "Key bytes dalam format PEM"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__content
msgid "Key file"
msgstr "Key file"

#. module: certificate
#: model:ir.actions.act_window,name:certificate.certificate_key_action_view_list
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Keys"
msgstr "Keys"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__write_uid
#: model:ir.model.fields,field_description:certificate.field_certificate_key__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__write_date
#: model:ir.model.fields,field_description:certificate.field_certificate_key__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__loading_error
#: model:ir.model.fields,field_description:certificate.field_certificate_key__loading_error
msgid "Loading error"
msgstr "Error memuat"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "Make sure to use a private key to sign documents."
msgstr "Pastikan Anda menggunakan private key untuk menandatangani dokumen."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Manage your certificates"
msgstr "Kelola sertifikat Anda"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.res_config_settings_view_form
msgid "Manage your keys"
msgstr "Kelola keys Anda"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__name
#: model:ir.model.fields,field_description:certificate.field_certificate_key__name
msgid "Name"
msgstr "Nama"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid ""
"No private key linked to the certificate, it is required to sign documents."
msgstr ""
"Tidak ada private key yang terhubung ke sertifikat, tapi diperlukan untuk "
"menandatangani dokumen."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Not valid certificates"
msgstr "Bukan sertifikat valid"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__content_format
msgid "Original certificate format"
msgstr "Format sertifikat original"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__pem
msgid "PEM"
msgstr "PEM"

#. module: certificate
#: model:ir.model.fields.selection,name:certificate.selection__certificate_certificate__content_format__pkcs12
msgid "PKCS12"
msgstr "PKCS12"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__pkcs12_password
msgid "Password to decrypt the PKS file."
msgstr "Password untuk decrypt file PKS."

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Private"
msgstr "Pribadi"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__private_key_id
msgid "Private Key"
msgstr "Kunci Pribadi"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__password
msgid "Private key password"
msgstr "Password private key"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "Public"
msgstr "Umum"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__public_key_id
msgid "Public Key"
msgstr "Public Key"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_key__public
msgid "Public/Private key"
msgstr "Public/Private key"

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__serial_number
msgid "Serial number"
msgstr "Nomor seri"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__active
msgid "Set active to false to archive the certificate"
msgstr "Tetapkan aktif menjadi FALSE untuk mengarsip sertifikat"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_key__active
msgid "Set active to false to archive the key."
msgstr "Tetapkan aktif menjadi FALSE untuk mengarsip key."

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__subject_common_name
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Subject Name"
msgstr "Nama Subyek"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kode ISO negara dalam dua karakter.\n"
"Anda dapat menggunakan kolom ini untuk pencarian cepat."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The certificate and private key are not compatible."
msgstr "Sertifikat dan private key tidak kompatibel."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The certificate and public key are not compatible."
msgstr "Sertifikat dan public key tidak kompatibel."

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__date_end
msgid "The date on which the certificate expires (UTC)"
msgstr "Tanggal pada mana sertifikat kadaluwarsa (UTC)"

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__date_start
msgid "The date on which the certificate starts to be valid (UTC)"
msgstr "Tanggal pada mana sertifikat mulai valid (UTC)"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The key size should be at least 512 bytes."
msgstr "Ukuran kunci harus setidaknya 512 bytes."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The private key could not be loaded."
msgstr "Private key tidak dapat dimuat."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The public exponent should be 65537 (or 3 for legacy purposes)."
msgstr "Eksponen publik harus 65537 (atau 3 untuk tujuan legacy)."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid "The public key could not be loaded."
msgstr "Public key tidak dapat dimuat."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "The public key from the certificate could not be loaded."
msgstr "Public key dari sertifikat tidak dapat dimuat."

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__serial_number
msgid "The serial number to add to electronic documents"
msgstr "Nomor seri untuk ditambahkan ke dokumen-dokumen elektronik"

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid ""
"This certificate could not be loaded. Either the content or the password is "
"erroneous."
msgstr "Sertifikat ini tidak dapat dimuat. Baik konten atau password salah."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/certificate.py:0
msgid "This certificate is not valid, its validity has expired."
msgstr "Sertifikat ini tidak valid, validitasnya sudah kadaluwarsa."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"This key could not be loaded. Either its content or its password is "
"erroneous."
msgstr "Key ini tidak dapat dimuat. Baik konten atau password salah."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported for "
"decryption: RSA."
msgstr ""
"Algoritma kriptografi asimetrik '%s' tidak didukung. Saat ini didukung untuk"
" decryption: RSA."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported for "
"signature: EC and RSA."
msgstr ""
"Algoritma kriptografi asimetrik '%s' tidak didukung. Saat ini didukung untuk"
" tandatangan: EC dan RSA."

#. module: certificate
#. odoo-python
#: code:addons/certificate/models/key.py:0
msgid ""
"Unsupported asymmetric cryptography algorithm '%s'. Currently supported: EC,"
" RSA."
msgstr ""
"Algoritma kriptografi asimetrik '%s' tidak didukung. Saat ini didukung: EC, "
"RSA."

#. module: certificate
#: model:ir.model.fields,help:certificate.field_certificate_certificate__public_key_id
msgid ""
"Used to set a public key in case the one self-contained in the certificate is erroneus.\n"
"                When a public key is set this way, it will be used instead of the one in the certificate.\n"
"             "
msgstr ""
"Digunakan untuk menetapkan public key sebagai pengaman bila public key yang di self-contain di sertifikat salah.\n"
"                Saat public key ditetapkan dengan cara ini, akan digunakan alih-alih yang di sertifikat.\n"
"             "

#. module: certificate
#: model:ir.model.fields,field_description:certificate.field_certificate_certificate__is_valid
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Valid"
msgstr "Valid"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "Valid certificates"
msgstr "Sertifikat-sertifikat valid"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "Validity"
msgstr "Masa Berlaku"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
msgid "certificate form"
msgstr "formulir sertifikat"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_list
msgid "certificate list"
msgstr "daftar sertifikat"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_search
msgid "certificate search"
msgstr "cari sertifikat"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_certificate_view_form
msgid "e.g. New Certificate"
msgstr "contoh. Sertifikat Baru"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_form
msgid "key form"
msgstr "key form"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_list
msgid "key list"
msgstr "key list"

#. module: certificate
#: model_terms:ir.ui.view,arch_db:certificate.certificate_key_view_search
msgid "key search"
msgstr "key search"
