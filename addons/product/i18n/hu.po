# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product
# 
# Translators:
# 5768b353f27900ae76ad88cc42dfd5b8_3bb349f, 2024
# f1b3a33e3b33fcf18004a5292e501f50_3500ca8 <373b677b151624c4521d9efc77b996fd_750224>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>kos Nagy <<EMAIL>>, 2024
# krnkris, 2024
# Kov<PERSON><PERSON> Tibor <<EMAIL>>, 2024
# Eiler Attila, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: gezza <<EMAIL>>, 2025\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid ""
"\n"
"\n"
"Note: products that you don't have access to will not be shown above."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_count
msgid "# Product Variants"
msgstr "# Termékváltozat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_count
msgid "# Products"
msgstr "# Termék"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$14.00"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$15.00"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"%(base)s with a %(discount)s %% %(discount_type)s and %(surcharge)s extra fee\n"
"Example: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"%(item_name)s: end date (%(end_date)s) should be after start date "
"(%(start_date)s)"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "%(percentage)s %% %(discount_type)s on %(base)s %(extra)s"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "%(percentage)s %% discount on %(pricelist)s"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "%(percentage)s %% discount on sales price"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
#: code:addons/product/models/product_tag.py:0
#: code:addons/product/models/product_template.py:0
msgid "%s (copy)"
msgstr "%s (másolat)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_template_label_2x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12_noprice
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_dymo
msgid "'Products Labels - %s' % (object.name)"
msgstr ""

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_packaging
msgid "'Products packaging - %s' % (object.name)"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "(e.g: product description, ebook, legal notice, ...)."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "+ %(amount)s extra fee"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "- %(amount)s rebate"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "- Barcode \"%(barcode)s\" already assigned to product(s): %(product_list)s"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__create_variant
msgid ""
"- Instantly: All possible variants are created as soon as the attribute and its values are added to a product.\n"
"        - Dynamically: Each variant is created only when its corresponding attributes and values are added to a sales order.\n"
"        - Never: Variants are never created for the attribute.\n"
"        Note: this cannot be changed once the attribute is used on a product."
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "1 year"
msgstr "1 év"

#. module: product
#: model:product.attribute.value,name:product.pav_warranty
msgid "1 year warranty extension"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "10"
msgstr "10"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "10 Units"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "123456789012"
msgstr ""

#. module: product
#: model:product.template,description_sale:product.product_product_4_product_template
msgid "160x80cm, with large legs."
msgstr "160x80cm, nagy lábakkal."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__2x7xprice
msgid "2 x 7 with price"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_6
msgid "2 year"
msgstr "2 év"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12xprice
msgid "4 x 12 with price"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x7xprice
msgid "4 x 7 with price"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<b>Tip: want to round at 9.99?</b>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                            Rules\n"
"                                        </span>\n"
"                                        <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                            Rule\n"
"                                        </span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                        Pricelists\n"
"                                    </span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                        Pricelist\n"
"                                    </span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr "<span class=\"o_stat_text\"> Termékek</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "<span class=\"o_stat_text\">Products</span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<span>%</span>\n"
"                                <span>on</span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>All general settings about this product are managed on</span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "<strong>Qty: </strong>"
msgstr "<strong>Menny.: </strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back\n"
"                                here to set up the feature."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""
"<strong>Vigyázat</strong>: tulajdonság törlése vagy hozzáadása\n"
"                         törli és újra létrehozza a meglévő változatokat, emiatt \n"
"                        elvesznek a változatokhoz kapcsolódó testreszabások is."

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_barcode_uniq
msgid "A barcode can only be assigned to one packaging."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_combo.py:0
msgid "A combo choice can't contain duplicate products."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_combo_item.py:0
msgid "A combo choice can't contain products of type \"combo\"."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_combo.py:0
msgid "A combo choice must contain at least 1 product."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "A combo product must contain at least 1 combo choice."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__description_sale
#: model:ir.model.fields,help:product.field_product_template__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"A termék leírása, mely kommunikálásra kerül a vevők felé.  Ez a leírás kerül"
" átmásolásra minden egyes vevői rendelésre, szállítási rendelésre és vevői "
"számlára/jóváírásra."

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "A packaging already uses the barcode"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.\n"
"            This is the perfect tool to handle several pricings, seasonal discounts, etc."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_packaging.py:0
msgid "A product already uses the barcode"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "A sellable combo product can only contain sellable products."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__access_token
msgid "Access Token"
msgstr "Hozzáférési token"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget - Blue"
msgstr ""

#. module: product
#: model:product.template,name:product.product_template_acoustic_bloc_screens
msgid "Acoustic Bloc Screens"
msgstr "Akusztikus blokk képernyők"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_needaction
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Akció szükséges"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__active
#: model:ir.model.fields,field_description:product.field_product_attribute_value__active
#: model:ir.model.fields,field_description:product.field_product_document__active
#: model:ir.model.fields,field_description:product.field_product_pricelist__active
#: model:ir.model.fields,field_description:product.field_product_product__active
#: model:ir.model.fields,field_description:product.field_product_template__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_active
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Active"
msgstr "Aktív"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Active Products"
msgstr "Aktív termékek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_ids
#: model:ir.model.fields,field_description:product.field_product_product__activity_ids
#: model:ir.model.fields,field_description:product.field_product_template__activity_ids
msgid "Activities"
msgstr "Tevékenységek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tevékenység kivétel dekoráció"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_state
#: model:ir.model.fields,field_description:product.field_product_product__activity_state
#: model:ir.model.fields,field_description:product.field_product_template__activity_state
msgid "Activity State"
msgstr "Tevékenység állapot"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tevékenység típus ikon"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "Add"
msgstr "Hozzáadás"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Add Products"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Add Products to pricelist report"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Add a quantity"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid ""
"Add products using the \"Add Products\" button at the top right to\n"
"                                include them in the report."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid "Add to all products"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__update_product_attribute_value__mode__add
msgid "Add to existing products"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Add to products"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
msgid "All"
msgstr "Összes"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "All Categories"
msgstr "Összes kategória"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__all_product_tag_ids
msgid "All Product Tag"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_ids
msgid "All Product Variants using this Tag"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__3_global
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "All Products"
msgstr "Összes termék"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "All categories"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "All countries"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/controllers/product_document.py:0
msgid "All files uploaded"
msgstr "Minden fájl feltöltve"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "All products"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
msgid "All variants"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,help:product.field_product_template_attribute_value__is_custom
msgid "Allow customers to set their own value"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "Aluminium"
msgstr "Alumínium"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
msgid "Applied On"
msgstr "Alkalmazott ekkor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__applied_on
msgid "Apply On"
msgstr "Alkalmazás ezen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Apply To"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Apply on"
msgstr "Alkalmazás itt"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_document_search
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Archived"
msgstr "Archivált"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "Termékbeszállító listájához rendeli a prioritást."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid ""
"At most %s quantities can be displayed simultaneously. Remove a selected "
"quantity to add others."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Attached To"
msgstr "Csatolva a következőhöz"

#. module: product
#: model:ir.model,name:product.model_ir_attachment
msgid "Attachment"
msgstr "Melléklet"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_product__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Mellékletek száma"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__local_url
msgid "Attachment URL"
msgstr "Melléklet webcíme"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_id
msgid "Attribute"
msgstr "Tulajdonság"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_line_id
msgid "Attribute Line"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Attribute Name"
msgstr "Tulajdonság neve"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_attribute_value_id
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__attribute_value_id
msgid "Attribute Value"
msgstr "Tulajdonság érték"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_list
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "Tulajdonság értékek"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Attributes"
msgstr "Tulajdonságok"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Attributes & Variants"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
msgid "Back to Order"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
msgid "Back to Quotation"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__barcode
#: model:ir.model.fields,field_description:product.field_product_product__barcode
#: model:ir.model.fields,field_description:product.field_product_template__barcode
msgid "Barcode"
msgstr "Vonalkód"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__barcode
msgid ""
"Barcode used for packaging identification. Scan this packaging barcode from "
"a transfer in the Barcode app to move all the contained units"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid ""
"Barcode(s) already assigned:\n"
"\n"
"%s"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__base
msgid ""
"Base price for computation.\n"
"Sales Price: The base price will be the Sales Price.\n"
"Cost Price: The base price will be the cost price.\n"
"Other Pricelist: Computation of the base price based on another Pricelist."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base
msgid "Based on"
msgstr "Alapján"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Based price"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Basic Pricelists"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "Fekete"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
msgid "Bye-bye, record!"
msgstr ""

#. module: product
#: model:product.template,name:product.product_product_10_product_template
msgid "Cabinet with Doors"
msgstr "Kabinet ajtókkal"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:product.field_product_template__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.update_product_attribute_value_form
msgid "Cancel"
msgstr "Visszavonás"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__categ_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__display_applied_on__2_product_category
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Category"
msgstr "Kategória"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Category: %s"
msgstr "Kategória: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__checksum
msgid "Checksum/SHA1"
msgstr "Ellenőrző összeg/SHA1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__child_id
msgid "Child Categories"
msgstr "Alkategóriák"

#. module: product
#: model:ir.actions.act_window,name:product.action_open_label_layout
msgid "Choose Labels Layout"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.pav_cleaning_kit
msgid "Cleaning kit"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Kódok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__html_color
#: model:ir.model.fields,field_description:product.field_product_tag__color
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__color
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__color
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Szín"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__color
#: model:ir.model.fields,field_description:product.field_product_product__color
#: model:ir.model.fields,field_description:product.field_product_template__color
msgid "Color Index"
msgstr "Szín index"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__columns
msgid "Columns"
msgstr "Oszlopok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__combination_indices
msgid "Combination Indices"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo_item__combo_id
#: model:ir.model.fields.selection,name:product.selection__product_template__type__combo
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Combo"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_form
msgid "Combo Choice"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_combo_action
#: model:ir.model.fields,field_description:product.field_product_product__combo_ids
#: model:ir.model.fields,field_description:product.field_product_template__combo_ids
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_tree
msgid "Combo Choices"
msgstr "Kombó választások"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__combo_item_ids
msgid "Combo Item"
msgstr "Kombó tétel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__base_price
msgid "Combo Price"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Combo products can't have attributes."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"Combos allow to choose one product amongst a selection of choices per "
"category."
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "Vállalatok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__company_id
#: model:ir.model.fields,field_description:product.field_product_combo_item__company_id
#: model:ir.model.fields,field_description:product.field_product_document__company_id
#: model:ir.model.fields,field_description:product.field_product_packaging__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__company_id
#: model:ir.model.fields,field_description:product.field_product_product__company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__company_id
#: model:ir.model.fields,field_description:product.field_product_template__company_id
msgid "Company"
msgstr "Vállalat"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Company Settings"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__complete_name
msgid "Complete Name"
msgstr "Teljes név"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__compute_price
msgid "Compute Price"
msgstr "Ár számítása"

#. module: product
#: model:product.template,name:product.product_product_11_product_template
msgid "Conference Chair"
msgstr "Konferencia szék"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "Conference room table"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Configure"
msgstr "Konfigurálás"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
#: model_terms:ir.ui.view,arch_db:product.update_product_attribute_value_form
msgid "Confirm"
msgstr "Megerősítés"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "Kapcsolat"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Contact Us"
msgstr "Kapcsolatfelvétel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__qty
msgid "Contained Quantity"
msgstr "Tartalmazott mennyiség"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_positive_qty
msgid "Contained Quantity should be positive."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Contained quantity"
msgstr "Tartalmazott mennyiség"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_category_id
#: model:ir.model.fields,help:product.field_product_template__uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"A mértékegységek közötti átváltás csak akkor lehetséges, ha ugyanabba a "
"kategóriába tartoznak. Az átváltás az arányszámok alapján történik."

#. module: product
#: model:product.template,name:product.product_product_13_product_template
msgid "Corner Desk Left Sit"
msgstr "Sarokasztal bal oldali üléssel"

#. module: product
#: model:product.template,name:product.product_product_5_product_template
msgid "Corner Desk Right Sit"
msgstr "Sarokasztal jobb oldali üléssel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__standard_price
#: model:ir.model.fields,field_description:product.field_product_template__standard_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__standard_price
msgid "Cost"
msgstr "Költség"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__cost_currency_id
#: model:ir.model.fields,field_description:product.field_product_template__cost_currency_id
msgid "Cost Currency"
msgstr "Költség pénznem"

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "Országcsoport"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__country_group_ids
msgid "Country Groups"
msgstr "Országcsoportok"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Create a new pricelist"
msgstr "Új árlista létrehozása"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
msgid "Create a new product"
msgstr "Új termék létrehozása"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Create a new product variant"
msgstr "Új termékváltozat létrehozása"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
msgid "Create a product"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__service_tracking
#: model:ir.model.fields,field_description:product.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Létrehozás rendeléskor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_category__create_uid
#: model:ir.model.fields,field_description:product.field_product_combo__create_uid
#: model:ir.model.fields,field_description:product.field_product_combo_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_document__create_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_product__create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_uid
#: model:ir.model.fields,field_description:product.field_product_tag__create_uid
#: model:ir.model.fields,field_description:product.field_product_template__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_product_category__create_date
#: model:ir.model.fields,field_description:product.field_product_combo__create_date
#: model:ir.model.fields,field_description:product.field_product_combo_item__create_date
#: model:ir.model.fields,field_description:product.field_product_document__create_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_date
#: model:ir.model.fields,field_description:product.field_product_packaging__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_date
#: model:ir.model.fields,field_description:product.field_product_product__create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_date
#: model:ir.model.fields,field_description:product.field_product_tag__create_date
#: model:ir.model.fields,field_description:product.field_product_template__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Creation"
msgstr "Létrehozás"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__1
msgid "Cubic Feet"
msgstr "Köbláb"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__0
msgid "Cubic Meters"
msgstr "Köbméter"

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_combo__currency_id
#: model:ir.model.fields,field_description:product.field_product_combo_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_product__currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__currency_id
#: model:ir.model.fields,field_description:product.field_product_template__currency_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__currency_id
msgid "Currency"
msgstr "Pénznem"

#. module: product
#: model:product.attribute.value,name:product.fabric_attribute_custom
msgid "Custom"
msgstr "Egyéni"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_value
msgid "Custom Value"
msgstr "Egyéni érték"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__partner_ref
msgid "Customer Ref"
msgstr "Vevői hivatkozás"

#. module: product
#: model:product.template,name:product.product_product_4_product_template
msgid "Customizable Desk"
msgstr "Testreszabható asztal"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__db_datas
msgid "Database Data"
msgstr "Adatbázis adat"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "Decimal Precision"
msgstr "Tizedes pontosság"

#. module: product
#. odoo-python
#: code:addons/product/models/res_company.py:0
msgid "Default"
msgstr "Alapértelmezett"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__default_extra_price
msgid "Default Extra Price"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__default_extra_price_changed
msgid "Default Extra Price Changed"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,help:product.field_product_product__uom_id
#: model:ir.model.fields,help:product.field_product_template__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Alapértelmezett mértékegység készletműveletekhez."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_po_id
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,help:product.field_product_template__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""
"Beszerzési rendelésekhez használt alapértelmezett mértékegység. Ugyanabban a"
" mértékegység kategóriában kell lennie, mint az alapértelmezett "
"mértékegységnek."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Define a new tag"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your volume unit of measure"
msgstr "Válassza ki a térfogat mértékegységét"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your weight unit of measure"
msgstr "Válassza ki a tömeg mértékegységét"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Delete"
msgstr "Törlés"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__delay
msgid "Delivery Lead Time"
msgstr "Kiszállítás átfutási idő"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__description
#: model:ir.model.fields,field_description:product.field_product_product__description
#: model:ir.model.fields,field_description:product.field_product_template__description
msgid "Description"
msgstr "Leírás"

#. module: product
#: model:product.template,name:product.product_product_3_product_template
msgid "Desk Combination"
msgstr "Asztal kombináció"

#. module: product
#: model:product.template,name:product.desk_organizer_product_template
msgid "Desk Organizer"
msgstr ""

#. module: product
#: model:product.template,name:product.desk_pad_product_template
msgid "Desk Pad"
msgstr ""

#. module: product
#: model:product.template,name:product.product_product_22_product_template
msgid "Desk Stand with Screen"
msgstr "Asztal állvány képernyővel"

#. module: product
#: model:product.template,description_sale:product.product_product_3_product_template
msgid "Desk combination, black-brown: chair + desk + drawer."
msgstr "Asztal kombináció, fekete-barna: szék + asztal + fiók."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__sequence
#: model:ir.model.fields,help:product.field_product_attribute_value__sequence
msgid "Determine the display order"
msgstr "Megjelenítési sorrend meghatározása"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Discard"
msgstr "Elvetés"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__percentage
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Discount"
msgstr "Kedvezmény"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__discount
msgid "Discount (%)"
msgstr "Kedvezmény (%)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price_discounted
msgid "Discounted Price"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_applied_on
msgid "Display Applied On"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_product_category__display_name
#: model:ir.model.fields,field_description:product.field_product_combo__display_name
#: model:ir.model.fields,field_description:product.field_product_combo_item__display_name
#: model:ir.model.fields,field_description:product.field_product_document__display_name
#: model:ir.model.fields,field_description:product.field_product_label_layout__display_name
#: model:ir.model.fields,field_description:product.field_product_packaging__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_name
#: model:ir.model.fields,field_description:product.field_product_product__display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:product.field_product_tag__display_name
#: model:ir.model.fields,field_description:product.field_product_template__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_type
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_type
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_type
msgid "Display Type"
msgstr "Megjelenítés típus"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Document"
msgstr "Dokumentum"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#: model:ir.model.fields,field_description:product.field_product_product__product_document_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_document_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Documents"
msgstr "Dokumentumok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_document_count
#: model:ir.model.fields,field_description:product.field_product_template__product_document_count
msgid "Documents Count"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
msgid "Documents of this variant"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Download"
msgstr "Letöltés"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Download examples"
msgstr "Példák letöltése"

#. module: product
#: model:product.template,name:product.product_product_27_product_template
msgid "Drawer"
msgstr "Fiók"

#. module: product
#: model:product.template,name:product.product_product_16_product_template
msgid "Drawer Black"
msgstr "Fekete fiók"

#. module: product
#: model_terms:product.template,description:product.product_product_27_product_template
msgid "Drawer with two routing possiblities."
msgstr "Fiók két irány lehetőséggel"

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Duration"
msgstr "Időtartam"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__dymo
msgid "Dymo"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__dynamic
msgid "Dynamically"
msgstr "Dinamikusan"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_template_attribute_value_attribute_value_unique
msgid "Each value should be defined only once per attribute per product."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Eco-friendly Wooden Chair"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
msgid "Edit"
msgstr "Szerkesztés"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_end
msgid "End Date"
msgstr "Befejező dátum"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_end
msgid "End date for this vendor price"
msgstr "Beszálítói ár utolsó napja"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_end
msgid ""
"Ending datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Ergonomic"
msgstr "Ergonomikus"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Error exporting file. Please try again."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__exclude_for
msgid "Exclude for"
msgstr "Kizárás innen"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__name
#: model:ir.model.fields,help:product.field_product_pricelist_item__price
msgid "Explicit rule name for this pricelist line."
msgstr "Meghatározott szabály neve az árlista sorhoz."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__extra_html
msgid "Extra Content"
msgstr "Extra tartalom"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_surcharge
msgid "Extra Fee"
msgstr "Extra költség"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Extra Info"
msgstr "Extra információ"

#. module: product
#: model:product.attribute,name:product.pa_extra_options
msgid "Extra Options"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo_item__extra_price
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__price_extra
msgid "Extra Price"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__price_extra
msgid ""
"Extra price for the variant with this attribute value on sale price. eg. 200"
" price extra, 1000 + 200 = 1200."
msgstr ""

#. module: product
#: model:product.attribute,name:product.fabric_attribute
msgid "Fabric"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_favorite
#: model:ir.model.fields,field_description:product.field_product_template__is_favorite
msgid "Favorite"
msgstr "Kedvenc"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Favorites"
msgstr "Kedvencek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__datas
msgid "File Content (base64)"
msgstr "Fájltartalom (base64)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__raw
msgid "File Content (raw)"
msgstr "Fájltartalom (nyers)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__file_size
msgid "File Size"
msgstr "Fájlméret"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__fixed_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__fixed
msgid "Fixed Price"
msgstr "Rögzített ár"

#. module: product
#: model:product.template,name:product.product_product_20_product_template
msgid "Flipover"
msgstr "Flipover"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Követők"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Követők (Partnerek)"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,help:product.field_product_product__activity_type_icon
#: model:ir.model.fields,help:product.field_product_template__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikon pld: fa-tasks"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"A szabály alkalmazásához, a beszerzési/értékesítési mennyiségnek nagyobbnak vagy egyenlőnek kell lennie az ebben a mezőben meghatározott minimum mennyiségnél.\n"
"A termék alapértelmezett mértékegységében feltüntetve."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__print_format
msgid "Format"
msgstr "Formátum"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__formula
msgid "Formula"
msgstr "Képlet"

#. module: product
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Four Person Desk"
msgstr "Négyszemélyes asztal"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid "Four person modern office workstation"
msgstr "Négyszemélyes, modern irodai munkaállomás"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__is_custom
msgid "Free text"
msgstr ""

#. module: product
#: model:product.template,name:product.product_product_furniture_product_template
msgid "Furniture Assembly"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Future Activities"
msgstr "Jövőbeni tevékenységek"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Általános információk"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Get product pictures using Barcode"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__packaging_ids
#: model:ir.model.fields,help:product.field_product_template__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr "A termék különböző csomagolási módjait adja meg."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__sequence
#: model:ir.model.fields,help:product.field_product_template__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "A termék megjelenítése során alkalmazandó sorrendet adja meg."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Gold Member Pricelist"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__type__consu
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Goods"
msgstr "Termékek"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__type
#: model:ir.model.fields,help:product.field_product_template__type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""
"A termékek kézzel megfogható, anyagiasult dolgok.\n"
"A szolgáltatásoknak nincs anyagi formája."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Google Images"
msgstr "Google Képek"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Group By"
msgstr "Csoportosítás"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__html_color
msgid "HTML Color Index"
msgstr "HTML színjegyzék"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__has_message
#: model:ir.model.fields,field_description:product.field_product_pricelist__has_message
#: model:ir.model.fields,field_description:product.field_product_product__has_message
#: model:ir.model.fields,field_description:product.field_product_template__has_message
msgid "Has Message"
msgstr "Van üzenet"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__html_color
#: model:ir.model.fields,help:product.field_product_template_attribute_value__html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color if the attribute type is 'Color'."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "History"
msgstr "Előzmények"

#. module: product
#: model:product.template,name:product.expense_hotel_product_template
msgid "Hotel Accommodation"
msgstr "Hotel szállás"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__id
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__id
#: model:ir.model.fields,field_description:product.field_product_attribute_value__id
#: model:ir.model.fields,field_description:product.field_product_category__id
#: model:ir.model.fields,field_description:product.field_product_combo__id
#: model:ir.model.fields,field_description:product.field_product_combo_item__id
#: model:ir.model.fields,field_description:product.field_product_document__id
#: model:ir.model.fields,field_description:product.field_product_label_layout__id
#: model:ir.model.fields,field_description:product.field_product_packaging__id
#: model:ir.model.fields,field_description:product.field_product_pricelist__id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__id
#: model:ir.model.fields,field_description:product.field_product_product__id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:product.field_product_tag__id
#: model:ir.model.fields,field_description:product.field_product_template__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__id
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__id
msgid "ID"
msgstr "ID"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_template__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kivétel tevékenységet jelző ikon"

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_needaction
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,help:product.field_product_product__message_needaction
#: model:ir.model.fields,help:product.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ha be van jelölve, akkor az új üzenetek figyelmet igényelnek."

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_has_error
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,help:product.field_product_product__message_has_error
#: model:ir.model.fields,help:product.field_product_template__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Ha be van jelölve, akkor néhány üzenetnél kézbesítési hiba lépett fel."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_id
msgid ""
"If not set, the vendor price will apply to all variants of this product."
msgstr ""
"Ha nincs beállítva, akkor a szállítói ár a termék összes változatára "
"érvényes lesz."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__active
msgid ""
"If unchecked, it will allow you to hide the attribute without removing it."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr ""
"Ha nincs bejelölve, akkor elrejtheti az árlistát annak eltávolítása nélkül."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__active
#: model:ir.model.fields,help:product.field_product_template__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr ""
"Ha nincs bejelölve, akkor eltüntetheti a terméket annak törlése nélkül."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__image
#: model:ir.model.fields,field_description:product.field_product_product__image_1920
#: model:ir.model.fields,field_description:product.field_product_template__image_1920
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__image
msgid "Image"
msgstr "Kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1024
#: model:ir.model.fields,field_description:product.field_product_template__image_1024
msgid "Image 1024"
msgstr "1024-es kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_128
#: model:ir.model.fields,field_description:product.field_product_template__image_128
msgid "Image 128"
msgstr "Kép 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_256
#: model:ir.model.fields,field_description:product.field_product_template__image_256
msgid "Image 256"
msgstr "256-os kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_512
#: model:ir.model.fields,field_description:product.field_product_template__image_512
msgid "Image 512"
msgstr "512-es kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_height
msgid "Image Height"
msgstr "Kép magassága"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_src
msgid "Image Src"
msgstr "Kép forrása"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_width
msgid "Image Width"
msgstr "Kép szélessége"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Image is a link"
msgstr "A kép egy hivatkozás"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid "Import Template for Pricelists"
msgstr "Importálási sablon árlistákhoz"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Import Template for Products"
msgstr "Importálási sablon termékekhez"

#. module: product
#. odoo-python
#: code:addons/product/models/product_supplierinfo.py:0
msgid "Import Template for Vendor Pricelists"
msgstr "Importálási sablon szállítói árlistákhoz"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_search
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Inactive"
msgstr "Inaktív"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__index_content
msgid "Indexed Content"
msgstr "Indexált tartalom"

#. module: product
#: model:product.template,name:product.product_product_24_product_template
msgid "Individual Workplace"
msgstr "Egyedi munkatér"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__always
msgid "Instantly"
msgstr "Azonnal"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Notes"
msgstr "Belső jegyzetek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__default_code
#: model:ir.model.fields,field_description:product.field_product_template__default_code
msgid "Internal Reference"
msgstr "Belső hivatkozás"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__barcode
msgid "International Article Number used for product identification."
msgstr "A termék azonosítására használt Nemzetközi Cikkszám (IAN)."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
msgid "Invalid Operation"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Készlet"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_product__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "Követő"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_product_variant
msgid "Is Product Variant"
msgstr "Termékváltozat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__has_configurable_attributes
#: model:ir.model.fields,field_description:product.field_product_template__has_configurable_attributes
msgid "Is a configurable product"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__is_product_variant
msgid "Is a product variant"
msgstr "Ez egy termék változat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__public
msgid "Is public document"
msgstr "Nyilvános dokumentum"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__0
msgid "Kilograms"
msgstr "Kilogramm"

#. module: product
#: model:product.template,name:product.product_product_6_product_template
msgid "Large Cabinet"
msgstr "Nagy kabinet"

#. module: product
#: model:product.template,name:product.product_product_8_product_template
msgid "Large Desk"
msgstr "Nagy asztal"

#. module: product
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Large Meeting Table"
msgstr "Nagy értekezlet asztal"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_category__write_uid
#: model:ir.model.fields,field_description:product.field_product_combo__write_uid
#: model:ir.model.fields,field_description:product.field_product_combo_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_document__write_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_product__write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_uid
#: model:ir.model.fields,field_description:product.field_product_tag__write_uid
#: model:ir.model.fields,field_description:product.field_product_template__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_product_category__write_date
#: model:ir.model.fields,field_description:product.field_product_combo__write_date
#: model:ir.model.fields,field_description:product.field_product_combo_item__write_date
#: model:ir.model.fields,field_description:product.field_product_document__write_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_date
#: model:ir.model.fields,field_description:product.field_product_packaging__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_date
#: model:ir.model.fields,field_description:product.field_product_tag__write_date
#: model:ir.model.fields,field_description:product.field_product_template__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Late Activities"
msgstr "Késő tevékenységek"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"A termékek beszerzési rendelésének visszaigazolása és az áruk "
"raktárépületébe beérkezése közt eltelt idő napokban. A beszerzési rendelés "
"tervezésének automatikus kiszámításához használja az ütemező."

#. module: product
#: model:product.attribute.value,name:product.fabric_attribute_leather
msgid "Leather"
msgstr ""

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Legs"
msgstr "Lábak"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_attribute_value__pav_attribute_line_ids
msgid "Lines"
msgstr "Sorok"

#. module: product
#: model:product.template,name:product.product_product_local_delivery_product_template
msgid "Local Delivery"
msgstr "Belföldi kiszállítás"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Locally handmade"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Logistics"
msgstr "Logisztika"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__exclude_for
msgid ""
"Make this attribute value not compatible with other values of the product or"
" some attribute values of optional and accessory products."
msgstr ""

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Termék csomagolás kezelése"

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Termékváltozatok kezelése"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Margins"
msgstr "Árrések"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_markup
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Markup"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Max. árrés"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_max_margin
msgid "Max. Price Margin"
msgstr "Max. árrés"

#. module: product
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__message
msgid "Message"
msgstr "Üzenet"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_has_error
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Üzenetkézbesítési hiba"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_ids
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_ids
msgid "Messages"
msgstr "Üzenetek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__mimetype
msgid "Mime Type"
msgstr "MIME típus"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min Qty"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Min. árrés"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_min_margin
msgid "Min. Price Margin"
msgstr "Min. árrés"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__min_quantity
msgid "Min. Quantity"
msgstr "Min. mennyiség"

#. module: product
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__mode
msgid "Mode"
msgstr "Mód"

#. module: product
#: model:product.template,name:product.monitor_stand_product_template
msgid "Monitor Stand"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__multi
msgid "Multi-checkbox"
msgstr ""

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_check_multi_checkbox_no_variant
msgid ""
"Multi-checkbox display type is not compatible with the creation of variants"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Tevékenységeim határideje"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__name
#: model:ir.model.fields,field_description:product.field_product_category__name
#: model:ir.model.fields,field_description:product.field_product_combo__name
#: model:ir.model.fields,field_description:product.field_product_document__name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__name
#: model:ir.model.fields,field_description:product.field_product_product__name
#: model:ir.model.fields,field_description:product.field_product_tag__name
#: model:ir.model.fields,field_description:product.field_product_template__name
msgid "Name"
msgstr "Név"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__no_variant
msgid "Never"
msgstr "Soha"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid "New"
msgstr "Új"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Következő tevékenység naptár esemény"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Következő tevékenység határideje"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_summary
#: model:ir.model.fields,field_description:product.field_product_product__activity_summary
#: model:ir.model.fields,field_description:product.field_product_template__activity_summary
msgid "Next Activity Summary"
msgstr "Következő tevékenység összegzése"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_id
msgid "Next Activity Type"
msgstr "Következő tevékenység típusa"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
msgid ""
"No product to print, if the product is archived please unarchive it before "
"printing its label."
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
msgid "No products could be found."
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "No products found in the report"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid "No vendor pricelist found"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
msgid "No, keep it"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
msgid "Note:"
msgstr "Megjegyzés:"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__number_related_products
msgid "Number Related Products"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Akciók száma"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Hibák száma"

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Üzenetek száma, melyek akciót igényelnek"

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kézbesítési hibával rendelkező üzenetek száma"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_item_count
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_item_count
msgid "Number of price rules"
msgstr ""

#. module: product
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Office Chair"
msgstr "Irodai szék"

#. module: product
#: model:product.template,name:product.product_product_12_product_template
msgid "Office Chair Black"
msgstr "Fekete irodai szék"

#. module: product
#: model:product.template,name:product.office_combo_product_template
msgid "Office Combo"
msgstr ""

#. module: product
#: model:product.template,name:product.product_order_01_product_template
msgid "Office Design Software"
msgstr "Iroda tervező szoftver"

#. module: product
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Office Lamp"
msgstr "Irodai lámpa"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"On the product %(product)s you cannot associate the value %(value)s with the"
" attribute %(attribute)s because they do not match."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"On the product %(product)s you cannot transform the attribute "
"%(attribute_src)s into the attribute %(attribute_dest)s."
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_document_kanban/upload_button/upload_button.js:0
msgid "Oops! '%(fileName)s' didn’t upload since its format isn’t allowed."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Eredeti (nem átméretezett, nem optimalizált) mellékletek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo_item__lst_price
msgid "Original Price"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base_pricelist_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Other Pricelist"
msgstr "Másik árlista"

#. module: product
#: model:product.template,name:product.product_template_dining_table
msgid "Outdoor dining table"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Package Type A"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "Csomagolás"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_id
msgid "Parent Category"
msgstr "Szülő kategória"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_path
msgid "Parent Path"
msgstr "Szülő útvonal"

#. module: product
#: model:product.template,name:product.product_product_9_product_template
msgid "Pedal Bin"
msgstr "Pedálos szemetesláda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__percent_price
msgid "Percentage Price"
msgstr "Százalékos ár"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__pills
msgid "Pills"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.fabric_attribute_plastic
msgid "Plastic"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Please enter a positive whole number."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_document.py:0
msgid ""
"Please enter a valid URL.\n"
"Example: https://www.odoo.com\n"
"\n"
"Invalid URL: %s"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Please select some products first."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Please specify the category for which this rule should be applied"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Please specify the product for which this rule should be applied"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"Please specify the product variant for which this rule should be applied"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__1
msgid "Pounds"
msgstr "Font"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Price"
msgstr "Ár"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_discount
msgid "Price Discount"
msgstr "Árengedmény"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_round
msgid "Price Rounding"
msgstr "Ár kerekítése"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#: model:ir.actions.act_window,name:product.product_pricelist_item_action
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Price Rules"
msgstr "Ár szabályok"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Type"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__list_price
#: model:ir.model.fields,help:product.field_product_template__list_price
msgid "Price at which the product is sold to customers."
msgstr "Az ár amin a termék értékesítésre kerül vevők részére"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Ár:"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_label_layout__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__pricelist_id
#: model:ir.model.fields,field_description:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__property_product_pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Pricelist"
msgstr "Árlista"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__applied_on
#: model:ir.model.fields,help:product.field_product_pricelist_item__display_applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "Kiválasztott kiegészítőre vonatkozó árlista tétel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__name
msgid "Pricelist Name"
msgstr "Árlista neve"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#: model:ir.actions.server,name:product.action_product_price_list_report
#: model:ir.actions.server,name:product.action_product_template_price_list_report
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "Pricelist Report"
msgstr "Árlista kimutatás"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid "Pricelist Report Preview"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Pricelist Rule"
msgstr "Árlista szabály"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__item_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Pricelist Rules"
msgstr "Árlista szabályok"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_country_group__pricelist_ids
msgid "Pricelists"
msgstr "Árlisták"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Árlisták kezelése itt"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Árazás"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Print"
msgstr "Nyomtatás"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Print Labels"
msgstr "Címkék nyomtatása"

#. module: product
#. odoo-python
#: code:addons/product/controllers/pricelist_report.py:0
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_combo_item__product_id
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_ids
#: model:ir.model.fields,field_description:product.field_product_packaging__product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__1_product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__display_applied_on__1_product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_search_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product"
msgstr "Termék"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_form
msgid "Product Attribute"
msgstr "Terméktulajdonság"

#. module: product
#: model:ir.model,name:product.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Egyéni terméktulajdonság érték"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_template_value_ids
msgid "Product Attribute Values"
msgstr "Terméktulajdonság értékek"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Product Attribute and Values"
msgstr "Terméktulajdonság és értékek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__attribute_line_ids
msgid "Product Attributes"
msgstr "Terméktulajdonságok"

#. module: product
#: model:ir.model,name:product.model_product_catalog_mixin
msgid "Product Catalog Mixin"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Termék kategóriák"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_product__categ_id
#: model:ir.model.fields,field_description:product.field_product_template__categ_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__2_product_category
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Category"
msgstr "Termék kategória"

#. module: product
#: model:ir.model,name:product.model_product_combo
msgid "Product Combo"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_combo_item
msgid "Product Combo Item"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__combo_item_count
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__product_count
msgid "Product Count"
msgstr "Termék száma"

#. module: product
#: model:res.groups,name:product.group_product_manager
msgid "Product Creation"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_document
msgid "Product Document"
msgstr "Termék dokumentum"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_dymo
msgid "Product Label (PDF)"
msgstr "Termék címke (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_2x7
msgid "Product Label 2x7 (PDF)"
msgstr ""

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12
msgid "Product Label 4x12 (PDF)"
msgstr ""

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12_noprice
msgid "Product Label 4x12 No Price (PDF)"
msgstr ""

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x7
msgid "Product Label 4x7 (PDF)"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel_dymo
msgid "Product Label Report"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel2x7
msgid "Product Label Report 2x7"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12
msgid "Product Label Report 4x12"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12noprice
msgid "Product Label Report 4x12 No Price"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x7
msgid "Product Label Report 4x7"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_view_form_normalized
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Termék neve"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template__packaging_ids
msgid "Product Packages"
msgstr "Termék csomagok"

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model:ir.model.fields,field_description:product.field_product_packaging__name
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr "Termékcsomagolás"

#. module: product
#: model:ir.actions.report,name:product.report_product_packaging
msgid "Product Packaging (PDF)"
msgstr "Termék csomagolás (PDF)"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_search_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packagings"
msgstr "Termék csomagolások"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_properties_definition
msgid "Product Properties"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Tag"
msgstr "Termék címke"

#. module: product
#: model:ir.actions.act_window,name:product.product_tag_action
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Tags"
msgstr "Termék címkék"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Template"
msgstr "Terméksablon"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_exclusion
msgid "Product Template Attribute Exclusion"
msgstr "Terméksablon tulajdonság kizárás"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Terméksablon tulajdonság sor"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_template_ids
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_tree_tag
msgid "Product Templates"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Terméksablon"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tooltip
#: model:ir.model.fields,field_description:product.field_product_template__product_tooltip
msgid "Product Tooltip"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__type
#: model:ir.model.fields,field_description:product.field_product_template__type
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Type"
msgstr "Terméktípus"

#. module: product
#: model:ir.model,name:product.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Termék mértékegység"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__0_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_document_form
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Variant"
msgstr "Termékváltozat"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid "Product Variant Values"
msgstr "Termékváltozat értékek"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model:ir.model.fields,field_description:product.field_product_tag__product_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_product_view_activity
#: model_terms:ir.ui.view,arch_db:product.product_product_view_tree_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Variants"
msgstr "Termékváltozatok"

#. module: product
#. odoo-python
#: code:addons/product/report/product_label_report.py:0
msgid "Product model not defined, Please contact your administrator."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#: code:addons/product/models/product_catalog_mixin.py:0
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_view_activity
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Products"
msgstr "Termékek"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Termékek ára"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Termékek árlistája"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Products Price Rules Search"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Termékár keresés"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "Products: %(category)s"
msgstr "Termékek: %(category)s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_loyalty
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_properties
#: model:ir.model.fields,field_description:product.field_product_template__product_properties
msgid "Properties"
msgstr "Tulajdonságok"

#. module: product
#: model:product.attribute.value,name:product.pav_protection_kit
msgid "Protection kit"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template__purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Purchase"
msgstr "Beszerzés"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_purchase
#: model:ir.model.fields,field_description:product.field_product_template__description_purchase
msgid "Purchase Description"
msgstr "Beszerzési leírás"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_po_id
msgid "Purchase Unit"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Quantities"
msgstr "Mennyiségek"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Quantities (Price)"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__custom_quantity
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__min_qty
msgid "Quantity"
msgstr "Mennyiség"

#. module: product
#. odoo-python
#: code:addons/product/controllers/pricelist_report.py:0
msgid "Quantity (%s UoM)"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Quantity already present (%s)."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__qty
msgid "Quantity of products contained in the packaging."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Quotation Description"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__radio
msgid "Radio"
msgstr "Rádió"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__code
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Reference"
msgstr "Hivatkozás"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid ""
"Register the prices requested by your vendors for each product, based on the"
" quantity and the period."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__product_tmpl_ids
msgid "Related Products"
msgstr "Hasonló termékek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_product_variant_ids
msgid "Related Variants"
msgstr "Hasonló változatok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__ir_attachment_id
msgid "Related attachment"
msgstr "Kapcsolódó melléklet"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "Remove"
msgstr "Eltávolítás"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Remove quantity"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_field
msgid "Resource Field"
msgstr "Erőforrásmező"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_id
msgid "Resource ID"
msgstr "Erőforrás azonosító"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_model
msgid "Resource Model"
msgstr "Erőforrás modell"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_name
msgid "Resource Name"
msgstr "Erőforrás neve"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_user_id
msgid "Responsible User"
msgstr "Felelős felhasználó"

#. module: product
#: model:product.template,name:product.expense_product_product_template
msgid "Restaurant Expenses"
msgstr "Éttermi költségek"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Round off to"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__rows
msgid "Rows"
msgstr "Sorok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__rule_tip
msgid "Rule Tip"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__sale_ok
#: model:ir.model.fields,field_description:product.field_product_template__sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Sales"
msgstr "Értékesítés"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_sale
#: model:ir.model.fields,field_description:product.field_product_template__description_sale
msgid "Sales Description"
msgstr "Értékesítés leírása"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__list_price
#: model:ir.model.fields,field_description:product.field_product_template__list_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__list_price
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Sales Price"
msgstr "Eladási ár"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__lst_price
msgid "Sales Price"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__select
msgid "Select"
msgstr "Kiválasztás"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value__sequence
#: model:ir.model.fields,field_description:product.field_product_combo__sequence
#: model:ir.model.fields,field_description:product.field_product_document__sequence
#: model:ir.model.fields,field_description:product.field_product_packaging__sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist__sequence
#: model:ir.model.fields,field_description:product.field_product_product__sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__sequence
#: model:ir.model.fields,field_description:product.field_product_tag__sequence
#: model:ir.model.fields,field_description:product.field_product_template__sequence
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__sequence
msgid "Sequence"
msgstr "Sorszám"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__type__service
msgid "Service"
msgstr "Szolgáltatás"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Services"
msgstr "Szolgáltatások"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, round off to 10.00 and set an extra at -0.01"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Show Name"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Show all records which has next action date is before today"
msgstr ""
"Az összes olyan rekord megjelenítése, melynél a következő akció dátuma a mai"
" nap előtti"

#. module: product
#: model:product.attribute,name:product.size_attribute
msgid "Size"
msgstr "Méret"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_partner__specific_property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__specific_property_product_pricelist
msgid "Specific Property Product Pricelist"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Adjon meg egy termék kategóriát, ha ez a szabály csak a kategóriába vagy "
"annak alkategóriáiba tartozó termékekre vonatkozik. Egyébként hagyja üresen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Adjon meg egy terméket, ha ez a szabály csak egy termékre vonatkozik. "
"Egyébként hagyja üresen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Adjon meg egy terméksablont, ha ez a szabály csak egy terméksablonhoz "
"tartozik. Egyébként hagyja üresen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_surcharge
msgid ""
"Specify the fixed amount to add or subtract (if negative) to the amount "
"calculated with the discount."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Adja meg az alapáron felül érvényesítedő árrés maximum összegét."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr ""
"Határozza meg az alapáron felül érvényesíthető minimum árkülönbözetet."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_start
msgid "Start Date"
msgstr "Kezdődátum"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_start
msgid "Start date for this vendor price"
msgstr "Beszállítói ár kezdő dátum"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_start
msgid ""
"Starting datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_state
#: model:ir.model.fields,help:product.field_product_product__activity_state
#: model:ir.model.fields,help:product.field_product_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tevékenységeken alapuló állapot\n"
"Lejárt: A tevékenység határideje lejárt\n"
"Ma: A határidő ma van\n"
"Tervezett: Jövőbeli határidő."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "Steel"
msgstr "Acél"

#. module: product
#: model:product.template,name:product.product_product_7_product_template
msgid "Storage Box"
msgstr "Tároló doboz"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__store_fname
msgid "Stored Filename"
msgstr "Tárolt fájlnév"

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Beszállító árlista"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_tag_name_uniq
msgid "Tag name already exists!"
msgstr "A címke név már létezik!"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tag_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_tag_ids
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Tags"
msgstr "Címkék"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Tags are used to search product for a given theme."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__template_value_ids
msgid "Template Values"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "The Internal Reference '%s' already exists."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "The Reference '%s' already exists."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"The attribute %(attribute)s must have at least one value for the product "
"%(product)s."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__attribute_id
msgid ""
"The attribute cannot be changed once the value is used on at least one "
"product."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"The default Unit of Measure and the purchase Unit of Measure must be in the "
"same category."
msgstr ""
"Az alapértelmezett mértékegységnek és a beszerzési mértékegységnek ugyanabba"
" a kategóriába kell tartoznia."

#. module: product
#: model:product.template,description_sale:product.desk_organizer_product_template
msgid ""
"The desk organiser is perfect for storing all kinds of small things and "
"since the 5 boxes are loose, you can move and place them in the way that "
"suits you and your things best."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__display_type
#: model:ir.model.fields,help:product.field_product_attribute_value__display_type
#: model:ir.model.fields,help:product.field_product_template_attribute_value__display_type
msgid "The display type used in the Product Configurator."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__sequence
msgid "The first in the sequence is the default one."
msgstr "A sorrendben az első az alapértelmezett."

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "The minimum margin should be lower than the maximum margin."
msgstr ""
"A minimális árrésnek alacsonyabbnak kell lennie, mint a maximális árrésnek."

#. module: product
#: model:ir.model.fields,help:product.field_product_combo__base_price
msgid ""
"The minimum price among the products in this combo. This value will be used "
"to prorate the price of this combo with respect to the other combos in a "
"combo product. This heuristic ensures that whatever product the user chooses"
" in a combo, it will always be the same price."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_category__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""
"Ezen kategórián belüli termékek száma (az alkategóriákat nem veszi "
"tekintetbe)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"The number of variants to generate is above allowed limit. You should either"
" not generate variants for each combination or generate them on demand from "
"the sales order. To do so, open the form view of attributes and change the "
"mode of *Create Variants*."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__price
msgid "The price to purchase a product"
msgstr "Ár a termék beszerzéséhez"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "The product template is archived so no combination is possible."
msgstr "A terméksablon archivált, így nem lehetséges kombinációk megadása."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__min_qty
msgid ""
"The quantity to purchase from this vendor to benefit from the price, "
"expressed in the vendor Product Unit of Measure if not any, in the default "
"unit of measure of the product otherwise."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "The rounding method must be strictly positive."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_combo_item__lst_price
#: model:ir.model.fields,help:product.field_product_product__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"Az eladási ár a terméksablonok kerül kezelésre. Kattintson a 'Változatok "
"konfigurálása' gombra tulajdonság felárak megadásához."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"The value %(value)s is not defined for the attribute %(attribute)s on the "
"product %(product)s."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "There are no possible combination."
msgstr "Nincs lehetséges kombináció."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "There are no remaining closest combination."
msgstr "Nincs fennmaradó legközelebbi kombináció."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "There are no remaining possible combination."
msgstr "Nincs fennmaradó lehetséges kombináció."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"This configuration of product attributes, values, and exclusions would lead "
"to no possible variant. Please archive or delete your product directly if "
"intended."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Az összes tulajdonság felárainak összege"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is added to sales orders and invoices."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is only for internal purposes."
msgstr "Ez a megjegyzés belső használatra szolgál."

#. module: product
#: model:ir.model.fields,help:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,help:product.field_res_users__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"A jelenlegi partnernek történő értékesítéseknél a rendszer az "
"alapértelmezett helyett ezt az árlistát fogja használni."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"This product is part of a combo, so its type can't be changed to \"combo\"."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/uom_uom.py:0
msgid ""
"This rounding precision is higher than the Decimal Accuracy (%(digits)s digits).\n"
"This may cause inconsistencies in computations.\n"
"Please set a precision between %(min_precision)s and 1."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Ez a szállítói termékkód kerül nyomtatásra az ajánlatkéréseken. Hagyja "
"üresen a belső referencia használatához."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Ez a szállítói terméknév kerül nyomtatásra az ajánlatkéréseken. Hagyja "
"üresen a belső megnevezés használatához."

#. module: product
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "Three Seater Sofa with Lounger in Steel Grey Colour"
msgstr ""

#. module: product
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Three-Seat Sofa"
msgstr "Háromszemélyes kanapé"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Today Activities"
msgstr "Mai tevékenységek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__type
msgid "Type"
msgstr "Típus"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_template__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kivétel tevékenység típusa a rekordon."

#. module: product
#. odoo-python
#: code:addons/product/controllers/pricelist_report.py:0
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "UOM"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
msgid "Unable to find report template for %s format"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Unit"
msgstr "Egység"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Unit Price"
msgstr "Egységár"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,field_description:product.field_product_product__uom_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,field_description:product.field_product_template__uom_id
msgid "Unit of Measure"
msgstr "Mértékegység"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_uom
#: model:ir.model.fields,field_description:product.field_product_product__uom_name
#: model:ir.model.fields,field_description:product.field_product_template__uom_name
msgid "Unit of Measure Name"
msgstr "Mértékegység név"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "Unit price:"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Units"
msgstr "Egység"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_uom
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Mértékegységek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_category_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_category_id
msgid "UoM Category"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Update extra prices"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_update_product_attribute_value
msgid "Update product attribute value"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid "Update product extra prices"
msgstr ""

#. module: product
#: model:ir.model.fields.selection,name:product.selection__update_product_attribute_value__mode__update_extra_price
msgid "Update the extra price on existing products"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_document_kanban/upload_button/upload_button.xml:0
msgid "Upload"
msgstr "Feltöltés"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Upload files to your product"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Upsell & Cross-Sell"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__url
msgid "Url"
msgstr "Webcím"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__compute_price
msgid ""
"Use the discount rules and activate the discount settings in order to show "
"discount to customer."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"Use this feature to store any files you would like to share with your "
"customers"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_used_on_products
msgid "Used on Products"
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Username"
msgstr "Felhasználónév"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Valid terméktulajdonság sorok"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Érvényesség"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Validity Period"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__name
msgid "Value"
msgstr "Érték"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_count
msgid "Value Count"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__standard_price
#: model:ir.model.fields,help:product.field_product_template__standard_price
msgid ""
"Value of the product (automatically computed in AVCO).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Values"
msgstr "Értékek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_id
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Variant"
msgstr "Változat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_variant_count
msgid "Variant Count"
msgstr "Változat számláló"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_variant
msgid "Variant Creation"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1920
msgid "Variant Image"
msgstr "Változat kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1024
msgid "Variant Image 1024"
msgstr "1024-es változat kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_128
msgid "Variant Image 128"
msgstr "128-as változat kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_256
msgid "Variant Image 256"
msgstr "256-os változat kép"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_512
msgid "Variant Image 512"
msgstr "512-es változat kép"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Változat információ"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__price_extra
msgid "Variant Price Extra"
msgstr "Változat felár"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__variant_seller_ids
msgid "Variant Seller"
msgstr "Változat értékesítő"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__additional_product_tag_ids
msgid "Variant Tags"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_variant_value_ids
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
msgid "Variant Values"
msgstr "Változat értékek"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Variant: %s"
msgstr "Változat: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Változatok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__partner_id
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "Beszállító"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendor Bills"
msgstr "Beszállítói számlák"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Beszállítói információ"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr "Beszállítói árlisták"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_code
msgid "Vendor Product Code"
msgstr "Beszállítói termékkód"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_name
msgid "Vendor Product Name"
msgstr "Beszállítói terméknév"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__seller_ids
msgid "Vendors"
msgstr "Beszállítók"

#. module: product
#: model:product.template,name:product.product_product_2_product_template
msgid "Virtual Home Staging"
msgstr "Virtuális otthon bemutató"

#. module: product
#: model:product.template,name:product.product_product_1_product_template
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Virtual Interior Design"
msgstr "Virtuális belső dizájn"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Visible to all"
msgstr "Látható mindenkinek"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__voice_ids
msgid "Voice"
msgstr "Hang"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume
#: model:ir.model.fields,field_description:product.field_product_template__volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Volume"
msgstr "Térfogat"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_volume_volume_in_cubic_feet
msgid "Volume unit of measure"
msgstr "Térfogat mértékegység"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__volume_uom_name
msgid "Volume unit of measure label"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#: code:addons/product/models/uom_uom.py:0
msgid "Warning!"
msgstr "Figyelem!"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Warnings"
msgstr "Figyelmeztetések"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight
#: model:ir.model.fields,field_description:product.field_product_template__weight
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Weight"
msgstr "Súly"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr "Tömeg mértékegység"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Súly mértékegység címke"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr "Fehér"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_color_wood
msgid "Wood"
msgstr "Fa"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__write_date
msgid "Write Date"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/wizard/update_product_attribute_value.py:0
msgid ""
"You are about to add the value \"%(attribute_value)s\" to %(product_count)s "
"products."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/wizard/update_product_attribute_value.py:0
msgid "You are about to update the extra price of %s products."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/res_config_settings.py:0
msgid ""
"You are deactivating the pricelist feature. Every active pricelist will be "
"archived."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
msgid ""
"You are setting a Decimal Accuracy less precise than the UOMs:\n"
"%s\n"
"This may cause inconsistencies in computations.\n"
"Please increase the rounding of those units of measure, or the digits of this Decimal Accuracy."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__percent_price
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_discount
msgid "You can apply a mark-up by setting a negative discount."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_markup
msgid "You can apply a mark-up on the cost"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"You can assign pricelists to your customers or select one when creating a "
"new sales quotation."
msgstr ""
"Hozzárendelhet árlistákat vevőkhöz, vagy kiválaszthat egyet értékesítési "
"ajánlat létrehozása során."

#. module: product
#: model:ir.model.fields,help:product.field_product_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Feltölthet egy fájlt a számítógépéről, vagy másolhat/beilleszthet egy, a "
"fájlra mutató internetes hivatkozást."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__image
#: model:ir.model.fields,help:product.field_product_template_attribute_value__image
msgid ""
"You can upload an image that will be used as the color of the attribute "
"value."
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "You can't edit this product in the catalog."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
msgid ""
"You cannot archive this attribute as there are still products linked to it"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"You cannot assign the Main Pricelist as Other Pricelist in PriceList Item"
msgstr "Nem lehet megadni a fő árlistát másik árlistaként az árlista tételben"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
msgid ""
"You cannot change the Variants Creation Mode of the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid ""
"You cannot change the attribute of the value %(value)s because it is used on"
" the following products: %(products)s"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"You cannot change the product of the value %(value)s set on product "
"%(product)s."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"You cannot change the value of the value %(value)s set on product "
"%(product)s."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
msgid "You cannot create recursive categories."
msgstr "Nem lehet létrehozni rekurzív kategóriákat."

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""
"Nem lehet a vállalt pénznemének kerekítési pontosságánál nagyobb 'Könyvelés'"
" tizedes pontosságot meghatározni"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid ""
"You cannot delete pricelist(s):\n"
"(%(pricelists)s)\n"
"They are used within pricelist(s):\n"
"%(other_pricelists)s"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
msgid "You cannot delete the %s product category."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
msgid ""
"You cannot delete the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid ""
"You cannot delete the value %(value)s because it is used on the following products:\n"
"%(products)s\n"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
msgid ""
"You cannot delete this product category, it is the default generic category."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"You cannot move the attribute %(attribute)s from the product %(product_src)s"
" to the product %(product_dest)s."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"You cannot update related variants from the values. Please update related "
"values from the variants."
msgstr ""

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Minden értékesítéshez és beszerzéshez szükséges megadni egy terméket,\n"
"                ami lehet készletezett termék, fogyóeszköz vagy szolgáltatás."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
msgid ""
"You must define a product for everything you sell, whether it's a physical product,\n"
"                a consumable or a service you offer to customers.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Meg kell adni terméket mindenhez, amit értékesít, akkor is, ha az készletezett termék, \n"
"                fogyóeszköz vagy vevőknek nyújtott szolgáltatás.\n"
"                A termék űrlap tartalmazza az értékesítés egyszerűsítéséhez használható információkat:\n"
"                árak, ajánlati megjegyzések, könyvelési adatok, beszerzési módszerek, stb."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "You must leave at least one quantity."
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
msgid "You need to set a positive quantity."
msgstr "Pozitív mennyiséget kell beállítani."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "csv"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_view_kanban
msgid "days"
msgstr "nap"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "discount"
msgstr "árengedmény"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_view_form_normalized
msgid "e.g. 1234567890"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_form
msgid "e.g. Burger Choice"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_view_form_normalized
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "e.g. Cheese Burger"
msgstr "pl.: Sajtburger"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "pl.: Lámpák"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr "pl.: Odoo Enterprise előfizetés"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "e.g. Starter - Meal - Desert"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "pl.: USD kiskereskedők"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "markup"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "on"
msgstr "adóalap:"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "pdf"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "per"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
msgid "product"
msgstr "termék"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "product cost"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "round off to 10.00 and set an extra at -0.01"
msgstr ""

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "sales price"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "a szülő vállalat"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template."
msgstr "a terméksablon."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "eddig"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "xlsx"
msgstr ""
