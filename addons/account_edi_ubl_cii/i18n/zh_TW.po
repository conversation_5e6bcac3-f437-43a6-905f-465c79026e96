# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_ubl_cii
# 
# Translators:
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"%s should have a KVK or OIN number set in Company ID field or as Peppol "
"e-address (EAS code 0106 or 0190)."
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr "1.0"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_facturx_export_22
msgid "42"
msgstr "42"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "A payment of %s was detected."
msgstr "偵測到 %s 的付款。"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_a_nz
msgid "A-NZ BIS Billing 3.0"
msgstr "澳洲紐西蘭 BIS 賬單 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__as
msgid "AS2 exchange"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move_send
msgid "Account Move Send"
msgstr "賬戶分錄傳送"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9923
msgid "Albania VAT"
msgstr "阿爾巴尼亞增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9922
msgid "Andorra VAT"
msgstr "安道爾增值稅"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Articles 226 items 11 to 15 Directive 2006/112/EN"
msgstr "指令 2006/112/EN 第 226 條，第 11 至 15 項目"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"At least one of the following fields %(field_list)s is required on "
"%(record)s."
msgstr "下列欄位 %(field_list)s 中的至少一項，為 %(record)s 所必需。"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_id
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_id
msgid "Attachment"
msgstr "附件"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0151
msgid "Australia ABN"
msgstr "澳洲 ABN 商業登記號碼"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9914
msgid "Austria UID"
msgstr "奧地利 UID"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9915
msgid "Austria VOKZ"
msgstr "奧地利 VOKZ"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_bis3
msgid "BIS Billing 3.0"
msgstr "BIS 賬單 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_a_nz
msgid "BIS Billing 3.0 A-NZ"
msgstr "BIS 賬單 3.0 澳洲紐西蘭"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_sg
msgid "BIS Billing 3.0 SG"
msgstr "BIS 賬單 3.0 新加坡"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_de
msgid "BIS3 DE (XRechnung)"
msgstr "BIS3 DE (XRechnung)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0208
msgid "Belgian Company Registry"
msgstr "比利時公司註冊處"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9925
msgid "Belgian VAT"
msgstr "比利時增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9924
msgid "Bosnia and Herzegovina VAT"
msgstr "波士尼亞與赫塞哥維納增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9926
msgid "Bulgaria VAT"
msgstr "保加利亞增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9913
msgid "Business Registers Network"
msgstr "商業登記網絡（歐洲）"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Check Partner(s)"
msgstr "檢查合作夥伴"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""
"用於識別 BIS 賬單 3.0 及其衍生產品所使用終端點的代碼。\n"
"             詳細列表： https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0210
msgid "Codice Fiscale"
msgstr "意大利稅務編號 Codice Fiscale"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0201
msgid "Codice Univoco Unità Organizzativa iPA"
msgstr "Codice Univoco Unità Organizzativa iPA"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_common
msgid ""
"Common functions for EDI documents: generate the data, the constraints, etc"
msgstr "EDI文檔常用功能：生成數據、限制等"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
msgid "Conditional cash/payment discount"
msgstr "有條件現金/付款折扣"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Configure"
msgstr "設定"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve a partner corresponding to '%s'. A new partner was "
"created."
msgstr "檢索不到與「%s」對應的合作夥伴；已建立一個新的合作夥伴。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve currency: %s. Did you enable the multicurrency option and"
" activate the currency?"
msgstr "未能讀取貨幣 %s 。是否已經啟用多貨幣選項及啟動該貨幣？"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Could not retrieve the tax: %(amount)s %% for line '%(line)s'."
msgstr "未能讀取稅項：%(amount)s%%，資料行：%(line)s。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Could not retrieve the tax: %(tax_percentage)s %% for line '%(line)s'."
msgstr "未能讀取稅項：%(tax_percentage)s%%，資料行：%(line)s。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve the tax: %s for the document level allowance/charge."
msgstr "未能就文件層級的津貼/費用，讀取所需的稅項：%s。"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9934
msgid "Croatia VAT"
msgstr "克羅地亞增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9928
msgid "Cyprus VAT"
msgstr "塞浦路斯增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9929
msgid "Czech Republic VAT"
msgstr "捷克共和國增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0060
msgid "DUNS Number"
msgstr "DUNS 資料統一編號系統號碼"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0184
msgid "Denmark CVR"
msgstr "丹麥 CVR"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0096
msgid "Denmark P"
msgstr "丹麥 P"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0198
msgid "Denmark SE"
msgstr "丹麥 SE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0130
msgid "Directorates of the European Commission"
msgstr "歐盟委員會各署"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_efff
msgid "E-FFF (BE)"
msgstr "E-FFF (BE)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0088
msgid "EAN Location Code"
msgstr "EAN 位置代碼"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr "EN 16931"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "Each invoice line shall have one and only one tax."
msgstr "發票的每個資料行應該只有一項稅項。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "Each invoice line should have a product or a label."
msgstr "每個發票資料行應該有一個產品或一個標籤。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Each invoice line should have at least one tax."
msgstr "發票的每個資料行應至少包含一項稅項。"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__em
msgid "Electronic mail"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr "建立 EDI 文件時發生錯誤(格式: %s)："

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0191
msgid "Estonia Company code"
msgstr "愛沙尼亞公司代碼"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9931
msgid "Estonia VAT"
msgstr "愛沙尼亞增值稅"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Export outside the EU"
msgstr "出口歐盟以外地區"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__facturx
msgid "Factur-X (CII)"
msgstr "Factur-X（CII）"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_cii
msgid "Factur-x/XRechnung CII 2.2.0"
msgstr "Factur-x / XRechnung CII 2.2.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__au
msgid "File Transfer Protocol"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0037
msgid "Finland LY-tunnus"
msgstr "芬蘭商業編號 LY-tunnus"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0216
msgid "Finland OVT code"
msgstr "芬蘭 OVT 代碼"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0213
msgid "Finland VAT"
msgstr "芬蘭增值稅"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"For intracommunity supply, the actual delivery date or the invoicing period "
"should be included."
msgstr "共同體內部供應的項目，應包括實際交貨日期或發票期間。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "For intracommunity supply, the delivery address should be included."
msgstr "共同體內部供應的項目，應包括送貨地址。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Format used to import the invoice: %s"
msgstr "匯入發票的格式: %s"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0002
msgid "France SIRENE"
msgstr "法國 SIRENE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0009
msgid "France SIRET"
msgstr "法國 SIRET"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9957
msgid "France VAT"
msgstr "法國增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0209
msgid "GS1 identification keys"
msgstr "GS1 主識別編碼"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0204
msgid "Germany Leitweg-ID"
msgstr "德國 Leitweg-ID"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9930
msgid "Germany VAT"
msgstr "德國增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9933
msgid "Greece VAT"
msgstr "希臘增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9910
msgid "Hungary VAT"
msgstr "匈牙利增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0196
msgid "Iceland Kennitala"
msgstr "冰島身份證號碼 Kennitala"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0202
msgid "Indirizzo di Posta Elettronica Certificata"
msgstr "意大利認證電郵地址 Indirizzo di Posta Elettronica Certificata"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Intra-Community supply"
msgstr "共同體內部供應"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr "Odoo 產生的發票"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9935
msgid "Ireland VAT"
msgstr "愛爾蘭增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__is_peppol_edi_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__is_peppol_edi_format
msgid "Is Peppol Edi Format"
msgstr "是 Peppol Edi 格式"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__is_ubl_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__is_ubl_format
msgid "Is Ubl Format"
msgstr "是 Ubl 格式"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0097
msgid "Italia FTI"
msgstr "意大利 FTI"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0211
msgid "Italia Partita IVA"
msgstr "意大利 Partita IVA"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0221
msgid "Japan IIN"
msgstr "日本 IIN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0188
msgid "Japan SST"
msgstr "日本 SST"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9919
msgid "Kennziffer des Unternehmensregisters"
msgstr "Kennziffer des Unternehmensregisters（公司註冊識別號碼）"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9939
msgid "Latvia VAT"
msgstr "拉脫維亞增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0199
msgid "Legal Entity Identifier (LEI)"
msgstr "法律實體識別碼（LEI）"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9936
msgid "Liechtenstein VAT"
msgstr "列支敦士登增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0200
msgid "Lithuania JAK"
msgstr "立陶宛 JAK"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9937
msgid "Lithuania VAT"
msgstr "立陶宛增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9938
msgid "Luxembourg VAT"
msgstr "盧森堡增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9942
msgid "Macedonia VAT"
msgstr "馬其頓增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0230
msgid "Malaysia"
msgstr "馬來西亞"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9943
msgid "Malta VAT"
msgstr "馬耳他增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9940
msgid "Monaco VAT"
msgstr "摩納哥增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9941
msgid "Montenegro VAT"
msgstr "蒙特內哥羅/黑山增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__nlcius
msgid "NLCIUS"
msgstr "NLCIUS"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0106
msgid "Netherlands KvK"
msgstr "荷蘭 KvK"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0190
msgid "Netherlands OIN"
msgstr "荷蘭 OIN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9944
msgid "Netherlands VAT"
msgstr "荷蘭增值稅"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"No gross price, net price nor line subtotal amount found for line in xml"
msgstr "在 XML 內找不到該資料行的總價、淨價或資料行小計金額"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0192
msgid "Norway Org.nr."
msgstr "挪威組織編號 Org.nr."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__an
msgid "O.F.T.P. (ODETTE File Transfer Protocol)"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr "Odoo"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Peppol Address"
msgstr "Peppol 地址"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid "Peppol Endpoint"
msgstr "Peppol 終端點"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Peppol ID"
msgstr "Peppol 識別碼"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_eas
msgid "Peppol e-address (EAS)"
msgstr "Peppol 電子地址（EAS）"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Please fill in partner's VAT or Peppol Address."
msgstr "請填寫合作夥伴的 VAT 或 Peppol 地址。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid ""
"Please fill in your company's VAT or Peppol Address to generate a complete "
"XML file."
msgstr "請填寫你公司的 VAT 或 Peppol 地址，以產生完整的 XML 檔案。"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9945
msgid "Poland VAT"
msgstr "波蘭增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9946
msgid "Portugal VAT"
msgstr "葡萄牙增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_ir_actions_report
msgid "Report Action"
msgstr "報表動作"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9947
msgid "Romania VAT"
msgstr "羅馬尼亞增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9918
msgid "S.W.I.F.T"
msgstr "S.W.I.F.T"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0142
msgid "SECETI Object Identifiers"
msgstr "SECETI 物件標識符"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_sg
msgid "SG BIS Billing 3.0"
msgstr "新加坡 BIS 賬單 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_nl
msgid "SI-UBL 2.0 (NLCIUS)"
msgstr "SI-UBL 2.0 (NLCIUS)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0135
msgid "SIA Object Identifiers"
msgstr "SIA 物件標識符"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9951
msgid "San Marino VAT"
msgstr "聖馬力諾增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9948
msgid "Serbia VAT"
msgstr "塞爾維亞增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0195
msgid "Singapore UEN"
msgstr "新加坡 UEN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9950
msgid "Slovakia VAT"
msgstr "斯洛伐克增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9949
msgid "Slovenia VAT"
msgstr "斯洛文尼亞增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9920
msgid "Spain VAT"
msgstr "西班牙增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0007
msgid "Sweden Org.nr."
msgstr "瑞典組織編號 Org.nr."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9955
msgid "Sweden VAT"
msgstr "瑞典增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0183
msgid "Swiss UIDB"
msgstr "瑞士 UIDB"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9927
msgid "Swiss VAT"
msgstr "瑞士增值稅"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Tax '%(tax_name)s' is invalid: %(error_message)s"
msgstr "稅項「%(tax_name)s」無效： %(error_message)s"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid ""
"The Peppol endpoint is not valid. It should contain exactly 10 digits "
"(Company Registry number).The expected format is: **********"
msgstr "Peppol 終端點無效。應剛好為 10 位數字（公司註冊號碼）。預期格式例子：**********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid "The Peppol endpoint is not valid. The expected format is: **********"
msgstr "Peppol 端點無效。預期格式為：**********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid ""
"The Peppol endpoint is not valid. The expected format is: **************"
msgstr "Peppol 端點無效。預期格式為：**************"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"The VAT number of the supplier does not seem to be valid. It should be of "
"the form: NO179728982MVA."
msgstr "供應商的增值稅編號似乎無效。其格式應為：NO179728982MVA。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "The VAT of the %s should be prefixed with its country code."
msgstr "增值稅 %s應加上其國家代碼。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "The country is required for the %s."
msgstr "以下項目需要國家/地區資料：%s。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The currency '%s' is not active."
msgstr "貨幣「%s」未啟用。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The element %(record)s is required on %(field_list)s."
msgstr "元素 %(record)s 是 %(field_list)s 所必需的。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The field %(field)s is required on %(record)s."
msgstr "欄位 %(field)s 是 %(record)s 所必需的。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"The field 'Sanitized Account Number' is required on the Recipient Bank."
msgstr "收款銀行需要填寫“經淨化的賬戶號碼”欄位。"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
msgid ""
"The invoice has been converted into a credit note and the quantities have "
"been reverted."
msgstr "發票已轉換為貸記單，並且數量已恢復。"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9952
msgid "Turkey VAT"
msgstr "土耳其增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_20
msgid "UBL 2.0"
msgstr "UBL 2.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_21
msgid "UBL 2.1"
msgstr "UBL 2.1"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_bis3
msgid "UBL BIS Billing 3.0.12"
msgstr "UBL BIS 賬單 3.0.12"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0193
msgid "UBL.BE party identifier"
msgstr "UBL.BE 交易方標識符"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_file
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_file
msgid "UBL/CII File"
msgstr "UBL/CII 檔案"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9959
msgid "USA EIN"
msgstr "美國僱主識別號碼 EIN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr "BIS 賬單 3.0 及其衍生產品使用的唯一標識符，又稱為「終端點識別碼」。"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9932
msgid "United Kingdom VAT"
msgstr "英國增值稅"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9953
msgid "Vatican VAT"
msgstr "梵蒂岡增值稅"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "View Partner(s)"
msgstr "檢視合作夥伴"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"When the Canary Island General Indirect Tax (IGIC) applies, the tax rate on "
"each invoice line should be greater than 0."
msgstr "若加那利群島一般間接稅(IGIC)適用，每個發票資料行的稅率應大於 0。"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__aq
msgid "X.400 address for mail text"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move.py:0
msgid "XML UBL"
msgstr "XML UBL"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__xrechnung
msgid "XRechnung CIUS"
msgstr "XRechnung CIUS"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"You should include at least one tax per invoice line. [BR-CO-04]-Each "
"Invoice line (BG-25) shall be categorized with an Invoiced item VAT category"
" code (BT-151)."
msgstr ""
"每個發票資料行應至少包含一項稅費。[BR-CO-04] - 每個發票行 (BG-25) 應依照已開立發票項目的增值稅類別代碼 (BT-151) "
"進行分類。"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Your endpoint"
msgstr "你的終端點"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__invoice_edi_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__invoice_edi_format
msgid "eInvoice format"
msgstr "電子發票格式"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr "factur-x.xml"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr "fx"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
