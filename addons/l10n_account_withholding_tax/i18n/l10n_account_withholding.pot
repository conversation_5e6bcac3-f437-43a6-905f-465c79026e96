# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_account_withholding
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.4a1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-12 02:38+0000\n"
"PO-Revision-Date: 2025-06-12 02:38+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/product_template.py:0
msgid "%(amount)s Excl. Taxes"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/product_template.py:0
msgid "%(amount)s Incl. Taxes"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/product_template.py:0
msgid "%(amount)s Tax Withheld"
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.report_payment_receipt_document
msgid "<span>Amount</span>"
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.report_payment_receipt_document
msgid "<span>Base</span>"
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.report_payment_receipt_document
msgid "<span>Tax</span>"
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.report_payment_receipt_document
msgid "<span>Withholding number</span>"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__account_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__account_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__account_id
msgid "Account"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/wizards/account_payment_register_withholding_line.py:0
msgid "All withholding lines in self must have the same payment register."
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_payment_withholding_line.py:0
msgid "All withholding lines in self must have the same payment."
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__analytic_distribution
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__analytic_distribution
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__analytic_distribution
msgid "Analytic Distribution"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__analytic_precision
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__analytic_precision
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__comodel_currency_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__comodel_currency_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__comodel_currency_id
msgid "Comodel Currency"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__comodel_date
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__comodel_date
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__comodel_date
msgid "Comodel Date"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__comodel_payment_type
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__comodel_payment_type
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__comodel_payment_type
msgid "Comodel Payment Type"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__comodel_percentage_paid_factor
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__comodel_percentage_paid_factor
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__comodel_percentage_paid_factor
msgid "Comodel Percentage Paid Factor"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__company_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__company_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__company_id
msgid "Company"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__create_uid
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__create_date
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__create_date
msgid "Created on"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__comodel_company_currency_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__comodel_company_currency_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__comodel_company_currency_id
msgid "Currency"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__withholding_default_account_id
msgid "Default Account"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_product_template__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_res_config_settings__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__display_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__display_withholding
msgid "Display Withholding"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__placeholder_type__given_by_name
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__previous_placeholder_type__given_by_name
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__placeholder_type__given_by_name
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__previous_placeholder_type__given_by_name
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__placeholder_type__given_by_name
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__previous_placeholder_type__given_by_name
msgid "Given By the Name"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__placeholder_type__given_by_sequence
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__previous_placeholder_type__given_by_sequence
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__placeholder_type__given_by_sequence
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__previous_placeholder_type__given_by_sequence
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__placeholder_type__given_by_sequence
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__previous_placeholder_type__given_by_sequence
msgid "Given By the Sequence"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_product_template__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_res_company__id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_res_config_settings__id
msgid "ID"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,help:l10n_account_withholding.field_account_tax__is_withholding_tax_on_payment
msgid ""
"If enabled, this tax will not affect your accounts until the registration of"
" payments."
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__write_uid
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__write_date
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__withholding_net_amount
msgid "Net Amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,help:l10n_account_withholding.field_account_payment_register__withholding_net_amount
msgid "Net amount after deducting the withholding lines"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__placeholder_type__not_defined
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__previous_placeholder_type__not_defined
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__placeholder_type__not_defined
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__previous_placeholder_type__not_defined
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__placeholder_type__not_defined
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__previous_placeholder_type__not_defined
msgid "Not defined"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__original_base_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__original_base_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__original_base_amount
msgid "Original Base Amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__original_tax_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__original_tax_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__original_tax_amount
msgid "Original Tax Amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__outstanding_account_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__withholding_outstanding_account_id
msgid "Outstanding Account"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_account_payment_register
msgid "Pay"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__payment_id
msgid "Payment"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__withholding_payment_account_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__withholding_payment_account_id
msgid "Payment Account"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__payment_register_id
msgid "Payment Register"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_account_payment_register_withholding_line
msgid "Payment register withholding line"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_account_payment_withholding_line
msgid "Payment withholding line"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_account_payment
msgid "Payments"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__placeholder_type
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__placeholder_type
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__placeholder_type
msgid "Placeholder Type"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__placeholder_value
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__placeholder_value
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__placeholder_value
msgid "Placeholder Value"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_withholding_line.py:0
msgid "Please enter the withholding number for the tax %(tax_name)s"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,help:l10n_account_withholding.field_account_payment_register_withholding_line__placeholder_value
#: model:ir.model.fields,help:l10n_account_withholding.field_account_payment_withholding_line__placeholder_value
#: model:ir.model.fields,help:l10n_account_withholding.field_account_withholding_line__placeholder_value
msgid "Populated by the comodel during edition of the line."
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__previous_placeholder_type
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__previous_placeholder_type
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__previous_placeholder_type
msgid "Previous Placeholder Type"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_product_template
msgid "Product"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__comodel_payment_type__inbound
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__comodel_payment_type__inbound
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__comodel_payment_type__inbound
msgid "Receive Money"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_register_withholding_line__comodel_payment_type__outbound
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_payment_withholding_line__comodel_payment_type__outbound
#: model:ir.model.fields.selection,name:l10n_account_withholding.selection__account_withholding_line__comodel_payment_type__outbound
msgid "Send Money"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__name
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__name
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.view_account_payment_form
msgid "Sequence Number"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__source_base_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__source_base_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__source_base_amount
msgid "Source Base Amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__source_base_amount_currency
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__source_base_amount_currency
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__source_base_amount_currency
msgid "Source Base Amount Currency"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__source_currency_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__source_currency_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__source_currency_id
msgid "Source Currency"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__source_tax_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__source_tax_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__source_tax_id
msgid "Source Tax"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__source_tax_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__source_tax_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__source_tax_amount
msgid "Source Tax Amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__source_tax_amount_currency
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__source_tax_amount_currency
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__source_tax_amount_currency
msgid "Source Tax Amount Currency"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_account_tax
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__tax_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__tax_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__tax_id
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.view_account_payment_form
msgid "Tax"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_withholding_line.py:0
msgid "The account \"%(account_name)s\" is not valid to use on withholding lines."
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_withholding_line.py:0
msgid "The base amount of a withholding tax line must be above 0."
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/wizards/account_payment_register.py:0
msgid "The withholding net amount cannot be negative."
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,help:l10n_account_withholding.field_res_company__withholding_tax_base_account_id
#: model:ir.model.fields,help:l10n_account_withholding.field_res_config_settings__withholding_tax_base_account_id
msgid "This account will be set on withholding tax base lines."
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,help:l10n_account_withholding.field_account_payment_register_withholding_line__withholding_sequence_id
#: model:ir.model.fields,help:l10n_account_withholding.field_account_payment_withholding_line__withholding_sequence_id
#: model:ir.model.fields,help:l10n_account_withholding.field_account_tax__withholding_sequence_id
#: model:ir.model.fields,help:l10n_account_withholding.field_account_withholding_line__withholding_sequence_id
msgid ""
"This sequence will be used to generate default numbers on payment "
"withholding lines."
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.view_account_payment_register_form
msgid "Total Withheld Amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__type_tax_use
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__type_tax_use
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__type_tax_use
msgid "Type Tax Use"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_withholding_line.py:0
msgid "WH Base Counterpart: %(names)s"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_withholding_line.py:0
msgid "WH Base: %(names)s"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_withholding_line.py:0
msgid "WH Tax: %(name)s"
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.view_account_payment_form
msgid "Withheld Amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_tax__is_withholding_tax_on_payment
msgid "Withhold On Payment"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__should_withhold_tax
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__should_withhold_tax
msgid "Withhold Tax Amounts"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,help:l10n_account_withholding.field_account_payment__should_withhold_tax
msgid "Withhold tax amounts from the payment amount."
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.view_account_payment_register_form
msgid "Withholding"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__withholding_hide_tax_base_account
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__withholding_hide_tax_base_account
msgid "Withholding Hide Tax Base Account"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment__withholding_line_ids
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register__withholding_line_ids
msgid "Withholding Lines"
msgstr ""

#. module: l10n_account_withholding
#. odoo-python
#: code:addons/l10n_account_withholding/models/account_tax.py:0
msgid ""
"Withholding On Payment taxes cannot use the 'Group of Taxes' or the "
"'Percentage Tax Included' computations."
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__withholding_sequence_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__withholding_sequence_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_tax__withholding_sequence_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__withholding_sequence_id
msgid "Withholding Sequence"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_res_company__withholding_tax_base_account_id
#: model:ir.model.fields,field_description:l10n_account_withholding.field_res_config_settings__withholding_tax_base_account_id
msgid "Withholding Tax Base"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__amount
msgid "Withholding amount"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_register_withholding_line__base_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_payment_withholding_line__base_amount
#: model:ir.model.fields,field_description:l10n_account_withholding.field_account_withholding_line__base_amount
msgid "Withholding base"
msgstr ""

#. module: l10n_account_withholding
#: model_terms:ir.ui.view,arch_db:l10n_account_withholding.res_config_settings_form
msgid "Withholding:"
msgstr ""

#. module: l10n_account_withholding
#: model:ir.model,name:l10n_account_withholding.model_account_withholding_line
msgid "withholding line"
msgstr ""
