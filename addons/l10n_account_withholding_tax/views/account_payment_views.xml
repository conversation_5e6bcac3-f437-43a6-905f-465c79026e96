<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_payment_form" model="ir.ui.view">
        <field name="name">account.payment.form</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <field name="memo" position="after">
                <field name="should_withhold_tax" invisible="not display_withholding" readonly="state != 'draft'"/>
            </field>
            <notebook name="payment_notebook" position="inside">
                <page string="Withholding" invisible="not should_withhold_tax">
                    <group>
                        <field name="withholding_line_ids" nolabel="1" colspan="2" readonly="state != 'draft'">
                            <list editable="bottom" no_open="1">
                                <field name="source_base_amount_currency" column_invisible="True"/> <!-- Needed for taxes computation -->
                                <field name="source_base_amount" column_invisible="True"/> <!-- Needed for taxes computation -->
                                <field name="source_tax_amount_currency" column_invisible="True"/> <!-- Needed for taxes computation -->
                                <field name="source_tax_amount" column_invisible="True"/> <!-- Needed for taxes computation -->
                                <field name="source_currency_id" column_invisible="True"/> <!-- Needed for taxes computation -->
                                <field name="comodel_currency_id" column_invisible="True"/> <!-- Needed for taxes computation -->
                                <field name="placeholder_value" column_invisible="True"/> <!-- Needed for the placeholder widget -->
                                <field name="name"
                                       string="Sequence Number"
                                       required="not withholding_sequence_id"
                                       widget="char_with_placeholder_field"
                                       options="{'placeholder_field': 'placeholder_value'}"
                                       width="92px"/>
                                <field name="account_id"
                                       column_invisible="parent.withholding_hide_tax_base_account"
                                       width="184px"
                                       optional="show"/>
                                <field name="base_amount"/>
                                <field name="tax_id"
                                       options="{'no_open': True, 'no_create': True}"
                                       string="Tax"
                                       width="92px"/>
                                <field name="analytic_distribution"
                                       widget="analytic_distribution"
                                       groups="analytic.group_analytic_accounting"/>
                                <field name="amount" string="Withheld Amount" sum="Total Withheld Amount"/>
                            </list>
                        </field>
                    </group>
                    <group>
                        <group>
                            <field name="outstanding_account_id"
                                   required="should_withhold_tax and withholding_line_ids and not withholding_payment_account_id"
                                   invisible="(not should_withhold_tax) or withholding_payment_account_id or not display_withholding"
                                   readonly="state != 'draft'"/>
                        </group>
                    </group>
                </page>
            </notebook>
        </field>
    </record>
</odoo>
