<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_tax_form" model="ir.ui.view">
        <field name="name">account.tax.form</field>
        <field name="model">account.tax</field>
        <field name="inherit_id" ref="account.view_tax_form"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet/group//group[last()]" position="inside">
                <field name="is_withholding_tax_on_payment" invisible="type_tax_use not in ['sale', 'purchase'] or amount >= 0"/>
            </xpath>
            <field name="hide_tax_exigibility" position="after">
                <field name="withholding_sequence_id" context="{'default_name': name}" invisible="type_tax_use == 'sale' or not is_withholding_tax_on_payment"/>
            </field>
            <field name="tax_exigibility" position="attributes">
                <attribute name="invisible" add="is_withholding_tax_on_payment" separator=" or "/>
            </field>
            <field name="price_include_override" position="attributes">
                <attribute name="invisible" add="is_withholding_tax_on_payment" separator=" or "/>
            </field>
            <field name="cash_basis_transition_account_id" position="attributes">
                <attribute name="invisible" add="is_withholding_tax_on_payment" separator=" or "/>
            </field>
            <field name="is_base_affected" position="attributes">
                <attribute name="invisible" add="not is_withholding_tax_on_payment" separator=" and "/>
            </field>
        </field>
    </record>
</odoo>
