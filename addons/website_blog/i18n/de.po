# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr "#{year}"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "%s (copy)"
msgstr "%s (<PERSON><PERSON>)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "“-Kopfzeile der Seite."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "“. Es werden Ergebnisse angezeigt für „"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- Alle Daten"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>Ferngläser sind leicht und gut transportfähig.</b> Wenn Sie nicht den "
"Luxus haben, eine Sternwarte von Ihrer Terrasse aus einzurichten und zu "
"betreiben, werden Sie wahrscheinlich reisen, um Ihre Beobachtungen "
"durchzuführen. Ein Fernglas lässt sich viel einfacher mitnehmen und ist "
"leichter zu transportieren als ein sperriges Teleskop."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>Holen Sie sich das Wissen der Experten</b>. Wenn Sie nicht bereits in "
"einem Astronomieverein oder -club aktiv sind, werden die Verkäufer im "
"Teleskopladen in der Lage sein, Sie zu den aktiven Vereinen in Ihrer Gegend "
"zu führen. Sobald Sie Verbindungen zu Leuten haben, die Teleskope gekauft "
"haben, können Sie Ratschläge darüber bekommen, was funktioniert und was zu "
"vermeiden ist, die stichhaltiger sind als alles, was Sie von einem Web-"
"Artikel oder einem Verkäufer bei Wal-Mart bekommen werden."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr ""
"<b>Veröffentlichen Sie Ihren Blogbeitrag,</b> damit er für Ihre Besucher "
"sichtbar wird."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Anmelden</b>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>Erst ausprobieren, dann kaufen.</b> Dies ist ein weiterer Vorteil von "
"Exkursionen mit dem Astronomieverein. Sie können ein paar schöne Stunden mit"
" Leuten verbringen, die sich mit Teleskopen auskennen und ihre Geräte "
"aufgebaut haben, um ihre Ausrüstung zu testen, die wichtigsten technischen "
"Aspekte kennenzulernen und sie auszuprobieren, bevor Sie Geld in Ihre eigene"
" Ausrüstung stecken."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Schreiben Sie hier Ihre Geschichte.</b> Mit der Symbolleiste oben "
"gestalten Sie Ihren Text stilvoll: Fügen Sie ein Bild oder eine Tabelle "
"hinzu, aktivieren Sie Fett- oder Kursivdruck und mehr. Bewegen Sie Bausteine"
" nach Wunsch, um grafisch schöne Blogbeiträge zu erhalten."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">Neben der einheimischen Bevölkerung ist auch die "
"lokale Tierwelt ein großer Publikumsmagnet.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">Es ist von entscheidender Bedeutung, dass Sie genau "
"das richtige Teleskop bekommen.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr ""
"<em class=\"h4 my-0\">Dieser „Wow“-Moment ist es, worum es in der Astrologie"
" geht.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">Je mehr Rezensionen man liest, desto mehr fällt auf, "
"dass sie meist an den äußersten Extremen der Meinungen angesiedelt "
"sind.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">Der Kosmos hat etwas Zeitloses an sich.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Ihr Studium des Mondes kann, wie alles andere auch, "
"sehr einfach bis sehr komplex sein.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>Keine Stichwörter definiert</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Nächsten "
"Beitrag lesen</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"me-1\">Show:</span>"
msgstr "<span class=\"me-1\">Anzeigen:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled ps-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled ps-0\">Blogs:</span>"

#. module: website_blog
#: model_terms:web_tour.tour,rainbow_man_message:website_blog.blog
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Gut gemacht!</b> Sie haben alle Schritte dieser Tour "
"absolviert.</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "Eine großartige Möglichkeit, versteckte Orte zu entdecken"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"Ein Urlaub im Copper Canyon verspricht eine aufregende Mischung aus "
"Entspannung, Kultur, Geschichte, Tierwelt und Wandern."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "Ein neuer Beitrag"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"Ein Reisender kann wählen, ob er das Gebiet erkunden möchte, indem er um den"
" Canyon herumwandert oder sich in ihn hineinwagt. Für diejenigen, die sich "
"in die Tiefen des Canyons wagen wollen, ist eine detaillierte Planung "
"erforderlich. Es gibt eine Reihe von Reiseunternehmen, die sich auf die "
"Organisation von Touren in diese Region spezialisiert haben. Besucher können"
" mit einem Touristenvisum, das für 180 Tage gültig ist, zum Copper Canyon "
"fliegen. Reisende können auch von überall in den Vereinigten Staaten aus "
"fahren und ein Visum an der mexikanischen Zollstation an der Grenze "
"erwerben."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "Über uns"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
" <b>Kontakt</b> mit einem <b>seriösen Teleskopgeschäft</b> ist äußert "
"wichtig. Achten Sie darauf, dass die Mitarbeiter vom Fach sind. Wenn Sie Ihr"
" Teleskop nicht im Fachhandel kaufen, riskieren Sie, falsch beraten zu "
"werden."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Auf Beitrag zugreifen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Aktiv"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Einige hinzufügen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Alle"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Alle Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Alle Blogs"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Alone in the ocean"
msgstr "Allein im Ozean"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "Wie schwierig ist der Auf- und Abbau?"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/website_blog.js:0
msgid "Amazing blog article: %(title)s! Check it live: %(url)s"
msgstr "Erstaunlicher Blogartikel: %(title)s! Jetzt live ansehen: %(url)s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Eine spannende Mischung aus Entspannung, Kultur, Geschichte, Wildnis und "
"Wandern."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"Wenn Sie alles geklärt haben, können Sie Ihre eigenen<b> Anschaffungen "
"machen</b>. Die Suche nach immer besseren und neueren Teleskopen wird Sie "
"ein Leben lang begleiten. Lassen Sie sich von der Astronomie verzaubern, die"
" Erfahrung wird jeden Aspekt Ihres Lebens bereichern. Es wird eine "
"Leidenschaft sein, die Sie nie wieder aufgeben wollen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"Der Copper Canyon ist auch die Heimat der Tarahumara. Dieses halbnomadische "
"Volk lebt in Höhlenwohnungen. Ihr Lebensunterhalt wird hauptsächlich durch "
"Landwirtschaft und Viehzucht bestritten."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archive"
msgstr "Archiv"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Archiviert"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archives"
msgstr "Archive"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "Artikel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "Artikel"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Astronomie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"Astronomie-Clubs sind lebendige Orte voller kenntnisreicher Amateure, die "
"ihr Wissen gerne teilen. Für den Preis einer Cola und Snacks gehen sie mit "
"Ihnen auf Sternenbeobachtung und beeindrucken Sie mit Funfacts und großem "
"Wissensreichtum."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "Astronomie ist „Sternguckerei“"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom Feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Name des Autors"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Avatar"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Awesome hotel rooms"
msgstr "Großartige Hotelzimmer"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Achtung hier kommt eine spannende Sache namens „Astronomie“"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Wenn Sie Teil der Gesellschaft engagierter Amateurastronomen werden, "
"erhalten Sie Zugang zu diesen organisierten Bemühungen, neue Ebenen in "
"unserer Fähigkeit zu erreichen, den Mond der Erde zu studieren. Und es wird "
"Ihnen Gleichgesinnte und Freunde geben, die Ihre Leidenschaft für die "
"Astronomie teilen und die ihre Erfahrungen und Fachgebiete mit Ihnen teilen "
"können, während Sie versuchen, herauszufinden, wohin Sie als Nächstes am "
"riesigen Nachthimmel, auf dem Mond und darüber hinaus auf Ihrer Suche nach "
"Wissen über das scheinbar endlose Universum über uns schauen könnten."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Werden Sie Teil der Gesellschaft der engagierten Amateurastronomen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Ausstattung der Schlafzimmer"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Bevor Sie so viel Geld ausgeben, ist es vielleicht besser, in ein gutes "
"Fernglas zu investieren, als in das bloße Auge. Es gibt sogar Ferngläser, "
"die für die Sternenbeobachtung geeignet sind und Ihnen die zusätzliche Sicht"
" bieten, die Sie brauchen, um die Wunder des Universums ein wenig besser zu "
"sehen. Ein gut durchdachtes Fernglas gibt Ihnen auch viel mehr Mobilität und"
" die Möglichkeit, Ihre „verbesserte Sicht“ immer zur Hand zu haben, wenn "
"sich Ihnen diese erstaunliche Aussicht gerade bietet."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Bevor Sie Ihren ersten Kauf tätigen ..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Mehr als das Auge sehen kann"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Blogbezeichnung"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blog Page"
msgstr "Blogseite"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog Post"
msgstr "Blogbeitrag"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Titel des Blogbeitrags"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
msgid "Blog Post Pages"
msgstr "Seiten der Blogbeiträge"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Überschrift des Blogbeitrags"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_post_pages
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blog Posts"
msgstr "Blogbeiträge"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Blog-Untertitel"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Blog-Stichwort"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Blog-Stichwortkategorie"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Blog-Stichwörter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Blog-Titel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Titel des Blogs"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blogs"
msgstr "Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs List"
msgstr "Liste der Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs Page"
msgstr "Seite der Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Bottom"
msgstr "Unten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Breadcrumb"
msgstr "Brotkrümel"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Aber wie soll man die riesige Auswahl an Angeboten durchschauen? Und, was "
"noch wichtiger ist, können Sie den Fotos und Beschreibungen der Hotels "
"wirklich vertrauen, die mit dem Ziel ausgewählt wurden, möglichst viele "
"Buchungen zu erhalten? Bewertungen von Reisenden können hilfreich sein, aber"
" seien Sie vorsichtig. Sie sind oft voreingenommen, manchmal veraltet und "
"dienen möglicherweise gar nicht Ihren Interessen. Woher wissen Sie, dass die"
" Kriterien des Rezensenten auch für Sie von Bedeutung sind?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Ein Teleskop kaufen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Der Kauf des richtigen Teleskops, um Ihre Liebe zur Astronomie auf die "
"nächste Stufe zu heben, ist ein wichtiger nächster Schritt in der "
"Entwicklung Ihrer Leidenschaft für die Sterne."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Kann veröffentlichen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cards"
msgstr "Karten"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Kategorie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Einrichtungen für Kinder"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Choose an image from the library."
msgstr "Wählen Sie ein Bild aus der Bibliothek."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Click here to add new content to your website."
msgstr "Klicken Sie hier, um neue Inhalte zu Ihrer Website hinzuzufügen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"Klicken Sie oben rechts auf<b>„Neu“</b>, um Ihren ersten Blogbeitrag zu "
"schreiben."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Schließen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__color
msgid "Color"
msgstr "Farbe"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Kommentieren"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments"
msgstr "Kommentare"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments/Views Stats"
msgstr "Statistiken zu Kommentaren/Aufrufen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Inhalt"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"Der Copper Canyon ist eine der sechs Schluchten in diesem Gebiet. Obwohl der"
" Name nahelegt, dass die Schlucht einen Bezug zum Kupferabbau haben könnte, "
"ist dies nicht der Fall. Der Name leitet sich von den kupferfarbenen und "
"grünen Flechten ab, die die Schlucht bedecken. Der Copper Canyon hat zwei "
"Klimazonen. Oben herrscht ein alpines Klima, unten ein subtropisches Klima. "
"Die Winter sind kalt mit häufigen Schneestürmen in den höheren Lagen. Die "
"Sommer sind trocken und heiß. Die Hauptstadt Chihuahua ist eine hoch "
"gelegene Wüste, in der das Wetter von kalten Wintern bis zu heißen Sommern "
"reicht. Die Region ist aufgrund der verschiedenen Ökosysteme, die in ihr "
"existieren, einzigartig."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cover"
msgstr "Titelbild"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Titeleigenschaften"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Date"
msgstr "Datum"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Datum (neu zu alt)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Datum (alt zu neu)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Beschreibung"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Dexter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "East Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"Helikopterrundflüge durch Ost-Maui geben Ihnen einen Blick auf den "
"zehntausend Fuß hohen Vulkan, Haleakala oder Haus der Sonne. Dieser Vulkan "
"ist ruhend und brach das letzte Mal im Jahr 1790 aus. Bei Maui-"
"Helikopterrundflügen werden Sie in der Lage sein, den Krater des Vulkans und"
" die trockene, karge Erde zu sehen, die die Südseite des Vulkanhangs umgibt."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Im Backend bearbeiten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Bearbeiten Sie die „"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Bearbeiten Sie die Kopfzeile der Seite „Alle Blogs“."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Bearbeiten Sie die Kopfzeile der Seite „Filterergebnisse“."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Edit your title, the subtitle is optional."
msgstr "Bearbeiten Sie Ihren Titel, der Untertitel ist optional."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Enter your post's title"
msgstr "Geben Sie den Titel Ihres Beitrags ein"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Dinge, die Sie bedenken sollten."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"Schließlich und vor allem sollte das Inspektionsteam des Qualitäts-"
"Hotelverzeichnisses das betreffende Hotel regelmäßig besucht, das Personal "
"getroffen, in einem Zimmer geschlafen und das Essen probiert haben. Sie "
"sollten das Hotel so erleben, wie es nur ein Hotelgast kann, und erst dann "
"sind sie wirklich in einer starken Position, um über das Hotel zu schreiben."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Follow Us"
msgstr "Folgen Sie uns"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"Für viele von uns, die in der Stadt leben, ist der Himmel dort oben nicht "
"wirklich sichtbar. Die Lichter der Stadt leisten gute Arbeit, um das "
"erstaunliche Schauspiel zu verschleiern, das sich die ganze Zeit über "
"unseren Köpfen abspielt."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"Für viele von uns beginnt die allererste Erfahrung, etwas über die "
"Himmelskörper zu lernen, als wir unseren ersten Vollmond am Himmel sahen. Es"
" ist wirklich ein großartiger Anblick, sogar für das bloße Auge."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"Vom kleinsten Baby bis zum fortgeschrittenen Astrophysiker ist für jeden "
"etwas dabei, der Spaß an der Astronomie haben möchte. In der Tat ist es eine"
" Wissenschaft, die so zugänglich ist, dass praktisch jeder sie überall "
"betreiben kann, wo er gerade ist. Alles, was sie wissen müssen, ist, nach "
"oben zu schauen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Full-Width"
msgstr "Volle Breite"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Sprechen Sie mit einem Experten"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Kaufen Sie sich ein Teleskop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Tauchen Sie in die Geschichte ein"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Loslegen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Grid"
msgstr "Gitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "Hier sind einige der wichtigsten Fakten, die Sie beachten sollten:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Urlaubstipps"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Hover Effect"
msgstr "Effekt bei Herüberstreifen"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Wie Sie nachschlagen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"Wie komplex ist das Teleskop und werden Sie Probleme mit der Wartung haben? "
"Vernetzen Sie sich, um die Antworten auf diese und andere Fragen zu "
"erhalten. Wenn Sie Ihre Hausaufgaben auf diese Weise machen, werden Sie "
"genau das richtige Teleskop für diesen nächsten großen Schritt in der "
"Entwicklung Ihrer Leidenschaft für die Astronomie finden."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "Wie mobil muss Ihr Teleskop sein?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "How to choose the right hotel"
msgstr "So wählen Sie das richtige Hotel aus"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Wenn es darauf ankommt, dass Ihr Hotel z. B. am Strand, in der Nähe des "
"Freizeitparks oder in der Nähe des Flughafens liegt, dann ist die Lage das A"
" und O. Jedes anständige Verzeichnis sollte einen Lageplan des Hotels und "
"seiner Umgebung anbieten. Es sollten sowohl Entfernungsangaben zum Flughafen"
" als auch eine Art interaktive Karte angeboten werden."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"Wenn die Nacht klar ist, können Sie erstaunliche Details der Mondoberfläche sehen, wenn Sie nur die Sterne in Ihrem Hinterhof betrachten.\n"
"Natürlich, wie Sie in Ihrer Liebe zur Astronomie wachsen, werden Sie viele Himmelskörper faszinierend finden. Aber der Mond wird vielleicht immer unsere erste Liebe sein, weil er das einzige weit entfernte Weltraumobjekt ist, das die einzigartige Auszeichnung hat, nahe an der Erde zu fliegen und auf dem der Mensch gelaufen ist."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
msgid "In"
msgstr "In"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"In vielerlei Hinsicht ist es ein großer Schritt von jemandem, der nur mit "
"der Astronomie herumalbert, zu einem ernsthaften Studenten der Wissenschaft."
" Aber Sie und ich wissen beide, dass es nach dem Kauf eines Teleskops noch "
"einen weiteren großen Schritt gibt, bevor man wirklich weiß, wie man es "
"benutzt."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Increase Readability"
msgstr "Lesbarkeit erhöhen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
msgid "Is Published"
msgstr "Ist veröffentlicht"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Islands"
msgstr "Inseln"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"Es macht großen Spaß, sich mit den Sternbildern zu beschäftigen, zu lernen, "
"wie man den Nachthimmel erkundet und die Planeten und bekannten Sterne "
"findet. Es gibt unzählige Websites und Bücher, die dabei helfen können."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"Es ist wichtig, ein Hotel zu wählen, in dem Sie sich wohlfühlen – moderne "
"oder traditionelle Einrichtung, lokales Dekor oder international, formell "
"oder entspannt. Das ideale Hotelverzeichnis sollte Sie über die verfügbaren "
"Optionen informieren."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"Es ist sicher, dass jeder von uns irgendwann in seinem Leben diesen Moment "
"hat, in dem wir plötzlich fassungslos sind, wenn wir der Unermesslichkeit "
"des Universums, das wir am Nachthimmel sehen, gegenüberstehen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"Es ist wirklich erstaunlich, wenn man darüber nachdenkt, dass man, wenn man "
"in einer beliebigen Nacht nach oben schaut, praktisch Hunderttausende von "
"Sternen, Sternensystemen, Planeten, Monden, Asteroiden, Kometen und "
"vielleicht sogar ein gelegentliches Spaceshuttle vorbeiziehen sehen kann. Es"
" ist sogar noch atemberaubender, wenn Sie erkennen, dass der Himmel, zu dem "
"Sie aufschauen, im Grunde genommen genau derselbe Himmel ist, den unsere "
"Vorfahren vor Hunderten und Tausenden von Jahren genossen haben, als sie "
"einfach nach oben schauten."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Jungle"
msgstr "Dschungel"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Wissen, was Sie sich ansehen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Wissen, wann Sie schauen sollten"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Large"
msgstr "Groß"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Letzter Beitragender"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Neueste Blogbeiträge"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Layout"
msgstr "Layout"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Wenn Sie die Hintergründe der großen Entdeckungen in der Astronomie kennen, "
"wird Ihr Blick in die Sterne noch bedeutungsvoller werden. Die Astronomie "
"ist eine der ältesten Wissenschaften der Welt. Informieren Sie sich also "
"über die großen Persönlichkeiten der Geschichte, die vor Ihnen die Sterne "
"betrachtet haben."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Freizeiteinrichtungen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "List"
msgstr "Liste"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"Lokale Atmosphäre ist toll, aber die hoteleigenen Restaurants und Bars "
"können eine wichtige Rolle für Ihren Aufenthalt spielen. Sie sollten auf die"
" Auswahl, den Stil und darauf achten, ob sie schick oder informell sind. Ein"
" guter Hotelbericht sollte Ihnen das sagen, und besonders über die "
"Frühstücksmöglichkeiten."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Ort"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Maui helicopter tours"
msgstr "Maui-Helikopterrundflüge"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"Maui-Helikopterrundflüge sind eine tolle Möglichkeit, die Insel aus einer "
"anderen Perspektive zu sehen und ein lustiges Abenteuer zu erleben. Wenn Sie"
" noch nie in einem Hubschrauber gesessen haben, ist dies ein großartiger "
"Ort, um es zu tun."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"Maui-Helikopterrundflüge sind eine großartige Möglichkeit, die Orte zu "
"besichtigen, die nicht zu Fuß oder mit dem Auto erreicht werden können. Die "
"Touren dauern etwa eine Stunde und reichen von etwa einhundertfünfundachtzig"
" bis zweihundertvierzig Dollar pro Person. Für viele ist dies eine einmalige"
" Gelegenheit, Naturlandschaften zu sehen, die es so nie wieder geben wird. "
"Die Mitnahme von Kameras und Videos, um die Momente festzuhalten, ermöglicht"
" es Ihnen auch, die Tour immer wieder zu erleben, wenn Sie im Laufe der "
"Jahre in Erinnerungen schwelgen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"Maui-Helikopterrundflüge ermöglichen es Ihnen, all diese Sehenswürdigkeiten "
"zu sehen. Denken Sie daran, eine Kamera mitzunehmen, wenn Sie eine Maui-"
"Hubschraubertour machen, um die Schönheit der Landschaft einzufangen und "
"Freunden und Familie zu Hause all die wunderbaren Dinge zu zeigen, die Sie "
"im Urlaub gesehen haben."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Medium"
msgstr "Medium"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Meta-Beschreibung"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Meta-Schlagwörter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Meta-Titel"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Molokai-Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"Helikopterrundflüge durch Molokai-Maui bringen Sie auf eine andere Insel, aber eine, die nur neun Meilen entfernt und leicht mit dem Flugzeug erreichbar ist. Diese Insel hat eine sehr kleine Bevölkerung mit einer anderen Kultur und Landschaft. Die gesamte Küste im Nordosten ist von Klippen und abgelegenen Stränden gesäumt. Sie sind mit keinem anderen Transportmittel als dem Flugzeug zu erreichen.\n"
"Die Menschen, die auf der Insel leben, haben diese bemerkenswerte Landschaft noch nie gesehen, es sei denn, sie haben Maui-Helikopterrundflüge unternommen, um sie zu sehen. Wenn das Wetter regnerisch war und es viel Niederschlag für die Saison gibt, werden Sie viele erstaunliche Wasserfälle sehen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"Wichtiger für den Familienreisenden als für den Geschäftsreisenden ist, dass"
" Sie aus dem Verzeichnis herausfinden, wie kinderfreundlich das Hotel ist, "
"und von dort aus Ihre Entscheidung treffen. Eine Sache, nach der es sich "
"lohnt zu suchen, ist, ob das Hotel einen Babysitter-Service anbietet. Für "
"den Geschäftsreisenden, der Kindern entfliehen möchte, ist dies natürlich "
"auch sehr relevant – vielleicht wäre ein nicht kinderfreundliches Hotel eher"
" etwas für ihn!"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "Meistgesehene Blogbeiträge"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Name"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.blog_post_action_add
msgid "New Blog Post"
msgstr "Neuer Blogbeitrag"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Next Article"
msgstr "Nächster Artikel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "No Cover"
msgstr "Kein Titelbild"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Noch kein Blogbeitrag vorhanden."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "Anzahl der Ansichten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Keine Treffer für „%s“."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "Kein Treffer gefunden für „"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Noch keine Stichwörter definiert."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Keine"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Nichts davon hindert Sie daran, mit Ihren Plänen fortzufahren und ein "
"großartiges Teleskopsystem zusammenzustellen. Stellen Sie nur sicher, dass "
"Sie eine hochwertige Beratung und Schulung erhalten, wie Sie Ihr Teleskop "
"Ihren Bedürfnissen entsprechend konfigurieren. Wenn Sie diese Richtlinien "
"befolgen, werden Sie stundenlang die phänomenalen Anblicke des Nachthimmels "
"genießen können, die mit dem bloßen Auge nicht zu erkennen sind."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal"
msgstr "Normal"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal picture"
msgstr "Normales Bild"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Not Published"
msgstr "Nicht veröffentlicht"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"Nicht nur die Kenntnis des Wetters wird dafür sorgen, dass Ihre "
"Sternenbeobachtung lohnend ist, sondern wenn Sie erfahren, wann die großen "
"Meteoritenschauer und andere große astronomische Ereignisse stattfinden, "
"wird die Aufregung der Astronomie für Sie lebendig."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"Um Ihre Mondanbetung auf die Spitze zu treiben, sollten Sie Ihre Ausrüstung "
"auf ein gutes Einsteigerteleskop aufrüsten, das Ihnen die beeindruckendsten "
"Details der Mondoberfläche zeigt. Mit jedem dieser Upgrades wird sich Ihr "
"Wissen und die Tiefe und der Umfang dessen, was Sie sehen können, "
"geometrisch verbessern. Für viele Amateurastronomen können wir manchmal gar "
"nicht genug bekommen von dem, was wir auf diesem unserem nächsten "
"Weltraumobjekt sehen können."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"Once you have reviewed the content on mobile, you can switch back to the "
"normal view by clicking here again"
msgstr ""
"Sobald Sie den Inhalt auf dem Handy angesehen haben, können Sie zur normalen"
" Ansicht zurückkehren, indem Sie erneut hier klicken"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Andere"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Our Latest Posts"
msgstr "Unsere neuesten Beiträge"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "Unsere Blogs"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Foto: Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Foto: Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Foto: Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Foto: Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Foto: Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Foto: Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Foto: Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Foto: Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Foto: PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Foto: SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Foto: Teddy Kelley, @teddykelley"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Picture size"
msgstr "Bildgröße"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Beiträge"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Posts List"
msgstr "Beitragsliste"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Veröffentlichungsdatum"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Published"
msgstr "Veröffentlicht"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Veröffentlicht ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Veröffentlichungsdatum"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Veröffentlichte Beiträge"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Veröffentlichungsoptionen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Veröffentlichungsdatum"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__rating_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr "Mehr lesen <i class=\"oi oi-chevron-right ms-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Restaurants, Cafés und Bars"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict to a specific website."
msgstr "Auf eine bestimmte Website beschränken."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO-optimiert"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Beispiel"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Satellites"
msgstr "Satelliten"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Search for an image. (eg: type \"business\")"
msgstr "Suchen Sie nach einem Bild. (z. B.: Geben Sie „Geschäft“ ein)"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seaside vs mountain side"
msgstr "Küste vs. Berglandschaft"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seeing the world from above"
msgstr "Die Welt von oben sehen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
msgid "Select Blog"
msgstr "Blog wählen"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select the blog you want to add the post to."
msgstr "Wählen Sie den Blog aus, dem Sie den Beitrag hinzufügen möchten."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select this menu item to create a new blog post."
msgstr "Wählen Sie dieses Menü zur Erstellung eines neuen Blogbeitrags."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Comment"
msgstr "Zum Kommentieren auswählen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Tweet"
msgstr "Zum Tweeten auswählen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "Seo-Name"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Trennen Sie Schlagwörter mit einem Komma voneinander"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Set a blog post <b>cover</b>."
msgstr "Wählen Sie ein <b>Titelbild</b> für den Blogbeitrag."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Mehrere Zugvögel und einheimische Vögel, Säugetiere und Reptilien nennen den"
" Copper Canyon ihr Zuhause. Auch die exquisite Tierwelt in diesem nahezu "
"unberührten Land ist einen Blick wert."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Share Links"
msgstr "Links teilen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Auf Facebook teilen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Auf LinkedIn teilen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on X"
msgstr "Auf X teilen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Diesen Beitrag teilen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Sidebar"
msgstr "Seitenleiste"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Sierra Tarahumara"
msgstr "Sierra Tarahumara"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"Sierra Tarahumara, im Volksmund als Copper Canyon bekannt, liegt in Mexiko. "
"Das Gebiet ist ein beliebtes Ziel bei allen, die einen abenteuerlichen "
"Urlaub suchen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Skies"
msgstr "Himmel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller"
msgstr "Kleiner"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller picture"
msgstr "Kleineres Bild"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Daher ist es von entscheidender Bedeutung, dass Sie genau das richtige "
"Teleskop für Ihren Standort und Ihre Vorlieben bei der Sternenbeobachtung "
"bekommen. Um mit zu beginnen, lassen Sie uns die drei wichtigsten Arten von "
"Teleskopen besprechen und dann einige „Teleskop 101“-Konzepte aufstellen, um"
" Ihre Chancen zu erhöhen, dass Sie das Richtige kaufen werden."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Es könnte also sein, dass wir uns einmal im Jahr im Urlaub auf einem "
"Campingplatz oder bei einem Verwandten auf dem Lande befinden, wenn der "
"Spender des Nachthimmels plötzlich beschließt, seine spektakuläre Show zu "
"zeigen. Wenn Sie schon einmal einen solchen Moment erlebt haben, in dem Sie "
"buchstäblich atemlos waren von dem, was der Nachthimmel uns zu bieten hat, "
"können Sie sich wahrscheinlich genau an den Moment erinnern, in dem Sie "
"nichts anderes sagen konnten als „Wow“ zu dem, was Sie gesehen haben."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"Um also genau die richtige Art von Teleskop auszuwählen, sind Ihre Ziele bei"
" der Verwendung des Teleskops wichtig. Um die Stärken und Schwächen nicht "
"nur der Objektive und des Teleskopdesigns zu verstehen, sondern auch die "
"Leistung des Teleskops in verschiedenen Situationen der Sternenbeobachtung, "
"ist es am besten, wenn Sie im Vorfeld Ihre Hausaufgaben machen und sich mit "
"den verschiedenen Arten vertraut machen. Bevor Sie also Ihren ersten Kauf "
"tätigen ..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"Sie verreisen also ins Ausland, haben Ihr Reiseziel gewählt und müssen nun "
"ein Hotel auswählen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "Jemand Berühmtes im <cite title=\"Source Title\">Quellentitel</cite>"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Spotting the fauna"
msgstr "Die Tierwelt entdecken"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "Start writing here..."
msgstr "Hier schreiben …"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Stil"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Untertitel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Untertitel"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Stichwortkategorien"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Stichwortkategorie"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "Formular für Stichwortkategorie"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Stichwortformular"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Stichwortliste"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists!"
msgstr "Stichwortkategorie existiert bereits!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Stichwortbezeichnung existiert bereits!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Stichwörter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags List"
msgstr "Liste der Stichwörter"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Taking pictures in the dark"
msgstr "Bilder im Dunkeln aufnehmen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Teaser"
msgstr "Teaser"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Teaser & Tags"
msgstr "Teaser & Stichwörter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Teaser-Inhalt"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"Vor zehn Jahren wären Sie wahrscheinlich in Ihr örtliches Reisebüro gegangen"
" und hätten der persönlichen Beratung durch die sogenannten „Experten“ "
"vertraut. Die Art und Weise, wie Sie im 21. Jahrhundert Ihr Hotel auswählen "
"und buchen, ist natürlich das Internet, indem Sie Reise-Websites nutzen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Dieser „Wow“-Moment ist es, worum es in der Astrologie geht. Für einige wird"
" dieser „Wow“-Moment zu einer Leidenschaft, die zu einer Karriere im Studium"
" der Sterne führt. Für einige wenige Glückliche wird dieser „Wow“-Moment zu "
"einer alles verzehrenden Besessenheit, die dazu führt, dass sie mit dem "
"Space Shuttle oder auf einer unserer frühen Weltraummissionen zu den Sternen"
" reisen. Aber für die meisten von uns wird Astrologie vielleicht zu einem "
"Zeitvertreib oder einem regelmäßigen Hobby. Aber wir tragen diesen "
"„Wow“-Moment für den Rest unseres Lebens mit uns und beginnen, nach "
"Möglichkeiten zu suchen, tiefer zu schauen und mehr über das spektakuläre "
"Universum zu lernen, das wir jede Nacht in den Millionen von Sternen über "
"uns sehen."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "Das Schöne an der Astronomie ist, dass sie für jeden etwas bietet!"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"Die beste Zeit, um den Mond zu betrachten, ist natürlich nachts, wenn es "
"wenig Wolken gibt und das Wetter für eine lange und anhaltende Studie "
"entgegenkommend ist. Das erste Viertel liefert die größte Detailgenauigkeit "
"beim Studium. Und lassen Sie sich nicht davon täuschen, dass ein Teil des "
"Mondes abgedunkelt ist, wenn er nicht im Vollmondstadium ist. Das Phänomen, "
"das als „Erdschein“ bekannt ist, gibt Ihnen die Möglichkeit, auch den "
"abgedunkelten Teil des Mondes mit einigen Details zu sehen, selbst wenn der "
"Mond nur zu einem Viertel oder zur Hälfte sichtbar ist."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "Die beste Zeit, den Mond zu sehen."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"Der Blogbeitrag ist ab diesem Datum für Ihre Besucher auf der Website "
"sichtbar, wenn er als veröffentlicht festgelegt wird."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"Die Klippen in dieser Region gehören zu den höchsten der Welt und zu sehen, "
"wie das Wasser in Kaskaden von den hohen Gipfeln herabstürzt, ist einfach "
"atemberaubend. Der kurze Ausflug von Maui mit Maui-Helikopterrundflügen ist "
"es wert, die Schönheit dieser natürlichen Umgebung zu sehen."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"Die vollständige URL, um über die Website auf das Dokument zuzugreifen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"Das nächste, was wir uns natürlich zulegen wollen, ist ein gutes Teleskop. "
"Vielleicht haben Sie schon einmal einen Hobbyisten gesehen, der in seinem "
"Studium weit fortgeschritten ist und diese wirklich cool aussehenden "
"Teleskope irgendwo auf einem Hügel aufstellt. Das erregt den "
"Amateurastronomen in Ihnen, denn das muss der logische nächste Schritt im "
"Wachstum Ihres Hobbys sein. Aber ein gutes Teleskop zu kaufen, kann geradezu"
" verwirrend und einschüchternd sein."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"Die Website sollte eine detaillierte Analyse der Freizeitangebote im Hotel –"
" Spa, Pool, Fitnessraum, Sauna – sowie Details zu anderen Einrichtungen in "
"der Nähe, wie z. B. Golfplätzen, bieten. 7. Besondere Bedürfnisse: Die "
"Website des Hotelverzeichnisses sollte den Besucher über die besonderen "
"Dienstleistungen und die Zugänglichkeitsrichtlinien des jeweiligen Hotels "
"informieren. Auch wenn dies nicht für jeden Besucher gilt, ist es für einige"
" absolut wichtig."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"Die Entscheidung für ein Stativ oder anderes Zubehör ändert sich erheblich "
"bei einem Teleskop, das auf Ihrer Terrasse stehen wird, im Gegensatz zu "
"einem Teleskop, das Sie an viele abgelegene Orte mitnehmen wollen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"Der Anblick ist wirklich atemberaubend und ein Anblick, den man nicht "
"verpassen sollte. Es ist auch sehr lehrreich, einen schlafenden Vulkan aus "
"der Nähe zu sehen, etwas, das man nicht jeden Tag sieht. Auf der Nord- und "
"Südseite des Vulkans bietet sich Ihnen jedoch ein ganz anderes Bild. Diese "
"Seiten sind üppig und grün und Sie werden einige schöne Wasserfälle und "
"prächtige Büsche sehen können. Auf dieser Seite der Insel gibt es tropische "
"Regenwälder, die nur mit dem Flugzeug leicht zu erreichen sind."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"Und dann ist da noch das Problem der Motivation des Rezensenten. Je mehr "
"Bewertungen Sie lesen, desto mehr fällt Ihnen auf, dass sie sich an den "
"Extremen der Meinungen festmachen. Auf der einen Seite haben Sie wütende "
"Kritiker, die ein Hühnchen zu rupfen haben, auf der anderen Seite "
"begeisterte Gäste, die das Hotel in den höchsten Tönen loben. Es wird Sie "
"nicht überraschen, wenn Sie erfahren, dass Hotels manchmal ihre eigenen "
"tollen Bewertungen veröffentlichen oder dass die Konkurrenten Schlange "
"stehen, um die Konkurrenz mit schlechten Bewertungen zu überziehen. Es ist "
"sinnvoll, bei der Auswahl eines Hotels zu überlegen, was Ihnen wirklich "
"wichtig ist. Dann sollten Sie sich für ein Online-Hotelverzeichnis "
"entscheiden, das aktuelle, unabhängige und unparteiische Informationen "
"liefert, auf die es wirklich ankommt."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr ""
"Es gibt noch weitere Überlegungen, die in Ihre endgültige Kaufentscheidung "
"einfließen sollten."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"Der Kosmos hat etwas Zeitloses an sich. Die Tatsache, dass die Planeten und "
"der Mond und die Sterne dahinter schon seit Ewigkeiten da sind, hat etwas "
"mit unserem Gefühl für unseren Platz im Universum zu tun. Tatsächlich sind "
"viele der Sterne, die wir mit bloßem Auge „sehen“, in Wirklichkeit Licht, "
"das von diesem Stern vor Hunderttausenden von Jahren kam. Dieses Licht "
"erreicht erst jetzt die Erde. Auf eine sehr reale Weise ist der Blick nach "
"oben also wie eine Zeitreise."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"Diese Dinge sind wirklich wichtig und jedes anständige Hotelverzeichnis "
"sollte Ihnen diese Art von Ratschlägen zu den Zimmern geben – nicht nur die "
"Anzahl der Zimmer, was die übliche Option ist!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "Dieses Feld wird für Ihre Besucher nicht sichtbar sein"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "This tag already exists"
msgstr "Dieses Stichwort existiert bereits"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Tiny"
msgstr "Sehr klein"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Titel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Above Cover"
msgstr "Titel über Titelbild"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Inside Cover"
msgstr "Titel in Titelbild"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"Wenn Sie den Mond mit bloßem Auge betrachten wollen, sollten Sie sich mit "
"der Mondkarte vertraut machen, damit Sie die Meere, Krater und andere "
"geografische Phänomene, die bereits von anderen kartiert wurden, besser "
"erkennen können und Ihr Studium angenehmer wird. Mondkarten sind in jedem "
"Astronomieshop oder online erhältlich und die Investition lohnt sich."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"Um zu lernen, wie man die Sterne besser beobachten kann, gibt es einige "
"grundlegende Dinge, die wir brauchen, um tiefer zu schauen, über das hinaus,"
" was wir mit dem bloßen Auge sehen können, und zu beginnen, die Sterne zu "
"studieren sowie sie zu genießen. Das erste, was Sie brauchen, ist überhaupt "
"keine Ausrüstung, sondern Literatur. Eine gute Sternkarte zeigt Ihnen die "
"wichtigsten Sternbilder, die Lage der wichtigsten Sterne, die wir zur "
"Navigation im Himmel verwenden, und die Planeten, die größer als Sterne "
"erscheinen. Und wenn Sie zu dieser Karte noch einige gut gemachte "
"Einführungsmaterialien in das Hobby der Astronomie hinzufügen, sind Sie auf "
"dem besten Weg."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"Ein gutes Fernglas kann Wunder bewirken, wenn es um die Details geht, die "
"Sie auf der Mondoberfläche sehen werden. Die besten Ergebnisse erzielen Sie "
"mit einer guten Weitfeldeinstellung des Fernglases, damit Sie die "
"Mondlandschaft in ihrer ganzen Schönheit aufnehmen können. Und weil es fast "
"unmöglich ist, das Fernglas so lange ruhig zu halten, wie Sie diesen "
"herrlichen Körper im Weltraum betrachten wollen, sollten Sie Ihrem "
"Ausrüstungsarsenal ein gutes Stativ hinzufügen, an dem Sie das Fernglas "
"befestigen können, damit Sie den Mond bequem und mit einer stabilen "
"Betrachtungsplattform studieren können."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"Um das Ganze auf ein natürliches Niveau zu bringen, können Sie "
"Partnerschaften mit anderen Astronomen nutzen oder eines der wirklich "
"großartigen Teleskope besuchen, die von Profis aufgebaut wurden, die in "
"bessere Techniken zur Beseitigung atmosphärischer Störungen investiert "
"haben, um den Mond noch besser zu sehen. Über das Internet können Sie auf "
"das Hubble und viele der großen Teleskope zugreifen, die ständig auf den "
"Mond gerichtet sind. Außerdem arbeiten viele Astronomievereine an "
"Möglichkeiten, mehrere Teleskope zu kombinieren, die sorgfältig mit "
"Computern synchronisiert werden, um den besten Blick auf die Mondlandschaft "
"zu erhalten."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Top Banner"
msgstr "Oberer Banner"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "Reisen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "Unveröffentlicht ("

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Beitrag ohne Titel"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Mit diesem Symbol können Sie eine Vorschau Ihres Blogbeitrags auf "
"<b>Mobilgeräten</b> anzeigen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Verwendet in:"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Viewpoints"
msgstr "Perspektiven"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "Ansichten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "Sichtbar auf allen Seiten des Blogs"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Auf der aktuellen Website sichtbar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "HIER SCHREIBEN ODER BAUSTEINE VERSCHIEBEN"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Website"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Website-Blogs"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Website-Snippet-Filter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "Website-URL"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Website-Meta-Beschreibung"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website-Meta-Schlagwörter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Website-Meta-Titel"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Opengraph-Bild der Website"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "Was, wenn Sie das Hubble steuern dürften?"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"Während jeder jederzeit nach oben schauen und sich in die Sterne verlieben "
"kann, besteht der Spaß an der Astronomie darin, zu lernen, wie man immer "
"geschickter und besser ausgerüstet wird, damit man jedes Mal, wenn man nach "
"oben schaut, mehr und mehr sieht und versteht. Hier sind einige Schritte, "
"die Sie unternehmen können, um die Momente, die Sie Ihrem Hobby der "
"Astronomie widmen können, viel angenehmer zu gestalten."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "With a View"
msgstr "Mit Aussicht"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr ""
"Schreiben Sie hier einen kleinen Text, um Ihren Blog oder Ihr Unternehmen zu"
" beschreiben."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"Sie sollten immer sorgfältig abwägen, welche Art von Einrichtungen Sie von "
"Ihrem Zimmer benötigen und das Hotel finden, das über die Einrichtungen "
"verfügt, die Sie für wichtig halten. Die Website des Hotelverzeichnisses "
"sollte auf Dinge eingehen wie: Bettgröße, Internetzugang (seine Kosten, ob "
"es eine WIFI- oder kabelgebundene Breitbandverbindung gibt), kostenlose "
"Annehmlichkeiten, Ausblicke aus dem Zimmer und Luxusangebote wie ein "
"Kissenmenü oder ein Badmenü, die Wahl zwischen Raucher- und "
"Nichtraucherzimmern usw."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"Sie werden all die Schönheit sehen, die Maui zu bieten hat und können eine "
"tolle Zeit mit der ganzen Familie genießen. Helikopterrundflüge sind nicht "
"zu teuer und dauern von fünfundvierzig Minuten bis über eine Stunde. Mit "
"Maui-Helikopterrundflügen können Sie Orte sehen, die normalerweise "
"unzugänglich sind. Orte, die zu Fuß oder mit dem Auto nicht erreichbar sind,"
" können aus der Luft bewundert werden. Atemberaubende Sehenswürdigkeiten "
"warten auf diejenigen, die sich auf einen unterhaltsamen Maui-"
"Helikopterrundflug einlassen. Wenn Sie längere Zeit auf der Insel bleiben, "
"sollten Sie mehrere Maui-Helikopterrundflüge in Erwägung ziehen."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "Abenteuer"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr ". Klicken Sie hier, um auf den Blog zuzugreifen:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "Brotkrümel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "durch"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "Entdeckung"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "Führungen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "wurde veröffentlicht im Blog"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "Unterkünfte"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "in"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "Teleskope"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr ", um einen Kommentar zu hinterlassen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "unveröffentlicht"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"
msgstr ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| Noch keine Kommentare"
