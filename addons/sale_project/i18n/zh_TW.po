# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    沒有發現里程碑。讓我們創建一個吧！\n"
"                </p><p>\n"
"                    追蹤要取得成功必須達成的主要進度點。\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "%(name)s's Sales Order Items"
msgstr "%(name)s 的銷售訂單項目"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "%(name)s's Sales Orders"
msgstr "%(name)s 的銷售訂單"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this project has been cancelled. We recommend either "
"updating the sales order item or cancelling this project in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" "
"title=\"與此專案關聯的銷售單已被取消。我們建議更新銷售單項目，或依照銷售單的取消動作取消此專案。\" "
"invisible=\"sale_order_state != 'cancel'\"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this task has been cancelled. We recommend either "
"updating the sales order item or cancelling this task in alignment with the "
"cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" "
"title=\"與此任務關聯的銷售單已被取消。我們建議更新銷售單項目，或依照銷售單的取消動作取消此任務。\" "
"invisible=\"sale_order_state != 'cancel'\"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> Sales Order\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Make Billable\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> 張銷售訂單\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            設為可計費\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Sales Orders\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            銷售訂單\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                        <span class=\"o_stat_text\">Preview</span>"
msgstr ""
"<span class=\"o_stat_text\">客戶</span>\n"
"                        <span class=\"o_stat_text\">預覽</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">銷售訂單</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">任務</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
msgid "<span>)</span>"
msgstr "<span>）</span>"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid ""
"A project must be defined on the quotation %(order)s or on the form of products creating a task on order.\n"
"The following product need a project in which to put its task: %(product_name)s"
msgstr ""
"必須在報價單 %(order)s，或在建立訂單任務的產品表單上，設定一個專案。\n"
"以下產品需要有專案，以放置其相關任務：%(product_name)s"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid ""
"A task will be created for the project upon sales order confirmation. The "
"analytic distribution of this project will also serve as a reference for "
"newly created sales order items."
msgstr "銷售訂單確認後，將為專案項目建立任務。此項目的分析分佈，也將作為新建立的銷售訂單項目的參考。"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"根據產品配置，可透過機制自動計算交貨數量：\n"
"  - 手動：在資料行手動設定數量\n"
"  - 從開支分析：數量是已過賬開支的數量總和\n"
"  - 工時表：連結至此銷售資料行的任務所記錄的工時總和\n"
"  - 庫存變動：採用已確認提貨的數量\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Based on Delivered Quantity (Manual)"
msgstr "基於交付數量(手動)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Based on Milestones"
msgstr "基於里程碑"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_task__allow_billable
msgid "Billable"
msgstr "可計費"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Cancelled"
msgstr "已取消"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Cost of Goods Sold"
msgstr "銷貨成本"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Cost of Goods Sold Items"
msgstr "銷貨成本項目"

#. module: sale_project
#: model:ir.actions.server,name:sale_project.model_sale_order_action_create_project
msgid "Create Project"
msgstr "建立專案"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a customer invoice"
msgstr "建立客戶發票"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a new quotation, the first step of a new sale!"
msgstr "建立一個報價是新銷售的第一步!"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"Create a project for the order with a task for each sales order line to "
"track the time spent."
msgstr "為訂單建立一個專案，並為每個銷售訂單資料行分配一個任務，以追蹤所花費的時間。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Create a task in an existing project to track the time spent."
msgstr "在現有專案中建立任務，追蹤所花費的時間。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a vendor bill"
msgstr "建立供應商帳單"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Create an empty project for the order to track the time spent."
msgstr "為訂單建立一個空白專案，追蹤所花費的時間。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr "建立發票，登記付款，並追蹤與客戶的溝通歷程。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your vendors."
msgstr "建立發票，登記付款，並追蹤與供應商的溝通歷程。"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "由訂單建立"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"Create projects or tasks later, and link them to order to track the time "
"spent."
msgstr "稍後建立專案或任務，然後將它們連結至訂單，以追蹤所花費的時間。"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__partner_id
msgid "Customer"
msgstr "客戶"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Customer Invoices"
msgstr "客戶發票"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Defined on quotation"
msgstr "在報價單定義"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Delivered"
msgstr "已送貨"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_only_product_template
msgid "Digital Marketing Campaign (project)"
msgstr "數碼行銷活動（專案項目）"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "顯示銷售訂單"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Done"
msgstr "完成"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Down Payments"
msgstr "預付訂金"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Empty project"
msgstr "空白專案"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_flooring_product_template
msgid "Flooring Services"
msgstr "地板服務"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "In Progress"
msgstr "進行中"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Invoice your milestones when they are reached."
msgstr "達到里程碑時開立發票。"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr "向客戶開立您的時間和材料應收憑單"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Invoiced"
msgstr "已開立發票"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_invoices
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_invoices_dashboard
msgid "Invoices"
msgstr "發票"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "發票策略"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "日記賬項目"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Load more"
msgstr "載入更多"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"手動設定訂單數量：根據手動輸入數量的應收憑單，不建立分析帳戶。\n"
"工時表：在合約的基礎上對相關應收憑單追蹤小時工時表。\n"
"建立任務和追蹤時間：在銷售訂單驗證上建立任務並追蹤工作時間。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Materials"
msgstr "材料"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "更新數量的方法"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Milestones"
msgstr "里程碑"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "New Sales Order Item"
msgstr "新銷售訂單項目"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Non-billable"
msgstr "非計費"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "Not Billed"
msgstr "不計費"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_task_only_product_template
msgid "Office Furniture Set (task)"
msgstr "辦公室傢俬套裝（任務）"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales order."
msgstr "客戶確認報價單後，報價單即變為銷售訂單。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Other Services"
msgstr "其他服務"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_painting_product_template
msgid "Painting"
msgstr "髹油"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr "達到里程碑後自動交貨的訂購數量百分比。"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_plumbing_product_template
msgid "Plumbing Services"
msgstr "渠管服務"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_task.py:0
msgid "Portal Sale Order"
msgstr "門戶網站銷售單"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Prepaid/Fixed Price"
msgstr "預付/固定價格"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "商品"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "產品款式"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__reinvoiced_sale_order_id
msgid ""
"Products added to stock pickings, whose operation type is configured to "
"generate analytic costs, will be re-invoiced in this sales order if they are"
" set up for it."
msgstr "新增至庫存提取的產品（其操作類型配置為產生分析成本），會在此銷售訂單中重新開立發票（若設定為如此操作）。"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_report__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "專案"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "專案及任務"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_account_id
msgid "Project Account"
msgstr "專案賬戶"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "專案里程碑"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "項目里程碑"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "專案範本"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "專案的銷售訂單"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Projects"
msgstr "專案"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom_qty
msgid "Quantity"
msgstr "數量"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Quantity (%)"
msgstr "數量（%）"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "報價單範本資料行"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "銷售及發票"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_report
msgid "Sales Analysis Report"
msgstr "銷售分析報告"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_milestone.py:0
#: code:addons/sale_project/models/project_task.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__reinvoiced_sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model:project.project,name:sale_project.so_template_project
msgid "Sales Order"
msgstr "銷售訂單"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Sales Order Item"
msgstr "銷售訂單項目"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr "達到里程碑後立即更新的銷售訂單項目。"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"將會加入此任務花費時間、以向客戶開立發票的銷售訂單項目。\n"
"預設情況下，會選擇專案設定的銷售訂單項目。如果沒有，將使用尚有剩餘時間的最後一個預付銷售訂單項目。\n"
"要將此任務設為不計費，可刪除此銷售訂單項目。你也可以單獨修改或刪除工時表每個記項的銷售訂單項目。"

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Sales Order Items"
msgstr "銷售訂單項目"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "銷售訂單資料行"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_sales_orders
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_sales_orders_dashboard
msgid "Sales Orders"
msgstr "銷售訂單"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"此專案的任務及工時表預設選擇的銷售訂單項目，除非工時表設定的員工明確連結至專案的另一個銷售訂單項目。\n"
"如有必要，可在任務或工時表的每個記項上單獨修改。"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "任務連結到的銷售訂單。"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_screw_driver_product_template
msgid "Screw Driver"
msgstr "螺絲批"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "搜尋發票"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "搜尋銷售訂單"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
msgid "Select who to bill..."
msgstr "選擇向誰計費⋯"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "服務發票政策"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Sold"
msgstr "已售出"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_state
msgid "Status"
msgstr "狀態"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "任務"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Task Created (%(name)s): %(link)s"
msgstr "已建立任務（%(name)s）：%(link)s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "任務重複"

#. module: sale_project
#: model:project.project,label_tasks:sale_project.so_template_project
msgid "Tasks"
msgstr "任務"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "任務分析"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "The Sale Order Item should contain a service product."
msgstr "銷售單項目應包含服務產品。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr "產品%s不應該有一個全局專案，因為它將生成一個專案。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr "產品%s不應該有專案或專案模板，因為它不會生成專案。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr "產品%s不應具有專案模板，因為它將在全局專案中生成任務。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid ""
"The project couldn't be created as the Sales Order must be confirmed, is "
"already linked to a project, or doesn't involve any services."
msgstr "未能建立專案，因銷售單必須已確認、已連結至專案項目，或不涉及任何服務。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid "This Sales Order must contain at least one product of type \"Service\"."
msgstr "此銷售訂單必須至少包含一種「服務」類型的產品。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "This task has been created from: %(order_link)s (%(product_name)s)"
msgstr "此任務建立自 %(order_link)s（%(product_name)s）"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "To Do"
msgstr "待辦事項"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "待開立發票"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "追蹤服務"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_vendor_bills
msgid "Vendor Bills"
msgstr "供應商賬單"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "View Sales Order"
msgstr "查看銷售訂單"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_and_task_product_template
msgid "Website Redesign Service (project & task)"
msgstr "網站重新設計服務（專案及任務）"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_wiring_product_template
msgid "Wiring Services"
msgstr "纜線服務"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_task.py:0
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr "您不能將訂單項目  %(order_id)s - %(product_id)s 連結到此任務，因為它是重新開立應收憑單的費用。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "You will be able to create an invoice and collect the payment."
msgstr "你將能夠建立發票，並收取付款。"
