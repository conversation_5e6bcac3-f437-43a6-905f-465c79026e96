# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Aucuns jalons trouvés. Créons-en un!\n"
"                </p><p>\n"
"                    Suivez les points de progression majeurs qui doivent être atteints afin de réussir.\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "%(name)s's Sales Order Items"
msgstr "Articles de commande de %(name)s"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "%(name)s's Sales Orders"
msgstr "Commandes clients de %(name)s"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this project has been cancelled. We recommend either "
"updating the sales order item or cancelling this project in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"La commande "
"client associé à ce projet a été annulé. Nous vous conseillons de mettre à "
"jour l'article de commande ou d'annuler ce projet en même temps que "
"l'annulation de la commande.\" invisible=\"sale_order_state != 'cancel'\"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this task has been cancelled. We recommend either "
"updating the sales order item or cancelling this task in alignment with the "
"cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"La commande "
"client associé à cette tâche a été annulé. Nous vous conseillons de mettre à"
" jour l'article de commande ou d'annuler cette tâche en même temps que "
"l'annulation de la commande.\" invisible=\"sale_order_state != 'cancel'\"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> Sales Order\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Make Billable\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> Commande(s)\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Rendre facturable(s)\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Sales Orders\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Commandes clients\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                        <span class=\"o_stat_text\">Preview</span>"
msgstr ""
"<span class=\"o_stat_text\">Client</span>\n"
"                        <span class=\"o_stat_text\">Aperçu</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">Commande client</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">Tâches</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
msgid "<span>)</span>"
msgstr "<span>)</span>"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid ""
"A project must be defined on the quotation %(order)s or on the form of products creating a task on order.\n"
"The following product need a project in which to put its task: %(product_name)s"
msgstr ""
"Un projet doit être défini sur le devis %(order)s ou sur le formulaire des produits créant une tâche lors de la commande.\n"
"Le produit suivant nécessite un projet dans lequel enregistrer sa tâche : %(product_name)s"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid ""
"A task will be created for the project upon sales order confirmation. The "
"analytic distribution of this project will also serve as a reference for "
"newly created sales order items."
msgstr ""
"Une tâche sera créée pour le projet lors de la confirmation de la commande "
"client. La répartition analytique de ce projet servira également de "
"référence pour les nouveaux articles de la commande client créée."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Selon la confirmation du produit, la quantité livrée peut être calculée automatiquement par mécanisme :\n"
" - Manuel : la quantité est définie manuellement sur la ligne\n"
"- Analytique à partir des dépenses : la quantité est la somme des dépenses comptabilisées\n"
"- Feuille de temps : la quantité est la somme des heures enregistrées sur les tâches liées à cette commande\n"
"- Mouvements de stock : la quantité provient des transferts confirmés\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Based on Delivered Quantity (Manual)"
msgstr "Basé sur la quantité livrée (manuelle)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Based on Milestones"
msgstr "Basé sur les jalons"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_task__allow_billable
msgid "Billable"
msgstr "Facturable"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Cancelled"
msgstr "Annulé"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Cost of Goods Sold"
msgstr "Coûts des marchandises vendues"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Cost of Goods Sold Items"
msgstr "Coût des marchandises vendues"

#. module: sale_project
#: model:ir.actions.server,name:sale_project.model_sale_order_action_create_project
msgid "Create Project"
msgstr "Créer un projet"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a customer invoice"
msgstr "Créer une facture client"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a new quotation, the first step of a new sale!"
msgstr "Créez un nouveau devis, la première étape d'une vente !"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"Create a project for the order with a task for each sales order line to "
"track the time spent."
msgstr ""
"Créez un projet pour la commande avec une tâche pour chaque ligne de "
"commande client afin de suivre le temps passé."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Create a task in an existing project to track the time spent."
msgstr "Créez une tâche dans un projet existant pour suivre le temps passé."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a vendor bill"
msgstr "Créer une facture fournisseur"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Create an empty project for the order to track the time spent."
msgstr "Créez un projet vide pour la commande afin de suivre le temps passé."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""
"Créez des factures, enregistrez des paiements et gardez une trace de vos "
"discussions avec vos clients."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your vendors."
msgstr ""
"Créez des factures, enregistrez des paiements et suivez les discussions avec"
" vos fournisseurs."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Créer à la commande"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"Create projects or tasks later, and link them to order to track the time "
"spent."
msgstr ""
"Créez des projets ou des tâches ultérieurement et reliez-les à la commande "
"pour suivre le temps passé."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__partner_id
msgid "Customer"
msgstr "Client"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Customer Invoices"
msgstr "Factures clients"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Defined on quotation"
msgstr "Défini sur le devis"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Delivered"
msgstr "Délivré"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_only_product_template
msgid "Digital Marketing Campaign (project)"
msgstr "Campagne de marketing digital (projet)"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "Afficher la commande client"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Done"
msgstr "Terminé"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Down Payments"
msgstr "Acomptes"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Empty project"
msgstr "Projet vide"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_flooring_product_template
msgid "Flooring Services"
msgstr "Services de revêtement de sol"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "In Progress"
msgstr "En cours"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Invoice your milestones when they are reached."
msgstr "Facturez vos jalons lorsqu'ils sont atteints."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr "Facturez votre temps et matériel à vos clients"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Invoiced"
msgstr "Facturé"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_invoices
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_invoices_dashboard
msgid "Invoices"
msgstr "Factures clients"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "Politique de facturation"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Load more"
msgstr "Charger plus"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Définir manuellement des quantités sur la commande : facture basée sur les quantités saisies manuellement, sans créer de compte analytique.\n"
"Feuilles de temps sur les contrats : facture basée sur les heures trackées sur la feuille de temps liée.\n"
"Créer une tâche et suivre les heures : créer une tâche à la validation de la commande et effectuer un suivi du temps passé."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Materials"
msgstr "Matériels"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Méthode de mise à jour des quantités livrées"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Milestones"
msgstr "Jalons"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "New Sales Order Item"
msgstr "Nouvel article de commande"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Non-billable"
msgstr "Non facturable"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "Not Billed"
msgstr "Non facturé"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_task_only_product_template
msgid "Office Furniture Set (task)"
msgstr "Mobilier de bureau (tâche)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales order."
msgstr ""
"Une fois le devis confirmé par le client, il devient une commande client."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Other Services"
msgstr "Autres services"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_painting_product_template
msgid "Painting"
msgstr "Peinture"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr ""
"Pourcentage de la quantité commandée qui sera automatiquement livré lorsque "
"le jalon sera atteint."

#. module: sale_project
#: model:product.template,name:sale_project.product_product_plumbing_product_template
msgid "Plumbing Services"
msgstr "Services de plomberie"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_task.py:0
msgid "Portal Sale Order"
msgstr "Commande client portail"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Prepaid/Fixed Price"
msgstr "Prépayé/Prix fixe"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "Produit"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__reinvoiced_sale_order_id
msgid ""
"Products added to stock pickings, whose operation type is configured to "
"generate analytic costs, will be re-invoiced in this sales order if they are"
" set up for it."
msgstr ""
"Les produits ajoutés aux transferts de stock, dont le type d'opération est "
"configuré pour générer des coûts analytiques, seront facturés à nouveau dans"
" cette commande client si cela est prévu."

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_report__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Projet"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "Projet & Tâche"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_account_id
msgid "Project Account"
msgstr "Compte de projet"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "Jalon du projet"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "Jalons du projet"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Modèle de projet"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Commande client du projet"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Projects"
msgstr "Projets"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom_qty
msgid "Quantity"
msgstr "Quantité"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Quantity (%)"
msgstr "Quantité (%)"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Ligne de modèle de devis"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "Ventes & Facturation"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_report
msgid "Sales Analysis Report"
msgstr "Rapport d'analyse des ventes"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_milestone.py:0
#: code:addons/sale_project/models/project_task.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__reinvoiced_sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model:project.project,name:sale_project.so_template_project
msgid "Sales Order"
msgstr "Commande client"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Sales Order Item"
msgstr "Article de la commande client"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr ""
"L'article de la commande qui sera mis à jour lorsque le jalon sera atteint."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Article de la commande client à laquelle sera ajouté le temps passé sur cette tâche afin d'être facturé à votre client.\n"
"Par défaut, l'article de la commande client défini sur le projet sera sélectionné. En son absence, le dernier article de la commande client prépayé qui a encore du temps sera utilisé.\n"
"Supprimez l'article de la commande client afin de rendre cette tâche non facturable. Vous pouvez également modifier ou supprimer l'article de la commande client de chaque entrée de feuille de temps individuellement."

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Sales Order Items"
msgstr "Articles de la commande client"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_sales_orders
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_sales_orders_dashboard
msgid "Sales Orders"
msgstr "Commandes clients"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Article de la commande client qui sera sélectionné par défaut sur les tâches et feuilles de temps de ce projet, sauf si l'employé renseigné sur les feuilles de temps est explicitement lié à un article de la commande client sur le projet.\n"
"Il peut être modifié sur chaque tâche et entrée de feuille de temps individuellement si nécessaire."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Commande client à laquelle la tâche est liée."

#. module: sale_project
#: model:product.template,name:sale_project.product_product_screw_driver_product_template
msgid "Screw Driver"
msgstr "Tournevis"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "Rechercher dans la facture"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "Rechercher dans la commande client"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
msgid "Select who to bill..."
msgstr "Sélectionner qui facturer..."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "Politique de facturation du service"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Sold"
msgstr "Vendu"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_state
msgid "Status"
msgstr "Statut"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Tâche"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Task Created (%(name)s): %(link)s"
msgstr "Tâche créée (%(name)s) : %(link)s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Récurrence de tâche"

#. module: sale_project
#: model:project.project,label_tasks:sale_project.so_template_project
msgid "Tasks"
msgstr "Tâches"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Analyse des tâches"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "The Sale Order Item should contain a service product."
msgstr "L’article de commande doit contenir un produit de service."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""
"Le produit %s ne devrait pas avoir de projet global car il va générer un "
"projet."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"Le produit %s ne devrait pas avoir de projet ni de modèle de projet car il "
"ne va pas générer de projet. "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""
"Le produit %s ne devrait pas avoir de modèle de projet car il va générer une"
" tâche dans un projet global."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid ""
"The project couldn't be created as the Sales Order must be confirmed, is "
"already linked to a project, or doesn't involve any services."
msgstr ""
"Impossible de créer le projet, car la commande doit être confirmée, est déjà"
" associée à un projet ou n'implique pas de services."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid "This Sales Order must contain at least one product of type \"Service\"."
msgstr ""
"Cette commande client doit contenir au moins un produit du type \"Service\"."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "This task has been created from: %(order_link)s (%(product_name)s)"
msgstr "Cette tâche a été créée depuis : %(order_link)s (%(product_name)s)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "To Do"
msgstr "À faire"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "À facturer"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "Service de tracking"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_vendor_bills
msgid "Vendor Bills"
msgstr "Factures fournisseurs"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "View Sales Order"
msgstr "Afficher la commande client"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_and_task_product_template
msgid "Website Redesign Service (project & task)"
msgstr "Service de refonte de site web (projet & tâche)"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_wiring_product_template
msgid "Wiring Services"
msgstr "Services de câblage"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_task.py:0
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"Vous ne pouvez pas lier la ligne de la commande %(order_id)s - "
"%(product_id)s à cette tâche car il s'agit d'une dépense refacturée."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "You will be able to create an invoice and collect the payment."
msgstr "Vous serez en mesure de créer une facture et d'encaisser le paiement."
