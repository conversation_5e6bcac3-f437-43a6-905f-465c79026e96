# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON>, 2025
# <AUTHOR> <EMAIL>, 2025
# Wil <PERSON>do<PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    没有找到里程碑。让我们创建一个!\n"
"                </p><p>\n"
"                    追踪必须达到的主要进展点，以获得成功。\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "%(name)s's Sales Order Items"
msgstr "%(name)s的销售订单行"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "%(name)s's Sales Orders"
msgstr "%(name)s的销售订单"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this project has been cancelled. We recommend either "
"updating the sales order item or cancelling this project in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this project has been cancelled. We recommend either "
"updating the sales order item or cancelling this project in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel' \"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this task has been cancelled. We recommend either "
"updating the sales order item or cancelling this task in alignment with the "
"cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" "
"title=\"与此任务相关的销售订单已取消。我们建议在取消销售订单的同时更新销售订单行或取消这项任务。\" "
"invisible=\"sale_order_state != 'cancel' \"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> Sales Order\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Make Billable\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> 销售订单\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            设为可计费\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Sales Orders\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            销售订单\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                        <span class=\"o_stat_text\">Preview</span>"
msgstr ""
"<span class=\"o_stat_text\">客户</span>\n"
"                        <span class=\"o_stat_text\">预览</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">销售订单</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">任务</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
msgid "<span>)</span>"
msgstr "<span>）</span>"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid ""
"A project must be defined on the quotation %(order)s or on the form of products creating a task on order.\n"
"The following product need a project in which to put its task: %(product_name)s"
msgstr ""
"必须在报价单 %(order)s，或在建立订单任务的产品表单上，设置一个项目。\n"
"以下产品需要有项目，以放置其相关任务：%(product_name)s"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid ""
"A task will be created for the project upon sales order confirmation. The "
"analytic distribution of this project will also serve as a reference for "
"newly created sales order items."
msgstr "销售订单确认后，将为该项目创建一个任务。该项目的分析分配也将作为新创建的销售订单行的参考。"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"根据产品配置，交付数量可以通过机制自动计算：\n"
"  - 手动：在生产线上手动设置数量\n"
"  - 费用分析：数量为已公布费用的数量总和\n"
"  - 时间表：数量为与销售项目相关联的任务所记录的工时总和\n"
"  - 库存分录：数量为已确认的拣货量。\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Based on Delivered Quantity (Manual)"
msgstr "基于交付数量（手动）"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Based on Milestones"
msgstr "基于里程碑"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_task__allow_billable
msgid "Billable"
msgstr "计费"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Cancelled"
msgstr "已取消"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Cost of Goods Sold"
msgstr "售出货物的成本"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Cost of Goods Sold Items"
msgstr ""

#. module: sale_project
#: model:ir.actions.server,name:sale_project.model_sale_order_action_create_project
msgid "Create Project"
msgstr "创建项目"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a customer invoice"
msgstr "创建客户发票"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a new quotation, the first step of a new sale!"
msgstr "创建新报价是新销售的第一步！"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"Create a project for the order with a task for each sales order line to "
"track the time spent."
msgstr "为订单创建一个项目，为每个销售订单明细创建一个任务，以追踪所花费的时间。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Create a task in an existing project to track the time spent."
msgstr "在现有项目中创建一个任务，追踪所花费的时间。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Create a vendor bill"
msgstr "创建供应商账单"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Create an empty project for the order to track the time spent."
msgstr "为订单创建一个空项目，以追踪所花费的时间。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr "创建发票，登记付款，追踪与客户的沟通。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your vendors."
msgstr "创建发票，登记付款，并追踪与供应商的沟通。"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "按订单创建"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"Create projects or tasks later, and link them to order to track the time "
"spent."
msgstr "稍后创建项目或任务，并将它们与订单联系起来，以追踪所花费的时间。"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__partner_id
msgid "Customer"
msgstr "客户"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Customer Invoices"
msgstr "客户发票"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Defined on quotation"
msgstr "根据报价定义"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Delivered"
msgstr "已交付"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_only_product_template
msgid "Digital Marketing Campaign (project)"
msgstr "数字营销活动（项目）"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "显示销售订单"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Done"
msgstr "已完成"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Down Payments"
msgstr "预付款"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Empty project"
msgstr "空项目"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_flooring_product_template
msgid "Flooring Services"
msgstr "地板服务"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "In Progress"
msgstr "进行中"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Invoice your milestones when they are reached."
msgstr "在达到里程碑时开具结算。"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr "向客户开具您的时间和材料的结算单"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Invoiced"
msgstr "已开具发票"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_invoices
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_invoices_dashboard
msgid "Invoices"
msgstr "发票"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "开票策略"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move
msgid "Journal Entry"
msgstr "日记账分录"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "日记账项目"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Load more"
msgstr "加载更多"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"手动设置订单计数：基于手动输入计数的结算单，不创建分析账户。\n"
"时间表：在合同的基础上对相关结算单跟踪小时时间表。\n"
"创建任务和跟踪时间：在销售订单验证上创建任务并跟踪工作时间。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Materials"
msgstr "材料"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "更新数量的方法"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Milestones"
msgstr "里程碑"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "New Sales Order Item"
msgstr "新销售订单行"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Non-billable"
msgstr "非计费"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "Not Billed"
msgstr "未开具账单"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_task_only_product_template
msgid "Office Furniture Set (task)"
msgstr "办公家具套装（任务）"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales order."
msgstr "报价一旦得到客户确认，就会成为销售订单。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "Other Services"
msgstr "其它服务"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_painting_product_template
msgid "Painting"
msgstr "作画"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr "一旦达到里程碑，将自动交付订购数量的百分比。"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_plumbing_product_template
msgid "Plumbing Services"
msgstr "渠管服务"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_task.py:0
msgid "Portal Sale Order"
msgstr "门户网站销售订单"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid "Prepaid/Fixed Price"
msgstr "预付/固定价格"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "产品"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "产品变体"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__reinvoiced_sale_order_id
msgid ""
"Products added to stock pickings, whose operation type is configured to "
"generate analytic costs, will be re-invoiced in this sales order if they are"
" set up for it."
msgstr "新增至库存提取的产品（其操作类型配置为产生分析成本），会在此销售订单中重新开具发票（若设定为如此操作）。"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_report__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "项目"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "项目 & 任务"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_account_id
msgid "Project Account"
msgstr "项目账户"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "项目里程碑"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "项目里程碑"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "项目模板"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "项目销售订单"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Projects"
msgstr "项目"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom_qty
msgid "Quantity"
msgstr "数量"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Quantity (%)"
msgstr "数量（%）"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "报价模板行"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "销售和开票"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_report
msgid "Sales Analysis Report"
msgstr "销售分析报表"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_milestone.py:0
#: code:addons/sale_project/models/project_task.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__reinvoiced_sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model:project.project,name:sale_project.so_template_project
msgid "Sales Order"
msgstr "销售订单"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Sales Order Item"
msgstr "销售订单行"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr "一旦达到里程碑，销售订单行将被更新。"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"销售订单行，该任务所花费的时间将被添加到该项目中，以便向客户开具结算。\n"
"默认情况下，项目上设置的销售订单行将被选中。如果没有，将使用最后一个有剩余时间的预付销售订单行。\n"
"移除销售订单行，以使这项任务不计费。你也可以单独改变或删除每个工时表项目的销售订单行。"

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Sales Order Items"
msgstr "销售明细"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单明细"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_sales_orders
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_sales_orders_dashboard
msgid "Sales Orders"
msgstr "销售订单"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"在这个项目的任务和工时表上默认选择的销售订单行，除非工时表上设置的员工与项目上的另一个销售订单行有明确的联系。\n"
"如果有必要，可以在每个任务和工时表条目上单独修改。"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "任务关联的销售订单。"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_screw_driver_product_template
msgid "Screw Driver"
msgstr "螺丝刀"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "在结算单中搜索"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "在销售订单中搜索"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
msgid "Select who to bill..."
msgstr "选择收款客户..."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "服务开票策略"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/components/project_profitability_section.xml:0
msgid "Sold"
msgstr "已售出"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_state
msgid "Status"
msgstr "状态"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "任务"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "Task Created (%(name)s): %(link)s"
msgstr "已创建任务（%(name)s）：%(link)s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "重新发起"

#. module: sale_project
#: model:project.project,label_tasks:sale_project.so_template_project
msgid "Tasks"
msgstr "任务"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "任务分析"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "The Sale Order Item should contain a service product."
msgstr "销售订单行中的项目应该是一个服务产品。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr "产品%s不应该有一个全局项目，因为它将生成一个项目。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr "产品模板%s不应该有项目或项目模板，因为它不会生成项目。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product_template.py:0
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr "产品模板%s不应具有项目模板，因为它将在全局项目中生成任务。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid ""
"The project couldn't be created as the Sales Order must be confirmed, is "
"already linked to a project, or doesn't involve any services."
msgstr "由于销售订单必须确认、已与项目关联或不涉及任何服务，因此无法创建项目。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
msgid "This Sales Order must contain at least one product of type \"Service\"."
msgstr "此销售订单必须至少包含一种\"服务\"类型的产品。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "This task has been created from: %(order_link)s (%(product_name)s)"
msgstr "此任务创建自%(order_link)s（%(product_name)s）"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
msgid "To Do"
msgstr "待办"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "开结算单"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "跟踪服务"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
#: model:ir.embedded.actions,name:sale_project.project_embedded_action_vendor_bills
msgid "Vendor Bills"
msgstr "供应商账单"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "View Sales Order"
msgstr "查看销售订单"

#. module: sale_project
#: model:product.template,name:sale_project.product_service_create_project_and_task_product_template
msgid "Website Redesign Service (project & task)"
msgstr "网站重新设计服务（项目和任务）"

#. module: sale_project
#: model:product.template,name:sale_project.product_product_wiring_product_template
msgid "Wiring Services"
msgstr "缆线服务"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_task.py:0
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr "您不能将订单项 %(order_id)s - %(product_id)s链接到这个任务，因为它是重新结算的费用。"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project_project.py:0
msgid "You will be able to create an invoice and collect the payment."
msgstr "您可以创建发票并收取付款。"
