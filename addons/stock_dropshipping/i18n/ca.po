# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_dropshipping
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> Bochaca <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot_report
msgid "Customer Lot Report"
msgstr ""

#. module: stock_dropshipping
#: model:ir.model.fields.selection,name:stock_dropshipping.selection__stock_picking_type__code__dropship
#: model:stock.route,name:stock_dropshipping.route_drop_shipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.purchase_order_form_inherit_stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_order_form_inherit_sale_stock
msgid "Dropship"
msgstr "Dropship"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_purchase_order__dropship_picking_count
#: model:ir.model.fields,field_description:stock_dropshipping.field_sale_order__dropship_picking_count
msgid "Dropship Count"
msgstr "És un comptador de Dropship"

#. module: stock_dropshipping
#: model:ir.actions.act_window,name:stock_dropshipping.action_picking_tree_dropship
#: model:ir.ui.menu,name:stock_dropshipping.dropship_picking
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_picking_internal_search_inherit_stock_dropshipping
msgid "Dropships"
msgstr ""

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking__is_dropship
msgid "Is a Dropship"
msgstr "És un Dropship"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot
msgid "Lot/Serial"
msgstr "Lot/núm. de sèrie"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipus de picking "

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_procurement_group
msgid "Procurement Group"
msgstr "Grup de proveïment "

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr ""

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_product_product
msgid "Product Variant"
msgstr "Variant de producte"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Comanda de compra"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order
msgid "Sales Order"
msgstr "Comanda"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línia comanda de venda"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_move
msgid "Stock Move"
msgstr "Moviment d'estoc"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Regla d'estoc"

#. module: stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.stock_picking_type_kanban
msgid "To Validate"
msgstr "Per a validar"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Transferència"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Tipus d'operació"
