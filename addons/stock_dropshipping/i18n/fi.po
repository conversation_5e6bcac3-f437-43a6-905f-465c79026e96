# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_dropshipping
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Tu<PERSON> Aura <<EMAIL>>, 2024
# Wil Odoo, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot_report
msgid "Customer Lot Report"
msgstr "Asiakkaan erien raportti"

#. module: stock_dropshipping
#: model:ir.model.fields.selection,name:stock_dropshipping.selection__stock_picking_type__code__dropship
#: model:stock.route,name:stock_dropshipping.route_drop_shipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.purchase_order_form_inherit_stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_order_form_inherit_sale_stock
msgid "Dropship"
msgstr "Suoratoimitus"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_purchase_order__dropship_picking_count
#: model:ir.model.fields,field_description:stock_dropshipping.field_sale_order__dropship_picking_count
msgid "Dropship Count"
msgstr "Suoratoimitusten määrä"

#. module: stock_dropshipping
#: model:ir.actions.act_window,name:stock_dropshipping.action_picking_tree_dropship
#: model:ir.ui.menu,name:stock_dropshipping.dropship_picking
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_picking_internal_search_inherit_stock_dropshipping
msgid "Dropships"
msgstr "Suoratoimitukset"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking__is_dropship
msgid "Is a Dropship"
msgstr "Suoratoimitettava"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot
msgid "Lot/Serial"
msgstr "Erä/sarja"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking_type
msgid "Picking Type"
msgstr "Keräilyn tyyppi"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_procurement_group
msgid "Procurement Group"
msgstr "Hankintaryhmä"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Tuotteen täydentäminen Mixin"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_product_product
msgid "Product Variant"
msgstr "Tuotevariantti"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Ostotilaus"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order
msgid "Sales Order"
msgstr "Myyntitilaus"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order_line
msgid "Sales Order Line"
msgstr "Myyntitilausrivi"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_move
msgid "Stock Move"
msgstr "Varastotuotteen siirto"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Varaston sääntö"

#. module: stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.stock_picking_type_kanban
msgid "To Validate"
msgstr "Vahvistettavaksi"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Siirto"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Toiminnon tyyppi"
