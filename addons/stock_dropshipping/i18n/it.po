# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_dropshipping
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot_report
msgid "Customer Lot Report"
msgstr "Resoconto lotto cliente"

#. module: stock_dropshipping
#: model:ir.model.fields.selection,name:stock_dropshipping.selection__stock_picking_type__code__dropship
#: model:stock.route,name:stock_dropshipping.route_drop_shipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.purchase_order_form_inherit_stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_order_form_inherit_sale_stock
msgid "Dropship"
msgstr "Dropshipping"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_purchase_order__dropship_picking_count
#: model:ir.model.fields,field_description:stock_dropshipping.field_sale_order__dropship_picking_count
msgid "Dropship Count"
msgstr "Numero di spedizioni dropshipping"

#. module: stock_dropshipping
#: model:ir.actions.act_window,name:stock_dropshipping.action_picking_tree_dropship
#: model:ir.ui.menu,name:stock_dropshipping.dropship_picking
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_picking_internal_search_inherit_stock_dropshipping
msgid "Dropships"
msgstr "Spedizioni dropshipping"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking__is_dropship
msgid "Is a Dropship"
msgstr "È dropshipping"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot
msgid "Lot/Serial"
msgstr "Lotto/Serie"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipologia prelievo"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_procurement_group
msgid "Procurement Group"
msgstr "Gruppo di approvvigionamento"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Mixin rifornimento prodotto"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_product_product
msgid "Product Variant"
msgstr "Variante prodotto"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Ordine di acquisto"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order_line
msgid "Sales Order Line"
msgstr "Riga ordine di vendita"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_move
msgid "Stock Move"
msgstr "Movimento di magazzino"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Regola di giacenza"

#. module: stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.stock_picking_type_kanban
msgid "To Validate"
msgstr "Da convalidare"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Trasferisci"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Tipo di operazione"
