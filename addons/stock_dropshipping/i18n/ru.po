# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_dropshipping
# 
# Translators:
# Wil <PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot_report
msgid "Customer Lot Report"
msgstr ""

#. module: stock_dropshipping
#: model:ir.model.fields.selection,name:stock_dropshipping.selection__stock_picking_type__code__dropship
#: model:stock.route,name:stock_dropshipping.route_drop_shipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.purchase_order_form_inherit_stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_order_form_inherit_sale_stock
msgid "Dropship"
msgstr "Dropship"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_purchase_order__dropship_picking_count
#: model:ir.model.fields,field_description:stock_dropshipping.field_sale_order__dropship_picking_count
msgid "Dropship Count"
msgstr "Подсчет дропшипов"

#. module: stock_dropshipping
#: model:ir.actions.act_window,name:stock_dropshipping.action_picking_tree_dropship
#: model:ir.ui.menu,name:stock_dropshipping.dropship_picking
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_picking_internal_search_inherit_stock_dropshipping
msgid "Dropships"
msgstr "Дропшипы"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking__is_dropship
msgid "Is a Dropship"
msgstr "Является ли Dropship"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_lot
msgid "Lot/Serial"
msgstr "Партия/серия"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking_type
msgid "Picking Type"
msgstr "Тип комплектования"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_procurement_group
msgid "Procurement Group"
msgstr "В рамках закупки"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Миксин пополнения запаса продукта"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_product_product
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Заказ на покупку"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_move
msgid "Stock Move"
msgstr "Перемещение запасов"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Правило запаса"

#. module: stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.stock_picking_type_kanban
msgid "To Validate"
msgstr "Проверить"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Перевод"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Тип операции"
