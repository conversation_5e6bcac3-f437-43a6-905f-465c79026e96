
.s_media_list:not([data-vcss]) {
    .row {
        margin: $grid-gutter-width 0;
    }
    [class*="col-"] {
        padding: 0;
    }
    .s_media_list_body {
        padding: $grid-gutter-width;
        background-color: map-get($grays, 'white');
    }
    .s_media_list_options {
        @include o-position-absolute(auto,0,0,0);
        display: flex;
        border-top: 1px solid map-get($grays, '400');
        .s_media_list_option {
            flex: 1 1 auto;
            padding: $grid-gutter-width/3 0;
            border-right: 1px solid map-get($grays, '400');
            text-align: center;
            &:last-child {
                border-right: 0;
            }
        }
    }
}
