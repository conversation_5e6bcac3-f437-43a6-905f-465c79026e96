# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_delivery
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Jun<PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/sale_order.py:0
msgid "%(name)s (Estimated Cost: %(cost)s)"
msgstr "%(name)s (見積費用: %(cost)s)"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "(計算　:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span> - 重量（推定）: </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span> - 重量　: </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>荷物重量: </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Carrier</strong>"
msgstr "<strong>配送業者</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Shipping Method:</strong>"
msgstr "<strong>買い物方法:</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Shipping Weight: </strong>"
msgstr "<strong>荷物重量: </strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                    <br/>"
msgstr ""
"<strong>荷物重量:</strong>\n"
"                    <br/>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Total Weight</strong>"
msgstr "<strong>重量合計</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Tracking Number</strong>"
msgstr "<strong>追跡番号</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Weight: </strong>"
msgstr "<strong>重量: </strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Weight</strong>"
msgstr "<strong>重量</strong>"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_route__shipping_selectable
msgid "Applicable on Shipping Methods"
msgstr "配送方法に適用可能"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "キャンセル"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid ""
"Cancelling a delivery may not be undoable. Are you sure you want to "
"continue?"
msgstr "配送のキャンセルは行えない場合があります。本当に続けますか？"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier"
msgstr "配送業者"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "配送コード"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier name"
msgstr "配送会社名"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__company_id
msgid "Company"
msgstr "会社"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Cost: %(price).2f %(currency)s"
msgstr "原価: %(price).2f %(currency)s"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_uid
msgid "Created by"
msgstr "作成者"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_date
msgid "Created on"
msgstr "作成日"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "配送業者選択ガイド"

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_action_delivery_carrier_form
msgid "Delivery Methods"
msgstr "配送方法"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "配送パッケージ選択ガイド"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "配送パッケージの種類"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.vpicktree_view_tree
msgid "Destination"
msgstr "配送先"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "配送先国"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "破棄"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__display_name
msgid "Display Name"
msgstr "表示名"

#. module: stock_delivery
#: model:ir.actions.act_window,name:stock_delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "追跡リンクの表示"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"見積金額：顧客に送料の見積金額が請求されます。\n"
"実費: 顧客に送料の実費が請求されます。送料は納品後に販売オーダで更新されます。"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Exception occurred with respect to carrier on the transfer"
msgstr "運送時に配送業者に関して例外が発生しました。"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Exception:"
msgstr "例外:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "FedEx"
msgstr "FedEx"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.product_template_hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "HS Code"
msgstr "HS コード"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__id
msgid "ID"
msgstr "ID"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_route
msgid "Inventory Routes"
msgstr "在庫ルート"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "請求ポリシー"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "返品の引き取り"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Manual actions might be needed."
msgstr "手動操作が必要になる場合があります。"

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "連携配送業者はありません。"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__country_of_origin
msgid "Origin of Goods"
msgstr "商品の原産地"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "梱包"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Package Details"
msgstr "梱包詳細"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
msgid "Package too heavy!"
msgstr "荷物が重すぎます！"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_quant_package
msgid "Packages"
msgstr "梱包"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "ピッキング"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "返品ラベルを印刷"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_product_template
msgid "Product"
msgstr "プロダクト"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "プロダクトの移動(在庫移動ライン)"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__delivery_type
msgid "Provider"
msgstr "プロバイダ"

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "実費"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "返品ラベル"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "返品ピッキング"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__route_ids
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_delivery_carrier_form_inherit_stock_delivery
msgid "Routes"
msgstr "ルート"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,help:stock_delivery.field_product_template__country_of_origin
msgid ""
"Rules of origin determine where goods originate, i.e. not where they have been shipped from, but where they have been produced or manufactured.\n"
"As such, the ‘origin’ is the 'economic nationality' of goods traded in commerce."
msgstr ""
"原産地規則は、商品の原産地、すなわち、出荷元ではなく、どこで生産または製造されたかを決定します。\n"
"そのため、 ‘原産地’ とは、商業的に取引される商品の ‘経済的国籍’ なのです。"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "販売価格"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "販売オーダ明細"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "保存"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "配送業者に送る"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s"
msgstr "配送会社 %(carrier_name)s に配送用に送られた配送、追跡番号%(ref)s"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "配送費用"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "配送情報"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_delivery_carrier
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_location_route_view_form_inherit_stock_delivery
msgid "Shipping Methods"
msgstr "配送方法"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__shipping_weight
msgid "Shipping Weight"
msgstr "荷物重量"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Shipping Weight:"
msgstr "荷物重量:"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,help:stock_delivery.field_product_template__hs_code
msgid "Standardized code for international shipping and goods declaration."
msgstr "国際輸送と貨物申告のための標準コード。"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move
msgid "Stock Move"
msgstr "在庫移動"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_package_type
msgid "Stock package type"
msgstr "在庫梱包タイプ"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_rounding
msgid "Technical field indicating weight's number of decimal places"
msgstr "重量の小数点以下の桁数を示す技術フィールド"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_is_kg
msgid "Technical field indicating whether weight uom is kg or not (i.e. lb)"
msgstr "重量単位がkgかそうでないかを示す技術フィールド(つまりlb)"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISOの国別コードは2文字です。\n"
"これを使ってクイックサーチできます。"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr "梱包を作成できません。ピッキングのプロダクトの合計重量が0.0 %sのためです。 "

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_carrier.py:0
msgid "The shipping price will be set once the delivery is done."
msgstr "配送料金は、配送が完了した時点で設定されます。"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr "荷物の重量が、この梱包タイプで許可されている最大重量を超えています。他の梱包タイプを選んで下さい。"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
msgid "There is no matching delivery rule."
msgstr "一致する配送規則がありません。"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "梱包に含まれる全てのプロダクトの総重量"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "ピッキングのプロダクト合計重量"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "トラッカーURL"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "追跡"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "追跡参照"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "トラッカーURL"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Tracking links for shipment:"
msgstr "配送用追跡リンク"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
msgid "Tracking:"
msgstr "追跡:"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_picking
msgid "Transfer"
msgstr "転送"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "重量"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "荷物重量"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "重量単位ラベル"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Weight:"
msgstr "重量:"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they have different "
"carriers (i.e. check that all of their transfers have a carrier assigned and"
" are using the same carrier)."
msgstr ""
"プロダクトの輸送会社が異なる場合、同じパッケージに梱包することはできません(つまり、全ての運送に配送業者が割当てられており、同じ配送業者を使用していることを確認して下さい）"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr "複数のトラッカーリンクがあり、それらはチャッターで利用できます。"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr "この配送方法は、この注文を追跡するための宅配業者のウェブサイトにリダイレクトされていません。"

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "郵便番号プレフィックス"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDShipping Weight:"
msgstr "^A0N,44,33^FD配送重量:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDWeight:"
msgstr "^A0N,44,33^FD重量:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FO310,200"
msgstr "^FO310,200"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FS"
msgstr "^FS"
