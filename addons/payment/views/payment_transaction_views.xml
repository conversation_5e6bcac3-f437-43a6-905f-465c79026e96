<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_transaction_form" model="ir.ui.view">
        <field name="name">payment.transaction.form</field>
        <field name="model">payment.transaction</field>
        <field name="arch" type="xml">
            <form string="Payment Transactions" create="false" edit="false">
                <header>
                    <button type="object" name="action_capture" invisible="state != 'authorized'" string="Capture Transaction" class="oe_highlight"/>
                    <button type="object" name="action_void" invisible="state != 'authorized'" string="Void Transaction"
                            confirm="Are you sure you want to void the authorized transaction? This action can't be undone."/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_refunds"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-money"
                                invisible="refunds_count == 0">
                            <field name="refunds_count" widget="statinfo" string="Refunds"/>
                        </button>
                    </div>
                    <group>
                        <group name="transaction_details">
                            <field name="reference"/>
                            <field name="source_transaction_id"
                                   invisible="not source_transaction_id"/>
                            <field name="amount"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="payment_method_id"/>
                            <field name="provider_id"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <!-- Used by some provider-specific views -->
                            <field name="provider_code" invisible="1"/>
                            <field name="provider_reference"/>
                            <field name="token_id" invisible="not token_id"/>
                            <field name="create_date"/>
                            <field name="last_state_change"/>
                            <field name="is_post_processed" groups="base.group_no_one"/>
                        </group>
                        <group name="transaction_partner">
                            <field name="partner_id" widget="res_partner_many2one"/>
                            <label for="partner_address" string="Address"/>
                            <div class="o_address_format">
                                <field name="partner_address" placeholder="Address" class="o_address_street"/>
                                <field name="partner_city" placeholder="City" class="o_address_city"/>
                                <field name="partner_state_id" placeholder="State" class="o_address_state" options="{'no_open': True}"/>
                                <field name="partner_zip" placeholder="ZIP" class="o_address_zip"/>
                                <field name="partner_country_id" placeholder="Country" class="o_address_country" options="{'no_open': True}"/>
                            </div>
                            <field name="partner_email" widget="email"/>
                            <field name="partner_phone" widget="phone"/>
                            <field name="partner_lang"/>
                        </group>
                    </group>
                    <separator string="Child transactions" invisible="not child_transaction_ids"/>
                    <field name="child_transaction_ids" invisible="not child_transaction_ids"/>
                    <group string="Message" invisible="not state_message">
                        <field name="state_message" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="payment_transaction_list" model="ir.ui.view">
        <field name="name">payment.transaction.list</field>
        <field name="model">payment.transaction</field>
        <field name="arch" type="xml">
            <list string="Payment Transactions" create="false">
                <field name="reference"/>
                <field name="create_date"/>
                <field name="payment_method_id"/>
                <field name="provider_id"/>
                <field name="partner_id"/>
                <field name="partner_name"/>
                <!-- Needed to display the currency of the amounts -->
                <field name="currency_id" column_invisible="True"/>
                <field name="amount"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company" optional="show"/>
            </list>
        </field>
    </record>

    <record id="payment_transaction_kanban" model="ir.ui.view">
        <field name="name">payment.transaction.kanban</field>
        <field name="model">payment.transaction</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" create="false">
                <field name="currency_id"/>
                <templates>
                    <t t-name="card">
                        <div class="d-flex">
                            <field name="reference" class="fw-bolder"/>
                            <field name="amount" class="ms-auto"/>
                        </div>
                        <field name="partner_name"/>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="payment_transaction_search" model="ir.ui.view">
        <field name="name">payment.transaction.search</field>
        <field name="model">payment.transaction</field>
        <field name="arch" type="xml">
            <search>
                <field name="reference"/>
                <field name="provider_id"/>
                <field name="partner_id"/>
                <field name="partner_name"/>
                <group expand="1" string="Group By">
                    <filter string="Provider" name="provider_id" context="{'group_by': 'provider_id'}"/>
                    <filter string="Partner" name="partner_id" context="{'group_by': 'partner_id'}"/>
                    <filter string="Status" name="state" context="{'group_by': 'state'}"/>
                    <filter string="Company" name="company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_payment_transaction" model="ir.actions.act_window">
        <field name="name">Payment Transactions</field>
        <field name="res_model">payment.transaction</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_neutral_face">
                There are no transactions to show
            </p>
        </field>
    </record>

    <record id="action_payment_transaction_linked_to_token" model="ir.actions.act_window">
        <field name="name">Payment Transactions Linked To Token</field>
        <field name="res_model">payment.transaction</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('token_id','=', active_id)]</field>
        <field name="context">{'create': False}</field>
    </record>

</odoo>
