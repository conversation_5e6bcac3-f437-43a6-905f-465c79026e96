# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Tanggal Diambil"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "(ID:"
msgstr "(ID:"

#. module: payment
#: model:payment.method,name:payment.payment_method_7eleven
msgid "7Eleven"
msgstr "7Eleven"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %(bank)s</li><li>Account "
"Number: %(account_number)s</li><li>Account Holder: "
"%(account_holder)s</li></ul>"
msgstr ""
"<h3>Mohon buat pembayaran ke: </h3><ul><li>Bank: %(bank)s</li><li>Nomor "
"Akun: %(account_number)s</li><li>Pemilik Akun: %(account_holder)s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> These properties are set to\n"
"                                match the behavior of providers and that of their integration with\n"
"                                Odoo regarding this payment method. Any change may result in errors\n"
"                                and should be tested on a test database first."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>Properti-properti ditetapkan agar\n"
"                                cocok dengan perilaku penyedia dan integrasi mereka dengan\n"
"                                Odoo mengenai metode pembayaran ini. Perubahan apapun dapat menghasilkan error\n"
"                                dan harus diperiksa pada database testing terlebih dahulu."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Some of the transactions you intend to capture can"
" only be captured in full. Handle the transactions individually to capture a"
" partial amount.\"/>"
msgstr ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Beberapa transaksi yang Anda ingin ambil hanya "
"dapat diambil secara penuh. Kelola transaksi secara individu untuk mengambil"
" hanya jumlah parsial.\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid ""
"<i class=\"fa fa-trash\" title=\"Delete payment method\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"
msgstr ""
"<i class=\"fa fa-trash\" title=\"Hapus metode pembayaran\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "<i class=\"oi oi-arrow-right me-1\"></i> Configure a payment provider"
msgstr ""
"<i class=\"oi oi-arrow-right me-1\"></i> Konfigurasikan penyedia pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Enable Payment Methods"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Aktifkan Metode Pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
msgid "<small class=\"text-600\">Save my payment details</small>"
msgstr "<small class=\"text-600\">Simpan detail pembayaran saya</small>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-danger\">Unpublished</span>"
msgstr "<span class=\"o_stat_text text-danger\">Belum diterbitka</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-success\">Published</span>"
msgstr "<span class=\"o_stat_text text-success\">Diterbitkan</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">Metode Pembayaran Tersimpan</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                All countries are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                Semua negara didukung.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                All currencies are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                Semua mata uang didukung.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid "<span><i class=\"fa fa-lock\"/> Secured by</span>"
msgstr "<span><i class=\"fa fa-lock\"/> Diamankan oleh</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"fa fa-file-text\"/> Show availability report</strong>"
msgstr ""
"<strong><i class=\"fa fa-file-text\"/> Tunjukkan laporan "
"ketersediaan</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Methods</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> Metode Pembayaran</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Providers</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> Penyedia Pembayaran</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong>No payment method available</strong>"
msgstr "<strong>Tidak ada metode pembayaran yang tersedia</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a partial capture pending. Please wait a\n"
"                    moment for it to be processed. Check your payment provider configuration if\n"
"                    the capture is still pending after a few minutes."
msgstr ""
"<strong>Peringatan!</strong> Terdapat capture parsial yang pending. Mohon tunggu sebentar\n"
"                    untuk memproses capture tersebut. Periksa konfigurasi penyedia pembayaran Anda bila\n"
"                    capture masih pending setelah beberapa menit."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> You can not capture a negative amount nor more\n"
"                    than"
msgstr ""
"<strong>Peringatan!</strong> Anda tidak dapat capture jumlah yang negatif atau yang lebih\n"
"                    dari"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<strong>Warning</strong> Creating a payment provider from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>Peringatan</strong> Membuat penyedia pembayaran dari tombol <em>BUAT</em> tidak dapat dilakukan.\n"
"                        Mohon alih-alih gunakan action <em>Duplikat</em>."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the\n"
"                                    correct partner before making this payment."
msgstr ""
"<strong>Peringatan</strong> Pastikan Anda sudah login sebagai\n"
"                                    partner yang benar sebelum melakukan pembayaran ini."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Peringatan</strong> Mata uang ini hilang atau tidak benar."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>Peringatan</strong> Anda harus log in untuk membayar."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(provider_name)s)."
msgstr ""
"Permintaan refund sejumlah %(amount)s telah dikirim. Pembayaran akan dibuat "
"sebentar lagi. Referensi transaksi refund: %(ref)s (%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated (%(provider_name)s)."
msgstr "Transaksi dengan referensi %(ref)s telah dimulai (%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated to save a new "
"payment method (%(provider_name)s)"
msgstr ""
"Transaksi dengan referensi %(ref)s telah dimulai untuk menyimpan metode "
"pembayaran baru (%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token)s (%(provider_name)s)."
msgstr ""
"Transaksi dengan referensi %(ref)s telah dimulai menggunakan metode "
"pembayaran %(token)s (%(provider_name)s)."

#. module: payment
#: model:payment.method,name:payment.payment_method_ach_direct_debit
msgid "ACH Direct Debit"
msgstr "ACH Direct Debit"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "ACTIVATE STRIPE"
msgstr "AKTIFKAN STRIPE"

#. module: payment
#: model:payment.method,name:payment.payment_method_abitab
msgid "Abitab"
msgstr "Abitab"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Account"
msgstr "Akun"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Nomor Rekening"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Activate"
msgstr "Aktifkan"

#. module: payment
#: model:ir.actions.server,name:payment.action_activate_stripe
msgid "Activate Stripe"
msgstr "Aktifkan Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__active
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Aktif"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "Alamat"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.method,name:payment.payment_method_affirm
msgid "Affirm"
msgstr "Affirm"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay_riverty
msgid "AfterPay"
msgstr "AfterPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay
msgid "Afterpay"
msgstr "Afterpay"

#. module: payment
#: model:payment.method,name:payment.payment_method_akulaku
msgid "Akulaku PayLater"
msgstr "Akulaku PayLater"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_hk
msgid "AliPayHK"
msgstr "AliPayHK"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_plus
msgid "Alipay+"
msgstr "Alipay+"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_express_checkout
msgid "Allow Express Checkout"
msgstr "Izinkan Express Checkout"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "Izinkan Menyimpan Metode Pembayaran"

#. module: payment
#: model:payment.method,name:payment.payment_method_alma
msgid "Alma"
msgstr "Alma"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__captured_amount
msgid "Already Captured"
msgstr "Sudah Di-capture"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__voided_amount
msgid "Already Voided"
msgstr "Sudah Dibatalkan"

#. module: payment
#: model:payment.method,name:payment.payment_method_amazon_pay
msgid "Amazon Pay"
msgstr "Amazon Pay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_aps
msgid "Amazon Payment Services"
msgstr "Layanan Amazon Payment"

#. module: payment
#: model:payment.method,name:payment.payment_method_amex
msgid "American Express"
msgstr "American Express"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Amount"
msgstr "Jumlah"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Jumlah Maksimum"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__amount_to_capture
msgid "Amount To Capture"
msgstr "Jumlah Untuk Di-capture"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred during the processing of your payment."
msgstr "Error terjadi saat memproses pembayaran Anda."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred while saving your payment method."
msgstr "Terjadi error selagi menyimpan metode pembayaran Anda."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Apply"
msgstr "Terapkan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "Are you sure you want to delete this payment method?"
msgstr "Apakah Anda yakin ingin menghapus metode pembayaran ini?"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Apakah Anda yakin ingin membatalkan transaksi yang sudah diotorisasi? "
"Tindakan ini tidak dapat dibatalkan."

#. module: payment
#: model:payment.method,name:payment.payment_method_argencard
msgid "Argencard"
msgstr "Argencard"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_asiapay
msgid "Asiapay"
msgstr "Asiapay"

#. module: payment
#: model:payment.method,name:payment.payment_method_astropay
msgid "Astropay TEF"
msgstr "Astropay TEF"

#. module: payment
#: model:payment.method,name:payment.payment_method_atome
msgid "Atome"
msgstr "Atome"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__auth_msg
msgid "Authorize Message"
msgstr "Otorisasikan Pesan"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Diotorisasi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__authorized_amount
msgid "Authorized Amount"
msgstr "Jumlah yang Diotorisasi"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Availability"
msgstr "Ketersediaan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Availability report"
msgstr "Laporan ketersediaan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
msgid "Available methods"
msgstr "Metode-metode yang tersedia"

#. module: payment
#: model:payment.method,name:payment.payment_method_axis
msgid "Axis"
msgstr "Axis"

#. module: payment
#: model:payment.method,name:payment.payment_method_bacs_direct_debit
msgid "BACS Direct Debit"
msgstr "BACS Direct Debit"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancomat_pay
msgid "BANCOMAT Pay"
msgstr "BANCOMAT Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_bca
msgid "BCA"
msgstr "BCA"

#. module: payment
#: model:payment.method,name:payment.payment_method_becs_direct_debit
msgid "BECS Direct Debit"
msgstr "BECS Direct Debit"

#. module: payment
#: model:payment.method,name:payment.payment_method_blik
msgid "BLIK"
msgstr "BLIK"

#. module: payment
#: model:payment.method,name:payment.payment_method_brankas
msgid "BRANKAS"
msgstr "BRANKAS"

#. module: payment
#: model:payment.method,name:payment.payment_method_bri
msgid "BRI"
msgstr "BRI"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancnet
msgid "BancNet"
msgstr "BancNet"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_guayaquil
msgid "Banco Guayaquil"
msgstr "Banco Guayaquil"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_pichincha
msgid "Banco Pichincha"
msgstr "Banco Pichincha"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_de_bogota
msgid "Banco de Bogota"
msgstr "Banco de Bogota"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancolombia
msgid "Bancolombia"
msgstr "Bancolombia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancontact
msgid "Bancontact"
msgstr "Bancontact"

#. module: payment
#: model:payment.method,name:payment.payment_method_bangkok_bank
msgid "Bangkok Bank"
msgstr "Bangkok Bank"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Bank"
msgstr "Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_account
msgid "Bank Account"
msgstr "Akun Bank"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nama Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_bni
msgid "Bank Negara Indonesia"
msgstr "Bank Negara Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_permata
msgid "Bank Permata"
msgstr "Bank Permata"

#. module: payment
#: model:payment.method,name:payment.payment_method_bsi
msgid "Bank Syariah Indonesia"
msgstr "Bank Syariah Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_transfer
msgid "Bank Transfer"
msgstr "Transfer Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_of_ayudhya
msgid "Bank of Ayudhya"
msgstr "Bank of Ayudhya"

#. module: payment
#: model:payment.method,name:payment.payment_method_bpi
msgid "Bank of the Philippine Islands"
msgstr "Bank of the Philippine Islands"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_reference
msgid "Bank reference"
msgstr "Referensi bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_belfius
msgid "Belfius"
msgstr "Belfius"

#. module: payment
#: model:payment.method,name:payment.payment_method_benefit
msgid "Benefit"
msgstr "Manfaat"

#. module: payment
#: model:payment.method,name:payment.payment_method_bharatqr
msgid "BharatQR"
msgstr "BharatQR"

#. module: payment
#: model:payment.method,name:payment.payment_method_billease
msgid "BillEase"
msgstr "BillEase"

#. module: payment
#: model:payment.method,name:payment.payment_method_billink
msgid "Billink"
msgstr "Billink"

#. module: payment
#: model:payment.method,name:payment.payment_method_bizum
msgid "Bizum"
msgstr "Bizum"

#. module: payment
#: model:payment.method,name:payment.payment_method_boleto
msgid "Boleto"
msgstr "Boleto"

#. module: payment
#: model:payment.method,name:payment.payment_method_boost
msgid "Boost"
msgstr "Boost"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__brand_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Brands"
msgstr "Brand"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cimb_niaga
msgid "CIMB Niaga"
msgstr "CIMB Niaga"

#. module: payment
#: model:payment.method,name:payment.payment_method_cmr
msgid "CMR"
msgstr "CMR"

#. module: payment
#: model:payment.method,name:payment.payment_method_cabal
msgid "Cabal"
msgstr "Cabal"

#. module: payment
#: model:payment.method,name:payment.payment_method_caixa
msgid "Caixa"
msgstr "Caixa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Cancel"
msgstr "Batal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Dibatalkan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__cancel_msg
msgid "Cancelled Message"
msgstr "Pesan Dibatalkan"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot delete payment method"
msgstr "Tidak dapat menghapus metode pembayaran"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot save payment method"
msgstr "Tidak dapat menyimpan metode pembayaran"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid "Capture"
msgstr "Capture"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__capture_manually
msgid "Capture Amount Manually"
msgstr "Capture Jumlah secara Manual"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "Cetak Transaksi"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Capture jumlah dari Odoo, saat pengiriman selesai.\n"
"Gunakan ini bila Anda hanya ingin menagih kartu pelanggan Anda saat\n"
"Anda yakin Anda dapat mengirim barang ke mereka."

#. module: payment
#: model:payment.method,name:payment.payment_method_card
msgid "Card"
msgstr "Kartu"

#. module: payment
#: model:payment.method,name:payment.payment_method_carnet
msgid "Carnet"
msgstr "Carnet"

#. module: payment
#: model:payment.method,name:payment.payment_method_cartes_bancaires
msgid "Cartes Bancaires"
msgstr "Cartes Bancaires"

#. module: payment
#: model:payment.method,name:payment.payment_method_cash_app_pay
msgid "Cash App Pay"
msgstr "Cash App Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_cashalo
msgid "Cashalo"
msgstr "Cashalo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cebuana
msgid "Cebuana"
msgstr "Cebuana"

#. module: payment
#: model:payment.method,name:payment.payment_method_cencosud
msgid "Cencosud"
msgstr "Cencosud"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__child_transaction_ids
msgid "Child Transactions"
msgstr "Child Transactions"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Child transactions"
msgstr "Child transactions"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Pilih metode pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Choose another method <i class=\"oi oi-arrow-down\"/>"
msgstr "Pilih metode lain <i class=\"oi oi-arrow-down\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_cirrus
msgid "Cirrus"
msgstr "Cirrus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "Kota"

#. module: payment
#: model:payment.method,name:payment.payment_method_clearpay
msgid "Clearpay"
msgstr "Clearpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "Tutup"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__code
#: model:ir.model.fields,field_description:payment.field_payment_provider__code
msgid "Code"
msgstr "Kode"

#. module: payment
#: model:payment.method,name:payment.payment_method_codensa
msgid "Codensa"
msgstr "Codensa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__color
msgid "Color"
msgstr "Warna"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "Perusahaan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Configuration"
msgstr "Konfigurasi"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Confirm Deletion"
msgstr "Konfirmasi Penghapusan"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "Dikonfirmasi"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordial
msgid "Cordial"
msgstr "Cordial"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordobesa
msgid "Cordobesa"
msgstr "Cordobesa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_id
msgid "Corresponding Module"
msgstr "Modul Terkait"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_country_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_country_ids
msgid "Countries"
msgstr "Negara"

#. module: payment
#: model:ir.model,name:payment.model_res_country
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "Negara"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "Buat Token"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_provider
msgid "Create a new payment provider"
msgstr "Buat penyedia pembayaran baru"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_method__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Creating a transaction from an archived token is forbidden."
msgstr "Dilarang membuat transaksi dari token yang diarsip."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Credentials"
msgstr "Kredensial"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__stripe
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "Kartu Kredit & Debit (via Stripe)"

#. module: payment
#: model:payment.method,name:payment.payment_method_credit
msgid "Credit Payment"
msgstr "Credit Payment"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_currency_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_currency_ids
msgid "Currencies"
msgstr "Mata Uang"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__main_currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__manual
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Instruksi pembayaran custom"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Pelanggan"

#. module: payment
#: model:payment.method,name:payment.payment_method_dana
msgid "Dana"
msgstr "Dana"

#. module: payment
#: model:payment.method,name:payment.payment_method_dankort
msgid "Dankort"
msgstr "Dankort"

#. module: payment
#: model:payment.method,name:payment.payment_method_davivienda
msgid "Davivienda"
msgstr "Davivienda"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__sequence
msgid "Define the display order"
msgstr "Tentukan urutan tampilan"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_demo
msgid "Demo"
msgstr "Demo"

#. module: payment
#: model:payment.method,name:payment.payment_method_diners
msgid "Diners Club International"
msgstr "Diners Club International"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Disabled"
msgstr "Dinonaktifkan"

#. module: payment
#: model:payment.method,name:payment.payment_method_discover
msgid "Discover"
msgstr "Temukan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_method__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: payment
#: model:payment.method,name:payment.payment_method_dolfin
msgid "Dolfin"
msgstr "Dolfin"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Don't hesitate to contact us if you don't receive it."
msgstr "Jangan ragu untuk menghubungi kami bila Anda tidak menerimanya."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__done_msg
msgid "Done Message"
msgstr "Pesan Selesai"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Draft"

#. module: payment
#: model:payment.method,name:payment.payment_method_duitnow
msgid "DuitNow"
msgstr "DuitNow"

#. module: payment
#: model:payment.method,name:payment.payment_method_emi_india
msgid "EMI"
msgstr "EMI"

#. module: payment
#: model:payment.method,name:payment.payment_method_eps
msgid "EPS"
msgstr "EPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_elo
msgid "Elo"
msgstr "Elo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Email"
msgstr "Email"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__enabled
msgid "Enabled"
msgstr "Diaktifkan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Enterprise"
msgstr "Enterprise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Error!"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Error: %s"
msgstr "Error: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_express_checkout
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_express_checkout
msgid "Express Checkout"
msgstr "Express Checkout"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__express_checkout_form_view_id
msgid "Express Checkout Form Template"
msgstr "Templat Formulir Express Checkout"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_express_checkout
msgid ""
"Express checkout allows customers to pay faster by using a payment method "
"that provides all required billing and shipping information, thus allowing "
"to skip the checkout process."
msgstr ""
"Checkout express memungkinkan pelanggan untuk membayar lebih cepat dengan "
"menggunakan metode pembayaran yang menyediakan semua informasi billing dan "
"pengiriman yang dibutuhkan, sehingga memungkinkan untuk melewati proses "
"checkout."

#. module: payment
#: model:payment.method,name:payment.payment_method_fps
msgid "FPS"
msgstr "FPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_fpx
msgid "FPX"
msgstr "FPX"

#. module: payment
#: model:payment.method,name:payment.payment_method_facilito
msgid "Facilito"
msgstr "Facilito"

#. module: payment
#: model:payment.method,name:payment.payment_method_floa_bank
msgid "Floa Bank"
msgstr "Floa Bank"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment
#: model:payment.method,name:payment.payment_method_frafinance
msgid "Frafinance"
msgstr "Frafinance"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__partial
msgid "Full & Partial"
msgstr "Penuh & Parsial"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__full_only
msgid "Full Only"
msgstr "Hanya Penuh"

#. module: payment
#: model:payment.method,name:payment.payment_method_gcash
msgid "GCash"
msgstr "GCash"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Buat Link Pembayaran"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Buat Link Pembayaran Penjualan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate and Copy Payment Link"
msgstr "Buat dan Salin Link Pembayaran"

#. module: payment
#: model:payment.method,name:payment.payment_method_giropay
msgid "Giropay"
msgstr "Giropay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Go to my Account <i class=\"oi oi-arrow-right ms-2\"/>"
msgstr "Pergi ke Akun saya <i class=\"oi oi-arrow-right ms-2\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_gopay
msgid "GoPay"
msgstr "GoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_gsb
msgid "Government Savings Bank"
msgstr "Government Savings Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_grabpay
msgid "GrabPay"
msgstr "GrabPay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: payment
#: model:payment.method,name:payment.payment_method_hd
msgid "HD Bank"
msgstr "HD Bank"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_draft_children
msgid "Has Draft Children"
msgstr "MemilikiDraft Children"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_remaining_amount
msgid "Has Remaining Amount"
msgstr "Memiliki Jumlah Tersisa"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "Apakah pembayaran sudah di post-process"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pre_msg
msgid "Help Message"
msgstr "Pesan Bantuan"

#. module: payment
#: model:payment.method,name:payment.payment_method_hipercard
msgid "Hipercard"
msgstr "Hipercard"

#. module: payment
#: model:payment.method,name:payment.payment_method_hoolah
msgid "Hoolah"
msgstr "Hoolah"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "How to configure your PayPal account"
msgstr "Bagaimana cara mengonfigurasi akun PayPal Anda"

#. module: payment
#: model:payment.method,name:payment.payment_method_humm
msgid "Humm"
msgstr "Humm"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_method__id
#: model:ir.model.fields,field_description:payment.field_payment_provider__id
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "Jika Anda merasa ini error, silakan hubungi administrator website."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image
#: model:ir.model.fields,field_description:payment.field_payment_provider__image_128
msgid "Image"
msgstr "Gambar"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"Pada mode testing, pembayaran palsu akan diproses melalui antarmuka pembayaran testing.\n"
"Mode ini disarankan saat sedang set up provider."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__inline_form_view_id
msgid "Inline Form Template"
msgstr "Templat Form Inline"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Install"
msgstr "Pasang"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_state
msgid "Installation State"
msgstr "Status Penginstalan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "Installed"
msgstr "Terpasang"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__is_amount_to_capture_valid
msgid "Is Amount To Capture Valid"
msgstr "Apakah Jumlah Untuk Di-capture Valid"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "Apakah di Post-Process"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__is_primary
msgid "Is Primary Payment Method"
msgstr "Apakah Metode Pembayaran Utama"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "Adalah Negara Yang Mendukung Stripe"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "It is currently linked to the following documents:"
msgstr "Saat ini di-link ke dokumen-dokumen berikut:"

#. module: payment
#: model:payment.method,name:payment.payment_method_jcb
msgid "JCB"
msgstr "JCB"

#. module: payment
#: model:payment.method,name:payment.payment_method_jeniuspay
msgid "JeniusPay"
msgstr "JeniusPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_jkopay
msgid "Jkopay"
msgstr "Jkopay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kbc_cbc
msgid "KBC/CBC"
msgstr "KBC/CBC"

#. module: payment
#: model:payment.method,name:payment.payment_method_knet
msgid "KNET"
msgstr "KNET"

#. module: payment
#: model:payment.method,name:payment.payment_method_kakaopay
msgid "KakaoPay"
msgstr "KakaoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kasikorn_bank
msgid "Kasikorn Bank"
msgstr "Kasikorn Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna
msgid "Klarna"
msgstr "Klarna"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_paynow
msgid "Klarna - Pay Now"
msgstr "Klarna - Pay Now"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_pay_over_time
msgid "Klarna - Pay over time"
msgstr "Klarna - Pay over time"

#. module: payment
#: model:payment.method,name:payment.payment_method_kredivo
msgid "Kredivo"
msgstr "Kredivo"

#. module: payment
#: model:payment.method,name:payment.payment_method_krungthai_bank
msgid "KrungThai Bank"
msgstr "KrungThai Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_linepay
msgid "LINE Pay"
msgstr "LINE Pay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "Landing Route"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Bahasa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "Tanggal Perubahan Status Terakhir"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_method__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: payment
#: model:onboarding.onboarding.step,button_text:payment.onboarding_onboarding_step_payment_provider
msgid "Let's do it"
msgstr "Ayo lakukan"

#. module: payment
#: model:payment.method,name:payment.payment_method_lider
msgid "Lider"
msgstr "Lider"

#. module: payment
#: model:payment.method,name:payment.payment_method_linkaja
msgid "LinkAja"
msgstr "LinkAja"

#. module: payment
#: model:payment.method,name:payment.payment_method_nuvei_local
msgid "Local Payments"
msgstr "Pembayaran Lokal"

#. module: payment
#: model:payment.method,name:payment.payment_method_lydia
msgid "Lydia"
msgstr "Lydia"

#. module: payment
#: model:payment.method,name:payment.payment_method_lyfpay
msgid "LyfPay"
msgstr "LyfPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_mpesa
msgid "M-Pesa"
msgstr "M-Pesa"

#. module: payment
#: model:payment.method,name:payment.payment_method_mbway
msgid "MB WAY"
msgstr "MB WAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_mada
msgid "Mada"
msgstr "Mada"

#. module: payment
#: model:payment.method,name:payment.payment_method_maestro
msgid "Maestro"
msgstr "Maestro"

#. module: payment
#: model:payment.method,name:payment.payment_method_magna
msgid "Magna"
msgstr "Magna"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Making a request to the provider is not possible because the provider is "
"disabled."
msgstr ""
"Membuat permintaan ke provider tidak dapat dilakukan karena provider "
"dinonaktifkan."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Manage your payment methods"
msgstr "Kelola metode pembayaran Anda"

#. module: payment
#: model:payment.method,name:payment.payment_method_mandiri
msgid "Mandiri"
msgstr "Mandiri"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Manual"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_manual_capture
msgid "Manual Capture Supported"
msgstr "Manual Capture Didukung"

#. module: payment
#: model:payment.method,name:payment.payment_method_mastercard
msgid "MasterCard"
msgstr "MasterCard"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__maximum_amount
msgid "Maximum Amount"
msgstr "Jumlah Maksimum"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__available_amount
msgid "Maximum Capture Allowed"
msgstr "Jumlah Maksimum Capture yang Diizinkan"

#. module: payment
#: model:payment.method,name:payment.payment_method_maya
msgid "Maya"
msgstr "Maya"

#. module: payment
#: model:payment.method,name:payment.payment_method_maybank
msgid "Maybank"
msgstr "Maybank"

#. module: payment
#: model:payment.method,name:payment.payment_method_meeza
msgid "Meeza"
msgstr "Meeza"

#. module: payment
#: model:payment.method,name:payment.payment_method_mercado_livre
msgid "Mercado Livre"
msgstr "Mercado Livre"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mercado_pago
msgid "Mercado Pago"
msgstr "Mercado Pago"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "Pesan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Messages"
msgstr "Pesan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Metode"

#. module: payment
#: model:payment.method,name:payment.payment_method_momo
msgid "MoMo"
msgstr "MoMo"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_money
msgid "Mobile money"
msgstr "Mobile money"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_pay
msgid "MobilePay"
msgstr "MobilePay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#: model:payment.method,name:payment.payment_method_multibanco
msgid "Multibanco"
msgstr "Multibanco"

#. module: payment
#: model:payment.method,name:payment.payment_method_mybank
msgid "MyBank"
msgstr "MyBank"

#. module: payment
#: model:payment.method,name:payment.payment_method_naps
msgid "NAPS"
msgstr "NAPS"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__name
#: model:ir.model.fields,field_description:payment.field_payment_provider__name
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Name"
msgstr "Nama"

#. module: payment
#: model:payment.method,name:payment.payment_method_napas_card
msgid "Napas Card"
msgstr "Napas Card"

#. module: payment
#: model:payment.method,name:payment.payment_method_naranja
msgid "Naranja"
msgstr "Naranja"

#. module: payment
#: model:payment.method,name:payment.payment_method_nativa
msgid "Nativa"
msgstr "Nativa"

#. module: payment
#: model:payment.method,name:payment.payment_method_naver_pay
msgid "Naver Pay"
msgstr "Naver Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_netbanking
msgid "Netbanking"
msgstr "Netbanking"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__code__none
msgid "No Provider Set"
msgstr "Tidak Ada Provider yang Ditetapkan"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Provider menu."
msgstr ""
"Tidak ada metode pembayaran manual yang dapat ditemukan untuk perusahaan "
"ini. Mohon buat satu metode dari menu Penyedia Pembayaran."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "No payment methods found for your payment providers."
msgstr ""
"Tidak ada metode pembayaran yang ditemukan untuk penyedia pembayaran Anda."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "No payment providers are configured."
msgstr "Tidak ada penyedia pembayaran yang dikonfigurasi."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "No token can be assigned to the public partner."
msgstr "Tidak ada token yang dapat ditetapkan ke mitra publik."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "None is configured for:"
msgstr "Tidak ada yang dikonfigurasikan untuk:"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_nuvei
msgid "Nuvei"
msgstr "Nuvei"

#. module: payment
#: model:payment.method,name:payment.payment_method_ovo
msgid "OVO"
msgstr "OVO"

#. module: payment
#: model:payment.method,name:payment.payment_method_oca
msgid "Oca"
msgstr "Oca"

#. module: payment
#: model:payment.method,name:payment.payment_method_octopus
msgid "Octopus"
msgstr "Octopus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Modul Odoo Enterprise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "Pembayaran offline menggunakan token"

#. module: payment
#: model:payment.method,name:payment.payment_method_omannet
msgid "OmanNet"
msgstr "OmanNet"

#. module: payment
#: model:ir.model,name:payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Langkah Onboarding"

#. module: payment
#: model:onboarding.onboarding.step,step_image_alt:payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Step Image"
msgstr "Gambar Langkah Onboarding"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_czech_republic
msgid "Online Banking Czech Republic"
msgstr "Online Banking Czech Republic"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_india
msgid "Online Banking India"
msgstr "Online Banking India"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_slovakia
msgid "Online Banking Slovakia"
msgstr "Online Banking Slovakia"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_thailand
msgid "Online Banking Thailand"
msgstr "Online Banking Thailand"

#. module: payment
#: model:onboarding.onboarding.step,title:payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "Pembayaran Online"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "Pembayaran langsung online"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "Pembayaran online menggunakan token"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "Pembayaran online dengan pengalihan"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Only administrators can access this data."
msgstr "Hanya administrator yang dapat mengakses data ini."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only authorized transactions can be voided."
msgstr "Hanya transaksi yang diotorisasi yang dapat dibatalkan."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only confirmed transactions can be refunded."
msgstr "Hanya transaksi yang dikonfirmasi yang dapat di-refund."

#. module: payment
#: model:payment.method,name:payment.payment_method_open_banking
msgid "Open banking"
msgstr "Open banking"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "Operasi"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Operation not supported."
msgstr "Operasi tidak didukung."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Lainnya"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Other payment methods"
msgstr "Metode pembayaran lainnya"

#. module: payment
#: model:payment.method,name:payment.payment_method_oxxopay
msgid "Oxxo Pay"
msgstr "Oxxo Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_p24
msgid "P24"
msgstr "P24"

#. module: payment
#: model:payment.method,name:payment.payment_method_poli
msgid "POLi"
msgstr "POLi"

#. module: payment
#: model:payment.method,name:payment.payment_method_pps
msgid "PPS"
msgstr "PPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_pse
msgid "PSE"
msgstr "PSE"

#. module: payment
#: model:payment.method,name:payment.payment_method_pace
msgid "Pace."
msgstr "Pace."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__partial
msgid "Partial"
msgstr "Sebagian"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "Rekanan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Nama Rekanan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Pay"
msgstr "Bayar"

#. module: payment
#: model:payment.method,name:payment.payment_method_paylater_india
msgid "Pay Later"
msgstr "Pay Later"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_easy
msgid "Pay-easy"
msgstr "Pay-easy"

#. module: payment
#: model:payment.method,name:payment.payment_method_paybright
msgid "PayBright"
msgstr "PayBright"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_id
msgid "PayID"
msgstr "PayID"

#. module: payment
#: model:payment.method,name:payment.payment_method_payme
msgid "PayMe"
msgstr "PayMe"

#. module: payment
#: model:payment.method,name:payment.payment_method_paynow
msgid "PayNow"
msgstr "PayNow"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
#: model:payment.provider,name:payment.payment_provider_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypay
msgid "PayPay"
msgstr "PayPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_paysafecard
msgid "PaySafeCard"
msgstr "PaySafeCard"

#. module: payment
#: model:payment.method,name:payment.payment_method_payu
msgid "PayU"
msgstr "PayU"

#. module: payment
#: model:payment.method,name:payment.payment_method_paylib
msgid "Paylib"
msgstr "Paylib"

#. module: payment
#: model:ir.model,name:payment.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "Wizard Capture Pembayaran"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_details
msgid "Payment Details"
msgstr "Detail Pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Followup"
msgstr "Followup Pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Form"
msgstr "Formulir Pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Payment Info"
msgstr "Payment Info"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Instruksi Pembayaran"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Link Pembayaran"

#. module: payment
#: model:ir.model,name:payment.model_payment_method
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__payment_method
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Payment Method"
msgstr "Metode Pembayaran"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_code
msgid "Payment Method Code"
msgstr "Kode Metode Pembayaran"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model:ir.actions.act_window,name:payment.action_payment_method
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Methods"
msgstr "Metode-Metode Pembayaran"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider
msgid "Payment Provider"
msgstr "Penyedia Pembayaran"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_provider
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_list
msgid "Payment Providers"
msgstr "Penyedia Pembayaran"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "Token Pembayaran"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "Jumlah Token Pembayaran"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "Token Pembayaran"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaksi Tagihan"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "Transaksi Tagihan"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "Transaksi Pembayaran yang di-Link ke Token"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "Payment details saved on %(date)s"
msgstr "Detail pembayaran disimpan pada %(date)s"

#. module: payment
#: model:payment.method,name:payment.payment_method_unknown
msgid "Payment method"
msgstr "Metode pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Payment methods"
msgstr "Metode pembayaran"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Pemrosesan pembayaran gagal"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment provider"
msgstr "Penyedia pembayaran"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider_onboarding_wizard
msgid "Payment provider onboarding wizard"
msgstr "Wizard onboarding penyedia pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Payment providers"
msgstr "Penyedia pembayaran"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
msgid "Payment: Post-process transactions"
msgstr "Pembayaran: Transaksi post-process"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "Pembayaran"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytm
msgid "Paytm"
msgstr "Paytm"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytrail
msgid "Paytrail"
msgstr "Paytrail"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "Ditunda"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pending_msg
msgid "Pending Message"
msgstr "Pesan Tertunda"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Telepon"

#. module: payment
#: model:payment.method,name:payment.payment_method_pix
msgid "Pix"
msgstr "Pix"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Please make sure that %(payment_method)s is supported by %(provider)s."
msgstr "Pastikan bahwa %(payment_method)s didukung oleh %(provider)s."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set a positive amount."
msgstr "Mohon tetapkan jumlah positif."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set an amount lower than %s."
msgstr "Mohon tetapkan jumlah yang lebih sedikit dari %s."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid "Please switch to company"
msgstr "Mohon ganti ke perusahaan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Please wait..."
msgstr "Mohon menunggu..."

#. module: payment
#: model:payment.method,name:payment.payment_method_post_finance
msgid "PostFinance Pay"
msgstr "PostFinance Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_poste_pay
msgid "PostePay"
msgstr "PostePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_presto
msgid "Presto"
msgstr "Presto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__primary_payment_method_id
msgid "Primary Payment Method"
msgstr "Metode Pembayaran Utama"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Diproses oleh"

#. module: payment
#: model:payment.method,name:payment.payment_method_promptpay
msgid "Prompt Pay"
msgstr "Prompt Pay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Provider"
msgstr "Pemberi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_code
msgid "Provider Code"
msgstr "Kode Penyedia"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_reference
msgid "Provider Reference"
msgstr "Referensi Provider"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__provider_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Providers"
msgstr "Penyedia"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__is_published
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Published"
msgstr "Terpublikasi"

#. module: payment
#: model:payment.method,name:payment.payment_method_qris
msgid "QRIS"
msgstr "QRIS"

#. module: payment
#: model:payment.method,name:payment.payment_method_rabbit_line_pay
msgid "Rabbit LINE Pay"
msgstr "Rabbit LINE Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_ratepay
msgid "Ratepay"
msgstr "Ratepay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_razorpay
msgid "Razorpay"
msgstr "Razorpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Reason:"
msgstr "Alasan"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Reason: %s"
msgstr "Alasan: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "Templat Formulir Pengalihan"

#. module: payment
#: model:payment.method,name:payment.payment_method_redpagos
msgid "Redpagos"
msgstr "Redpagos"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Reference"
msgstr "Referensi"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "Referensi harus unik!"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_method__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_refund
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
msgid "Refund"
msgstr "Pengembalian Dana"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_refund
#: model:ir.model.fields,help:payment.field_payment_provider__support_refund
msgid ""
"Refund is a feature allowing to refund customers directly from the payment "
"in Odoo."
msgstr ""
"Refund adalah fitur yang memungkinkan untuk mengembalikkan uang pelanggan "
"langsung dari pembayaran di Odoo."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Refunds"
msgstr "Pengembalian"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "Perhitungan Refund"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "ID Dokumen Terkait"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Model Dokumen Terkait"

#. module: payment
#: model:payment.method,name:payment.payment_method_revolut_pay
msgid "Revolut Pay"
msgstr "Revolut Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_rupay
msgid "RuPay"
msgstr "RuPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_sepa_direct_debit
#: model:payment.provider,name:payment.payment_provider_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "SEPA Direct Debit"

#. module: payment
#: model:payment.method,name:payment.payment_method_spei
msgid "SPEI"
msgstr "SPEI"

#. module: payment
#: model:payment.method,name:payment.payment_method_samsung_pay
msgid "Samsung Pay"
msgstr "Samsung Pay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Save"
msgstr "Simpan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Saving your payment method."
msgstr "Menyimpan metode pembayaran Anda."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select countries. Leave empty to allow any."
msgstr "Pilih negara. Biarkan kosong untuk mengizinkan semua negara."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select countries. Leave empty to make available everywhere."
msgstr ""
"Pilih negara. Biarkan kosong untuk membuatnya tersedia untuk diseluruh "
"dunia."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select currencies. Leave empty not to restrict any."
msgstr ""
"Pilih mata uang. Biarkan kosong untuk tidak membatasi mata uang apapun."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select currencies. Leave empty to allow any."
msgstr "Pilih mata uang. Biarkan kosong untuk mengizinkan semua mata uang."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "Metode pembayaran onboarding terpilih"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__sequence
#: model:ir.model.fields,field_description:payment.field_payment_provider__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopback
msgid "ShopBack"
msgstr "ShopBack"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopeepay
msgid "ShopeePay"
msgstr "ShopeePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopping
msgid "Shopping Card"
msgstr "Shopping Card"

#. module: payment
#: model:payment.method,name:payment.payment_method_scb
msgid "Siam Commerical Bank"
msgstr "Siam Commerical Bank"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Skip <i class=\"oi oi-arrow-right ms-1 small\"/>"
msgstr "Skip <i class=\"oi oi-arrow-right ms-1 small\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_sofort
msgid "Sofort"
msgstr "Sofort"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid ""
"Some of the transactions you intend to capture can only be captured in full."
" Handle the transactions individually to capture a partial amount."
msgstr ""
"Beberapa transaksi yang Anda ingin capture hanya dapat di-capture secara "
"penuh. Kelola transaksi secara individu untuk melakukan capture pada jumlah "
"yang parsial."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "Sumber Transaksi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "Status"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "Status"

#. module: payment
#: model:onboarding.onboarding.step,done_text:payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "Langkah Selesai!"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.provider,name:payment.payment_provider_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__support_partial_capture
msgid "Support Partial Capture"
msgstr "Mendukung Capture Parsial"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__payment_method_ids
msgid "Supported Payment Methods"
msgstr "Metode Pembayaran yang Didukung"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Supported by"
msgstr "Didukung oleh"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Supported providers:"
msgstr "Penyedia yang didukung:"

#. module: payment
#: model:payment.method,name:payment.payment_method_swish
msgid "Swish"
msgstr "Swish"

#. module: payment
#: model:payment.method,name:payment.payment_method_tenpay
msgid "TENPAY"
msgstr "TENPAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_ttb
msgid "TTB"
msgstr "TTB"

#. module: payment
#: model:payment.method,name:payment.payment_method_tmb
msgid "Tamilnad Mercantile Bank Limited"
msgstr "Tamilnad Mercantile Bank Limited"

#. module: payment
#: model:payment.method,name:payment.payment_method_tarjeta_mercadopago
msgid "Tarjeta MercadoPago"
msgstr "Tarjeta MercadoPago"

#. module: payment
#: model:payment.method,name:payment.payment_method_techcom
msgid "Techcombank"
msgstr "Techcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_tendopay
msgid "TendoPay"
msgstr "TendoPay"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__test
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Test Mode"
msgstr "Mode Testing"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Thank you!"
msgstr ""
"<p>Dear ${object.partner_name atau 'pelamar'},</p>\n"
"        <p>Kami terima kasih atas minat Anda pada perusahaan kami dan untuk aplikasi Anda.\n"
"        Sayangnya, profil Anda tidak cocok dengan kebutuhan kita atau kampanye rekrutmen kami telah mencapai istilah.</p>\n"
"        <p>Jika Anda ingin informasi lebih lanjut, jangan ragu untuk menghubungi kami melalui telepon.</p>\n"
"        <p>Salam</p>\n"
"        <br>${object.user_id dan object.user_id.signature | aman atau ''}"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "Token akses tidak valid."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid "The amount to capture must be positive and cannot be superior to %s."
msgstr ""
"Jumlah untuk di-capture harus positif dan tidak boleh lebih besar dari %s."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__image
#: model:ir.model.fields,help:payment.field_payment_method__image_payment_form
msgid "The base image used for this payment method; in a 64x64 px format."
msgstr ""
"Gambar dasar yang digunakan untuk metode pembayaran ini; dalam format 64x64 "
"px."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__brand_ids
msgid ""
"The brands of the payment methods that will be displayed on the payment "
"form."
msgstr ""
"Brand metode pembayaran yang akan ditampilkan pada formulir pembayaran."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__child_transaction_ids
msgid "The child transactions of the transaction."
msgstr "Anak transaksi dari transaksi."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__payment_details
msgid "The clear part of the payment method's payment details."
msgstr "Bagian jelas dari detail pembayaran metode pembayaran."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__color
msgid "The color of the card in kanban view"
msgstr "Warna kartu di tampilan kanban"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "Pesan informasi pelengkap mengenai status"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_country_ids
msgid ""
"The countries in which this payment provider is available. Leave blank to "
"make it available in all countries."
msgstr ""
"Negara-negara di mana penyedia pembayaran ini tersedia. Biarkan kosong untuk"
" membuatnya tersedia di semua negara."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_currency_ids
msgid ""
"The currencies available with this payment provider. Leave empty not to "
"restrict any."
msgstr ""
"Mata uang yang tersedia dengan metode pembayaran ini. Biarkan kosong untuk "
"tidak membatasi mata uang apapun."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "The following fields must be filled: %s"
msgstr "Field-field berikut harus diisi: %s"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The following kwargs are not whitelisted: %s"
msgstr "Kwarg berikut tidak di-whitelist: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "Referensi internal transaksi"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_country_ids
msgid ""
"The list of countries in which this payment method can be used (if the "
"provider allows it). In other countries, this payment method is not "
"available to customers."
msgstr ""
"Daftar negara-negara di mana metode pembayaran ini dapat digunakan (bila "
"penyedia mengizinkannya). Di negara-negara lain, metode pembayaran ini tidak"
" tersedia untuk pelanggan."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_currency_ids
msgid ""
"The list of currencies for that are supported by this payment method (if the"
" provider allows it). When paying with another currency, this payment method"
" is not available to customers."
msgstr ""
"Daftar mata uang yang mana didukung oleh metode pembayaran ini (bila "
"penyedia mengizinkannya). Saat membayar dengan mata uang lain, metode "
"pembayaran ini tidak tersedia untuk pelanggan."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__provider_ids
msgid "The list of providers supporting this payment method."
msgstr "Daftar penyedia yang mendukung metode pembayaran ini."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__main_currency_id
msgid "The main currency of the company, used to display monetary fields."
msgstr ""
"Mata uang utama perusahaan ini, digunakan untuk menampilkan field moneter."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__maximum_amount
msgid ""
"The maximum payment amount that this payment provider is available for. "
"Leave blank to make it available for any payment amount."
msgstr ""
"Jumlah pembayaran maksimum yang provider pembayaran ini dapat berikan. "
"BIarkan kosong untuk jumlah maksimum yang tidak terhingga."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "Pesan yang ditampilkan bila pembayaran diotorisasi"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__cancel_msg
msgid ""
"The message displayed if the order is cancelled during the payment process"
msgstr "Pesan ditampilkan bila pesanan dibatalkan pada proses pembayaran"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr ""
"Pesan yang ditampilkan bila order sukses dilakukan setelah proses pembayaran"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr "Pesan yang ditampilkan bila order pending setelah proses pembayaran"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr ""
"Pesan yang ditampilkan untuk menjelaskan dan membantu proses pembayaran"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr "Pembayaran harus langsung, dengan pengalihan, atau dibuat oleh token."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__primary_payment_method_id
msgid ""
"The primary payment method of the current payment method, if the latter is a brand.\n"
"For example, \"Card\" is the primary payment method of the card brand \"VISA\"."
msgstr ""
"Metode pembayaran utama dari metode pembayaran saat ini, bila yang terakhir adalah brand.\n"
"Contohnya, \"Kartu\" adalah metode pembayaran utama bila brand adalah kartu \"VISA\"."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__provider_ref
msgid "The provider reference of the token of the transaction."
msgstr "Referensi penyedia dari token transaksi."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_reference
msgid "The provider reference of the transaction"
msgstr "Referensi provider transaksi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image_payment_form
msgid "The resized image displayed on the payment form."
msgstr "Gambar tampilan yang disesuaikan di formulir pembayaran."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "Rute yang user akan dialihkan ke setelah transaksi"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "The saving of your payment method has been canceled."
msgstr "Penyimpanan metode pembayaran Anda telah dibatalkan."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of the related child transactions"
msgstr "Sumber transaksi dari anak transaksi terkait"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__code
#: model:ir.model.fields,help:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,help:payment.field_payment_transaction__payment_method_code
msgid "The technical code of this payment method."
msgstr "kode teknis untuk metode pembayaran ini."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__code
#: model:ir.model.fields,help:payment.field_payment_token__provider_code
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_code
msgid "The technical code of this payment provider."
msgstr "Kode teknis penyedia pembayaran ini."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"Templat yang merender formulir yang dikirim untuk mengalihkan user saat "
"membuat pembayaran"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__express_checkout_form_view_id
msgid "The template rendering the express payment methods' form."
msgstr "Tempalt yang merender formulir metode pembayaran express."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr ""
"Templat yang merender formulir pembayaran inlien saat membuat pembayaran "
"langsung"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__token_inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a payment by "
"token."
msgstr ""
"Templat yang merender formulir pembayaran inline saat membuat pembayaran "
"dengan token."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(provider_name)s)."
msgstr ""
"Transaksi dengan referensi %(ref)s untuk %(amount)s menemukan error "
"(%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(provider_name)s)."
msgstr ""
"Transaksi dengan referensi %(ref)s untuk %(amount)s telah diotorisasi "
"(%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(provider_name)s)."
msgstr ""
"Transaksi dengan referensi %(ref)s untuk %(amount)s telah dikonfirmasi "
"(%(provider_name)s)."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "Tidak ada transaksi untuk ditunjukkan"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "There is no token created yet."
msgstr "Belum ada token yang dibuat."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "There is nothing to be paid."
msgstr "Tidak ada yang perlu dibayar."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "Tidak ada yang perlu dibayar."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"payment method."
msgstr ""
"Action ini juga akan mengarsip token %s yang didaftarkan dengan metode "
"pembayaran ini."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"provider. "
msgstr ""
"ACtion ini juga akan mengarsip token %s yang didaftarkan dengan penyedia "
"ini."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"provider's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"Ini menentukan apakah pelanggan dapat menyimpan metode pembayaran sebagai token pembayaran.\n"
"Token pembayaran adalah link anonim ke detail metode pembayaran yang disimpan di\n"
"database penyedia, mengizinkan pelanggan untuk menggunakan ulang di pembelian berikutnya."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_express_checkout
msgid ""
"This controls whether customers can use express payment methods. Express "
"checkout enables customers to pay with Google Pay and Apple Pay from which "
"address information is collected at payment."
msgstr ""
"Ini menentukan apakah pelanggan dapat menggunakan metode pembayaran express."
" Express checkout memungkinkan pelanggan untuk membayar dengan Google Pay "
"dan Apple Pay dari mana informasi alamat dikumpulkan pada pembayaran."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment providers.\n"
"                     Setting an email for this partner is advised."
msgstr ""
"Mitra ini tidak memiliki email, yang mungkin bermasalah untuk beberapa penyedia pembayaran.\n"
"                     Disarankan untuk menetapkan email untuk mitra ini."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This payment method needs a partner in crime; you should enable a payment "
"provider supporting this method first."
msgstr ""
"Metode pembayaran ini membutuhkan pendamping; Anda harus mengaktifkan "
"penyedia pembayaran yang mendukung metode ini terlebih dahulu."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"This transaction has been confirmed following the processing of its partial "
"capture and partial void transactions (%(provider)s)."
msgstr ""
"Transaksi ini telah dikonfirmasi mengikuti pemrosesan capture parisal dan "
"transaksi parsial yang void (%(provider)s)."

#. module: payment
#: model:payment.method,name:payment.payment_method_tienphong
msgid "Tienphong"
msgstr "Tienphong"

#. module: payment
#: model:payment.method,name:payment.payment_method_tinka
msgid "Tinka"
msgstr "Tinka"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__token_inline_form_view_id
msgid "Token Inline Form Template"
msgstr "Templat Formulir Inline Token"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_tokenization
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_tokenization
msgid "Tokenization"
msgstr "Tokenisasi"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_tokenization
msgid ""
"Tokenization is the process of saving the payment details as a token that "
"can later be reused without having to enter the payment details again."
msgstr ""
"Tokenisasi adalah proses menyimpan detail pembayaran sebagai token yang "
"dapat digunakan lagi nanti tanpa harus memasukkan lagi detail pembayaran."

#. module: payment
#: model:payment.method,name:payment.payment_method_toss_pay
msgid "Toss Pay"
msgstr "Toss Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_touch_n_go
msgid "Touch'n Go"
msgstr "Touch'n Go"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__transaction_ids
msgid "Transaction"
msgstr "Transaksi"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Transaction authorization is not supported by the following payment "
"providers: %s"
msgstr ""
"Otorisasi transaksi tidak didukung oleh penyedia pembayaran berikut: %s"

#. module: payment
#: model:payment.method,name:payment.payment_method_truemoney
msgid "TrueMoney"
msgstr "TrueMoney"

#. module: payment
#: model:payment.method,name:payment.payment_method_trustly
msgid "Trustly"
msgstr "Trustly"

#. module: payment
#: model:payment.method,name:payment.payment_method_twint
msgid "Twint"
msgstr "Twint"

#. module: payment
#: model:payment.method,name:payment.payment_method_upi
msgid "UPI"
msgstr "UPI"

#. module: payment
#: model:payment.method,name:payment.payment_method_ussd
msgid "USSD"
msgstr "USSD"

#. module: payment
#: model:payment.method,name:payment.payment_method_unionpay
msgid "UnionPay"
msgstr "UnionPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_uob
msgid "United Overseas Bank"
msgstr "United Overseas Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_uatp
msgid "Universal Air Travel Plan"
msgstr "Universal Air Travel Plan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Unpublished"
msgstr "Belum dipublikasikan"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__none
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__none
msgid "Unsupported"
msgstr "Tidak didukung"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Upgrade"
msgstr "Upgrade"

#. module: payment
#: model:payment.method,name:payment.payment_method_vpay
msgid "V PAY"
msgstr "V PAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_visa
msgid "VISA"
msgstr "VISA"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "Validasi metode pembayaran"

#. module: payment
#: model:payment.method,name:payment.payment_method_venmo
msgid "Venmo"
msgstr "Venmo"

#. module: payment
#: model:payment.method,name:payment.payment_method_vietcom
msgid "Vietcombank"
msgstr "Vietcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_vipps
msgid "Vipps"
msgstr "Vipps"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__void_remaining_amount
msgid "Void Remaining Amount"
msgstr "Batalkan Jumlah Tersisa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "Batalkan Transaksi"

#. module: payment
#: model:payment.method,name:payment.payment_method_wallets_india
msgid "Wallets India"
msgstr "Wallets India"

#. module: payment
#: model:payment.method,name:payment.payment_method_walley
msgid "Walley"
msgstr "Walley"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_provider.py:0
msgid "Warning"
msgstr "Peringatan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__warning_message
msgid "Warning Message"
msgstr "Pesan Peringatan"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Warning!"
msgstr "Peringatan!"

#. module: payment
#: model:payment.method,name:payment.payment_method_wechat_pay
msgid "WeChat Pay"
msgstr "WeChat Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_welend
msgid "WeLend"
msgstr "WeLend"

#. module: payment
#: model:payment.method,name:payment.payment_method_webpay
msgid "WebPay"
msgstr "WebPay"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr "Apakah token pembayaran akan dibuat pada saat post-process transaksi"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_capture_wizard__support_partial_capture
msgid ""
"Whether each of the transactions' provider supports the partial capture."
msgstr "Apakah setiap penyedia transaksi mendukung capture parsial."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__is_published
msgid ""
"Whether the provider is visible on the website or not. Tokens remain "
"functional but are only visible on manage forms."
msgstr ""
"Apakah penyedia terlihat pada website atau tidak. Token akan tetap berfungsi"
" tapi hanya terlihat pada kelola formulir."

#. module: payment
#: model:payment.provider,name:payment.payment_provider_transfer
msgid "Wire Transfer"
msgstr "Transfer rekening"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_worldline
msgid "Worldline"
msgstr "Worldline"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid ""
"You can't unarchive tokens linked to inactive payment methods or disabled "
"providers."
msgstr ""
"Anda tidak dapat membatalkan arsip token yang terhubung ke metode pembayaran"
" yang tidak aktif atau penyedia yang dinonaktifkan."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot change the company of a payment provider with existing "
"transactions."
msgstr ""
"Anda tidak dapat mengubah penyedia pembayaran perusahaan dengan transaksi "
"yang sudah tersedia."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "You cannot delete the default payment method."
msgstr "Anda tidak dapat menghapus metode pembayaran standar."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot delete the payment provider %s; disable it or uninstall it "
"instead."
msgstr ""
"Anda tidak dapat menghapus penyedia pembayaran %s; nonaktifkan atau "
"uninstal."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "You cannot publish a disabled provider."
msgstr "Anda tidak dapat menerbitkan penyedia yang dinonaktifkan."

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "You do not have access to this payment token."
msgstr "Anda tidak dapat mengakses token pembayaran ini."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid ""
"You should receive an email confirming your payment within a few\n"
"                                    minutes."
msgstr ""
"Anda seharusnya menerima email yang mengonfirmasi pembayaran Anda dalam beberapa\n"
"                                    menit."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,auth_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,auth_msg:payment.payment_provider_aps
#: model_terms:payment.provider,auth_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,auth_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_demo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,auth_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,auth_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,auth_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,auth_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,auth_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,auth_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,auth_msg:payment.payment_provider_xendit
msgid "Your payment has been authorized."
msgstr "Tagihan Anda telah disahkan."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_aps
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_demo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_xendit
msgid "Your payment has been cancelled."
msgstr "Pembayaran Anda telah dibatalkan."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,pending_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,pending_msg:payment.payment_provider_aps
#: model_terms:payment.provider,pending_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,pending_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_demo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,pending_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,pending_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,pending_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,pending_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,pending_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,pending_msg:payment.payment_provider_xendit
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr ""
"Pembayaran Anda sudah sukses diproses tapi sedang menunggu persetujuan."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,done_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,done_msg:payment.payment_provider_aps
#: model_terms:payment.provider,done_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,done_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,done_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,done_msg:payment.payment_provider_demo
#: model_terms:payment.provider,done_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,done_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,done_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,done_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,done_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,done_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,done_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,done_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,done_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,done_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,done_msg:payment.payment_provider_xendit
msgid "Your payment has been successfully processed."
msgstr "Pembayaran Anda sukses diproses."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment has not been processed yet."
msgstr "Pembayaran Anda belum diproses."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Your payment is on its way!"
msgstr "Pembayaran Anda sedang dikirim!"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment method has been saved."
msgstr "Metode pembayaran Anda sudah disimpan."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Your payment methods"
msgstr "Metode pembayaran Anda"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "Kode Pos"

#. module: payment
#: model:payment.method,name:payment.payment_method_zalopay
msgid "Zalopay"
msgstr "Zalopay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
#: model:payment.method,name:payment.payment_method_zip
msgid "Zip"
msgstr "Kode Pos"

#. module: payment
#: model:payment.method,name:payment.payment_method_cofidis
msgid "cofidis"
msgstr "cofidis"

#. module: payment
#: model:payment.method,name:payment.payment_method_enets
msgid "eNETS"
msgstr "eNETS"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "express checkout not supported"
msgstr "express checkout tidak didukung"

#. module: payment
#: model:payment.method,name:payment.payment_method_ideal
msgid "iDEAL"
msgstr "iDEAL"

#. module: payment
#: model:payment.method,name:payment.payment_method_in3
msgid "in3"
msgstr "in3"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible country"
msgstr "negara yang tidak kompatibel"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible currency"
msgstr "mata uang yang tidak kompatibel"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible website"
msgstr "website yang tidak kompatibel"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "manual capture not supported"
msgstr "manual capture tidak didukung"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "maximum amount exceeded"
msgstr "jumlah maksimum dilampaui"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "no supported provider available"
msgstr "tidak ada penyedia yang didukung yang tersedia"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "payment method"
msgstr "metode pembayaran"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "provider"
msgstr "penyedia"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid ""
"to make this\n"
"                    payment."
msgstr ""
"untuk membuat pembayaran\n"
"                    ini."

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization not supported"
msgstr "tokenisasi tidak didukung"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization without payment no supported"
msgstr "tokenisasi tanpa pembayaran tidak didukung"
