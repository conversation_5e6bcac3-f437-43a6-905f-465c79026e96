# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "البيانات المحضرة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "(ID:"
msgstr "(المعرّف: "

#. module: payment
#: model:payment.method,name:payment.payment_method_7eleven
msgid "7Eleven"
msgstr "7Eleven"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %(bank)s</li><li>Account "
"Number: %(account_number)s</li><li>Account Holder: "
"%(account_holder)s</li></ul>"
msgstr ""
"<h3>رجى الدفع إلى: </h3><ul><li>البنك: %(bank)s</li><li>رقم الحساب: "
"%(account_number)s</li><li>صاحب الحساب: %(account_holder)s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> These properties are set to\n"
"                                match the behavior of providers and that of their integration with\n"
"                                Odoo regarding this payment method. Any change may result in errors\n"
"                                and should be tested on a test database first."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> تم ضبط هذه الخصائص على\n"
"                                مطابقة سلوك مقدمي الخدمة وتكاملهم معهم\n"
"                                Odoo فيما يتعلق بطريقة الدفع هذه. أي تغيير قد يؤدي إلى أخطاء\n"
"                                ويجب اختباره على قاعدة بيانات اختبارية أولاً. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"الرئيسية \" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Some of the transactions you intend to capture can"
" only be captured in full. Handle the transactions individually to capture a"
" partial amount.\"/>"
msgstr ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"بعض المعاملات التي تنوي تحصيلها يمكن تحصيلها فقط "
"بشكل كامل. قم بالتعامل مع المعاملات بشكل فردي حتى تتمكن من تحصيل المبالغ "
"الجزئية. \"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid ""
"<i class=\"fa fa-trash\" title=\"Delete payment method\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"
msgstr ""
"<i class=\"fa fa-trash\" title=\"حذف طريقة الدفع \" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "<i class=\"oi oi-arrow-right me-1\"></i> Configure a payment provider"
msgstr "<i class=\"oi oi-arrow-right me-1\"></i> قم بتهيئة مزود الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Enable Payment Methods"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            قم بتمكين طرق الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
msgid "<small class=\"text-600\">Save my payment details</small>"
msgstr "<small class=\"text-600\">احفظ تفاصيل الدفع الخاصة بي</small>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-danger\">Unpublished</span>"
msgstr "<span class=\"o_stat_text text-danger\">غير منشور</span> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-success\">Published</span>"
msgstr "<span class=\"o_stat_text text-success\">منشور</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">طرق الدفع المحفوظة</span> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                All countries are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                كافة الدول مدعومة.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                All currencies are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                كافة الدول مدعومة.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid "<span><i class=\"fa fa-lock\"/> Secured by</span>"
msgstr "<span><i class=\"fa fa-lock\"/> محمي بواسطة</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"fa fa-file-text\"/> Show availability report</strong>"
msgstr "<strong><i class=\"fa fa-file-text\"/> إظهار تقرير التوافر</strong> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Methods</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> طرق الدفع</strong> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Providers</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> مزودو السعر</strong> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong>No payment method available</strong>"
msgstr "<strong>لا توجد طريقة دفع متاحة</strong> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a partial capture pending. Please wait a\n"
"                    moment for it to be processed. Check your payment provider configuration if\n"
"                    the capture is still pending after a few minutes."
msgstr ""
"<strong>تحذير!</strong> توجد عملية تحصيل دفع جزئي قيد الانتظار. يرجى الانتظار\n"
"                    قليلاً حتى تتم معالجتها. تحقق من تهيئة مزود الدفع إذا كانت\n"
"                    عملية تحصيل الدفع لا تزال قيد الانتظار بعد عدة دقائق. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> You can not capture a negative amount nor more\n"
"                    than"
msgstr ""
"<strong>تحذير!</strong> لا يمكنك تحصيل مبلغ بقيمة سالبة لأكثر\n"
"                    من "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<strong>Warning</strong> Creating a payment provider from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>تحذير</strong> إنشاء مزود دفع باستخدام رز <em>إنشاء</em> غير مدعوم.\n"
"                        يرجى استخدام إجراء <em>استنساخ</em> عوضاً عن ذلك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the\n"
"                                    correct partner before making this payment."
msgstr ""
"<strong>تحذير</strong> تأكد من أنك قد قمت بتسجيل دخولك\n"
"                                    كالشريك الصحيح قبل القيام بالدفع. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>تحذير</strong> العملة غير موجودة أو غير صحيحة. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>تحذير</strong> عليك تسجيل دخولك لإكمال الدفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(provider_name)s)."
msgstr ""
"تم إرسال طلب استرداد أموال لـ %(amount)s. سوف يتم إنشاء الدفع قريباً. مرجع "
"معاملة استرداد الأموال: %(ref)s (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated (%(provider_name)s)."
msgstr "لقد بدأت معاملة لها المرجع %(ref)s (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated to save a new "
"payment method (%(provider_name)s)"
msgstr ""
"لقد بدأت معاملة لها المرجع %(ref)s لحفظ طريقة دفع جديدة (%(provider_name)s)."
" "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token)s (%(provider_name)s)."
msgstr ""
"لقد بدأت معاملة لها المرجع %(ref)s باستخدام طريقة الدفع %(token)s "
"(%(provider_name)s). "

#. module: payment
#: model:payment.method,name:payment.payment_method_ach_direct_debit
msgid "ACH Direct Debit"
msgstr "خصم ACH المباشر "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "ACTIVATE STRIPE"
msgstr "تفعيل STRIPE "

#. module: payment
#: model:payment.method,name:payment.payment_method_abitab
msgid "Abitab"
msgstr "Abitab"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Account"
msgstr "الحساب "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "رقم الحساب"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Activate"
msgstr "تفعيل"

#. module: payment
#: model:ir.actions.server,name:payment.action_activate_stripe
msgid "Activate Stripe"
msgstr "تفعيل Stripe "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__active
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "نشط"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "العنوان"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.method,name:payment.payment_method_affirm
msgid "Affirm"
msgstr "Affirm"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay_riverty
msgid "AfterPay"
msgstr "AfterPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay
msgid "Afterpay"
msgstr "Afterpay"

#. module: payment
#: model:payment.method,name:payment.payment_method_akulaku
msgid "Akulaku PayLater"
msgstr "Akulaku PayLater"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_hk
msgid "AliPayHK"
msgstr "AliPayHK"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_plus
msgid "Alipay+"
msgstr "Alipay+"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_express_checkout
msgid "Allow Express Checkout"
msgstr "السماح بالدفع والخروج والسريع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "السماح بحفظ طرق الدفع "

#. module: payment
#: model:payment.method,name:payment.payment_method_alma
msgid "Alma"
msgstr "Alma"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__captured_amount
msgid "Already Captured"
msgstr "تم تحصيل الدفع بالفعل "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__voided_amount
msgid "Already Voided"
msgstr "تم إبطاله بالفعل "

#. module: payment
#: model:payment.method,name:payment.payment_method_amazon_pay
msgid "Amazon Pay"
msgstr "Amazon Pay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_aps
msgid "Amazon Payment Services"
msgstr "خدمات دفع Amazon "

#. module: payment
#: model:payment.method,name:payment.payment_method_amex
msgid "American Express"
msgstr "American Express"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Amount"
msgstr "مبلغ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "الحد الأقصى للمبلغ "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__amount_to_capture
msgid "Amount To Capture"
msgstr "المبلغ لتحصيله "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred during the processing of your payment."
msgstr "حدث خطأ أثناء معالجة عملية الدفع الخاصة بك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred while saving your payment method."
msgstr "حدث خطأ أثناء حفظ طريقة الدفع الخاصة بك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Apply"
msgstr "تطبيق"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "مؤرشف"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "Are you sure you want to delete this payment method?"
msgstr "هل أنت متأكد من أنك ترغب في حذف طريقة الدفع هذه؟ "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"هل أنت متأكد أنك تريد إبطال المعاملة المُصرح بها؟ لا يمكن التراجع عن هذا "
"الإجراء. "

#. module: payment
#: model:payment.method,name:payment.payment_method_argencard
msgid "Argencard"
msgstr "Argencard"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_asiapay
msgid "Asiapay"
msgstr "Asiapay"

#. module: payment
#: model:payment.method,name:payment.payment_method_astropay
msgid "Astropay TEF"
msgstr "Astropay TEF"

#. module: payment
#: model:payment.method,name:payment.payment_method_atome
msgid "Atome"
msgstr "Atome"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__auth_msg
msgid "Authorize Message"
msgstr "رسالة التصريح "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "مصرح به "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__authorized_amount
msgid "Authorized Amount"
msgstr "المبلغ المصرح به "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Availability"
msgstr "التوافر"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Availability report"
msgstr "تقرير التوافر "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
msgid "Available methods"
msgstr "الطرق المتاحة "

#. module: payment
#: model:payment.method,name:payment.payment_method_axis
msgid "Axis"
msgstr "المحور "

#. module: payment
#: model:payment.method,name:payment.payment_method_bacs_direct_debit
msgid "BACS Direct Debit"
msgstr "خصم BACS المباشر "

#. module: payment
#: model:payment.method,name:payment.payment_method_bancomat_pay
msgid "BANCOMAT Pay"
msgstr "BANCOMAT Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_bca
msgid "BCA"
msgstr "BCA"

#. module: payment
#: model:payment.method,name:payment.payment_method_becs_direct_debit
msgid "BECS Direct Debit"
msgstr "خصم BECS المباشر "

#. module: payment
#: model:payment.method,name:payment.payment_method_blik
msgid "BLIK"
msgstr "BLIK"

#. module: payment
#: model:payment.method,name:payment.payment_method_brankas
msgid "BRANKAS"
msgstr "BRANKAS"

#. module: payment
#: model:payment.method,name:payment.payment_method_bri
msgid "BRI"
msgstr "BRI"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancnet
msgid "BancNet"
msgstr "BancNet"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_guayaquil
msgid "Banco Guayaquil"
msgstr "Banco Guayaquil"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_pichincha
msgid "Banco Pichincha"
msgstr "Banco Pichincha"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_de_bogota
msgid "Banco de Bogota"
msgstr "Banco de Bogota"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancolombia
msgid "Bancolombia"
msgstr "Bancolombia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancontact
msgid "Bancontact"
msgstr "Bancontact"

#. module: payment
#: model:payment.method,name:payment.payment_method_bangkok_bank
msgid "Bangkok Bank"
msgstr "Bangkok Bank"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Bank"
msgstr "البنك"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_account
msgid "Bank Account"
msgstr "الحساب البنكي"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "اسم البنك"

#. module: payment
#: model:payment.method,name:payment.payment_method_bni
msgid "Bank Negara Indonesia"
msgstr "Bank Negara Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_permata
msgid "Bank Permata"
msgstr "Bank Permata"

#. module: payment
#: model:payment.method,name:payment.payment_method_bsi
msgid "Bank Syariah Indonesia"
msgstr "Bank Syariah Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_transfer
msgid "Bank Transfer"
msgstr "تحويل بنكي "

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_of_ayudhya
msgid "Bank of Ayudhya"
msgstr "Bank of Ayudhya"

#. module: payment
#: model:payment.method,name:payment.payment_method_bpi
msgid "Bank of the Philippine Islands"
msgstr "Bank of the Philippine Islands"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_reference
msgid "Bank reference"
msgstr "مرجع البنك "

#. module: payment
#: model:payment.method,name:payment.payment_method_belfius
msgid "Belfius"
msgstr "Belfius"

#. module: payment
#: model:payment.method,name:payment.payment_method_benefit
msgid "Benefit"
msgstr "فائدة "

#. module: payment
#: model:payment.method,name:payment.payment_method_bharatqr
msgid "BharatQR"
msgstr "BharatQR"

#. module: payment
#: model:payment.method,name:payment.payment_method_billease
msgid "BillEase"
msgstr "BillEase"

#. module: payment
#: model:payment.method,name:payment.payment_method_billink
msgid "Billink"
msgstr "Billink"

#. module: payment
#: model:payment.method,name:payment.payment_method_bizum
msgid "Bizum"
msgstr "Bizum"

#. module: payment
#: model:payment.method,name:payment.payment_method_boleto
msgid "Boleto"
msgstr "Boleto"

#. module: payment
#: model:payment.method,name:payment.payment_method_boost
msgid "Boost"
msgstr "Boost"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__brand_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Brands"
msgstr "العلامات التجارية "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cimb_niaga
msgid "CIMB Niaga"
msgstr "CIMB Niaga"

#. module: payment
#: model:payment.method,name:payment.payment_method_cmr
msgid "CMR"
msgstr "CMR"

#. module: payment
#: model:payment.method,name:payment.payment_method_cabal
msgid "Cabal"
msgstr "Cabal"

#. module: payment
#: model:payment.method,name:payment.payment_method_caixa
msgid "Caixa"
msgstr "Caixa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Cancel"
msgstr "إلغاء"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "تم الإلغاء "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__cancel_msg
msgid "Cancelled Message"
msgstr "الرسالة الملغية "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot delete payment method"
msgstr "لا يمكن حذف طريقة الدفع "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot save payment method"
msgstr "لا يمكن حفظ طريقة الدفع "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid "Capture"
msgstr "تحصيل "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__capture_manually
msgid "Capture Amount Manually"
msgstr "تحصيل المبلغ يدوياً "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "تسجيل المعاملة "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"قم بالتقاط المبلغ من أودو، عندما يتم احتساب التوصيل. \n"
"استخدم ذلك إذا كنت ترغب في تغيير بطاقات عملائك، فقط عندما \n"
"تكون متأكداً من قدرتك على شحن البضاعة إليهم. "

#. module: payment
#: model:payment.method,name:payment.payment_method_card
msgid "Card"
msgstr "البطاقة"

#. module: payment
#: model:payment.method,name:payment.payment_method_carnet
msgid "Carnet"
msgstr "Carnet"

#. module: payment
#: model:payment.method,name:payment.payment_method_cartes_bancaires
msgid "Cartes Bancaires"
msgstr "Cartes Bancaires"

#. module: payment
#: model:payment.method,name:payment.payment_method_cash_app_pay
msgid "Cash App Pay"
msgstr "الدفع عن طريق Cash App "

#. module: payment
#: model:payment.method,name:payment.payment_method_cashalo
msgid "Cashalo"
msgstr "Cashalo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cebuana
msgid "Cebuana"
msgstr "Cebuana"

#. module: payment
#: model:payment.method,name:payment.payment_method_cencosud
msgid "Cencosud"
msgstr "Cencosud"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__child_transaction_ids
msgid "Child Transactions"
msgstr "المعاملات التابعة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Child transactions"
msgstr "المعاملات التابعة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "اختر طريقة الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Choose another method <i class=\"oi oi-arrow-down\"/>"
msgstr "اختر طريقة أخرى <i class=\"oi oi-arrow-down\"/> "

#. module: payment
#: model:payment.method,name:payment.payment_method_cirrus
msgid "Cirrus"
msgstr "Cirrus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "المدينة"

#. module: payment
#: model:payment.method,name:payment.payment_method_clearpay
msgid "Clearpay"
msgstr "Clearpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "إغلاق"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__code
#: model:ir.model.fields,field_description:payment.field_payment_provider__code
msgid "Code"
msgstr "رمز "

#. module: payment
#: model:payment.method,name:payment.payment_method_codensa
msgid "Codensa"
msgstr "Codensa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__color
msgid "Color"
msgstr "اللون"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "الشركة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Configuration"
msgstr "التهيئة "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Confirm Deletion"
msgstr "تأكيد الحذف"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "تم التأكيد "

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordial
msgid "Cordial"
msgstr "Cordial"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordobesa
msgid "Cordobesa"
msgstr "Cordobesa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_id
msgid "Corresponding Module"
msgstr "التطبيق المقابل "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_country_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_country_ids
msgid "Countries"
msgstr "الدول"

#. module: payment
#: model:ir.model,name:payment.model_res_country
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "الدولة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "إنشاء رمز "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_provider
msgid "Create a new payment provider"
msgstr "إنشاء مزود دفع جديد "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_method__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Creating a transaction from an archived token is forbidden."
msgstr "يحظر إنشاء معاملة من رمز قد تمت أرشفته. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Credentials"
msgstr "بيانات الاعتماد"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__stripe
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "البطاقة الائتمانية وبطاقة الخصم (عن طريق Stripe) "

#. module: payment
#: model:payment.method,name:payment.payment_method_credit
msgid "Credit Payment"
msgstr "الدفع عن طريق الائتمان "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_currency_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_currency_ids
msgid "Currencies"
msgstr "العملات"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__main_currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "العملة"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__manual
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "تعليمات الدفع المخصصة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "العميل"

#. module: payment
#: model:payment.method,name:payment.payment_method_dana
msgid "Dana"
msgstr "Dana"

#. module: payment
#: model:payment.method,name:payment.payment_method_dankort
msgid "Dankort"
msgstr "Dankort"

#. module: payment
#: model:payment.method,name:payment.payment_method_davivienda
msgid "Davivienda"
msgstr "Davivienda"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__sequence
msgid "Define the display order"
msgstr "تحديد ترتيب العرض "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_demo
msgid "Demo"
msgstr "النسخة التجريبية"

#. module: payment
#: model:payment.method,name:payment.payment_method_diners
msgid "Diners Club International"
msgstr "Diners Club International"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Disabled"
msgstr "معطل"

#. module: payment
#: model:payment.method,name:payment.payment_method_discover
msgid "Discover"
msgstr "اكتشف"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_method__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: payment
#: model:payment.method,name:payment.payment_method_dolfin
msgid "Dolfin"
msgstr "Dolfin"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Don't hesitate to contact us if you don't receive it."
msgstr "لا تتردد في التواصل معنا في حال لم تصلك. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__done_msg
msgid "Done Message"
msgstr "رسالة الانتهاء "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "مسودة"

#. module: payment
#: model:payment.method,name:payment.payment_method_duitnow
msgid "DuitNow"
msgstr "DuitNow"

#. module: payment
#: model:payment.method,name:payment.payment_method_emi_india
msgid "EMI"
msgstr "EMI"

#. module: payment
#: model:payment.method,name:payment.payment_method_eps
msgid "EPS"
msgstr "EPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_elo
msgid "Elo"
msgstr "Elo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__enabled
msgid "Enabled"
msgstr "ممكن "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Enterprise"
msgstr "للمؤسسات "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "خطأ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Error: %s"
msgstr "خطأ: %s "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_express_checkout
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_express_checkout
msgid "Express Checkout"
msgstr "الدفع السريع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__express_checkout_form_view_id
msgid "Express Checkout Form Template"
msgstr "قالب استمارة الدفع والخروج السريع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_express_checkout
msgid ""
"Express checkout allows customers to pay faster by using a payment method "
"that provides all required billing and shipping information, thus allowing "
"to skip the checkout process."
msgstr ""
"يسمح الدفع السريع للعملاء بالدفع بشكل أسرع باستخدام طريقة دفع توفر جميع "
"معلومات الفوترة والشحن المطلوبة، مما يسمح بتخطي عملية الدفع. "

#. module: payment
#: model:payment.method,name:payment.payment_method_fps
msgid "FPS"
msgstr "FPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_fpx
msgid "FPX"
msgstr "FPX"

#. module: payment
#: model:payment.method,name:payment.payment_method_facilito
msgid "Facilito"
msgstr "Facilito"

#. module: payment
#: model:payment.method,name:payment.payment_method_floa_bank
msgid "Floa Bank"
msgstr "Floa Bank"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment
#: model:payment.method,name:payment.payment_method_frafinance
msgid "Frafinance"
msgstr "Frafinance"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__partial
msgid "Full & Partial"
msgstr "كامل وجزئي "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__full_only
msgid "Full Only"
msgstr "كامل فقط "

#. module: payment
#: model:payment.method,name:payment.payment_method_gcash
msgid "GCash"
msgstr "GCash"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "إنشاء رابط الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "إنشاء رابط دفع المبيعات "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate and Copy Payment Link"
msgstr "إنشاء ونسخ رابط الدفع "

#. module: payment
#: model:payment.method,name:payment.payment_method_giropay
msgid "Giropay"
msgstr "Giropay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Go to my Account <i class=\"oi oi-arrow-right ms-2\"/>"
msgstr "الذهاب إلى حسابي <i class=\"oi oi-arrow-right ms-2\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_gopay
msgid "GoPay"
msgstr "GoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_gsb
msgid "Government Savings Bank"
msgstr "Government Savings Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_grabpay
msgid "GrabPay"
msgstr "GrabPay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: payment
#: model:payment.method,name:payment.payment_method_hd
msgid "HD Bank"
msgstr "HD Bank"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_draft_children
msgid "Has Draft Children"
msgstr "به توابع بحالة المسودة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_remaining_amount
msgid "Has Remaining Amount"
msgstr "يحتوي على مبلغ متبقي "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "هل تجاوزت عملية الدفع المعالجة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pre_msg
msgid "Help Message"
msgstr "رسالة المساعدة"

#. module: payment
#: model:payment.method,name:payment.payment_method_hipercard
msgid "Hipercard"
msgstr "Hipercard"

#. module: payment
#: model:payment.method,name:payment.payment_method_hoolah
msgid "Hoolah"
msgstr "Hoolah"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "How to configure your PayPal account"
msgstr "كيف تقوم بإعداد حسابك على paypal "

#. module: payment
#: model:payment.method,name:payment.payment_method_humm
msgid "Humm"
msgstr "Humm"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_method__id
#: model:ir.model.fields,field_description:payment.field_payment_provider__id
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "المُعرف"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "إذا كنت تظن أن هذا خطأ، يرجى التواصل مع مدير الموقع الإلكتروني. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image
#: model:ir.model.fields,field_description:payment.field_payment_provider__image_128
msgid "Image"
msgstr "صورة"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"في وضع الاختبار، تتم معالجة دفع مزيف عن طريق واجهة دفع تجريبية. \n"
"يُنصح بهذه الوضعية عند ضبط مزود الدفع. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__inline_form_view_id
msgid "Inline Form Template"
msgstr "قالب استمارة مضمنة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Install"
msgstr "تثبيت"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_state
msgid "Installation State"
msgstr "حالة التثبيت"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "Installed"
msgstr "تم التثبيت "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__is_amount_to_capture_valid
msgid "Is Amount To Capture Valid"
msgstr "المبلغ الذي يجب تحصيله صالح "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "بعد مرحلة المعالجة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__is_primary
msgid "Is Primary Payment Method"
msgstr "طريقة الدفع الرئيسية "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "الدول التي تدعم Stripe "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "It is currently linked to the following documents:"
msgstr "مرتبط حالياً بالمستندات التالية: "

#. module: payment
#: model:payment.method,name:payment.payment_method_jcb
msgid "JCB"
msgstr "JCB"

#. module: payment
#: model:payment.method,name:payment.payment_method_jeniuspay
msgid "JeniusPay"
msgstr "JeniusPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_jkopay
msgid "Jkopay"
msgstr "Jkopay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kbc_cbc
msgid "KBC/CBC"
msgstr "KBC/CBC"

#. module: payment
#: model:payment.method,name:payment.payment_method_knet
msgid "KNET"
msgstr "KNET"

#. module: payment
#: model:payment.method,name:payment.payment_method_kakaopay
msgid "KakaoPay"
msgstr "KakaoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kasikorn_bank
msgid "Kasikorn Bank"
msgstr "Kasikorn Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna
msgid "Klarna"
msgstr "Klarna"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_paynow
msgid "Klarna - Pay Now"
msgstr "Klarna - Pay Now"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_pay_over_time
msgid "Klarna - Pay over time"
msgstr "Klarna - Pay over time"

#. module: payment
#: model:payment.method,name:payment.payment_method_kredivo
msgid "Kredivo"
msgstr "Kredivo"

#. module: payment
#: model:payment.method,name:payment.payment_method_krungthai_bank
msgid "KrungThai Bank"
msgstr "KrungThai Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_linepay
msgid "LINE Pay"
msgstr "LINE Pay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "المسار النهائي "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "اللغة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "تاريخ آخر تغيير للحالة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_method__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: payment
#: model:onboarding.onboarding.step,button_text:payment.onboarding_onboarding_step_payment_provider
msgid "Let's do it"
msgstr "فلنقم بذلك "

#. module: payment
#: model:payment.method,name:payment.payment_method_lider
msgid "Lider"
msgstr "Lider"

#. module: payment
#: model:payment.method,name:payment.payment_method_linkaja
msgid "LinkAja"
msgstr "LinkAja"

#. module: payment
#: model:payment.method,name:payment.payment_method_nuvei_local
msgid "Local Payments"
msgstr "المدفوعات المحلية "

#. module: payment
#: model:payment.method,name:payment.payment_method_lydia
msgid "Lydia"
msgstr "Lydia"

#. module: payment
#: model:payment.method,name:payment.payment_method_lyfpay
msgid "LyfPay"
msgstr "LyfPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_mpesa
msgid "M-Pesa"
msgstr "M-Pesa"

#. module: payment
#: model:payment.method,name:payment.payment_method_mbway
msgid "MB WAY"
msgstr "MB WAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_mada
msgid "Mada"
msgstr "Mada"

#. module: payment
#: model:payment.method,name:payment.payment_method_maestro
msgid "Maestro"
msgstr "مايسترو "

#. module: payment
#: model:payment.method,name:payment.payment_method_magna
msgid "Magna"
msgstr "Magna"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Making a request to the provider is not possible because the provider is "
"disabled."
msgstr "لا يمكن تقديم طلب للمزود لأن المزود قد تم تعطيله. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Manage your payment methods"
msgstr "إدارة طرق السداد"

#. module: payment
#: model:payment.method,name:payment.payment_method_mandiri
msgid "Mandiri"
msgstr "Mandiri"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "يدوي"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_manual_capture
msgid "Manual Capture Supported"
msgstr "يدعم عملية التحصيل اليدوية "

#. module: payment
#: model:payment.method,name:payment.payment_method_mastercard
msgid "MasterCard"
msgstr "MasterCard"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__maximum_amount
msgid "Maximum Amount"
msgstr "الحد الأقصى للمبلغ "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__available_amount
msgid "Maximum Capture Allowed"
msgstr "الحد الأقصى المسموح به لتحصيل الأموال "

#. module: payment
#: model:payment.method,name:payment.payment_method_maya
msgid "Maya"
msgstr "Maya"

#. module: payment
#: model:payment.method,name:payment.payment_method_maybank
msgid "Maybank"
msgstr "Maybank"

#. module: payment
#: model:payment.method,name:payment.payment_method_meeza
msgid "Meeza"
msgstr "Meeza"

#. module: payment
#: model:payment.method,name:payment.payment_method_mercado_livre
msgid "Mercado Livre"
msgstr "Mercado Livre"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mercado_pago
msgid "Mercado Pago"
msgstr "Mercado Pago"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "الرسالة"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Messages"
msgstr "الرسائل"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "الطريقة "

#. module: payment
#: model:payment.method,name:payment.payment_method_momo
msgid "MoMo"
msgstr "MoMo"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_money
msgid "Mobile money"
msgstr "Mobile money"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_pay
msgid "MobilePay"
msgstr "MobilePay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#: model:payment.method,name:payment.payment_method_multibanco
msgid "Multibanco"
msgstr "Multibanco"

#. module: payment
#: model:payment.method,name:payment.payment_method_mybank
msgid "MyBank"
msgstr "MyBank"

#. module: payment
#: model:payment.method,name:payment.payment_method_naps
msgid "NAPS"
msgstr "NAPS"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__name
#: model:ir.model.fields,field_description:payment.field_payment_provider__name
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Name"
msgstr "الاسم"

#. module: payment
#: model:payment.method,name:payment.payment_method_napas_card
msgid "Napas Card"
msgstr "Napas Card"

#. module: payment
#: model:payment.method,name:payment.payment_method_naranja
msgid "Naranja"
msgstr "Naranja"

#. module: payment
#: model:payment.method,name:payment.payment_method_nativa
msgid "Nativa"
msgstr "Nativa"

#. module: payment
#: model:payment.method,name:payment.payment_method_naver_pay
msgid "Naver Pay"
msgstr "Naver Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_netbanking
msgid "Netbanking"
msgstr "Netbanking"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__code__none
msgid "No Provider Set"
msgstr "لم يتم تعيين مزود "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Provider menu."
msgstr ""
"لم يتم العثور على طريقة دفع يدوية لهذه الشركة. يرجى إنشاء واحدة من قائمة "
"مزودالدفع. "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "No payment methods found for your payment providers."
msgstr "لم يتم العثور على طرق دفع لمزودي الدفع لديك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "No payment providers are configured."
msgstr "لم تتم تهيئة أي مزودي دفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "No token can be assigned to the public partner."
msgstr "لا يمكن تعيين رمز للشريك العام. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "None is configured for:"
msgstr "لم تتم تهيئة أي شيء لـ: "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_nuvei
msgid "Nuvei"
msgstr "Nuvei"

#. module: payment
#: model:payment.method,name:payment.payment_method_ovo
msgid "OVO"
msgstr "OVO"

#. module: payment
#: model:payment.method,name:payment.payment_method_oca
msgid "Oca"
msgstr "Oca"

#. module: payment
#: model:payment.method,name:payment.payment_method_octopus
msgid "Octopus"
msgstr "Octopus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "تطبيق أودو للمؤسسات "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "الدفع دون الاتصال بالإنترنت عن طريق الرمز "

#. module: payment
#: model:payment.method,name:payment.payment_method_omannet
msgid "OmanNet"
msgstr "OmanNet"

#. module: payment
#: model:ir.model,name:payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "خطة تمهيدية "

#. module: payment
#: model:onboarding.onboarding.step,step_image_alt:payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Step Image"
msgstr "صورة خطوة التهيئة "

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_czech_republic
msgid "Online Banking Czech Republic"
msgstr "Online Banking Czech Republic"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_india
msgid "Online Banking India"
msgstr "Online Banking India"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_slovakia
msgid "Online Banking Slovakia"
msgstr "Online Banking Slovakia"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_thailand
msgid "Online Banking Thailand"
msgstr "Online Banking Thailand"

#. module: payment
#: model:onboarding.onboarding.step,title:payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "المدفوعات عبر الإنترنت "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "الدفع المباشر عبر الإنترنت "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "الدفع عبر الإنترنت عن طريق الرمز "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "الدفع عبر الإنترنت مع إعادة التوجيه "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Only administrators can access this data."
msgstr "وحدهم المدراء يُسمح لهم بالوصول إلى هذه البيانات. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only authorized transactions can be voided."
msgstr "وحدها المعاملات المصرح بها يمكن إبطالها. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only confirmed transactions can be refunded."
msgstr "وحدها المعاملات المؤكدة يمكن استرداد الأموال فيها. "

#. module: payment
#: model:payment.method,name:payment.payment_method_open_banking
msgid "Open banking"
msgstr "الخدمات البنكية المفتوحة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "العملية"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Operation not supported."
msgstr "العملية غير مدعومة."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "غير ذلك"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Other payment methods"
msgstr "طرق الدفع الأخرى "

#. module: payment
#: model:payment.method,name:payment.payment_method_oxxopay
msgid "Oxxo Pay"
msgstr "Oxxo Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_p24
msgid "P24"
msgstr "P24"

#. module: payment
#: model:payment.method,name:payment.payment_method_poli
msgid "POLi"
msgstr "POLi"

#. module: payment
#: model:payment.method,name:payment.payment_method_pps
msgid "PPS"
msgstr "PPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_pse
msgid "PSE"
msgstr "PSE"

#. module: payment
#: model:payment.method,name:payment.payment_method_pace
msgid "Pace."
msgstr "Pace."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__partial
msgid "Partial"
msgstr "جزئي"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "الشريك"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "اسم الشريك"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Pay"
msgstr "الدفع "

#. module: payment
#: model:payment.method,name:payment.payment_method_paylater_india
msgid "Pay Later"
msgstr "Pay Later"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_easy
msgid "Pay-easy"
msgstr "Pay-easy"

#. module: payment
#: model:payment.method,name:payment.payment_method_paybright
msgid "PayBright"
msgstr "PayBright"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_id
msgid "PayID"
msgstr "PayID"

#. module: payment
#: model:payment.method,name:payment.payment_method_payme
msgid "PayMe"
msgstr "PayMe"

#. module: payment
#: model:payment.method,name:payment.payment_method_paynow
msgid "PayNow"
msgstr "PayNow"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
#: model:payment.provider,name:payment.payment_provider_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypay
msgid "PayPay"
msgstr "PayPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_paysafecard
msgid "PaySafeCard"
msgstr "PaySafeCard"

#. module: payment
#: model:payment.method,name:payment.payment_method_payu
msgid "PayU"
msgstr "PayU"

#. module: payment
#: model:payment.method,name:payment.payment_method_paylib
msgid "Paylib"
msgstr "Paylib"

#. module: payment
#: model:ir.model,name:payment.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "معالج تحصيل الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_details
msgid "Payment Details"
msgstr "تفاصيل الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Followup"
msgstr "متابعة الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Form"
msgstr "استمارة الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Payment Info"
msgstr "معلومات السداد "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "تعليمات الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "رابط الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_method
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__payment_method
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Payment Method"
msgstr "طريقة الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_code
msgid "Payment Method Code"
msgstr "كود طريقة الدفع "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model:ir.actions.act_window,name:payment.action_payment_method
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Methods"
msgstr "طرق الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_provider
msgid "Payment Provider"
msgstr "مزود الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_provider
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_list
msgid "Payment Providers"
msgstr "مزودي الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "رمز الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "عدد رموز الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "رموز الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "معاملات الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "معاملات الدفع المرتبطة برمز "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "Payment details saved on %(date)s"
msgstr "تفاصيل الدفع المخزنة في %(date)s "

#. module: payment
#: model:payment.method,name:payment.payment_method_unknown
msgid "Payment method"
msgstr "طريقة الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Payment methods"
msgstr "طرق الدفع "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "فشلت معالجة عملية الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment provider"
msgstr "مزود الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_provider_onboarding_wizard
msgid "Payment provider onboarding wizard"
msgstr "معالج تهيئة مزود الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Payment providers"
msgstr "مزودو الدفع "

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
msgid "Payment: Post-process transactions"
msgstr "الدفع: معاملات ما بعد العملية "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "الدفعات"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytm
msgid "Paytm"
msgstr "Paytm"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytrail
msgid "Paytrail"
msgstr "Paytrail"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "قيد الانتظار "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pending_msg
msgid "Pending Message"
msgstr "رسالة مُعلقة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "رقم الهاتف"

#. module: payment
#: model:payment.method,name:payment.payment_method_pix
msgid "Pix"
msgstr "Pix"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Please make sure that %(payment_method)s is supported by %(provider)s."
msgstr ""
"يرجى التأكد من أن طريقة الدفع %(payment_method)s مدعومة من قِبَل "
"%(provider)s. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set a positive amount."
msgstr "يرجى تحديد مبلغ إيجابي. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set an amount lower than %s."
msgstr "يرجى تعيين مبلغ أقل من %s. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid "Please switch to company"
msgstr "يرجى التبديل إلى شركة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Please wait..."
msgstr "انتظر من فضلك... "

#. module: payment
#: model:payment.method,name:payment.payment_method_post_finance
msgid "PostFinance Pay"
msgstr "PostFinance Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_poste_pay
msgid "PostePay"
msgstr "PostePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_presto
msgid "Presto"
msgstr "Presto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__primary_payment_method_id
msgid "Primary Payment Method"
msgstr "طريقة الدفع الرئيسية "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "تمت المعالجة بواسطة "

#. module: payment
#: model:payment.method,name:payment.payment_method_promptpay
msgid "Prompt Pay"
msgstr "Prompt Pay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Provider"
msgstr "المزود"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_code
msgid "Provider Code"
msgstr "كود المزود "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_reference
msgid "Provider Reference"
msgstr "مرجع المزود "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__provider_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Providers"
msgstr "المزودون"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__is_published
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Published"
msgstr "تم النشر "

#. module: payment
#: model:payment.method,name:payment.payment_method_qris
msgid "QRIS"
msgstr "QRIS"

#. module: payment
#: model:payment.method,name:payment.payment_method_rabbit_line_pay
msgid "Rabbit LINE Pay"
msgstr "Rabbit LINE Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_ratepay
msgid "Ratepay"
msgstr "Ratepay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_razorpay
msgid "Razorpay"
msgstr "Razorpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Reason:"
msgstr "السبب:"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Reason: %s"
msgstr "السبب: %s "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "إعادة التوجيه من القالب "

#. module: payment
#: model:payment.method,name:payment.payment_method_redpagos
msgid "Redpagos"
msgstr "Redpagos"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "يجب أن يكون الرقم المرجعي فريداً! "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_method__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_refund
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
msgid "Refund"
msgstr "استرداد الأموال "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_refund
#: model:ir.model.fields,help:payment.field_payment_provider__support_refund
msgid ""
"Refund is a feature allowing to refund customers directly from the payment "
"in Odoo."
msgstr ""
"استرداد الأموال هي ميزة تتيح استرداد الأموال للعملاء مباشرةً من خلال الدفع "
"في Odoo. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Refunds"
msgstr "الاستردادات "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "عدد عمليات استرداد الأموال "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "معرف المستند ذي الصلة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "نموذج المستند ذي الصلة "

#. module: payment
#: model:payment.method,name:payment.payment_method_revolut_pay
msgid "Revolut Pay"
msgstr "Revolut Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_rupay
msgid "RuPay"
msgstr "RuPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_sepa_direct_debit
#: model:payment.provider,name:payment.payment_provider_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "خصم SEPA المباشر"

#. module: payment
#: model:payment.method,name:payment.payment_method_spei
msgid "SPEI"
msgstr "SPEI"

#. module: payment
#: model:payment.method,name:payment.payment_method_samsung_pay
msgid "Samsung Pay"
msgstr "Samsung Pay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Save"
msgstr "حفظ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Saving your payment method."
msgstr "جاري حفظ طريقة الدفع الخاصة بك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select countries. Leave empty to allow any."
msgstr "قم بتحديد الدول، أو اتركه فارغاً للسماح بأي دولة. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select countries. Leave empty to make available everywhere."
msgstr "قم بتحديد الدول. اتركه فارغاً لجعله متاحاً في كل مكان. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select currencies. Leave empty not to restrict any."
msgstr "قم بتحديد العملات. اتركها فارغة حتى لا تقتصر على أي واحدة. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select currencies. Leave empty to allow any."
msgstr "قم بتحديد العملات، أو اتركه فارغاً للسماح بأي عملة. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "طريقة تهيئة الدفع المحددة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__sequence
#: model:ir.model.fields,field_description:payment.field_payment_provider__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: payment
#: model:payment.method,name:payment.payment_method_shopback
msgid "ShopBack"
msgstr "ShopBack"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopeepay
msgid "ShopeePay"
msgstr "ShopeePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopping
msgid "Shopping Card"
msgstr "بطاقة التسوق "

#. module: payment
#: model:payment.method,name:payment.payment_method_scb
msgid "Siam Commerical Bank"
msgstr "Siam Commerical Bank"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Skip <i class=\"oi oi-arrow-right ms-1 small\"/>"
msgstr "تخطي <i class=\"oi oi-arrow-right ms-1 small\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_sofort
msgid "Sofort"
msgstr "Sofort"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid ""
"Some of the transactions you intend to capture can only be captured in full."
" Handle the transactions individually to capture a partial amount."
msgstr ""
"بعض المعاملات التي تنوى تحصيلها يمكن تحصيلها بشكل كامل فقط. قم بمعالجة "
"المعاملات كل على حدة لتحصيل المبلغ الجزئي. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "المعاملة المصدرية "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "الولاية "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "الحالة"

#. module: payment
#: model:onboarding.onboarding.step,done_text:payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "تم اكتمال الخطوة!"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.provider,name:payment.payment_provider_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__support_partial_capture
msgid "Support Partial Capture"
msgstr "دعم التحصيل التلقائي للمدفوعات "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__payment_method_ids
msgid "Supported Payment Methods"
msgstr "طرق الدفع المدعومة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Supported by"
msgstr "مدعوم من قِبَل "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Supported providers:"
msgstr "المزودون المدعومون: "

#. module: payment
#: model:payment.method,name:payment.payment_method_swish
msgid "Swish"
msgstr "Swish"

#. module: payment
#: model:payment.method,name:payment.payment_method_tenpay
msgid "TENPAY"
msgstr "TENPAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_ttb
msgid "TTB"
msgstr "TTB"

#. module: payment
#: model:payment.method,name:payment.payment_method_tmb
msgid "Tamilnad Mercantile Bank Limited"
msgstr "Tamilnad Mercantile Bank Limited"

#. module: payment
#: model:payment.method,name:payment.payment_method_tarjeta_mercadopago
msgid "Tarjeta MercadoPago"
msgstr "Tarjeta MercadoPago"

#. module: payment
#: model:payment.method,name:payment.payment_method_techcom
msgid "Techcombank"
msgstr "Techcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_tendopay
msgid "TendoPay"
msgstr "TendoPay"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__test
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Test Mode"
msgstr "وضع الاختبار "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Thank you!"
msgstr "شكرًا لك!"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "رمز الوصول غير صالح. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid "The amount to capture must be positive and cannot be superior to %s."
msgstr "يجب أن يكون المبلغ المراد تحصيله موجباً ولا يمكن أن يتخطى %s. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__image
#: model:ir.model.fields,help:payment.field_payment_method__image_payment_form
msgid "The base image used for this payment method; in a 64x64 px format."
msgstr "الصورة الأساسية المستخدمة لطريقة الدفع هذه؛ بتنسيق 64x64 px. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__brand_ids
msgid ""
"The brands of the payment methods that will be displayed on the payment "
"form."
msgstr "العلامات التجارية لطرق الدفع التي سيتم عرضها في نموذج الدفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__child_transaction_ids
msgid "The child transactions of the transaction."
msgstr "المعاملات التابعة للمعاملة. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__payment_details
msgid "The clear part of the payment method's payment details."
msgstr "الجزء الواضح من تفاصيل دفع طريقة الدفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__color
msgid "The color of the card in kanban view"
msgstr "لون البطاقة في عرض كانبان "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "رسالة المعلومات التكميلية عن الحالة "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_country_ids
msgid ""
"The countries in which this payment provider is available. Leave blank to "
"make it available in all countries."
msgstr ""
"الدول التي يكون فيها مزود الدفع هذا متاحاً. اتركه فارغاً لجعله متاحاً في "
"كافة الدول. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_currency_ids
msgid ""
"The currencies available with this payment provider. Leave empty not to "
"restrict any."
msgstr ""
"العملات المتاحة مع مزود الدفع هذا. اتركه فارغاً حتى لا تقتصر على أي واحدة. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "The following fields must be filled: %s"
msgstr "يجب ملء الحقول التالية: %s "

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The following kwargs are not whitelisted: %s"
msgstr "لم يتم إدراج kwargs التالية في القائمة البيضاء: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "مرجع المعاملة الداخلي "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_country_ids
msgid ""
"The list of countries in which this payment method can be used (if the "
"provider allows it). In other countries, this payment method is not "
"available to customers."
msgstr ""
"قائمة البلدان التي يمكن استخدام طريقة الدفع هذه فيها (إذا كان المزود يسمح "
"بذلك). وفي بلدان أخرى، لا تتوفر طريقة الدفع هذه للعملاء. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_currency_ids
msgid ""
"The list of currencies for that are supported by this payment method (if the"
" provider allows it). When paying with another currency, this payment method"
" is not available to customers."
msgstr ""
"قائمة العملات التي تدعمها طريقة الدفع هذه (إذا كان المزود يسمح بذلك). عند "
"الدفع بعملة أخرى، لن تكون طريقة الدفع هذه متاحة للعملاء. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__provider_ids
msgid "The list of providers supporting this payment method."
msgstr "قائمة مزودي الدفع الذين يدعمون طريقة الدفع هذه. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__main_currency_id
msgid "The main currency of the company, used to display monetary fields."
msgstr "عملة الشركة الرئيسية، تُستخدم لعرض الحقول النقدية. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__maximum_amount
msgid ""
"The maximum payment amount that this payment provider is available for. "
"Leave blank to make it available for any payment amount."
msgstr ""
"المبلغ الأقصى للدفع الذي يكون مزود الدفع هذا متاحاً فيه. اتركه فارغاً لجعله "
"متاحاً لأي مبلغ دفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "الرسالة المعروضة إذا كان الدفع مصرحاً به "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__cancel_msg
msgid ""
"The message displayed if the order is cancelled during the payment process"
msgstr "الرسالة التي يتم عرضها إذا تم إلغاء الطلب أثناء معالجة الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr "الرسالة المعروضة إذا تم إكمال الطلب بنجاح بعد معالجة الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr "الرسالة المعروضة إذا كان الطلب معلقاً بعد معالجة الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr "الرسالة المعروضة لشرح ومساعدة عملية الدفع "

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"يجب أن يكون الدفع إما مباشراً، أو عن طريق إعادة التوجيه أو عن طريق رمز. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__primary_payment_method_id
msgid ""
"The primary payment method of the current payment method, if the latter is a brand.\n"
"For example, \"Card\" is the primary payment method of the card brand \"VISA\"."
msgstr ""
"طريقة الدفع الأساسية لطريقة الدفع الحالية، إذا كانت الثاني عبارة عن علامة تجارية.\n"
"على سبيل المثال، \"البطاقة\" هي طريقة الدفع الأساسية للعلامة التجارية للبطاقة \"VISA\". "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__provider_ref
msgid "The provider reference of the token of the transaction."
msgstr "مرجع مزود الدفع لرمز المعاملة. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_reference
msgid "The provider reference of the transaction"
msgstr "مرجع مزود الدفع للمعاملة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image_payment_form
msgid "The resized image displayed on the payment form."
msgstr "الصورة التي تم تغيير حجمها والمعروضة على استمارة الدفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "المسار الذي تتم إعادة توجيه المستخدم إليه بعد المعاملة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "The saving of your payment method has been canceled."
msgstr "لقد تم إلغاء حفظ طريقة الدفع الخاصة بك. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of the related child transactions"
msgstr "المعاملة المصدرية للمعاملات التابعة ذات الصلة "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__code
#: model:ir.model.fields,help:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,help:payment.field_payment_transaction__payment_method_code
msgid "The technical code of this payment method."
msgstr "الكود التقني لطريقة الدفع هذه. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__code
#: model:ir.model.fields,help:payment.field_payment_token__provider_code
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_code
msgid "The technical code of this payment provider."
msgstr "الكود التقني لمزود الدفع هذا. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"القالب يقوم بتكوين استمارة يتم إرسالها لإعادة توجيه المستخدم عند الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__express_checkout_form_view_id
msgid "The template rendering the express payment methods' form."
msgstr "القالب الذي يقوم بتكوين استمارة الدفع والخروج السريع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr "القالب يقوم بتكوين استمارة الدفع الضمني عند إكمال عملية دفع مباشر "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__token_inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a payment by "
"token."
msgstr ""
"القالب يقوم بتكوين استمارة الدفع الضمني عند إكمال عملية دفع عن طريق الرمز. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(provider_name)s)."
msgstr ""
"المعاملة مع المرجع %(ref)s لـ %(amount)s واجهت خطأً (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(provider_name)s)."
msgstr ""
"المعاملة مع المرجع %(ref)s لـ %(amount)s تم تفويضها (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(provider_name)s)."
msgstr ""
"المعاملات مع المرجع %(ref)s لـ %(amount)s تم تأكيدها (%(provider_name)s). "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "لا توجد معاملات لعرضها "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "There is no token created yet."
msgstr "لم يتم إنشاء رمز بعد. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "There is nothing to be paid."
msgstr "لا يوجد شيء لدفعه. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "لا يوجد شيء لدفعه. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"payment method."
msgstr "سيقوم هذا الإجراء أيضاً بأرشفة الرموز %s المسجلة مع طريقة الدفع هذه. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"provider. "
msgstr "سيقوم هذا الإجراء أيضاً بأرشفة الرموز %s المسجلة مع مزود الدفع هذا. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"provider's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"يتحكم ذلك بإمكانية حفظ العملاء لطرق الدفع كرموز دفع. \n"
"رمز الدفع هو رابط مجهول المصدر لتفاصيل طريقة الدفع المحفوظة في \n"
"قاعدة بيانات مزود الدفع، مما يتيح للعميل إعادة استخدامها لعملية الشراء القادمة. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_express_checkout
msgid ""
"This controls whether customers can use express payment methods. Express "
"checkout enables customers to pay with Google Pay and Apple Pay from which "
"address information is collected at payment."
msgstr ""
"يتحكم ذلك بإمكانية استخدام العملاء طرق الدفع السريعة. يتيح الدفع والخروج "
"السريع للعملاء الدفع عن طريق Google Pay وApple Pay والتي يتم جمع معلومات "
"العنوان منها عند الدفع. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment providers.\n"
"                     Setting an email for this partner is advised."
msgstr ""
"ليس لهذا الشريك عنوان بريد إلكتروني، مما قد يسبب المشاكل مع بعض مزودي الدفع.\n"
"                     ننصح بتعيين بريد إلكتروني لهذا الشريك. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This payment method needs a partner in crime; you should enable a payment "
"provider supporting this method first."
msgstr ""
"تتطلب طريقة الدفع هذه شريكاً في الجريمة؛ سيتوجب عليك تمكين مزود الدفع الذي "
"يدعم هذه الطريقة أولاً. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"This transaction has been confirmed following the processing of its partial "
"capture and partial void transactions (%(provider)s)."
msgstr ""
"لقد تم تأكيد هذه المعاملة بعد معالجة عمليات تحصيل الدفع الجزئي والمعاملات "
"الجزية الباطلة (%(provider)s). "

#. module: payment
#: model:payment.method,name:payment.payment_method_tienphong
msgid "Tienphong"
msgstr "Tienphong"

#. module: payment
#: model:payment.method,name:payment.payment_method_tinka
msgid "Tinka"
msgstr "Tinka"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__token_inline_form_view_id
msgid "Token Inline Form Template"
msgstr "قالب استمارة مضمنة للرمز "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_tokenization
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_tokenization
msgid "Tokenization"
msgstr "الترميز الآلي "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_tokenization
msgid ""
"Tokenization is the process of saving the payment details as a token that "
"can later be reused without having to enter the payment details again."
msgstr ""
"الترميز الآلي هو عملية حفظ تفاصيل الدفع كرمز يمكن إعادة استخدامه لاحقاً دون "
"الحاجة إلى إدخال تفاصيل الدفع مرة أخرى. "

#. module: payment
#: model:payment.method,name:payment.payment_method_toss_pay
msgid "Toss Pay"
msgstr "Toss Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_touch_n_go
msgid "Touch'n Go"
msgstr "Touch'n Go"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__transaction_ids
msgid "Transaction"
msgstr "معاملة"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Transaction authorization is not supported by the following payment "
"providers: %s"
msgstr "تفويض المعاملات غير مدعوم من قِبَل مزودي الدفع التالين: %s "

#. module: payment
#: model:payment.method,name:payment.payment_method_truemoney
msgid "TrueMoney"
msgstr "TrueMoney"

#. module: payment
#: model:payment.method,name:payment.payment_method_trustly
msgid "Trustly"
msgstr "Trustly"

#. module: payment
#: model:payment.method,name:payment.payment_method_twint
msgid "Twint"
msgstr "Twint"

#. module: payment
#: model:payment.method,name:payment.payment_method_upi
msgid "UPI"
msgstr "UPI"

#. module: payment
#: model:payment.method,name:payment.payment_method_ussd
msgid "USSD"
msgstr "USSD"

#. module: payment
#: model:payment.method,name:payment.payment_method_unionpay
msgid "UnionPay"
msgstr "UnionPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_uob
msgid "United Overseas Bank"
msgstr "United Overseas Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_uatp
msgid "Universal Air Travel Plan"
msgstr "Universal Air Travel Plan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Unpublished"
msgstr "غير منشور"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__none
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__none
msgid "Unsupported"
msgstr "غير مدعوم "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Upgrade"
msgstr "ترقية"

#. module: payment
#: model:payment.method,name:payment.payment_method_vpay
msgid "V PAY"
msgstr "V PAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_visa
msgid "VISA"
msgstr "VISA"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "تصديق طريقة الدفع "

#. module: payment
#: model:payment.method,name:payment.payment_method_venmo
msgid "Venmo"
msgstr "Venmo"

#. module: payment
#: model:payment.method,name:payment.payment_method_vietcom
msgid "Vietcombank"
msgstr "Vietcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_vipps
msgid "Vipps"
msgstr "Vipps"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__void_remaining_amount
msgid "Void Remaining Amount"
msgstr "المبلغ المتبقي المبطل "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "إبطال المعاملة"

#. module: payment
#: model:payment.method,name:payment.payment_method_wallets_india
msgid "Wallets India"
msgstr "Wallets India"

#. module: payment
#: model:payment.method,name:payment.payment_method_walley
msgid "Walley"
msgstr "Walley"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_provider.py:0
msgid "Warning"
msgstr "تحذير"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__warning_message
msgid "Warning Message"
msgstr "رسالة تحذير"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Warning!"
msgstr "تحذير!"

#. module: payment
#: model:payment.method,name:payment.payment_method_wechat_pay
msgid "WeChat Pay"
msgstr "WeChat Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_welend
msgid "WeLend"
msgstr "WeLend"

#. module: payment
#: model:payment.method,name:payment.payment_method_webpay
msgid "WebPay"
msgstr "WebPay"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr "ما إذا كان رمز الدفع يجب إنشاؤه عند معالجة المعاملة أم لا "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_capture_wizard__support_partial_capture
msgid ""
"Whether each of the transactions' provider supports the partial capture."
msgstr "ما إذا كان مزود كل من المعاملات يدعم التحصيل الجزئي للمدفوعات أم لا. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__is_published
msgid ""
"Whether the provider is visible on the website or not. Tokens remain "
"functional but are only visible on manage forms."
msgstr ""
"ما إذا كان مزود الدفع مرئياً على الموقع الإلكتروني أم لا. سيكون بإمكانك "
"استخدام الرموز ولكن ستكون مرئية فقط في إدارة الاستمارات. "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_transfer
msgid "Wire Transfer"
msgstr "تحويل بنكي"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_worldline
msgid "Worldline"
msgstr "Worldline"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid ""
"You can't unarchive tokens linked to inactive payment methods or disabled "
"providers."
msgstr ""
"لا يمكنك إلغاء أرشفة الرموز المرتبطة بطرق دفع غير نشطة أو مزودي دفع معطلين. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot change the company of a payment provider with existing "
"transactions."
msgstr "لا يمكنك تغيير شركة مزود دفع يحتوي على معاملات موجودة. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "You cannot delete the default payment method."
msgstr "لا يمكنك حذف طريقة الدفع الافتراضية. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot delete the payment provider %s; disable it or uninstall it "
"instead."
msgstr "لا يمكنك حذف مزود الدفع %s؛ قم بتعطيله أو إلغاء تثبيته عوضاً عن ذلك. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "You cannot publish a disabled provider."
msgstr "لا يمكنك نشر مزود دفع تم تعطيله. "

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "You do not have access to this payment token."
msgstr "لا تملك صلاحية الوصول إلى رمز الدفع هذا. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid ""
"You should receive an email confirming your payment within a few\n"
"                                    minutes."
msgstr ""
"سوف تتلقى رسالة بريد إلكتروني لتأكيد الدفع خلال بضع\n"
"                                    دقائق. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,auth_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,auth_msg:payment.payment_provider_aps
#: model_terms:payment.provider,auth_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,auth_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_demo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,auth_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,auth_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,auth_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,auth_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,auth_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,auth_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,auth_msg:payment.payment_provider_xendit
msgid "Your payment has been authorized."
msgstr "تم التصريح بالدفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_aps
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_demo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_xendit
msgid "Your payment has been cancelled."
msgstr "لقد تم إلغاء الدفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,pending_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,pending_msg:payment.payment_provider_aps
#: model_terms:payment.provider,pending_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,pending_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_demo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,pending_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,pending_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,pending_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,pending_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,pending_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,pending_msg:payment.payment_provider_xendit
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "لقد تمت معالجة الدفع بنجاح ولكن بانتظار الموافقة. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,done_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,done_msg:payment.payment_provider_aps
#: model_terms:payment.provider,done_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,done_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,done_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,done_msg:payment.payment_provider_demo
#: model_terms:payment.provider,done_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,done_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,done_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,done_msg:payment.payment_provider_nuvei
#: model_terms:payment.provider,done_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,done_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,done_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,done_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,done_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,done_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,done_msg:payment.payment_provider_xendit
msgid "Your payment has been successfully processed."
msgstr "لقد تمت معالجة الدفع بنجاح. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment has not been processed yet."
msgstr "لم تتم معالجة عملية الدفع بعد. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Your payment is on its way!"
msgstr "مدفوعاتك في الطريق! "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment method has been saved."
msgstr "لقد تم حفظ طريقة الدفع الخاصة بك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Your payment methods"
msgstr "طرق الدفع الخاصة بك "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "الرمز البريدي"

#. module: payment
#: model:payment.method,name:payment.payment_method_zalopay
msgid "Zalopay"
msgstr "Zalopay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
#: model:payment.method,name:payment.payment_method_zip
msgid "Zip"
msgstr "الرمز البريدي"

#. module: payment
#: model:payment.method,name:payment.payment_method_cofidis
msgid "cofidis"
msgstr "cofidis"

#. module: payment
#: model:payment.method,name:payment.payment_method_enets
msgid "eNETS"
msgstr "eNETS"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "express checkout not supported"
msgstr "خاصية الدفع والخروج السريع غير مدعومة "

#. module: payment
#: model:payment.method,name:payment.payment_method_ideal
msgid "iDEAL"
msgstr "iDEAL"

#. module: payment
#: model:payment.method,name:payment.payment_method_in3
msgid "in3"
msgstr "in3"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible country"
msgstr "الدولة غير متوافقة "

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible currency"
msgstr "العملة غير متوافقة "

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible website"
msgstr "الموقع الإلكتروني غير متوافق "

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "manual capture not supported"
msgstr "عملية التحصيل اليدوية غير مدعومة "

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "maximum amount exceeded"
msgstr "تم تجاوز الحد الأقصى للمبلغ "

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "no supported provider available"
msgstr "لا يوجد مزود دفع مدعوم متاح "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "payment method"
msgstr "طريقة الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "provider"
msgstr "مزود "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid ""
"to make this\n"
"                    payment."
msgstr ""
"لإتمام عملية\n"
"                    الدفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization not supported"
msgstr "الترميز الآلي غير مدعوم "

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization without payment no supported"
msgstr "الترميز الآلي دون الدفع غير مدعوم "
