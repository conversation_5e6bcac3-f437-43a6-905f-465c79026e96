<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- === PRIMARY PAYMENT METHODS === -->

    <record id="payment_method_7eleven" model="payment.method">
        <field name="name">7Eleven</field>
        <field name="code">7eleven</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/7eleven.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_ach_direct_debit" model="payment.method">
        <field name="name">ACH Direct Debit</field>
        <field name="code">ach_direct_debit</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/ach_direct_debit.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.pr'),
                         ref('base.us'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_affirm" model="payment.method">
        <field name="name">Affirm</field>
        <field name="code">affirm</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/affirm.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.us'),
                         ref('base.ca'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CAD'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_afterpay" model="payment.method">
        <field name="name">Afterpay</field>
        <field name="code">afterpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/afterpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                         ref('base.nz'),
                         ref('base.us'),
                         ref('base.ca'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CAD'),
                         ref('base.USD'),
                         ref('base.AUD'),
                         ref('base.NZD'),
                     ])]"
        />
    </record>

    <record id="payment_method_afterpay_riverty" model="payment.method">
        <field name="name">AfterPay</field>
        <field name="code">afterpay_riverty</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/afterpay_riverty.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.nl'),
                         ref('base.be'),
                         ref('base.de'),
                         ref('base.at'),
                         ref('base.fi'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_akulaku" model="payment.method">
        <field name="name">Akulaku PayLater</field>
        <field name="code">akulaku</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/akulaku.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_alipay" model="payment.method">
        <field name="name">Alipay</field>
        <field name="code">alipay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/alipay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
    </record>

    <record id="payment_method_alipay_hk" model="payment.method">
        <field name="name">AliPayHK</field>
        <field name="code">alipay_hk</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/alipay_hk.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.hk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.HKD'),
                     ])]"
        />
    </record>

    <record id="payment_method_alipay_plus" model="payment.method">
        <field name="name">Alipay+</field>
        <field name="code">alipay_plus</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/alipay_plus.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.at'),
                         ref('base.au'),
                         ref('base.be'),
                         ref('base.bg'),
                         ref('base.ch'),
                         ref('base.cr'),
                         ref('base.cy'),
                         ref('base.de'),
                         ref('base.dk'),
                         ref('base.ee'),
                         ref('base.es'),
                         ref('base.fi'),
                         ref('base.fr'),
                         ref('base.gr'),
                         ref('base.hk'),
                         ref('base.hr'),
                         ref('base.hu'),
                         ref('base.ie'),
                         ref('base.is'),
                         ref('base.it'),
                         ref('base.kr'),
                         ref('base.li'),
                         ref('base.lt'),
                         ref('base.lu'),
                         ref('base.lv'),
                         ref('base.mt'),
                         ref('base.my'),
                         ref('base.nl'),
                         ref('base.no'),
                         ref('base.ph'),
                         ref('base.pl'),
                         ref('base.pt'),
                         ref('base.ro'),
                         ref('base.se'),
                         ref('base.si'),
                         ref('base.sk'),
                         ref('base.th'),
                         ref('base.uk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.CHF'),
                         ref('base.DKK'),
                         ref('base.EUR'),
                         ref('base.GBP'),
                         ref('base.HKD'),
                         ref('base.KRW'),
                         ref('base.MYR'),
                         ref('base.NOK'),
                         ref('base.PHP'),
                         ref('base.SEK'),
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_alma" model="payment.method">
        <field name="name">Alma</field>
        <field name="code">alma</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/alma.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.fr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_amazon_pay" model="payment.method">
        <field name="name">Amazon Pay</field>
        <field name="code">amazon_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/amazon_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.be'),
                         ref('base.cy'),
                         ref('base.dk'),
                         ref('base.fr'),
                         ref('base.hu'),
                         ref('base.ie'),
                         ref('base.it'),
                         ref('base.lu'),
                         ref('base.pt'),
                         ref('base.es'),
                         ref('base.uk'),
                         ref('base.us'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.AUD'),
                         ref('base.GBP'),
                         ref('base.DKK'),
                         ref('base.HKD'),
                         ref('base.JPY'),
                         ref('base.NZD'),
                         ref('base.NOK'),
                         ref('base.ZAR'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_astropay" model="payment.method">
        <field name="name">Astropay TEF</field>
        <field name="code">astropay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ec'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_atome" model="payment.method">
        <field name="name">Atome</field>
        <field name="code">atome</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/atome.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.my'),
                         ref('base.sg'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.MYR'),
                         ref('base.SGD'),
                     ])]"
        />
    </record>

    <record id="payment_method_axis" model="payment.method">
        <field name="name">Axis</field>
        <field name="code">axis</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/axis.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.fr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bacs_direct_debit" model="payment.method">
        <field name="name">BACS Direct Debit</field>
        <field name="code">bacs_direct_debit</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bacs_direct_debit.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.uk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.GBP'),
                     ])]"
        />
    </record>

    <record id="payment_method_bancnet" model="payment.method">
        <field name="name">BancNet</field>
        <field name="code">bancnet</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bancnet.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_bancomat_pay" model="payment.method">
        <field name="name">BANCOMAT Pay</field>
        <field name="code">bancomat_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bancomat_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.it'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bancontact" model="payment.method">
        <field name="name">Bancontact</field>
        <field name="code">bancontact</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bancontact.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.be'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bangkok_bank" model="payment.method">
        <field name="name">Bangkok Bank</field>
        <field name="code">bangkok_bank</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_bank_account" model="payment.method">
        <field name="name">Bank Account</field>
        <field name="code">bank_account</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
    </record>

    <record id="payment_method_bank_bca" model="payment.method">
        <field name="name">BCA</field>
        <field name="code">bank_bca</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank_bca.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bank_of_ayudhya" model="payment.method">
        <field name="name">Bank of Ayudhya</field>
        <field name="code">bank_of_ayudhya</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_bank_permata" model="payment.method">
        <field name="name">Bank Permata</field>
        <field name="code">bank_permata</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank_permata.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bank_reference" model="payment.method">
        <field name="name">Bank reference</field>
        <field name="code">bank_reference</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
    </record>

    <record id="payment_method_bank_transfer" model="payment.method">
        <field name="name">Bank Transfer</field>
        <field name="code">bank_transfer</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                         ref('base.NGN'),
                     ])]"
        />
    </record>

    <record id="payment_method_becs_direct_debit" model="payment.method">
        <field name="name">BECS Direct Debit</field>
        <field name="code">becs_direct_debit</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/becs_direct_debit.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                     ])]"
        />
    </record>

    <record id="payment_method_belfius" model="payment.method">
        <field name="name">Belfius</field>
        <field name="code">belfius</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/belfius.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.be'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_benefit" model="payment.method">
        <field name="name">Benefit</field>
        <field name="code">benefit</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/benefit.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.bh'),
               ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.BHD'),
                     ])]"
        />
    </record>

    <record id="payment_method_bharatqr" model="payment.method">
        <field name="name">BharatQR</field>
        <field name="code">bharatqr</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bharatqr.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_billease" model="payment.method">
        <field name="name">BillEase</field>
        <field name="code">billease</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/billease.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
              eval="[Command.set([
                        ref('base.ph'),
                    ])]"
        />
        <field name="supported_currency_ids"
              eval="[Command.set([
                        ref('base.PHP'),
                    ])]"
        />
    </record>

    <record id="payment_method_billink" model="payment.method">
        <field name="name">Billink</field>
        <field name="code">billink</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/billink.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.nl'),
                         ref('base.be'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bizum" model="payment.method">
        <field name="name">Bizum</field>
        <field name="code">bizum</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bizum.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.es'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_blik" model="payment.method">
        <field name="name">BLIK</field>
        <field name="code">blik</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/blik.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.pl'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PLN'),
                     ])]"
        />
    </record>

    <record id="payment_method_bni" model="payment.method">
        <field name="name">Bank Negara Indonesia</field>
        <field name="code">bni</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bni.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_boleto" model="payment.method">
        <field name="name">Boleto</field>
        <field name="code">boleto</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/boleto.png"/>
        <field name="support_tokenization">True</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.br'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.BRL'),
                     ])]"
        />
    </record>

    <record id="payment_method_boost" model="payment.method">
        <field name="name">Boost</field>
        <field name="code">boost</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/boost.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.my'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.MYR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bpi" model="payment.method">
       <field name="name">Bank of the Philippine Islands</field>
       <field name="code">bpi</field>
       <field name="sequence">1000</field>
       <field name="active">False</field>
       <field name="image" type="base64" file="payment/static/img/bank.png"/>
       <field name="support_tokenization">False</field>
       <field name="support_express_checkout">False</field>
       <field name="support_refund">none</field>
       <field name="supported_country_ids"
              eval="[Command.set([
                        ref('base.ph'),
                    ])]"
       />
       <field name="supported_currency_ids"
              eval="[Command.set([
                        ref('base.PHP'),
                    ])]"
       />
    </record>

    <record id="payment_method_brankas" model="payment.method">
        <field name="name">BRANKAS</field>
        <field name="code">brankas</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/brankas.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bri" model="payment.method">
        <field name="name">BRI</field>
        <field name="code">bri</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bri.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_bsi" model="payment.method">
        <field name="name">Bank Syariah Indonesia</field>
        <field name="code">bsi</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bsi.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_card" model="payment.method">
        <field name="name">Card</field>
        <field name="code">card</field>
        <field name="sequence">10</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
        <field name="support_tokenization">True</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
    </record>

    <record id="payment_method_cash_app_pay" model="payment.method">
        <field name="name">Cash App Pay</field>
        <field name="code">cash_app_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cash_app_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.us'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_cashalo" model="payment.method">
        <field name="name">Cashalo</field>
        <field name="code">cashalo</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cashalo.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_cebuana" model="payment.method">
        <field name="name">Cebuana</field>
        <field name="code">cebuana</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cebuana.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_cimb_niaga" model="payment.method">
        <field name="name">CIMB Niaga</field>
        <field name="code">cimb_niaga</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cimb_niaga.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_clearpay" model="payment.method">
        <field name="name">Clearpay</field>
        <field name="code">clearpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/clearpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.uk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.GBP'),
                     ])]"
        />
    </record>

    <record id="payment_method_cofidis" model="payment.method">
        <field name="name">cofidis</field>
        <field name="code">cofidis</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cofidis.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.be'),
                         ref('base.fr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_dana" model="payment.method">
        <field name="name">Dana</field>
        <field name="code">dana</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/dana.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_dolfin" model="payment.method">
        <field name="name">Dolfin</field>
        <field name="code">dolfin</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/dolfin.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_duitnow" model="payment.method">
        <field name="name">DuitNow</field>
        <field name="code">duitnow</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/duitnow.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.my'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.MYR'),
                     ])]"
        />
    </record>

    <record id="payment_method_emi_india" model="payment.method">
        <field name="name">EMI</field>
        <field name="code">emi_india</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_enets" model="payment.method">
        <field name="name">eNETS</field>
        <field name="code">enets</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/enets.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sg'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SGD'),
                     ])]"
        />
    </record>

    <record id="payment_method_eps" model="payment.method">
        <field name="name">EPS</field>
        <field name="code">eps</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/eps.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.at'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_floa_bank" model="payment.method">
        <field name="name">Floa Bank</field>
        <field name="code">floa_bank</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/floa_bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.br'),
                         ref('base.es'),
                         ref('base.fr'),
                         ref('base.it'),
                         ref('base.pt'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_fps" model="payment.method">
        <field name="name">FPS</field>
        <field name="code">fps</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.hk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.HKD'),
                     ])]"
        />
    </record>

    <record id="payment_method_fpx" model="payment.method">
        <field name="name">FPX</field>
        <field name="code">fpx</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/fpx.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.my'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.MYR'),
                     ])]"
        />
    </record>

    <record id="payment_method_frafinance" model="payment.method">
        <field name="name">Frafinance</field>
        <field name="code">frafinance</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/frafinance.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.be'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_gcash" model="payment.method">
        <field name="name">GCash</field>
        <field name="code">gcash</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/gcash.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_giropay" model="payment.method">
        <field name="name">Giropay</field>
        <field name="code">giropay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/giropay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.de'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_gopay" model="payment.method">
        <field name="name">GoPay</field>
        <field name="code">gopay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/gopay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_grabpay" model="payment.method">
        <field name="name">GrabPay</field>
        <field name="code">grabpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/grabpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.my'),
                         ref('base.sg'),
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SGD'),
                         ref('base.MYR'),
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_gsb" model="payment.method">
        <field name="name">Government Savings Bank</field>
        <field name="code">gsb</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_hd" model="payment.method">
        <field name="name">HD Bank</field>
        <field name="code">hd</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.vn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.VND'),
                     ])]"
        />
    </record>

    <record id="payment_method_hoolah" model="payment.method">
        <field name="name">Hoolah</field>
        <field name="code">hoolah</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/hoolah.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sg'),
                         ref('base.my'),
                         ref('base.hk'),
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SGD'),
                         ref('base.MYR'),
                         ref('base.HKD'),
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_humm" model="payment.method">
        <field name="name">Humm</field>
        <field name="code">humm</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/humm.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                         ref('base.nz'),
                         ref('base.uk'),
                         ref('base.ie'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.NZD'),
                         ref('base.GBP'),
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_ideal" model="payment.method">
        <field name="name">iDEAL</field>
        <field name="code">ideal</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/ideal.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.nl'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_in3" model="payment.method">
        <field name="name">in3</field>
        <field name="code">in3</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/in3.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.nl'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_jeniuspay" model="payment.method">
        <field name="name">JeniusPay</field>
        <field name="code">jeniuspay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/jeniuspay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_jkopay" model="payment.method">
        <field name="name">Jkopay</field>
        <field name="code">jkopay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/jkopay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.cn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CNY'),
                     ])]"
        />
    </record>

    <record id="payment_method_kakaopay" model="payment.method">
        <field name="name">KakaoPay</field>
        <field name="code">kakaopay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/kakaopay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.kr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.KRW'),
                     ])]"
        />
    </record>

    <record id="payment_method_kasikorn_bank" model="payment.method">
        <field name="name">Kasikorn Bank</field>
        <field name="code">kasikorn_bank</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_kbc_cbc" model="payment.method">
        <field name="name">KBC/CBC</field>
        <field name="code">kbc_cbc</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/kbc.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.be'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_klarna" model="payment.method">
        <field name="name">Klarna</field>
        <field name="code">klarna</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/klarna.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                         ref('base.at'),
                         ref('base.be'),
                         ref('base.ca'),
                         ref('base.cz'),
                         ref('base.dk'),
                         ref('base.fi'),
                         ref('base.fr'),
                         ref('base.de'),
                         ref('base.gr'),
                         ref('base.ie'),
                         ref('base.it'),
                         ref('base.nl'),
                         ref('base.nz'),
                         ref('base.no'),
                         ref('base.pl'),
                         ref('base.pt'),
                         ref('base.es'),
                         ref('base.se'),
                         ref('base.ch'),
                         ref('base.uk'),
                         ref('base.us'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.EUR'),
                         ref('base.CAD'),
                         ref('base.CZK'),
                         ref('base.DKK'),
                         ref('base.NZD'),
                         ref('base.NOK'),
                         ref('base.PLN'),
                         ref('base.SEK'),
                         ref('base.CHF'),
                         ref('base.GBP'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_klarna_paynow" model="payment.method">
        <field name="name">Klarna - Pay Now</field>
        <field name="code">klarna_paynow</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/klarna.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.de'),
                         ref('base.nl'),
                         ref('base.se'),
                         ref('base.ch'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.SEK'),
                         ref('base.CHF'),
                     ])]"
        />
    </record>

    <record id="payment_method_klarna_pay_over_time" model="payment.method">
        <field name="name">Klarna - Pay over time</field>
        <field name="code">klarna_pay_over_time</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/klarna.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                         ref('base.at'),
                         ref('base.ca'),
                         ref('base.cz'),
                         ref('base.fi'),
                         ref('base.fr'),
                         ref('base.de'),
                         ref('base.gr'),
                         ref('base.ie'),
                         ref('base.it'),
                         ref('base.nl'),
                         ref('base.nz'),
                         ref('base.no'),
                         ref('base.pt'),
                         ref('base.es'),
                         ref('base.se'),
                         ref('base.uk'),
                         ref('base.us'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.EUR'),
                         ref('base.CAD'),
                         ref('base.CZK'),
                         ref('base.NZD'),
                         ref('base.NOK'),
                         ref('base.SEK'),
                         ref('base.GBP'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_knet" model="payment.method">
        <field name="name">KNET</field>
        <field name="code">knet</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/knet.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.kw'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.KWD'),
                     ])]"
        />
    </record>

    <record id="payment_method_kredivo" model="payment.method">
        <field name="name">Kredivo</field>
        <field name="code">kredivo</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/kredivo.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_krungthai_bank" model="payment.method">
        <field name="name">KrungThai Bank</field>
        <field name="code">krungthai_bank</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_linepay" model="payment.method">
        <field name="name">LINE Pay</field>
        <field name="code">linepay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/linepay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.jp'),
                         ref('base.tw'),
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.JPY'),
                         ref('base.TWD'),
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_linkaja" model="payment.method">
        <field name="name">LinkAja</field>
        <field name="code">linkaja</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/linkaja.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_lydia" model="payment.method">
        <field name="name">Lydia</field>
        <field name="code">lydia</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/lydia.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.fr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.GBP'),
                     ])]"
        />
    </record>

    <record id="payment_method_lyfpay" model="payment.method">
        <field name="name">LyfPay</field>
        <field name="code">lyfpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/lyfpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.fr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_mada" model="payment.method">
        <field name="name">Mada</field>
        <field name="code">mada</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mada.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sa'),
                         ref('base.ae'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SAR'),
                     ])]"
        />
    </record>

    <record id="payment_method_mandiri" model="payment.method">
        <field name="name">Mandiri</field>
        <field name="code">mandiri</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mandiri.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_maya" model="payment.method">
        <field name="name">Maya</field>
        <field name="code">maya</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/maya.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_maybank" model="payment.method">
        <field name="name">Maybank</field>
        <field name="code">maybank</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/maybank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_mbway" model="payment.method">
        <field name="name">MB WAY</field>
        <field name="code">mbway</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mbway.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.pt'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_mobile_money" model="payment.method">
        <field name="name">Mobile money</field>
        <field name="code">mobile_money</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mtn-mobile-money.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.gh'),
                         ref('base.cm'),
                         ref('base.ci'),
                         ref('base.ml'),
                         ref('base.sn'),
                         ref('base.ug'),
                         ref('base.rw'),
                         ref('base.zm'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.GHS'),
                         ref('base.XAF'),
                         ref('base.XOF'),
                         ref('base.UGX'),
                         ref('base.RWF'),
                         ref('base.ZMW'),
                     ])]"
        />
    </record>

    <record id="payment_method_mobile_pay" model="payment.method">
        <field name="name">MobilePay</field>
        <field name="code">mobile_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mobile_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.dk'),
                         ref('base.fi'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.DKK'),
                         ref('base.SEK'),
                         ref('base.NOK'),
                     ])]"
        />
    </record>

    <record id="payment_method_momo" model="payment.method">
        <field name="name">MoMo</field>
        <field name="code">momo</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/momo.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.vn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.VND'),
                     ])]"
        />
    </record>

    <record id="payment_method_mpesa" model="payment.method">
        <field name="name">M-Pesa</field>
        <field name="code">mpesa</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mpesa.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ke'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.KES'),
                     ])]"
        />
    </record>

    <record id="payment_method_multibanco" model="payment.method">
        <field name="name">Multibanco</field>
        <field name="code">multibanco</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/multibanco.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.pt'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_mybank" model="payment.method">
        <field name="name">MyBank</field>
        <field name="code">mybank</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mybank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.it'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_nuvei_local" model="payment.method">
        <field name="name">Local Payments</field>
        <field name="code">nuvei_local</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.uy'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.UYU'),
                     ])]"
        />
    </record>

    <record id="payment_method_napas_card" model="payment.method">
        <field name="name">Napas Card</field>
        <field name="code">napas_card</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/napas_card.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="support_refund">full_only</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.vn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.VND'),
                     ])]"
        />
    </record>

    <record id="payment_method_naver_pay" model="payment.method">
        <field name="name">Naver Pay</field>
        <field name="code">naver_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/naver_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.kr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.KRW'),
                     ])]"
        />
    </record>

    <record id="payment_method_netbanking" model="payment.method">
        <field name="name">Netbanking</field>
        <field name="code">netbanking</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_octopus" model="payment.method">
        <field name="name">Octopus</field>
        <field name="code">octopus</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/octopus.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.hk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.HKD'),
                     ])]"
        />
    </record>

    <record id="payment_method_online_banking_czech_republic" model="payment.method">
        <field name="name">Online Banking Czech Republic</field>
        <field name="code">online_banking_czech_republic</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.cz'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CZK'),
                     ])]"
        />
    </record>

    <record id="payment_method_online_banking_india" model="payment.method">
        <field name="name">Online Banking India</field>
        <field name="code">online_banking_india</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="support_refund">full_only</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_online_banking_slovakia" model="payment.method">
        <field name="name">Online Banking Slovakia</field>
        <field name="code">online_banking_slovakia</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_online_banking_thailand" model="payment.method">
        <field name="name">Online Banking Thailand</field>
        <field name="code">online_banking_thailand</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_open_banking" model="payment.method">
        <field name="name">Open banking</field>
        <field name="code">open_banking</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.uk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.GBP'),
                     ])]"
        />
    </record>

    <record id="payment_method_ovo" model="payment.method">
        <field name="name">OVO</field>
        <field name="code">ovo</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/ovo.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

    <record id="payment_method_oxxopay" model="payment.method">
        <field name="name">Oxxo Pay</field>
        <field name="code">oxxopay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/oxxopay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.mx'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.MXN'),
                     ])]"
        />
    </record>

    <record id="payment_method_paybright" model="payment.method">
        <field name="name">PayBright</field>
        <field name="code">paybright</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paybright.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ca'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CAD'),
                     ])]"
        />
    </record>

    <record id="payment_method_pace" model="payment.method">
        <field name="name">Pace.</field>
        <field name="code">pace</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/pace.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sg'),
                         ref('base.jp'),
                         ref('base.my'),
                         ref('base.hk'),
                         ref('base.tw'),
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SGD'),
                         ref('base.MYR'),
                         ref('base.JPY'),
                         ref('base.HKD'),
                         ref('base.THB'),
                         ref('base.TWD'),
                     ])]"
        />
    </record>

    <record id="payment_method_paylater_india" model="payment.method">
        <field name="name">Pay Later</field>
        <field name="code">paylater_india</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/pay_later.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="support_refund">full_only</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_pay_easy" model="payment.method">
        <field name="name">Pay-easy</field>
        <field name="code">pay_easy</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/pay_easy.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.jp'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.JPY'),
                     ])]"
        />
    </record>

    <record id="payment_method_pay_id" model="payment.method">
        <field name="name">PayID</field>
        <field name="code">pay_id</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/pay_id.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                         ref('base.nz'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.NZD'),
                     ])]"
        />
    </record>

    <record id="payment_method_paylib" model="payment.method">
        <field name="name">Paylib</field>
        <field name="code">paylib</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paylib.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.fr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_payme" model="payment.method">
        <field name="name">PayMe</field>
        <field name="code">payme</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/payme.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.hk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.HKD'),
                     ])]"
        />
    </record>

    <record id="payment_method_paynow" model="payment.method">
        <field name="name">PayNow</field>
        <field name="code">paynow</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paynow.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sg'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SGD'),
                     ])]"
        />
    </record>

    <record id="payment_method_paypal" model="payment.method">
        <field name="name">Paypal</field>
        <field name="code">paypal</field>
        <field name="sequence">20</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paypal.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
    </record>

    <record id="payment_method_paypay" model="payment.method">
        <field name="name">PayPay</field>
        <field name="code">paypay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paypay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.jp'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.JPY'),
                     ])]"
        />
    </record>

    <record id="payment_method_paysafecard" model="payment.method">
        <field name="name">PaySafeCard</field>
        <field name="code">paysafecard</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paysafecard.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="support_refund">full_only</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.at'),
                         ref('base.au'),
                         ref('base.be'),
                         ref('base.br'),
                         ref('base.ca'),
                         ref('base.hr'),
                         ref('base.cy'),
                         ref('base.cz'),
                         ref('base.dk'),
                         ref('base.fi'),
                         ref('base.fr'),
                         ref('base.ge'),
                         ref('base.de'),
                         ref('base.gi'),
                         ref('base.hu'),
                         ref('base.is'),
                         ref('base.ie'),
                         ref('base.it'),
                         ref('base.kw'),
                         ref('base.lv'),
                         ref('base.ie'),
                         ref('base.li'),
                         ref('base.lt'),
                         ref('base.lu'),
                         ref('base.mt'),
                         ref('base.mx'),
                         ref('base.md'),
                         ref('base.me'),
                         ref('base.nl'),
                         ref('base.nz'),
                         ref('base.no'),
                         ref('base.py'),
                         ref('base.pe'),
                         ref('base.pl'),
                         ref('base.pt'),
                         ref('base.ro'),
                         ref('base.sa'),
                         ref('base.rs'),
                         ref('base.sk'),
                         ref('base.si'),
                         ref('base.es'),
                         ref('base.se'),
                         ref('base.ch'),
                         ref('base.tr'),
                         ref('base.ae'),
                         ref('base.uk'),
                         ref('base.us'),
                         ref('base.uy'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.AUD'),
                         ref('base.BRL'),
                         ref('base.CAD'),
                         ref('base.CZK'),
                         ref('base.DKK'),
                         ref('base.GEL'),
                         ref('base.GIP'),
                         ref('base.HUF'),
                         ref('base.ISK'),
                         ref('base.KWD'),
                         ref('base.CHF'),
                         ref('base.MXN'),
                         ref('base.MDL'),
                         ref('base.NZD'),
                         ref('base.NOK'),
                         ref('base.PYG'),
                         ref('base.PEN'),
                         ref('base.PLN'),
                         ref('base.RON'),
                         ref('base.SAR'),
                         ref('base.RSD'),
                         ref('base.SEK'),
                         ref('base.CHF'),
                         ref('base.TRY'),
                         ref('base.AED'),
                         ref('base.GBP'),
                         ref('base.USD'),
                         ref('base.UYU'),
                     ])]"
        />
    </record>

    <record id="payment_method_paytm" model="payment.method">
        <field name="name">Paytm</field>
        <field name="code">paytm</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paytm.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_paytrail" model="payment.method">
        <field name="name">Paytrail</field>
        <field name="code">paytrail</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/paytrail.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.fi'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_payu" model="payment.method">
        <field name="name">PayU</field>
        <field name="code">payu</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/payu.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.pl'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PLN'),
                     ])]"
        />
    </record>

    <record id="payment_method_pix" model="payment.method">
        <field name="name">Pix</field>
        <field name="code">pix</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/pix.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.br'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.BRL'),
                     ])]"
        />
    </record>

    <record id="payment_method_poli" model="payment.method">
        <field name="name">POLi</field>
        <field name="code">poli</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/poli.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                         ref('base.nz'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.NZD'),
                     ])]"
        />
    </record>

    <record id="payment_method_post_finance" model="payment.method">
        <field name="name">PostFinance Pay</field>
        <field name="code">post_finance_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/pf_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.at'),
                         ref('base.be'),
                         ref('base.bg'),
                         ref('base.ch'),
                         ref('base.cy'),
                         ref('base.cz'),
                         ref('base.de'),
                         ref('base.dk'),
                         ref('base.ee'),
                         ref('base.es'),
                         ref('base.fi'),
                         ref('base.fr'),
                         ref('base.gr'),
                         ref('base.hr'),
                         ref('base.hu'),
                         ref('base.ie'),
                         ref('base.it'),
                         ref('base.lt'),
                         ref('base.lu'),
                         ref('base.lv'),
                         ref('base.mt'),
                         ref('base.nl'),
                         ref('base.pl'),
                         ref('base.pt'),
                         ref('base.ro'),
                         ref('base.se'),
                         ref('base.si'),
                         ref('base.sk'),
                         ref('base.uk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CHF'),
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_poste_pay" model="payment.method">
        <field name="name">PostePay</field>
        <field name="code">poste_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/poste_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.it'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_pps" model="payment.method">
        <field name="name">PPS</field>
        <field name="code">pps</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.hk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.HKD'),
                     ])]"
        />
    </record>

    <record id="payment_method_promptpay" model="payment.method">
        <field name="name">Prompt Pay</field>
        <field name="code">promptpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/promptpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_pse" model="payment.method">
        <field name="name">PSE</field>
        <field name="code">pse</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.co'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.COP'),
                     ])]"
        />
    </record>

    <record id="payment_method_p24" model="payment.method">
        <field name="name">P24</field>
        <field name="code">p24</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/p24.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.pl'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.PLN'),
                     ])]"
        />
    </record>

    <record id="payment_method_qris" model="payment.method">
        <field name="name">QRIS</field>
        <field name="code">qris</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/qris.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),
                     ])]"
        />
    </record>

     <record id="payment_method_rabbit_line_pay" model="payment.method">
        <field name="name">Rabbit LINE Pay</field>
        <field name="code">rabbit_line_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/rabbit_line_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_ratepay" model="payment.method">
        <field name="name">Ratepay</field>
        <field name="code">ratepay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/ratepay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.at'),
                         ref('base.de'),
                         ref('base.nl'),
                         ref('base.ch'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.CHF'),
                     ])]"
        />
    </record>

    <record id="payment_method_revolut_pay" model="payment.method">
        <field name="name">Revolut Pay</field>
        <field name="code">revolut_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/revolut_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.uk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.GBP'),
                     ])]"
        />
    </record>

    <record id="payment_method_samsung_pay" model="payment.method">
        <field name="name">Samsung Pay</field>
        <field name="code">samsung_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/samsung_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
    </record>


    <record id="payment_method_scb" model="payment.method">
        <field name="name">Siam Commerical Bank</field>
        <field name="code">scb</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_sepa_direct_debit" model="payment.method">
        <field name="name">SEPA Direct Debit</field>
        <field name="code">sepa_direct_debit</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/sepa.png"/>
        <field name="support_tokenization">True</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.at'),
                         ref('base.be'),
                         ref('base.cy'),
                         ref('base.ee'),
                         ref('base.fi'),
                         ref('base.fr'),
                         ref('base.de'),
                         ref('base.gr'),
                         ref('base.ie'),
                         ref('base.it'),
                         ref('base.lv'),
                         ref('base.lt'),
                         ref('base.lu'),
                         ref('base.mt'),
                         ref('base.nl'),
                         ref('base.pt'),
                         ref('base.sk'),
                         ref('base.si'),
                         ref('base.es'),
                         ref('base.ch'),
                         ref('base.cz'),
                         ref('base.uk'),
                         ref('base.is'),
                         ref('base.hu'),
                         ref('base.ro'),
                         ref('base.se'),
                         ref('base.hr'),
                         ref('base.no'),
                         ref('base.bg'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_shopback" model="payment.method">
        <field name="name">ShopBack</field>
        <field name="code">shopback</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/shopback.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sg'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SGD'),
                     ])]"
        />
    </record>

    <record id="payment_method_shopeepay" model="payment.method">
        <field name="name">ShopeePay</field>
        <field name="code">shopeepay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/shopeepay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.id'),
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.IDR'),

                     ])]"
        />
    </record>

    <record id="payment_method_sofort" model="payment.method">
        <field name="name">Sofort</field>
        <field name="code">sofort</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/sofort.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.de'),
                         ref('base.at'),
                         ref('base.be'),
                         ref('base.nl'),
                         ref('base.es'),
                         ref('base.ch'),
                         ref('base.pl'),
                         ref('base.it'),
                         ref('base.uk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.CHF'),
                     ])]"
        />
    </record>

    <record id="payment_method_spei" model="payment.method">
        <field name="name">SPEI</field>
        <field name="code">spei</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/spei.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.mx'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.MXN'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_swish" model="payment.method">
        <field name="name">Swish</field>
        <field name="code">swish</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/swish.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.se'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SEK'),
                     ])]"
        />
    </record>

    <record id="payment_method_techcom" model="payment.method">
        <field name="name">Techcombank</field>
        <field name="code">techcom</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/techcom.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.vn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.VND'),
                     ])]"
        />
    </record>

    <record id="payment_method_tendopay" model="payment.method">
        <field name="name">TendoPay</field>
        <field name="code">tendopay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/tendopay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ph'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.PHP'),
                     ])]"
        />
    </record>

    <record id="payment_method_tenpay" model="payment.method">
        <field name="name">TENPAY</field>
        <field name="code">tenpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/tenpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.cn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CNY'),
                     ])]"
        />
    </record>

    <record id="payment_method_tienphong" model="payment.method">
        <field name="name">Tienphong</field>
        <field name="code">tienphong</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.vn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.VND'),
                     ])]"
        />
    </record>

    <record id="payment_method_tinka" model="payment.method">
        <field name="name">Tinka</field>
        <field name="code">tinka</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/tinka.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.nl'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                     ])]"
        />
    </record>

    <record id="payment_method_tmb" model="payment.method">
        <field name="name">Tamilnad Mercantile Bank Limited</field>
        <field name="code">tmb</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/tmb.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_toss_pay" model="payment.method">
        <field name="name">Toss Pay</field>
        <field name="code">toss_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/toss_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.kr'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.KRW'),
                     ])]"
        />
    </record>

    <record id="payment_method_touch_n_go" model="payment.method">
        <field name="name">Touch'n Go</field>
        <field name="code">touch_n_go</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/touch_n_go.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.my'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.MYR'),
                     ])]"
        />
    </record>

    <record id="payment_method_truemoney" model="payment.method">
        <field name="name">TrueMoney</field>
        <field name="code">truemoney</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/truemoney.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_trustly" model="payment.method">
        <field name="name">Trustly</field>
        <field name="code">trustly</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/trustly.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.at'),
                         ref('base.de'),
                         ref('base.dk'),
                         ref('base.ee'),
                         ref('base.es'),
                         ref('base.fi'),
                         ref('base.uk'),
                         ref('base.lv'),
                         ref('base.lt'),
                         ref('base.nl'),
                         ref('base.no'),
                         ref('base.se'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.GBP'),
                         ref('base.DKK'),
                         ref('base.SEK'),
                         ref('base.NOK'),
                         ref('base.EUR'),
                         ref('base.CZK'),
                     ])]"
        />
    </record>

    <record id="payment_method_ttb" model="payment.method">
        <field name="name">TTB</field>
        <field name="code">ttb</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.th'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.THB'),
                     ])]"
        />
    </record>

    <record id="payment_method_twint" model="payment.method">
        <field name="name">Twint</field>
        <field name="code">twint</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/twint.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.ch'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CHF'),
                     ])]"
        />
    </record>

    <record id="payment_method_uatp" model="payment.method">
        <field name="name">Universal Air Travel Plan</field>
        <field name="code">uatp</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/uatp.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
    </record>

    <record id="payment_method_unknown" model="payment.method">
        <field name="name">Payment method</field>
        <field name="code">unknown</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/unknown.png"/>
        <field name="support_tokenization">True</field>
        <field name="support_express_checkout">True</field>
        <field name="support_refund">partial</field>
    </record>

    <record id="payment_method_uob" model="payment.method">
        <field name="name">United Overseas Bank</field>
        <field name="code">uob</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.sg'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.SGD'),
                     ])]"
        />
    </record>

    <record id="payment_method_upi" model="payment.method">
        <field name="name">UPI</field>
        <field name="code">upi</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/upi.png"/>
        <field name="support_tokenization">True</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_ussd" model="payment.method">
        <field name="name">USSD</field>
        <field name="code">ussd</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/flutterwave.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
    </record>

    <record id="payment_method_venmo" model="payment.method">
        <field name="name">Venmo</field>
        <field name="code">venmo</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/venmo.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.us'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_vietcom" model="payment.method">
        <field name="name">Vietcombank</field>
        <field name="code">vietcom</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/vietcom.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.vn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.VND'),
                     ])]"
        />
    </record>

    <record id="payment_method_vipps" model="payment.method">
        <field name="name">Vipps</field>
        <field name="code">vipps</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/vipps.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.no'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.NOK'),
                     ])]"
        />
    </record>

    <record id="payment_method_vpay" model="payment.method">
        <field name="name">V PAY</field>
        <field name="code">vpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/vpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.EUR'),
                         ref('base.GBP'),
                         ref('base.PLN'),
                         ref('base.DKK'),
                         ref('base.NOK'),
                         ref('base.SEK'),
                         ref('base.CHF'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_wallets_india" model="payment.method">
        <field name="name">Wallets India</field>
        <field name="code">wallets_india</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/wallet.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="support_refund">full_only</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.in'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.INR'),
                     ])]"
        />
    </record>

    <record id="payment_method_walley" model="payment.method">
        <field name="name">Walley</field>
        <field name="code">walley</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/walley.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.dk'),
                         ref('base.fi'),
                         ref('base.no'),
                         ref('base.se'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.DKK'),
                         ref('base.EUR'),
                         ref('base.NOK'),
                         ref('base.SEK'),
                     ])]"
        />
    </record>

    <record id="payment_method_webpay" model="payment.method">
        <field name="name">WebPay</field>
        <field name="code">webpay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/webpay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.cl'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.CLP'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <record id="payment_method_wechat_pay" model="payment.method">
        <field name="name">WeChat Pay</field>
        <field name="code">wechat_pay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/wechat_pay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.EUR'),
                         ref('base.CAD'),
                         ref('base.CNY'),
                         ref('base.HKD'),
                         ref('base.JPY'),
                         ref('base.NZD'),
                         ref('base.GBP'),
                         ref('base.USD'),
                         ref('base.SGD'),
                     ])]"
        />
    </record>

    <record id="payment_method_welend" model="payment.method">
        <field name="name">WeLend</field>
        <field name="code">welend</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/welend.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.hk'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.HKD'),
                     ])]"
        />
    </record>

    <record id="payment_method_zalopay" model="payment.method">
        <field name="name">Zalopay</field>
        <field name="code">zalopay</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/zalopay.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">none</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.vn'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.VND'),
                     ])]"
        />
    </record>

    <record id="payment_method_zip" model="payment.method">
        <field name="name">Zip</field>
        <field name="code">zip</field>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/zip.png"/>
        <field name="support_tokenization">False</field>
        <field name="support_express_checkout">False</field>
        <field name="support_refund">partial</field>
        <field name="supported_country_ids"
               eval="[Command.set([
                         ref('base.au'),
                         ref('base.ca'),
                         ref('base.nz'),
                         ref('base.us'),
                     ])]"
        />
        <field name="supported_currency_ids"
               eval="[Command.set([
                         ref('base.AUD'),
                         ref('base.CAD'),
                         ref('base.NZD'),
                         ref('base.USD'),
                     ])]"
        />
    </record>

    <!-- === PAYMENT METHOD BRANDS === -->

    <record id="payment_method_abitab" model="payment.method">
        <field name="name">Abitab</field>
        <field name="code">abitab</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_nuvei_local')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/abitab.png"/>
    </record>

    <record id="payment_method_amex" model="payment.method">
        <field name="name">American Express</field>
        <field name="code">amex</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/amex.png"/>
    </record>

    <record id="payment_method_argencard" model="payment.method">
        <field name="name">Argencard</field>
        <field name="code">argencard</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/argencard.png"/>
    </record>

    <record id="payment_method_banco_de_bogota" model="payment.method">
        <field name="name">Banco de Bogota</field>
        <field name="code">banco_de_bogota</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_bank_reference')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bank.png"/>
    </record>

    <record id="payment_method_banco_guayaquil" model="payment.method">
        <field name="name">Banco Guayaquil</field>
        <field name="code">banco_guayaquil</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_astropay')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/banco_guayaquil.png"/>
    </record>

    <record id="payment_method_bancolombia" model="payment.method">
        <field name="name">Bancolombia</field>
        <field name="code">bancolombia</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_bank_reference')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/bancolombia.png"/>
    </record>

    <record id="payment_method_banco_pichincha" model="payment.method">
        <field name="name">Banco Pichincha</field>
        <field name="code">banco_pichincha</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_astropay')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/banco_pichincha.png"/>
    </record>

    <record id="payment_method_cabal" model="payment.method">
        <field name="name">Cabal</field>
        <field name="code">cabal</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cabal.png"/>
    </record>

    <record id="payment_method_caixa" model="payment.method">
        <field name="name">Caixa</field>
        <field name="code">caixa</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/caixa.png"/>
    </record>

    <record id="payment_method_carnet" model="payment.method">
        <field name="name">Carnet</field>
        <field name="code">carnet</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/wallet.png"/>
    </record>

    <record id="payment_method_cartes_bancaires" model="payment.method">
        <field name="name">Cartes Bancaires</field>
        <field name="code">cartes_bancaires</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
    </record>

    <record id="payment_method_cencosud" model="payment.method">
        <field name="name">Cencosud</field>
        <field name="code">cencosud</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cencosud.png"/>
    </record>

    <record id="payment_method_cirrus" model="payment.method">
        <field name="name">Cirrus</field>
        <field name="code">cirrus</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cirrus.png"/>
    </record>

    <record id="payment_method_cmr" model="payment.method">
        <field name="name">CMR</field>
        <field name="code">cmr</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
    </record>

    <record id="payment_method_codensa" model="payment.method">
        <field name="name">Codensa</field>
        <field name="code">codensa</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/codensa.png"/>
    </record>

    <record id="payment_method_cordial" model="payment.method">
        <field name="name">Cordial</field>
        <field name="code">cordial</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cordial.png"/>
    </record>

    <record id="payment_method_cordobesa" model="payment.method">
        <field name="name">Cordobesa</field>
        <field name="code">cordobesa</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/cordobesa.png"/>
    </record>

    <record id="payment_method_credit" model="payment.method">
        <field name="name">Credit Payment</field>
        <field name="code">credit</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
    </record>

    <record id="payment_method_dankort" model="payment.method">
        <field name="name">Dankort</field>
        <field name="code">dankort</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/dankort.png"/>
    </record>

    <record id="payment_method_davivienda" model="payment.method">
        <field name="name">Davivienda</field>
        <field name="code">davivienda</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_bank_reference')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/davivienda.png"/>
    </record>

    <record id="payment_method_diners" model="payment.method">
        <field name="name">Diners Club International</field>
        <field name="code">diners</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/diners.png"/>
    </record>

    <record id="payment_method_discover" model="payment.method">
        <field name="name">Discover</field>
        <field name="code">discover</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/discover.png"/>
    </record>

    <record id="payment_method_elo" model="payment.method">
        <field name="name">Elo</field>
        <field name="code">elo</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/elo.png"/>
    </record>

    <record id="payment_method_facilito" model="payment.method">
        <field name="name">Facilito</field>
        <field name="code">facilito</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_astropay')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/facilito.png"/>
    </record>

    <record id="payment_method_hipercard" model="payment.method">
        <field name="name">Hipercard</field>
        <field name="code">hipercard</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/hipercard.png"/>
    </record>

    <record id="payment_method_jcb" model="payment.method">
        <field name="name">JCB</field>
        <field name="code">jcb</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/jcb.png"/>
    </record>

    <record id="payment_method_lider" model="payment.method">
        <field name="name">Lider</field>
        <field name="code">lider</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/lider.png"/>
    </record>

    <record id="payment_method_mercado_livre" model="payment.method">
        <field name="name">Mercado Livre</field>
        <field name="code">mercado_livre</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mercado_livre.png"/>
    </record>


    <record id="payment_method_meeza" model="payment.method">
        <field name="name">Meeza</field>
        <field name="code">meeza</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/meeza.png"/>
    </record>

    <record id="payment_method_maestro" model="payment.method">
        <field name="name">Maestro</field>
        <field name="code">maestro</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/maestro.png"/>
    </record>

    <record id="payment_method_magna" model="payment.method">
        <field name="name">Magna</field>
        <field name="code">magna</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/magna.png"/>
    </record>

    <record id="payment_method_mastercard" model="payment.method">
        <field name="name">MasterCard</field>
        <field name="code">mastercard</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/mastercard.png"/>
    </record>

    <record id="payment_method_naps" model="payment.method">
        <field name="name">NAPS</field>
        <field name="code">naps</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/naps.png"/>
    </record>

    <record id="payment_method_naranja" model="payment.method">
        <field name="name">Naranja</field>
        <field name="code">naranja</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/naranja.png"/>
    </record>

    <record id="payment_method_nativa" model="payment.method">
        <field name="name">Nativa</field>
        <field name="code">nativa</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/nativa.png"/>
    </record>

    <record id="payment_method_oca" model="payment.method">
        <field name="name">Oca</field>
        <field name="code">oca</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/card.png"/>
    </record>

    <record id="payment_method_omannet" model="payment.method">
        <field name="name">OmanNet</field>
        <field name="code">omannet</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/omannet.png"/>
    </record>

    <record id="payment_method_presto" model="payment.method">
        <field name="name">Presto</field>
        <field name="code">presto</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/presto.png"/>
    </record>

    <record id="payment_method_redpagos" model="payment.method">
        <field name="name">Redpagos</field>
        <field name="code">redpagos</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_nuvei_local')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/redpagos.png"/>
    </record>

    <record id="payment_method_rupay" model="payment.method">
        <field name="name">RuPay</field>
        <field name="code">rupay</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/rupay.png"/>
    </record>

    <record id="payment_method_shopping" model="payment.method">
        <field name="name">Shopping Card</field>
        <field name="code">shopping</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/shopping.png"/>
    </record>

    <record id="payment_method_tarjeta_mercadopago" model="payment.method">
        <field name="name">Tarjeta MercadoPago</field>
        <field name="code">tarjeta_mercadopago</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/tarjeta_mercadopago.png"/>
    </record>

    <record id="payment_method_unionpay" model="payment.method">
        <field name="name">UnionPay</field>
        <field name="code">unionpay</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/unionpay.png"/>
    </record>

    <record id="payment_method_visa" model="payment.method">
        <field name="name">VISA</field>
        <field name="code">visa</field>
        <field name="primary_payment_method_id" eval="ref('payment.payment_method_card')"/>
        <field name="sequence">1000</field>
        <field name="active">False</field>
        <field name="image" type="base64" file="payment/static/img/visa.png"/>
    </record>

</odoo>
