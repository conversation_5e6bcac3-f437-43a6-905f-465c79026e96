# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_razorpay_oauth
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-12-16 13:41+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid "Account ID"
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.authorization_error
msgid "An error occurred"
msgstr ""

#. module: payment_razorpay_oauth
#. odoo-python
#: code:addons/payment_razorpay_oauth/models/payment_provider.py:0
msgid "An error occurred when communicating with the proxy."
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.authorization_error
msgid "An error occurred while linking your Razorpay account with Odoo."
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid "Are you sure you want to disconnect?"
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.authorization_error
msgid "Back to the Razorpay provider"
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid "Connect"
msgstr ""

#. module: payment_razorpay_oauth
#. odoo-python
#: code:addons/payment_razorpay_oauth/models/payment_provider.py:0
msgid "Could not establish the connection."
msgstr ""

#. module: payment_razorpay_oauth
#. odoo-python
#: code:addons/payment_razorpay_oauth/controllers/onboarding.py:0
msgid "Could not find Razorpay provider with id %s"
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid "Generate your webhook"
msgstr ""

#. module: payment_razorpay_oauth
#. odoo-python
#: code:addons/payment_razorpay_oauth/models/payment_provider.py:0
msgid "Other Payment Providers"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model,name:payment_razorpay_oauth.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_access_token
msgid "Razorpay Access Token"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_access_token_expiry
msgid "Razorpay Access Token Expiry"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_account_id
msgid "Razorpay Account ID"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_key_id
msgid "Razorpay Key Id"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_key_secret
msgid "Razorpay Key Secret"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_public_token
msgid "Razorpay Public Token"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_refresh_token
msgid "Razorpay Refresh Token"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,field_description:payment_razorpay_oauth.field_payment_provider__razorpay_webhook_secret
msgid "Razorpay Webhook Secret"
msgstr ""

#. module: payment_razorpay_oauth
#. odoo-python
#: code:addons/payment_razorpay_oauth/models/payment_provider.py:0
msgid ""
"Razorpay is not available in your country; please use another payment "
"provider."
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid "Reset Your Razorpay Account"
msgstr ""

#. module: payment_razorpay_oauth
#: model:ir.model.fields,help:payment_razorpay_oauth.field_payment_provider__razorpay_key_id
msgid "The key solely used to identify the account with Razorpay."
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid "This provider is linked with your Razorpay account."
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid "Webhook Secret"
msgstr ""

#. module: payment_razorpay_oauth
#: model_terms:ir.ui.view,arch_db:payment_razorpay_oauth.payment_provider_form_razorpay_oauth
msgid ""
"You are currently connected to Razorpay through the credentials method, which is\n"
"                    deprecated. Click the \"Connect\" button below to use the recommended OAuth\n"
"                    method."
msgstr ""

#. module: payment_razorpay_oauth
#. odoo-python
#: code:addons/payment_razorpay_oauth/models/payment_provider.py:0
msgid "Your Razorpay webhook was successfully set up!"
msgstr ""
