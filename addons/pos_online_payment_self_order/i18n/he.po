# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_online_payment_self_order
# 
# Translators:
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: pos_online_payment_self_order
#: model:ir.model,name:pos_online_payment_self_order.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: pos_online_payment_self_order
#: model_terms:ir.ui.view,arch_db:pos_online_payment_self_order.res_config_settings_view_form_menu
msgid "Online Payment"
msgstr "תשלום מקוון"

#. module: pos_online_payment_self_order
#. odoo-javascript
#: code:addons/pos_online_payment_self_order/static/src/components/order_widget/order_widget.js:0
msgid "Order"
msgstr "הזמנה"

#. module: pos_online_payment_self_order
#. odoo-javascript
#: code:addons/pos_online_payment_self_order/static/src/components/order_widget/order_widget.js:0
msgid "Pay"
msgstr "שלם"

#. module: pos_online_payment_self_order
#. odoo-javascript
#: code:addons/pos_online_payment_self_order/static/src/components/order_widget/order_widget.js:0
msgid "Pay at cashier"
msgstr ""

#. module: pos_online_payment_self_order
#: model_terms:ir.ui.view,arch_db:pos_online_payment_self_order.res_config_settings_view_form_menu
msgid "Pay at cashier if empty"
msgstr ""

#. module: pos_online_payment_self_order
#: model_terms:ir.ui.view,arch_db:pos_online_payment_self_order.res_config_settings_view_form_menu
msgid "Payment Methods"
msgstr "אמצעי תשלום"

#. module: pos_online_payment_self_order
#. odoo-javascript
#: code:addons/pos_online_payment_self_order/static/src/pages/payment_page/payment_page.xml:0
msgid "Payment in progress"
msgstr "תשלום בתהליך"

#. module: pos_online_payment_self_order
#: model:ir.model,name:pos_online_payment_self_order.model_pos_config
msgid "Point of Sale Configuration"
msgstr "תצורת קופה"

#. module: pos_online_payment_self_order
#: model:ir.model,name:pos_online_payment_self_order.model_pos_order
msgid "Point of Sale Orders"
msgstr "הזמנות קופה"

#. module: pos_online_payment_self_order
#: model:ir.model,name:pos_online_payment_self_order.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "אמצעי תשלום קופה"

#. module: pos_online_payment_self_order
#. odoo-javascript
#: code:addons/pos_online_payment_self_order/static/src/pages/payment_page/payment_page.xml:0
msgid "Scan the QR code to pay"
msgstr ""

#. module: pos_online_payment_self_order
#: model:ir.model.fields,field_description:pos_online_payment_self_order.field_pos_config__self_order_online_payment_method_id
#: model:ir.model.fields,field_description:pos_online_payment_self_order.field_res_config_settings__pos_self_order_online_payment_method_id
msgid "Self Online Payment"
msgstr ""

#. module: pos_online_payment_self_order
#. odoo-javascript
#: code:addons/pos_online_payment_self_order/static/src/pages/payment_page/payment_page.js:0
msgid "The current order cannot be paid (maybe it is already paid)."
msgstr ""

#. module: pos_online_payment_self_order
#: model:ir.model.fields,help:pos_online_payment_self_order.field_pos_config__self_order_online_payment_method_id
#: model:ir.model.fields,help:pos_online_payment_self_order.field_res_config_settings__pos_self_order_online_payment_method_id
msgid ""
"The online payment method to use when a customer pays a self-order online."
msgstr ""

#. module: pos_online_payment_self_order
#. odoo-python
#: code:addons/pos_online_payment_self_order/models/pos_config.py:0
msgid ""
"The online payment method used for self-order in a POS config must have at "
"least one published payment provider supporting the currency of that POS "
"config."
msgstr ""

#. module: pos_online_payment_self_order
#: model:ir.model.fields,field_description:pos_online_payment_self_order.field_pos_order__use_self_order_online_payment
msgid "Use Self Order Online Payment"
msgstr ""
