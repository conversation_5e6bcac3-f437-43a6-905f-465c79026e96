# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
msgid " '*' is not a valid Regex Barcode Pattern. Did you mean '.*'?"
msgstr ""
" '*' no es un patrón de código de barras válido para la expresión regular. "
"¿Se refiere a'.*'?"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"<i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.\n"
"                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching\n"
"                                pattern. The pattern syntax is that of regular expression, and a barcode is matched\n"
"                                if the regular expression matches a prefix of the barcode."
msgstr ""
"Las <i>nomenclaturas de códigos de barras</i> definen cómo se reconocen y categorizan\n"
"los códigos de barras. Cuando se escanea un código de barras se asocia a la <i>primera</i>\n"
"regla con un patrón coincidente. La sintaxis de los patrones es la de una expresión\n"
"regular, y un código de barras se reconoce si la expresión regular coincide con un\n"
"prefijo del código de barras."

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid ""
"A barcode nomenclature defines how the point of sale identify and interprets"
" barcodes"
msgstr ""
"Una nomenclatura de código de barras define cómo un punto de venta "
"identifica e interpreta los códigos de barras"

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid "Add a new barcode nomenclature"
msgstr "Añadir una nueva nomenclatura de código de barras"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__alias
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__alias
msgid "Alias"
msgstr "Alias"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__always
msgid "Always"
msgstr "Siempre"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__name
msgid "An internal identification for this barcode nomenclature rule"
msgstr ""
"Un identificador interno para esta regla de nomenclatura del código de "
"barras"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__name
msgid "An internal identification of the barcode nomenclature"
msgstr "Un identificador interno para la nomenclatura de código de barras"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__any
msgid "Any"
msgstr "Cualquiera"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/components/barcode_scanner.xml:0
msgid "Barcode"
msgstr "Código de barras"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcodes_barcode_events_mixin
msgid "Barcode Event Mixin"
msgstr "Mixin de eventos de código de barras"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_nomenclature
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Barcode Nomenclature"
msgstr "Nomenclatura de código de barras"

#. module: barcodes
#: model:ir.actions.act_window,name:barcodes.action_barcode_nomenclature_form
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_tree
msgid "Barcode Nomenclatures"
msgstr "Nomenclaturas de código de barras"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__pattern
msgid "Barcode Pattern"
msgstr "Patrón de código de barras"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_rule
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_rule_form
msgid "Barcode Rule"
msgstr "Regla de código de barras"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Barcode Scanned"
msgstr "Código de barras escaneado"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
msgid "Barcode: %(barcode)s"
msgstr "Código de barras: %(barcode)s"

#. module: barcodes
#: model:ir.model,name:barcodes.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_date
msgid "Created on"
msgstr "Creado el"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__display_name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean13
msgid "EAN-13"
msgstr "EAN-13"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__ean2upc
msgid "EAN-13 to UPC-A"
msgstr "EAN-13 a UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean8
msgid "EAN-8"
msgstr "EAN-8"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__encoding
msgid "Encoding"
msgstr "Codificación"

#. module: barcodes
#: model:ir.model,name:barcodes.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__id
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__id
msgid "ID"
msgstr "ID"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_events_mixin.py:0
msgid ""
"In order to use barcodes.barcode_events_mixin, method on_barcode_scanned "
"must be implemented"
msgstr ""
"Para poder utilizar barcodes.barcode_events_mixin, es necesario implementar "
"el método on_barcode_scanned."

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__none
msgid "Never"
msgstr "Nunca"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_res_company__nomenclature_id
msgid "Nomenclature"
msgstr "Nomenclatura"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"Patterns can also define how numerical values, such as weight or price, can be\n"
"                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's\n"
"                                define where the number's digits are encoded. Floats are also supported with the\n"
"                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases,\n"
"                                the barcode field on the associated records <i>must</i> show these digits as\n"
"                                zeroes."
msgstr ""
"Los patrones también pueden definir el modo en que los valores numéricos, como el peso o el precio, pueden\n"
"                                codificarse en el código de barras. Estos se indican con el parámetro <code>{NNN}</code> donde las N\n"
"                                definen dónde se codifican los dígitos del número. También se admiten valores flotantes con los\n"
"                                decimales indicados con D, como <code>{NNNDD}</code>. En estos casos,\n"
"                                el campo de código de barras de los registros asociados <i>debe</i> mostrar estos dígitos como\n"
"                                ceros."

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/components/barcode_scanner.js:0
msgid "Please, Scan again!"
msgstr "¡Escanee de nuevo!"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__name
msgid "Rule Name"
msgstr "Nombre de regla"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__rule_ids
msgid "Rules"
msgstr "Reglas"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Tables"
msgstr "Tablas"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/components/barcode_scanner.xml:0
msgid "Tap to scan"
msgstr "Presione para escanear"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__pattern
msgid "The barcode matching pattern"
msgstr "Patrón de concordancia para el código de barras"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
msgid ""
"The barcode pattern %(pattern)s does not lead to a valid regular expression."
msgstr ""
"El patrón %(pattern)s del código de barras no lleva a una expresión regular "
"válida."

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__rule_ids
msgid "The list of barcode rules"
msgstr "La lista de reglas de códigos de barras"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__alias
msgid "The matched pattern will alias to this barcode"
msgstr "El patrón de concordancia será un alias para este código de barras"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: a rule can only "
"contain one pair of braces."
msgstr ""
"Hay un error de sintaxis en el patrón del código de barras %(pattern)s: una "
"regla solo puede contener un par de llaves."

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: braces can only "
"contain N's followed by D's."
msgstr ""
"Hay un error de sintaxis en el patrón del código de barras %(pattern)s: las "
"llaves solo pueden contener N seguido de D."

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: empty braces."
msgstr ""
"Hay un error de sintaxis en el patrón del código de barras %(pattern)s: "
"llaves vacías."

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"Esta regla solo aplicará si el código de barras está codificado con la "
"codificación especificada"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__type
msgid "Type"
msgstr "Tipo"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid ""
"UPC Codes can be converted to EAN by prefixing them with a zero. This "
"setting determines if a UPC/EAN barcode should be automatically converted in"
" one way or another when trying to match a rule with the other encoding."
msgstr ""
"Los códigos UPC se pueden convertir en EAN prefijándolos con un cero. Este "
"ajuste determina si un código de barras UPC/EAN debe ser convertido "
"automáticamente en uno u otro sentido al buscar una coincidencia con la otra"
" codificación."

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__upca
msgid "UPC-A"
msgstr "UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__upc2ean
msgid "UPC-A to EAN-13"
msgstr "UPC-A a EAN-13"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid "UPC/EAN Conversion"
msgstr "Conversión UPC/EAN"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__product
msgid "Unit Product"
msgstr "Producto unitario"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
msgid "Unknown barcode command"
msgstr "Comando de código de barras desconocido"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__sequence
msgid ""
"Used to order rules such that rules with a smaller sequence match first"
msgstr ""
"Usado para ordenar reglas. Primero se busca una concordancia para reglas con"
" menor secuencia."

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Valor del último código de barras escaneado."
