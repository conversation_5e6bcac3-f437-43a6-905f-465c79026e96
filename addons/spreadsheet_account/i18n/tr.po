# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "%s is not a valid year."
msgstr "geçerli bir yıl değil."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""
"%s geçerli bir dönem biçemi değildir. Geçerli dönem biçemleri 21/12/2022\", "
"\"Q1/2022\", \"12/2022\" ve \"2022\"dir."

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "Hesap"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Bank and Cash"
msgstr "Banka ve Kasa"

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
msgid "Cell Audit"
msgstr ""

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Cost of Revenue"
msgstr "Gelir Maliyeti"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Credit Card"
msgstr "Kredi Kartı"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Current Assets"
msgstr "Dönen Varlıklar"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Current Liabilities"
msgstr "Kısa Vadeli Borçlar"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Current Year Earnings"
msgstr "Cari Yıldaki Kazanç"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Depreciation"
msgstr "Amortisman"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Equity"
msgstr "Özsermaye"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Expenses"
msgstr "Masraflar"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Fixed Assets"
msgstr "Duran Varlıklar"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total balance for the specified account(s) and period."
msgstr "Belirtilen hesap(lar) ve dönem için toplam bakiyeyi alın."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total credit for the specified account(s) and period."
msgstr "Belirtilen hesap(lar) ve dönem için toplam alacağı alın."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total debit for the specified account(s) and period."
msgstr "Belirtilen hesap(lar) ve dönem içim toplam borcu alın."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Income"
msgstr "Gelir"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Non-current Assets"
msgstr "Cari Olmayan Varlıklar "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Non-current Liabilities"
msgstr "Uzun Vadeli Yükümlülükler"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Off-Balance Sheet"
msgstr "Bilanço Dışı"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Offset applied to the years."
msgstr "Yıllara uygulanan sapma"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Other Income"
msgstr "Diğer Gelirler"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Payable"
msgstr "Borç"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Prepayments"
msgstr "Avans Ödemeleri"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Receivable"
msgstr "Alacak"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Return the partner balance for the specified account(s) and period"
msgstr "Belirtilen hesap(lar) ve dönem için İş Ortağı bakiyesini getir"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Return the residual amount for the specified account(s) and period"
msgstr "Belirtilen hesap(lar) ve dönem için kalan tutarı getir"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Returns the account codes of a given group."
msgstr "Belirlenen grup için hesap numaralarını getirir."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr "Verilen tarihi kapsayan mali yılın bitiş tarihini verir."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr "Verilen tarihi kapsayan mali yılın başlangıç tarihini verir."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
msgid "See records"
msgstr "Kayıtlara bakın"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Set to TRUE to include unposted entries."
msgstr "Doğrulanmayan kayıtları dahil etmek için DOĞRU olarak ayarlayın."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The balance for given partners could not be computed."
msgstr "Belirtilen İş Ortakları için bakiye hesaplanamıyor."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The company fiscal year could not be found."
msgstr "Firma mali yılı bulunamadı."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company to target (Advanced)."
msgstr "Hedeflenecek şirket (Gelişmiş)."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company."
msgstr "Şirket."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"The date range. Supported formats are \"21/12/2022\", \"Q1/2022\", "
"\"12/2022\", and \"2022\"."
msgstr ""
"Tarih aralığı. Desteklenen biçemler \"21/12/2022\", \"Q1/2022\", \"12/2022\""
" ve \"2022\"dir."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year end."
msgstr "Mali yıl sonunun belirleneceği gün"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year start."
msgstr "Mali yıl başlangıcının belirleneceği gün"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The partner ids (separated by a comma)."
msgstr "Partner kimlikleri (virgülle ayrılmış)"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The prefix of the accounts."
msgstr "Hesapların ön eki."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"The prefix of the accounts. If none provided, all receivable and payable "
"accounts will be used."
msgstr ""
"Hesapların ön eki. Belirtilmediği durumda tüm alacak ve borç hesapları "
"kullanılacaktır."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The residual amount for given accounts could not be computed."
msgstr "Belirtilen hesaplar için kalan tutar hesaplanamadı."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The technical account type (possible values are: %s)."
msgstr "Teknik hesap türü (olası değerler: %s)."
