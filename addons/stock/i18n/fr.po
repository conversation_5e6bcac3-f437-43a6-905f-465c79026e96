# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"\n"
"\n"
"Transfers %(transfer_list)s: You need to supply a Lot/Serial number for products %(product_list)s."
msgstr ""
"\n"
"\n"
"Transferts %(transfer_list)s : indiquez un lot/numéro de série pour les produits %(product_list)s."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"(%(serial_number)s) exists in location %(location)s"
msgstr ""
"\n"
"(%(serial_number)s) présent dans l'emplacement %(location)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"The quantity done for the product %(product)s doesn't respect the rounding precision defined on the unit of measure %(unit)s.\n"
"Please change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"\n"
"La quantité réalisée pour le produit %(product)s ne respecte pas les règles d'arrondissement définies dans l'unité de mesure %(unit)s.\n"
"Veuillez changer la quantité réalisée ou les règles d'arrondissement de votre unité de mesure."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
" * Brouillon : Le transfert n'est pas encore confirmé. La réservation ne s'applique pas.\n"
" * En attente d'une autre opération : Ce transfert est en attente d'une autre opération avant d'être prêt.\n"
" * En attente : Ce transfert est en attente de la disponibilité de certains articles.\n"
"(a) La politique d'envoi est \"Dès que possible\" : aucun n'article n'a pu être réservé.\n"
"(b) La politique d'envoi est \"Quand tous les articles sont prêts\" : tous les articles n'ont pu être réservés.\n"
" * Prêt : Le transfert est prêt à être réalisé.\n"
"(a) (a) La politique d'envoi est \"Dès que possible\" : au moins un article a pu être réservé.\n"
"(b) La politique d'envoi est \"Quand tous les articles sont prêts\" : tous les articles ont pu être réservés.\n"
" * Terminé : Le transfert a pu être réalisé.\n"
" * Annulé : Le transfert a été annulé."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid " - Product: %(product)s, Lot/Serial Number: %(lot)s"
msgstr " - Produit : %(product)s, Lot/Numéro de série : %(lot)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,help:stock.field_stock_quant__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr ""
"Si différent de 0, la date prochaine date d'inventaire à cet emplacement "
"sera automatiquement mise à jour selon la fréquence."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_count
msgid "# Returns"
msgstr "# Retours"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s (copy)(%(id)s)"
msgstr "%(name)s (copie)(%(id)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence cross dock"
msgstr "%(name)s Séquence de transfert croisé"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence in"
msgstr "%(name)s Séquence d'entrée"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence internal"
msgstr "%(name)s Séquence interne"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence out"
msgstr "%(name)s Séquence de sortie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence packing"
msgstr "%(name)s Séquence d'emballage"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking"
msgstr "%(name)s Séquence de transfert"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence quality control"
msgstr "%(name)s Séquence contrôle qualité"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence storage"
msgstr "%(name)s Séquence de stockage"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"%(operations)s have default source or destination locations within warehouse"
" %(warehouse)s, therefore you cannot archive it."
msgstr ""
"%(operations)s a/ont des emplacements source ou de destination par défaut "
"dans %(warehouse)s, vous ne pouvez donc pas les/l'archiver."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "%(product)s: Insufficient Quantity To Scrap"
msgstr "%(product)s : Quantité insuffisante pour la mise au rebut"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"%(product_name)s --> Product UoM is %(product_uom)s "
"(%(product_uom_category)s) - Move UoM is %(move_uom)s "
"(%(move_uom_category)s)"
msgstr ""
"%(product_name)s --> l'UdM du produit est %(product_uom)s "
"(%(product_uom_category)s) - l'UdM du mouvement est %(move_uom)s "
"(%(move_uom_category)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%(warehouse)s Sequence %(code)s"
msgstr "%(warehouse)s Séquence %(code)s"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid ""
"%(warehouse)s can only provide %(free_qty)s %(uom)s, while the quantity to "
"order is %(qty_to_order)s %(uom)s."
msgstr ""
"%(warehouse)s peut uniquement fournir %(free_qty)s %(uom)s, alors que la "
"quantité à commander est %(qty_to_order)s %(uom)s."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s : Réapprovisionner le produit depuis %(supplier)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_rule.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "%s [reverted]"
msgstr "%s [inversé]"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "%s days"
msgstr "%s jours"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%s: Can't split: quantities done can't be above demand"
msgstr ""
"%s : Impossible de fractionner : les quantités réalisées ne peuvent être "
"supérieures à la demande"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split, all demand is done. For split you need at least one "
"line not fully fulfilled"
msgstr ""
"%s : Il n'y a rien à fractionner, la totalité de la demande a été traitée. "
"Il faut au moins une ligne qui ne soit pas entièrement complétée pour qu'il "
"puisse y avoir un fractionnement."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split. Fill the quantities you want in a new transfer in the "
"done quantities"
msgstr ""
"%s : Il n'y a rien à fractionner. Complétez les quantités souhaitées dans un"
" nouveau transfert dans les quantités traitées."

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "'Feuille de comptage'"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Bon de livraison - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'Localisation - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'Lot-Série - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'Type d'\\opération - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_packages
msgid "'Packages - %s' % (object.name)"
msgstr "'Colis - %s' % (object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Opérations de transfert - %s - %s' % (object.partner_id.name or '', "
"object.name)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "(copy of) %s"
msgstr "(Copie de) %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(document barcode)"
msgstr "(code-barres document)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(package barcode)"
msgstr "(code-barres du colis)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(product barcode)"
msgstr "(code-barres produit)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(serial barcode)"
msgstr "(code-barres de série)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: The stock move is created but not confirmed.\n"
"* Waiting Another Move: A linked stock move should be done before this one.\n"
"* Waiting Availability: The stock move is confirmed but the product can't be reserved.\n"
"* Available: The product of the stock move is reserved.\n"
"* Done: The product has been transferred and the transfer has been confirmed."
msgstr ""
"* Nouveau : Le mouvement de stock est créé, mais pas confirmé.\n"
"* En attente d'un autre mouvement : Un mouvement de stock lié doit être effectué avec celui-ci.\n"
"* En attente de disponibilité : Le mouvement de stock est confirmé, mais le produit ne peut pas être réservé.\n"
"* Disponible : Le produit du mouvement de stock est réservé.\n"
"* Terminé : Le produit a été transféré et le transfert a été confirmé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move__location_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* Emplacement fournisseur: emplacement virtuel représentant l'emplacement d'origine des articles provenant des fournisseurs\n"
"* Vue: emplacement virtuel utilisé pour avoir une struture hiérarchique de votre entrepôt, aggrégeant ses emplacements enfants; ne peut directelent contenir des articles\n"
"* Emplacement interne: emplacements physiques au sein de vos entrepôts,\n"
"* Emplacement client: emplacement virtuel représentant l'emplacement de destination des articles envoyés à vos clients\n"
"* Perte d'inventaire: emplacement virtuel servant de contre-partie pour les opérations d'inventaire utilisées pour corriger les niveaux de stock (Inventaires physiques)\n"
"* Production: emplacement virtuel servant de contre-partie pour les opérations de produiction: cet emplacement consomme les composants et produit les articles finis\n"
"* Emplacement de transit: emplacement de contre-partie qui est utilisé lors des opérations entre sociétés ou entre entrepôts"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d jour(s)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", max :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Des actions manuelles pourraient être requises."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 jour"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 mois"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 semaine"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "12.0"
msgstr "12.0"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
msgid "1234567890"
msgstr "1234567890"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "12345678901"
msgstr "12345678901"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 avec le prix"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "2021-9-01"
msgstr "01-09-2021"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "2023-01-01"
msgstr "01-01-2023"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "2023-09-24"
msgstr "24-09-2023"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "3.00"
msgstr "3.00"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__4x12
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_lots
msgid "4 x 12 - One per lot/SN"
msgstr "4 x 12 - Une par lot/NS"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_units
msgid "4 x 12 - One per unit"
msgstr "4 x 12 - Une par unité"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 avec le prix"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 avec le prix"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "54326786758"
msgstr "54326786758"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>Inventaire actuel : </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"<br/>\n"
"                Want to speed up operations?"
msgstr ""
"<br/>\n"
"                Vous voulez accélérer les opérations ?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr ""
"<br>Un besoin est créé dans <b>%s</b> et une règle sera déclenchée pour le "
"satisfaire."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring the missing quantity in this location."
msgstr ""
"<br>Si les produits ne sont pas disponibles dans <b>%s</b>, une règle sera "
"déclenchée pour amener la quantité manquante dans cet emplacement."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>The products will be moved towards <b>%(destination)s</b>, <br/> as "
"specified from <b>%(operation)s</b> destination."
msgstr ""
"<br>Les produits seront transportés vers <b>%(destination)s</b>, <br/> comme"
" indiqué dans la destination de <b>%(operation)s</b>."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Bonjour <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        Nous sommes heureux de vous informer que votre commande à été livrée.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Votre référence de suivi est\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Veuillez trouver votre bon de livraison en pièce jointe pour plus de détails.<br/><br/>\n"
"        Merci,\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                               Tous les produits n'ont pas pu être réservés. Cliquer sur le bouton \"Vérifier la disponibilité\" pour essayer de réserver les produits."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Back Orders</span>"
msgstr "<span class=\"col-6\">Reliquats</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">En retard</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Operations</span>"
msgstr "<span class=\"col-6\">Opérations</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr "<span class=\"col-6\">En attente</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Scannable Package Contents</span>"
msgstr "<span class=\"o_form_label\">Contenus d'emballages connectés</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Allocation</span>"
msgstr "<span class=\"o_stat_text\">Allocation</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Prévisions</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">In:</span>"
msgstr "<span class=\"o_stat_text\">Entrée :</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Location</span>"
msgstr "<span class=\"o_stat_text\">Emplacement</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "<span class=\"o_stat_text\">Lot/Serial Numbers</span>"
msgstr "<span class=\"o_stat_text\">Lot/Numéros de série</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Max:</span>"
msgstr "<span class=\"o_stat_text\">Max :</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Min:</span>"
msgstr "<span class=\"o_stat_text\">Min :</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Moves</span>"
msgstr "<span class=\"o_stat_text\">Mouvements</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Next Transfer</span>"
msgstr "<span class=\"o_stat_text\">Transfert suivant</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">En stock</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">Opérations</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Out:</span>"
msgstr "<span class=\"o_stat_text\">Sortie :</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">Mouvements de produit</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Putaway Rules</span>"
msgstr "<span class=\"o_stat_text\">Stratégies de rangement</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "<span class=\"o_stat_text\">Routes</span>"
msgstr "<span class=\"o_stat_text\">Routes</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Storage Capacities</span>"
msgstr "<span class=\"o_stat_text\">Capacités de stockage</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">Traçabilité</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>Adresse de livraison :</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "<span>OBTRETU</span>"
msgstr "<span>OBTRETU</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "<span>On Hand: </span>"
msgstr "<span>En stock : </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>Type de colis : </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>Produits sans colis assigné</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>Quantités restantes non encore livrées :</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "<span>days</span>"
msgstr "<span>jours</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                La ligne de mouvement \"terminé\" a été corrigée.\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Customer Address</strong>"
msgstr "<strong>Adresse du client</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Delivery address</strong>"
msgstr "<strong>Adresse de livraison</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>En raison de certains mouvements de stock effectués entre votre mise"
" à jour initiale de la quantité et maintenant, la différence de quantité "
"n'est plus cohérente.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>De</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>Lot/Numéro de série</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty:</strong>"
msgstr "<strong>Qté max :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty:</strong>"
msgstr "<strong>Qté min :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order</strong>"
msgstr "<strong>Commande</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Pack Date:</strong>"
msgstr "<strong>Date d'emballage :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>Type de colis :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Code-barres du produit</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>Produit</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>Quantité</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Recipient address</strong>"
msgstr "<strong>Adresse destinataire</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>Date planifiée</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date</strong>"
msgstr "<strong>Date d'expédition</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Signature</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status</strong>"
msgstr "<strong>Statut</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>La demande initiale a été mise à jour.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>À</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>Produit(s) suivi(s) :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Vendor Address</strong>"
msgstr "<strong>Adresse du fournisseur</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse Address</strong>"
msgstr "<strong>Adresse de l'entrepôt</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse address</strong>"
msgstr "<strong>Adresse de l'entrepôt</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products?</strong>"
msgstr "<strong>Où voulez-vous envoyer les produits ?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Cela peut donner lieu à des incohérences dans votre inventaire."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type!"
msgstr "Un code-barres peut uniquement être assigné à un type de colis !"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "A replenishment rule already exists for this product on this location."
msgstr "Une règle de réassort existe déjà pour ce produit à cet emplacement."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__is_storable
#: model:ir.model.fields,help:stock.field_product_template__is_storable
#: model:ir.model.fields,help:stock.field_stock_move__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr "Un produit stockable est un produit pour lequel vous gérez le stock."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "Un avertissement peut être défini sur un contact (stock)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "Action"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__replenish_location
msgid ""
"Activate this function to get all quantities to replenish at this particular"
" location"
msgstr ""
"Activez cette fonction pour obtenir toutes les quantités à réapprovisionner "
"à cet emplacement particulier"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_route__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "Actif"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
msgid "Activity State"
msgstr "Statut de l'activité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_view_activity
msgid "Activity view"
msgstr "Vue de l'activité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "Ajouter un produit"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "Ajouter un lot/numéro de série"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "Ajouter un nouvel emplacement"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "Ajouter une nouvelle route"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "Ajouter une nouvelle catégorie d'emplacement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr "Ajouter une note interne qui figurera sur le Bon de Préparation"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Ajoutez et personnalisez des opérations de transit pour traiter les mouvements de produits dans vos entrepôts. Exemple : déchargement > contrôle qualité > mise en stock pour les produits entrants, et transfert > emballage > expédition pour les produits sortants. \n"
" Vous pouvez également définir des stratégies de rangement pour vos entrepôts afin d'envoyer directement les produits entrants à des emplacements enfants spécifiques (par ex. un bac ou une étagère spécifique)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Ajoutez et personnalisez des opérations de transit pour traiter les "
"mouvements de produits dans vos entrepôts. Exemple : déchargement > contrôle"
" qualité> mise en stock pour les produits entrants, et transfert > "
"emballage > expédition pour les produits sortants. Vous pouvez également "
"définir des stratégies de rangement pour vos entrepôts afin d'envoyer "
"directement les produits entrants à des emplacements enfants spécifiques "
"(par un bac ou une étagère spécifique)."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/fields/stock_move_line_x2_many_field.js:0
msgid "Add line: %s"
msgstr "Ajouter une ligne : %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "Ajouter des contrôles qualité à vos opérations de transfert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Info complémentaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Informations supplémentaires"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__address
#: model:ir.model.fields,field_description:stock.field_stock_picking__warehouse_address_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Address"
msgstr "Adresse"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "Adresse à laquelle les biens doivent être livrés. Optionnel."

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_adjustments
msgid "Adjustments"
msgstr "Ajustements"

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "Administrateur"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "Planification avancée"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "Avancé : appliquer les règles d'approvisionnement"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__after
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "After"
msgstr "Après"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Tous"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Categories"
msgstr "Toutes les catégories"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "All Companies"
msgstr "Toutes les sociétés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Products"
msgstr "Tous les produits"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "Tous les transferts"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
msgid "All Warehouses"
msgstr "Tous les entrepôts"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "Tout en une fois"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr ""
"Toutes nos relations contractuelles seront gouvernées exclusivement par les "
"lois des États-Unis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "Tous les mouvements de retour"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "Autoriser un nouveau produit"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "Autoriser les produits différents"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "Emplacement autorisé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__allowed_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__allowed_route_ids
msgid "Allowed Route"
msgstr "Route autorisée"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__always
msgid "Always"
msgstr "Toujours"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Andrwep"
msgstr "Andrwep"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "Jour et mois de l'inventaire annuel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "Mois d'inventaire annuel"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""
"Mois d'inventaire pour les produits qui ne sont pas dans un emplacement avec"
" un inventaire cyclique. Ne pas sélectionner de mois ne fera pas "
"d'inventaire automatique"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"Another parent/sub replenish location %s exists, if you wish to change it, "
"uncheck it first"
msgstr ""
"Un autre emplacement de réapprovisionnement parent/sous %s existe, si vous "
"souhaitez le modifier, décochez-le d'abord"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Any Category"
msgstr "Toute catégorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "Applicabilité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Applicable à"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "Applicable aux conditionnements"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_selectable
msgid "Applicable on Product"
msgstr "Applicable aux produits"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Applicable aux catégories de produits"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Applicable aux entrepôts"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Apply"
msgstr "Appliquer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply All"
msgstr "Appliquer à tout"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_id
#: model:ir.model.fields,help:stock.field_stock_replenish_mixin__route_id
msgid ""
"Apply specific route for the replenishment instead of product's default "
"routes."
msgstr ""
"Appliquer des routes spécifiques pour le réassort au lieu des routes par "
"défaut du produit."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "Avril"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "Archivé"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Are you sure you want to cancel this transfer?"
msgstr "Êtes-vous sûr de vouloir annuler ce transfert ?"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__direct
msgid "As soon as possible"
msgstr "Aussi vite que possible"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__ask
msgid "Ask"
msgstr "Me demander"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Assign"
msgstr "Assigner"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Assign All"
msgstr "Assigner tout"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "Assigner un propriétaire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Mouvements assignés"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "Assigné à"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "À la confirmation"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "At Customer"
msgstr "Chez le client"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "Attributs"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "Août"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "Automatique"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_delivery_slip
msgid "Auto Print Delivery Slip"
msgstr "Impression automatique du bon de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_lot_labels
msgid "Auto Print Lot/SN Labels"
msgstr "Impression automatique des étiquettes de lot/NS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_package_label
msgid "Auto Print Package Label"
msgstr "Impression automatique de l'étiquette du colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_packages
msgid "Auto Print Packages"
msgstr "Impression automatique des colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_product_labels
msgid "Auto Print Product Labels"
msgstr "Impression automatique des étiquettes de produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report
msgid "Auto Print Reception Report"
msgstr "Impression automatique du rapport de réception"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid "Auto Print Reception Report Labels"
msgstr "Impression automatique des étiquettes du rapport de réception"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_return_slip
msgid "Auto Print Return Slip"
msgstr "Impression automatique du bon de retour"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate"
msgstr "Automatiser"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "Mouvement automatique"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "Automatique, pas d'étape ajoutée"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Available"
msgstr "Disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "Produits disponibles"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "Quantité disponible"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Available quantity should be set to zero before changing inventory tracking"
msgstr ""
"La quantité disponible doit être mise à zéro avant de changer le suivi des "
"stocks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "Reliquat de"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
msgid "Back Orders"
msgstr "Reliquats"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Confirmation de reliquat"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "Ligne de confirmation de reliquat"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Lignes de confirmation de reliquat"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Création de reliquats"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Reliquats"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "Code-barres"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Barcode Demo"
msgstr "Code-barres démo"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Nomenclatures des codes-barres"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "Règle de code-barres"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "Lecteur de codes-barres"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__valid_ean
msgid "Barcode is valid EAN"
msgstr "Le code-barres est valide EAN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch, Wave & Cluster Transfers"
msgstr "Transferts par lot, vague & cluster"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__before
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Before"
msgstr "Avant"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "Avant la date prévue"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""
"Le texte ci-dessous sert de suggestion et n'engage pas la responsabilité "
"d'Odoo S.A.."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "Message bloquant"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Blocking: %s"
msgstr "Bloquant : %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "Contenu en vrac"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Poids en vrac"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "Par lots"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "By Quantity"
msgstr "Par quantité"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "Par numéro de série unique"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"Par défaut, le système prendra dans le stock de l'emplacement d'origine et "
"attendra passivement la disponibilité. L'autre possibilité vous permet de "
"créer d'office un approvisionnement à l'emplacement d'origine (sans tenir "
"compte son niveau de stock actuel) pour acheminer les articles. Pour faire "
"des mouvements chaînés et faire en sorte que celui-ci attende le précédent, "
"la seconde option devra être choisie."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""
"En décochant cette case, vous pouvez masquer un emplacement sans pour autant"
" le supprimer."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "COPY"
msgstr "COPIE"

#. module: stock
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "Boîtier pour cables"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Vue calendrier"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any customer or supplier location."
msgstr "Impossible de trouver un emplacement client ou fournisseur."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any generic route %s."
msgstr "Impossible de trouver une route générique %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "Annuler"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "Annuler le mouvement suivant"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
msgid "Cancelled"
msgstr "Annulé"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot move an empty package"
msgstr "Impossible de déplacer un colis vide"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot remove the location of a non empty package"
msgstr "Impossible de supprimer l'emplacement d'un colis non vide"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "Capacité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "Capacité par colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "Capacité par produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.product_search_form_view_stock_report
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "Catégorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "Catégorie de route"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"Certains pays appliquent une retenue à la source sur le montant des "
"factures, selon leur législation. Chaque retenue à la source sera versée par"
" le client à l'administration fiscale. En aucun cas, My Company (Chicago) ne"
" peut être tenue des frais liés à la législation du pays. Le montant de la "
"facture sera donc intégralement dû à My Company (Chicago) et n'inclut pas de"
" frais liés à la législation du pays dans lequel se trouve le client."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Changer la quantité de produits"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Changing the Lot/Serial number for move lines with different products is not"
" allowed."
msgstr ""
"La modification du numéro de lot/série n'est pas autorisée pour des "
"mouvements de stock concernant des produits différents."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr ""
"Modifier la société sur cet enregistrement est interdit à ce stade, vous "
"devriez plutôt l'archiver et en créer un nouveau."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Changing the operation type of this record is forbidden at this point."
msgstr ""
"Modifier le type d'opération de cet enregistrement est interdit à ce stade."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "Modifier le produit est uniquement autorisé en état \"brouillon\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__barcode_separator
msgid ""
"Character(s) used to separate data contained within an aggregate barcode "
"(i.e. a barcode containing multiple barcode encodings)"
msgstr ""
"Caractère(s) utilisé(s) pour séparer les données comprises dans un code-"
"barres agrégé (code-barres contenant plusieurs encodages de codes-barres)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "Vérifier la disponibilité"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr ""
"Vérifier l'existence de colis de destination sur des lignes de mouvement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "Vérifier l'existence d'une opération d'emballage sur le transfert"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid ""
"Check this box if you want to generate shipping label in this operation."
msgstr ""
"Cochez cette case si vous souhaitez générer une étiquette d'expédition pour "
"cette opération."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Cochez cette case pour autoriser l'utilisation de cet emplacement pour les "
"rebuts et les produits endommagés."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "Choisir le modèle d'étiquettes"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose Type of Labels To Print"
msgstr "Choisissez le type d'étiquettes à imprimer"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "Choisir une date de récupération de l'inventaire"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose destination location"
msgstr "Choisir l'emplacement de destination"

#. module: stock
#: model:ir.model,name:stock.model_lot_label_layout
msgid "Choose the sheet layout to print lot labels"
msgstr ""
"Choisissez la disposition de la feuille pour imprimer les étiquettes de lot"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Choisir le modèle pour imprimer les étiquettes"

#. module: stock
#: model:ir.model,name:stock.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "Choisissez d'imprimer des étiquettes de produit ou de lot/NS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "Choisir la date"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "Effacer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Close"
msgstr "Fermer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__closest_location
#: model:product.removal,name:stock.removal_closest
msgid "Closest Location"
msgstr "Emplacement le plus proche"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__color
msgid "Color"
msgstr "Couleur"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Company"
msgstr "Société"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "Calculer les frais d'expédition"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Calculer les frais d'expédition et expédier avec DHL"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Calculer les frais d'expédition et expédier avec Easypost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Calculer les frais d'expédition et expédier avec FedEx"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "Calculer les frais d'expédition et expédier avec Sendcloud"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "Calculer les frais d'expédition et expédier avec Shiprocket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "Calculer les frais d'expédition et expédier avec Starshipit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Calculer les frais d'expédition et expédier avec UPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Calculer les frais d'expédition et expédier avec USPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Calculer les frais d'expédition et expédier avec bpost"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid "Computes when a move should be reserved"
msgstr "Calcule quand un mouvement doit être réservé"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "Configuration"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Confirm"
msgstr "Confirmer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "Confirmé"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "Conflit dans l'inventaire"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Conflict in Inventory Adjustment"
msgstr "Conflit dans l'ajustement d'inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Conflicts"
msgstr "Conflits"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__visibility_days
msgid ""
"Consider product forecast these many days in the future upon product replenishment, set to 0 for just-in-time.\n"
"The value depends on the type of the route (Buy or Manufacture)"
msgstr ""
"Considérez les prévisions de produit ces nombreux jours à l'avenir lors du réassort du produit, mis à 0 pour le juste-à-temps.\n"
"La valeur dépend du type de route (Acheter ou Fabriquer)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "Consignation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "Ligne traitée"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "Contact"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "Contient"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Contenu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Contents"
msgstr "Contenus"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "Continuer"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
msgid "Control panel buttons"
msgstr "Boutons du panneau de commande"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Il est possible de convertir deux unités de mesures si elles appartiennent à"
" la même catégorie. Cette conversion utilise les facteurs définis pour ces "
"unités."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "Couloir (X)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "Comptage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_move_ready
msgid "Count Move Ready"
msgstr "Nombre de mouvements prêts"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "Nombre de transferts"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "Nombre de transferts liés à un reliquat"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "Nombre de transferts à l'état de brouillon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "Nombre de transferts en retard"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "Nombre de transferts prêts"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "Nombre de transferts en attente"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "Feuille de comptage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "Quantité comptée"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "Emplacements de contrepartie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Créer un reliquat "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Create Backorder?"
msgstr "Créer un reliquat ? "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Create New"
msgstr "Créer nouveau"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "Créer de nouveaux lots/numéros de série"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Create Stock"
msgstr "Créer un stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                            products later. Do not create a backorder if you will not\n"
"                            process the remaining products."
msgstr ""
"Créer un reliquat si vous vous attendez à traiter la quantité de produits restante\n"
"                            . Ne créez pas de reliquat si vous ne voulez pas\n"
"                            traiter la quantité de produits restante."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "Créer un nouveau type d'opération"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "Créer un nouveau colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr ""
"Créer des feuilles de travail personnalisables pour vos contrôles qualité"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr ""
"Créez de nouvelles stratégies de rangement pour dispatcher automatiquement "
"certains articles vers leur emplacement approprié au moment de la réception."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create products easily by scanning using"
msgstr "Créez facilement des produits en les scannant à l'aide de"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "Create some storable products to see their stock info in this view."
msgstr ""
"Créez des produits stockables pour voir leurs informations de stock dans "
"cette vue."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "Créé le"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr ""
"Créer un nouvel entrepôt activera automatiquement les Emplacements dans la "
"configuration"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "Date de création"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "Date de création, en général la date de la commande"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Creation date"
msgstr "Date de création"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__date
msgid ""
"Creation date of this move line until updated due to: quantity being "
"increased, 'picked' status has updated, or move line is done."
msgstr ""
"Date de création de cette ligne de mouvement jusqu'à la mise à jour en "
"raison de : augmentation de la quantité, mise à jour du statut 'prélevé' ou "
"de la ligne de mouvement terminée."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross Dock"
msgstr "Correspondance"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__xdock_type_id
msgid "Cross Dock Type"
msgstr "Type de correspondance"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross-Dock"
msgstr "Correspondance"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "Route de correspondance"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Stock actuel"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Quantité actuelle de produits en stock.\n"
"Dans le contexte d'un seul emplacement de stock, ceci inclut les marchandises stockées dans cet emplacement et ses enfants.\n"
"Dans le contexte d'un seul entrepôt, ceci inclut les marchandises stockées dans l'emplacement de l'entrepôt et ses enfants.\n"
"Dans le contexte d'un magasin, ceci inclut les marchandises stockées dans l'entrepôt de ce magasin et ses enfants.\n"
"Dans les autres contextes, ceci inclut les marchandises stockées dans n'importe quel emplacement de type 'interne'."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "Personnalisé"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "Client"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "Délai de livraison au client"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "Emplacement client"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Emplacements clients"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot_report
msgid "Customer Lot Report"
msgstr "Rapport sur les lots clients"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lot_report
msgid "Customer lots"
msgstr "Lots clients"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Customizable Desk"
msgstr "Bureau personnalisable"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Counting"
msgstr "Comptage cyclique"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_DATE"
msgstr "DEMO_DATE"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_ORIGIN_DISPLAY_NAME"
msgstr "DEMO_ORIGIN_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PARTNER_NAME"
msgstr "DEMO_PARTNER_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PRODUCT_DISPLAY_NAME"
msgstr "DEMO_PRODUCT_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_QUANTITY"
msgstr "DEMO_QUANTITY"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_SOURCE_DISPLAY_NAME"
msgstr "DEMO_SOURCE_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_UOM"
msgstr "DEMO_UOM"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "Connecteur DHL Express"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "Date"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__search_date_category
msgid "Date Category"
msgstr "Catégorie Date"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "Traitement de la date"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "Date planifiée"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "Date à laquelle le réassort devrait avoir lieu."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "Date à laquelle le transfert a été traité ou annulé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr "Date pour l'inventaire planifié suivant, basé sur un agenda cyclique."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "Date du transfert"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "Date du dernier inventaire à cet emplacement."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "Date de réservation"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "Jour et mois auxquels l'inventaire annuel doit être effectué."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "Jour du mois"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"Jour du mois de la date d'inventaire annuel. Si zéro ou négatif, alors le premier jour du mois sera sélectionné à la place.\n"
"Si plus grand que le dernier jour du mois, alors le dernier jour du mois sera sélectionné."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Day(s)"
msgstr "Jour(s)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "Jours"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Days To Order"
msgstr "Jours pour commander"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "Jours quand prioritaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "Date limite"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "L'échéance dépassée ou/et par la date prévue"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Deadline updated due to delay on %s"
msgstr "Échéance mise à jour suite à retard sur %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "Décembre"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Barcode Name"
msgstr "Nom de code-barres par défaut"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Name"
msgstr "Nom par défaut"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default OBTRETU Barcode"
msgstr "Code-barres OBTRETU par défaut"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Return Name"
msgstr "Nom de retour par défaut"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "Route d'entrée à suivre par défaut"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "Route de sortie à suivre par défaut"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unité de mesure par défaut utilisée pour toutes les opérations de stock."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "Par défaut : prendre dans le stock"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "Routes par défaut à travers l'entrepôt"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo automatically creates requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr ""
"Définissez un règle de stock minimum afin qu'Odoo crée automatiquement des "
"achats ou des ordres de fabrications pour réapprovisionner votre stock."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "Définissez un nouvel entrepôt"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"Définissez vos emplacements de façon à ce qu'ils reflètent la structure de votre entrepôt et de votre\n"
" organisation. Odoo est capable de gérer des emplacements physiques\n"
" (entrepôts, étagères, bacs, etc.), des emplacements de partenaire (clients,\n"
" fournisseurs) ainsi que des emplacements virtuels. Ces derniers servent de contrepartie aux\n"
" opérations de stock, telles que les ordres de fabrication,\n"
" la consommation, les inventaires, etc."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Définit la méthode par défaut utilisée pour suggérer l'emplacement exact (étagère) où prendre les produits, quel lot, etc. pour cet emplacement. Cette méthode peut être appliquée au niveau de la catégorie de produits, et un repli est effectué sur les emplacements parents si aucun n'est défini ici.\n"
"\n"
"FIFO : les produits/lots stockés en premier seront sortis en premier.\n"
"LIFO : les produits/lots stockés en dernier seront sortis en premier.\n"
"Emplacement le plus proche : les produits/lots les plus proches de l'emplacement cible seront déplacés en premier.\n"
"FEFO : les produits/lots dont la date de retrait est la plus proche seront retirés en premier (la disponibilité de cette méthode dépend du paramètre \"Dates d'expiration\")."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "Date d'alerte de retard"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Delay on %s"
msgstr "Délai au %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver (1 step)"
msgstr "Livrer (étape 1)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 1 step (ship)"
msgstr "Livrer en 1 étape (expédition)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 2 steps (pick + ship)"
msgstr "Livrer en 2 étapes (transfert + expédition)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "Livrer en 3 étapes (transfert + colisage + expédition)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Delivered"
msgstr "Livré"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Delivered Qty"
msgstr "Qté livrée"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_outgoing
#: model:ir.ui.menu,name:stock.out_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deliveries"
msgstr "Livraisons"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
msgid "Delivery"
msgstr "Livraison"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "Adresse de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__delivery_date
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery Date"
msgstr "Date de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Modes de livraison"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_out
msgid "Delivery Orders"
msgstr "Bons de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "Route de livraison"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Delivery Slip"
msgstr "Bon de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "Type de livraison"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery date"
msgstr "Date de livraison"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""
"Délai de livraison, en jours. C'est le nombre de jours, promis au client, "
"entre la confirmation de la commande et la livraison. "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_count
msgid "Delivery order count"
msgstr "N° de bon de livraison"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Delivery orders of %s"
msgstr "Livraisons de %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "Demande"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Demo Address and Name"
msgstr "Adresse et nom démo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Demo Display Name"
msgstr "Nom d'affichage démo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Lot/SN"
msgstr "Démo Lot/NS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Product"
msgstr "Produit démo"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""
"En fonction des modules installés, ceci va vous permettre de définir la "
"route pour le produit dans ce conditionnement en particulier: qu'il soit "
"acheté, fabriqué, réapprovisionné sur commande, etc..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"En fonction des modules installés, cela va vous permettre de définir les "
"routes sur le produit : acheter, fabriquer, réapprovisionner sur commande, "
"etc."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__note
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "Description"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "Description pour les bons de livraison"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "Description pour les transferts internes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "Description pour les réceptions"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "Description du transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Description pour les bons de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "Description sur le transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "Description sur les réceptions"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Description on transfer"
msgstr "Description sur le transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "Description du transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_location_id
msgid "Dest Location"
msgstr "Emplacement de dest"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id
msgid "Dest Package"
msgstr "Colis dest"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id_domain
msgid "Dest Package Id Domain"
msgstr "Domaine ID colis dest"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "Adresse de destination "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Destination Location"
msgstr "Emplacement de destination"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_usage
msgid "Destination Location Type"
msgstr "Type d'emplacement de destination"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "Emplacement de destination :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "Mouvements de destination"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "Colis de destination"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package:"
msgstr "Colis de destination :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "Emplacement de destination"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_from_rule
msgid "Destination location origin from rule"
msgstr "Emplacement de destination de la règle"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "Route de destination"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Detailed Operations"
msgstr "Opérations détaillées"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "Détails visibles"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "Différence"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "Ignorer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "Ignorer et résoudre le conflit manuellement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_fleet
msgid "Dispatch Management System"
msgstr "Système de gestion des envois"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "Montrer l'assignation des numéros de série"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_complete
msgid "Display Complete"
msgstr "Montrer complet"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_import_lot
msgid "Display Import Lot"
msgstr "Afficher le lot d'import"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "Afficher des lots et des numéros de série sur les bons de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__display_name
#: model:ir.model.fields,field_description:stock.field_picking_label_type__display_name
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "Afficher les lots & numéros de série sur les bons de livraisons"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "Montrer le contenu du colis"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "Boîte jetable"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "Confirmez-vous que vous voulez mettre au rebut"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Documentation"
msgstr "Documentation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Done"
msgstr "Terminé"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
msgid "Done By"
msgstr "Fait par"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_quantity
msgid "Done Packaging Quantity"
msgstr "Quantité de conditionnements terminés"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "Brouillon"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Mouvements en brouillon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "Dropshipping"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Due to receipts scheduled in the future, you might end up with "
"excessive stock . Check the Forecasted Report  before reordering"
msgstr ""
"En raison de réceptions programmées dans le futur, il se peut que vous vous "
"retrouviez avec un excédent de stock. Vérifiez le rapport des prévisions "
"avant le réapprovisionnement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "Duplicated SN Warning"
msgstr "Avertissement NS dupliqué(s)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__sn_duplicated
msgid "Duplicated Serial Number"
msgstr "Numéro de série dupliqué"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Connecteur Easypost"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Edit Product"
msgstr "Modifier le produit"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr ""
"La modification des quantités dans un emplacement d'ajustement d'inventaire "
"est interdite, ces emplacements sont utilisés comme contre-partie lorsque "
"des corrections de quantités sont réalisées."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "Date effective"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "E-mail de confirmation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "E-mail de confirmation du transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "Modèle d'e-mail de confirmation du transfert"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr "E-mail envoyé au client lorsque la commande est traitée."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Empty Locations"
msgstr "Emplacements vides"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"Appréciez une expérience à haute fréquence avec l'app Code-barres d'Odoo. "
"Cette application est très rapide et fonctionne même sans une connexion "
"internet stable. Code-barres prend en charge tous les flux : Ajustement "
"d'inventaire, transfert par lot, déplacements de lot ou de palettes, "
"vérifications d'inventaire, etc... Allez-au menu \"Apps\" pour activer "
"l'interface de Code-barres."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Assurez la traçabilité d'un produit stockable dans votre entrepôt."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"Toute opération de stock dans Odoo déplace des produits d'un emplacement à "
"un autre. Par exemple, si vous recevez des produits d'un fournisseur, Odoo "
"déplacera les produits de l'emplacement Fournisseurs vers l'emplacement "
"Stock. Chaque rapport peut porter sur un emplacement physique, un "
"emplacement partenaire ou un emplacement virtuel."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "Exception(s) sur le transfert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "Exception(s) :"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Exp"
msgstr "Prévu"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Exp %s"
msgstr "Prévu %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "Prévu"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "Livraison prévue :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "Dates d'expiration"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Note externe..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_favorite
msgid "Favorite"
msgstr "Favori"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__favorite_user_ids
msgid "Favorite User"
msgstr "Utilisateur favori"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Favorites"
msgstr "Favoris"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "Février"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "Connecteur FedEx"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "Emplacement filtré"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "Filtres"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_final_id
msgid "Final Location"
msgstr "Emplacement final"

#. module: stock
#: model:product.removal,name:stock.removal_fifo
msgid "First In First Out (FIFO)"
msgstr "First In First Out (FIFO)"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Lot Number"
msgstr "Premier numéro de lot"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN/Lot"
msgstr "Premier numéro de série/lot"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Serial Number"
msgstr "Premier numéro de série"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "Fixe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "Groupe d'approvisionnement fixé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Forcer la stratégie d'enlèvement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Forecast"
msgstr "Prévision"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "Prévision de disponibilité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Forecast Description"
msgstr "Description de la prévision"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Forecast Report"
msgstr "Rapport de prévision"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Quantité prévue (calculée comme quantité en stock - sortante + entrante)\n"
"Dans le contexte d'un emplacement de stock seul, cela comprend les marchandises stockées dans cet emplacement et ses enfants.\n"
"Dans le contexte d'un entrepôt seul, cela comprend les marchandises stockées dans l'emplacement de stock de cet entrepôt et ses enfants.\n"
"Dans les autres cas, cela comprend les marchandises stockées dans tous les emplacements de type \"interne\""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Quantité planifiée (calculée comme quantité en stock - quantité réservée)\n"
"Dans un contexte avec un unique emplacement de stock, cela inclut les biens stockés dans cet emplacement, ainsi que dans chacun de ses enfants.\n"
"Dans un contexte avec un unique entrepôt, cela inclut les biens stockés dans l'emplacement de stock de cet entrepôt, ou chacun de ses enfants.\n"
"Autrement, cela inclut les bien stockés dans quelque emplacement de type interne."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
msgid "Forecasted"
msgstr "Prévu"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date"
msgstr "Date planifiée"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date + Visibility Days"
msgstr "Date prévisionnelle + jours de visibilité"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
msgid "Forecasted Deliveries"
msgstr "Livraisons planifiées"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "Date prévue espérée"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted Inventory"
msgstr "Inventaire planifié"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecasted_quantity
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
msgid "Forecasted Quantity"
msgstr "Quantité prévue"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
msgid "Forecasted Receipts"
msgstr "Réceptions planifiées"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_forecasted_product_product_action
#: model:ir.actions.client,name:stock.stock_forecasted_product_template_action
msgid "Forecasted Report"
msgstr "Prévision de Stock"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
msgid "Forecasted Stock"
msgstr "Stock prévu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "Poids estimé"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted with Pending"
msgstr "Prévue en attente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__print_format
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "Format"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__free_qty
msgid "Free Qty"
msgstr "Qté disponible"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock"
msgstr "Disponible en stock"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock in Transit"
msgstr "Stock disponible en transit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "Quantité virtuellement disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Free to Use"
msgstr "Virtuellement disponible"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "De"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "Appartenant à"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "Nom complet de l'emplacement "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "Activités futures"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Deliveries"
msgstr "Livraison future"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future P&L"
msgstr "P&L future"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Productions"
msgstr "Production future"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Receipts"
msgstr "Réceptions à venir"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "General"
msgstr "Général"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate"
msgstr "Générer"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Lot numbers"
msgstr "Générer des numéros de lot"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Serial numbers"
msgstr "Générer des numéros de séries"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate Serials/Lots"
msgstr "Générer des numéros de série/lot"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Generate Shipping Labels"
msgstr "Générer des étiquettes d'expédition"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr "Obtenir une traçabilité complète des fournisseurs aux clients"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr ""
"Recevoir des avertissements informatifs ou bloquants sur les partenaires."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""
"Donnez aux catégories les plus spécialisées une priorité plus haute pour les"
" avoir en tête de liste."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "Donne la séquence de cette ligne quand on montre les entrepôts."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Go to Warehouses"
msgstr "Accéder aux entrepôts"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Regrouper par"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "Regrouper par..."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "HTML reports cannot be auto-printed, skipping report: %s"
msgstr ""
"Les rapports HTML ne peuvent pas être imprimés automatiquement, ignorant "
"donc le rapport : %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Hardware"
msgstr "Matériel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "A un message"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "A des opérations de colisage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "A des colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__has_return
msgid "Has Return"
msgstr "Avec retour"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "Comporte des mises au rebut"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "Est tracké"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "A des variantes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "Ayant la catégorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "Hauteur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "Hauteur (Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "La hauteur doit être positive"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "Masqué jusqu'à la prochaine planification"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__hide_reservation_method
msgid "Hide Reservation Method"
msgstr "Masquer la méthode de réservation"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "History"
msgstr "Historique"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
msgid "Horizon"
msgstr "Horizon"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr "Comment les produits de ce type de transfert doivent être réservés"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__id
#: model:ir.model.fields,field_description:stock.field_picking_label_type__id
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_route__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""
"Si un paiement est toujours dû plus de 60 jours après la date de paiement, "
"My Company (Chicago) se réserve le droit de contacter une entreprise de "
"recouvrement. Toutes les dépenses associées devront être payées par le "
"client."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"If a separator is defined, a QR code containing all serial numbers contained"
" in package will be generated, using the defined character(s) to separate "
"each numbers"
msgstr ""
"Lorsqu'un séparateur est défini, un code QR contenant tous les numéros de "
"série du colis est généré, à l'aide du ou des caractères définis afin de "
"séparer chaque numéro."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "Si tous les produits sont les mêmes"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Si cette case est cochée, lorsque ce mouvement est annulé, le mouvement lié "
"l'est aussi"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Si renseigné, les opérations sont emballées dans ce colis."

#. module: stock
#: model:ir.model.fields,help:stock.field_lot_label_layout__label_quantity
msgid ""
"If the UoM of a lot is not 'units', the lot will be considered as a unit and"
" only one label will be printed for this lot."
msgstr ""
"Si l'UdM d'un lot n'est pas 'unités', le lot sera considéré comme une unité "
"et une seule étiquette sera imprimée pour ce lot."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Si le champ actif n'est pas coché, cela vous permettra de cacher les points "
"de commande sans les supprimer."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Si le champ actif n'est pas coché, la route est masquée sans être supprimée."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "Si l'emplacement est vide"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__sn_duplicated
msgid "If the same SN is in another Quant"
msgstr "Si le même NS est dans une autre Quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_delivery_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the delivery slip "
"of a picking when it is validated."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement le bon de livraison"
" d'un transfert lorsqu'il est validé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_lot_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN labels "
"of a picking when it is validated."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement les étiquettes de "
"lot/NS d'un transfert lorsqu'il est validé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_package_label
msgid ""
"If this checkbox is ticked, Odoo will automatically print the package label "
"when \"Put in Pack\" button is used."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement l'étiquette du "
"colis lorsque l'on clique sur le bouton \"Mettre en colis\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_packages
msgid ""
"If this checkbox is ticked, Odoo will automatically print the packages and "
"their contents of a picking when it is validated."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement les colis et leurs "
"contenus d'un transfert lorsqu'il est validé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a picking when it is validated."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement les étiquettes de "
"produit d'un transfert lorsqu'il est validé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report labels of a picking when it is validated."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement les étiquettes du "
"rapport de réception d'un transfert lorsqu'il est validé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report of a picking when it is validated and has assigned moves."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement le rapport de "
"réception d'un transfert lorsqu'il est validé et qu'il a des mouvements "
"assignés."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_return_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the return slip of"
" a picking when it is validated."
msgstr ""
"Si cette case est cochée, Odoo imprimera automatiquement le bon de retour "
"d'un transfert lorsqu'il est validé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_show_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically show the reception "
"report (if there are moves to allocate to) when validating."
msgstr ""
"Si cette case est cochée, Odoo affichera automatiquement le rapport de "
"réception (s'il y a des déplacementsà allouer) lors de la validation."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"Si cette case est cochée, les lignes de préparation contiendront les "
"opérations de stock détaillées. Si ce n'est pas le cas, les lignes de "
"préparation contiendront une synthèse des opérations de stock détaillées."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""
"Si cette case est cochée, le système considérera que vous souhaitez créer "
"des lots/numéros de série et vous permettra donc de les saisir dans un champ"
" de texte."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"Si cette case est cochée, vous pourrez choisir les lots/numéros de série. "
"Vous pouvez aussi décider de ne pas définir de lots sur ce type d'opération."
" Cela signifie que du stock sans lot sera créé ou qu'il n'y aura pas de "
"restriction sur le lot choisi."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__return_id
msgid ""
"If this picking was created as a return of another picking, this field links"
" to the original picking."
msgstr ""
"Si ce transfert a été créé en tant que retour d'un autre transfert, ce champ"
" renvoie au transfert d'origine."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Si cette expédition est divisée, alors ce champs indique l'expédition qui "
"contient la partie déjà envoyée."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr "Si coché, vous serez capable de sélectionner des colis à déplacer."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr "Si décoché, cela permet de masquer la règle sans la supprimer."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
msgid "Immediate Transfer"
msgstr "Transfert immédiat"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Lots"
msgstr "Importer des lots"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Serials"
msgstr "Importer des numéros de série"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Import Serials/Lots"
msgstr "Importer des numéros de série/lot"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Import Template for Inventory Adjustments"
msgstr "Modèle d'importation pour les ajustements d'inventaire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "In Stock"
msgstr "En stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "Type d'entrée"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid ""
"In case of outgoing flow, validate the transfer before this date to allow to deliver at promised date to the customer.\n"
"        In case of incoming flow, validate the transfer before this date in order to have these products in stock at the date promised by the supplier"
msgstr ""
"La validation du transfert avant cette date permet, en cas de flux sortant, de livrer le client à la date promise.\n"
"        La validation du transfert avant cette date permet, en cas de flux entrant, de disposer des produits en stock à la date promise par le fournisseur."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "In internal locations"
msgstr "Dans emplacements internes"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""
"Afin d'être admissible, toute réclamation doit être notifiée à My Company "
"(Chicago) par lettre recommandée adressée à son siège social dans les 8 "
"jours suivant la livraison des marchandises ou la prestation des services."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "Entrant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "Date d'entrée"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Incoming Draft Transfer"
msgstr "Brouillon de transfert entrant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "Mouvement de stock entrant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "Réceptions"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Incorrect type of action submitted as a report, skipping action"
msgstr "Type d'action incorrect soumis en tant que rapport, action ignorée"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr "Indique l'écart entre la quantité théorique et la quantité comptée"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "Demande initiale"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Input"
msgstr "Entrée"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "Emplacement d'entée"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Install"
msgstr "Installer"

#. module: stock
#: model:ir.actions.server,name:stock.action_install_barcode
msgid "Install Barcode"
msgstr "Installer Code-barres"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_company.py:0
msgid "Inter-warehouse transit"
msgstr "Transit entre entrepôts"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
msgid "Intermediate Location"
msgstr "Emplacement intermédiaire"

#. module: stock
#: model:ir.ui.menu,name:stock.int_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Internal"
msgstr "Interne"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "Emplacement interne"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Emplacements internes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__ref
msgid "Internal Reference"
msgstr "Référence interne"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "Transfert interne"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_internal
#: model:stock.picking.type,name:stock.picking_type_internal
msgid "Internal Transfers"
msgstr "Transferts internes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Emplacement de transit interne"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "Type interne"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations among descendants"
msgstr "Emplacements internes parmi les descendants"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "Internal locations having stock can't be converted"
msgstr ""
"Les emplacements internes disposant d'un stock ne peuvent pas être convertis"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr ""
"Référence interne, dans le cas où celle-ci est différente du lot/numéro de "
"série du fabricant"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Invalid domain left operand %s"
msgstr "Opérande gauche du domaine invalide : %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain operator %s"
msgstr "Opérateur du domaine invalide : %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr ""
"Opérande droit du domaine non valide '%s'. Il doit être de type "
"Entier/Flottant"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr ""
"Configuration de règles invalide, la règle suivante entraîne une boucle "
"infinie : %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "Quantité inventoriée"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Inventaire"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory Adjustment"
msgstr "Ajustement d'inventaire"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "Référence/motif de l'ajustement"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "Avertissement d'ajustement d'inventaire"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Inventory Adjustments"
msgstr "Ajustements d'inventaire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "Feuille de comptage d'inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "Date d'inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,field_description:stock.field_stock_quant__cyclic_inventory_frequency
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Inventory Frequency"
msgstr "Fréquence d'inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "Emplacement d'inventaire"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Emplacements de l'inventaire"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "Perte d'inventaire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_view_inherit_stock
msgid "Inventory Management"
msgstr "Gestion de l'inventaire"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Inventory On Hand"
msgstr "Stock disponible"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "Aperçu du stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "Quantité comptée enregistrée"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Reason"
msgstr "Motif de l'inventaire"

#. module: stock
#: model:ir.model,name:stock.model_stock_route
msgid "Inventory Routes"
msgstr "Routes d'inventaire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Inventory Valuation"
msgstr "Valorisation des stocks"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_at_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory at Date"
msgstr "Stock à date"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__is_empty
msgid "Is Empty"
msgstr "Est vide"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "Est un nouveau colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "Est verrouillé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_multi_location
msgid "Is Multi Location"
msgstr "Est un emplacement multi"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_partial_package
msgid "Is Partial Package"
msgstr "Est un colis partiel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "Est signé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "Est un emplacement de rebut ?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "La demande initiale est-elle modifiable ?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "Est en retard"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr ""
"Est en retard ou sera en retard en fonction de l'échéance et de la date "
"prévue"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "La quantité traitée est-elle modifiable ?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr ""
"La quantité de %s débloquée ne peut pas être supérieure à la quantité en "
"stock."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr ""
"Indique si les marchandises doivent être livrées partiellement ou en une "
"seule fois."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__move_type
msgid "It specifies goods to be transferred partially or all at once"
msgstr ""
"Spécifie si les marchandises doivent être transférées partiellement ou en "
"une seule fois."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "Données JSON pour le widget popover"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "Janvier"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "John Doe"
msgstr "John Doe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "Json Lead Days"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.js:0
msgid "Json Popup"
msgstr "Popup Json"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "Historique de réassort Json"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "Juillet"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "Juin"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Tableau de bord graphique Kanban"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "Garder la quantité comptée"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "Garder la différence"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Keep current lines"
msgstr "Conserver les lignes actuelles"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr ""
"Garder la <strong>quantité comptée</strong>(la différence sera mise à jour)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr ""
"Garder la <strong>différence</strong> (la quantité comptée sera mise à jour "
"pour refléter la différence constatée lors du compte)"

#. module: stock
#: model:ir.actions.server,name:stock.action_print_labels
msgid "Labels"
msgstr "Libellés"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__label_type
msgid "Labels to print"
msgstr "Etiquettes à imprimer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Laptop"
msgstr "Ordinateur portable"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "Les 12 derniers mois"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "Les 3 derniers mois"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "Les 30 derniers jours"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__last_count_date
msgid "Last Count Date"
msgstr "Date du dernier comptage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "Dernier partenaire de livraison"

#. module: stock
#: model:product.removal,name:stock.removal_lifo
msgid "Last In First Out (LIFO)"
msgstr "Last In First Out (LIFO)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Inventory"
msgstr "Dernier inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__last_used
msgid "Last Used"
msgstr "Dernière utilisation"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__last_count_date
msgid "Last time the Quantity was Updated"
msgstr "La dernière fois que la quantité a été mise à jour"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "En retard"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "Activités en retard"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Availability"
msgstr "Disponibilités tardives"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Transferts en retard"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "Dernier statut de disponibilité du transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "Date avec jours de délai"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__lead_time
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "Délai"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Lead Times"
msgstr "Délais"

#. module: stock
#: model:product.removal,name:stock.removal_least_packages
msgid "Least Packages"
msgstr "Le moins de colis"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "Laisser vide"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr ""
"Laissez ce champ vide si cette route est partagée entre toutes les sociétés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "Légende"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "Longueur"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "La longueur doit être positive"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "Étiquette d'unité de mesure de longueur"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
#: model:ir.model.fields,help:stock.field_stock_quant_relocate__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Laissez ce champ vide si cet emplacement est partagé par plusieurs sociétés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "Mouvements liés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of detailed operations"
msgstr "Vue de liste des opérations détaillées"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of next transfers"
msgstr "Vue liste des transferts suivants"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "Vue liste des opérations"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "Emplacement"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "Code-barre de l'emplacement de stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "Nom de l'emplacement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "Emplacement de stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Location Type"
msgstr "Type d'emplacement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "Emplacement : Stocker dans"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "Emplacement : Si entrée dans"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model:ir.ui.menu,name:stock.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Emplacements"

#. module: stock
#: model:ir.actions.server,name:stock.action_toggle_is_locked
msgid "Lock/Unlock"
msgstr "Verrouiller/Déverrouiller"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "Logistique"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Lot"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_lot_customer_report_view_list
msgid "Lot / Serial Number"
msgstr "Lot / Numéro de série"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__lot_label_format
msgid "Lot Label Format to auto-print"
msgstr "Format de l'étiquette de lot à imprimer automatiquement"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_lot_template_view
msgid "Lot Label Report"
msgstr "Rapport d'étiquettes de lots"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__lot_properties_definition
msgid "Lot Properties"
msgstr "Propriétés du lot"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Lot numbers"
msgstr "Numéros de lot"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__lots
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Lot/SN Labels"
msgstr "Étiquettes de lot/NS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Lot/SN:"
msgstr "Lot/N° série"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "Lot/série"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Lot/Serial #"
msgstr "Lot/Série #"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Lot/Serial Number"
msgstr "Lot/numéro de série"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "Lot/numéro de série (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "Lot/numéro de série (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "Nom du lot/numéro de série"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Lot/Serial Number Relocated"
msgstr "Lot/numéro de série relocalisé"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial:"
msgstr "Lot/Numéro de série :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "Lots & Numéros de série"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the delivery slip"
msgstr "Les numéros de lot & de série apparaîtront sur le bon de livraison"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
msgid "Lots / Serial Numbers"
msgstr "Lots / Numéros de série"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "Lots visibles"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr ""
"Les numéros de séries ou de lot n'ont pas été fournis pour les produits "
"trackés"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "Lots/Numéros de série"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"Les lots ou numéros de série vous permettent de tracer vos produits.\n"
"A partir de leur rapport de traçabilité, vous verrez l'historique complet de leur usage, ainsi que leur composition."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_replenish
msgid "Low on stock? Let's replenish."
msgstr "Pas assez de stock ? Procédons au réassort."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "Règle de MTO"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Gérer différents propriétaires de stock"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Gérer les lots/numéros de série"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "Gérer plusieurs emplacements de stock"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "Gérer plusieurs entrepôts"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Gérer les colis"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Gérer les règles de flux tirés/poussés."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""
"Gérer les conditionnements de produits (exemples: pack de 6 bouteilles, "
"boîte de 10 pièces)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "Manuelle"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "Opération manuelle"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "Manual Replenishment"
msgstr "Réassort manuel"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "Manuellement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "Fabrication"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "Mars"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Marquer comme à faire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Max"
msgstr "Max"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "Quantité max"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight"
msgstr "Poids max"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "Le poids max doit être positif"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "Le poids maximum doit être positif"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr ""
"Nombre maximum de jours avant la date prévue avant réservation pour les "
"transferts prioritaires."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr ""
"Maximum de jours entre la date planifiée et la réservation du produit."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "Poids maximum pouvant être expédié dans ce conditionnement"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "Mai"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Message pour le transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "Messages"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "Mode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Min"
msgstr "Min"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "Quantité min"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Règle d'inventaire minimum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Règles de stock minimum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_ids
msgid "Move"
msgstr "Mouvement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "Détails du mouvement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "Déplacer des colis entiers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "Ligne de mouvement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "Mouvements de stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "Nombre de lignes de mouvement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_quantity
msgid "Move Quantity"
msgstr "Quantité à déplacer"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "Mouvement qui a créé le mouvement de retour"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "Mouvements"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.ui.menu,name:stock.stock_move_menu
msgid "Moves Analysis"
msgstr "Analyse des mouvements"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_pivot
msgid "Moves History"
msgstr "Historique des mouvements"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"Les mouvements créés à travers cette règle de réapprovisionnement seront mis"
" dans ce groupe d'approvisionnement. Si aucun n'est donné, les mouvements "
"générés par les règles de stock seront groupés dans un seul transfert."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "Routes en plusieurs étapes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "Quantité multiple"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "Règles de capacité multiples pour un type de colis"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "Règles de capacité multiples pour un produit."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"My Company (Chicago) s'engage à faire de son mieux pour fournir des services"
" performants dans les temps impartis. Toutefois, aucune de ses obligations "
"ne peut être considérée comme une obligation de résultat. My Company "
"(Chicago) ne peut en aucune circonstance être tenue par le client "
"d'apparaître comme tiers dans le cadre d'une demande en dommages-intérêts "
"déposée contre le client par un consommateur final."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "Mes comptages"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "Mes transferts"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "Nom"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Name Demo"
msgstr "Nom démo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "Nbre d'entrées"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "Nbre de sorties"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "Quantité prévue négative"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "Stock négatif"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "Poids net"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__never
msgid "Never"
msgstr "Jamais"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__never_product_template_attribute_value_ids
msgid "Never attribute Values"
msgstr "Jamais de valeurs d'attributs"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "New"
msgstr "Nouveau"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "New Move: %(product)s"
msgstr "Nouveau déplacement : %(product)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "Nouvelle quantité en stock"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Nouveau transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l'activité suivante"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected"
msgstr "Prochain prévu"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Next Transfers"
msgstr "Transferts suivants"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "Prochaine date à laquelle la quantité en stock doit être comptée."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "Prochain(s) transfert(s) impacté(s) :"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__no
msgid "No"
msgstr "Non"

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "No %s selected or a delivery order selected"
msgstr "Aucun %s sélectionné ou bon de livraison sélectionné"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "Aucun reliquat"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "Aucun message"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "No Stock On Hand"
msgstr "Pas de stock disponible"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found."
msgstr "Aucun besoin d'allocation trouvé."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "No data yet!"
msgstr "Pas encore de données !"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No delivery to do!"
msgstr "Pas de livraison planifiée !"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "No negative quantities allowed"
msgstr "Les quantités négatives ne sont pas autorisées"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "No operation made on this lot."
msgstr "Aucune opération faite sur ce lot"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a transfer!"
msgstr "Aucune opération trouvée. Créons un transfert !"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "No product found to generate Serials/Lots for."
msgstr "Aucun produit n'a été trouvé pour générer des séries/lots."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "Aucun produit trouvé. Créons-en un !"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""
"Pas de produits à retourner (seules les lignes avec le statut \"terminé\" et"
" pas complètement retournées peuvent être renvoyées). "

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "Aucune stratégie de rangement trouvée. Créons-en une !"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No receipt yet! Create a new one."
msgstr "Il n'y a pas encore de reçu ! Créez-en un nouveau."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "Aucune règle de réapprovisionnement trouvée"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"No rule has been found to replenish \"%(product)s\" in \"%(location)s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"Aucune règle n'a été trouvée pour réapprovisionner \"%(product)s\" dans \"%(location)s\".\n"
"Vérifiez la configuration des routes du produit."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "No source location defined on stock rule: %s!"
msgstr "Pas d'emplacement d'origine défini sur la règle de stock : %s !"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "Aucun mouvement de stock trouvé"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "No stock to show"
msgstr "Pas de stock à afficher"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No transfer found. Let's create one!"
msgstr "Pas de transfert trouvé. Créons-en un !"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "Normal"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Not Available"
msgstr "Pas disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "Non reporté"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "Note"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "Notes"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Nothing to check the availability for."
msgstr "Pas de vérification de disponibilité à effectuer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "Novembre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Number of SN"
msgstr "Nombre de numéros de série"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN/Lots"
msgstr "Nombre de numéros de séries/lots"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "Nombre de mouvements entrants au cours des 12 derniers mois"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "Nombre de mouvements sortants au cours des 12 derniers mois"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Numbers of days  in advance that replenishments demands are created."
msgstr ""
"Nombre de jours à l'avance pendant lesquels les demandes de réassort sont "
"créées."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "Octobre"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Odoo opens a PDF preview by default. If you (Enterprise users only) want to print instantly,\n"
"                                        install the IoT App on a computer that is on the same local network as the\n"
"                                        barcode operator and configure the routing of the reports.\n"
"                                        <br/>"
msgstr ""
"Odoo ouvre un aperçu PDF par défaut. Si vous (uniquement les utilisateurs d'Enterprise) veulent imprimer instantanément,\n"
"                                        installez l'application IoT sur un ordinateur qui est sur le même réseau local que\n"
"                                        l'opérateur de code-barres et configurez l'acheminement des rapports.\n"
"                                        <br/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Office Chair"
msgstr "Chaise de bureau"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "On Hand"
msgstr "En stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_qty
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "Quantité en stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "On hand Quantity"
msgstr "Quantité disponible"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr ""
"Quantité en stock qui n'a pas été réservée pour un transfert, dans l'unité "
"de mesure du produit."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "En stock :"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__lots
msgid "One per lot/SN"
msgstr "Une par lot/NS"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__units
msgid "One per unit"
msgstr "Une par unité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Open"
msgstr "Ouvrir"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__move
msgid "Operation Quantities"
msgstr "Quantités de l'opération"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Operation Type"
msgstr "Type d'opération"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "Type d'opérations pour les retours"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "Types d'opérations"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_quant.py:0
msgid "Operation not supported"
msgstr "Opération non prise en charge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "Type d'opération"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "Type d'opération (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "Type d'opération (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "Opérations"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Types d'opérations"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Operations sans colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Optimize your transfers by grouping operations together and assigning jobs "
"to workers"
msgstr ""
"Optimisez vos transferts en regroupant les opérations et en attribuant des "
"tâches aux travailleurs"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""
"Adresse facultative pour la livraison des biens, utilisée en particulier "
"pour l'allocation des stocks."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "Détails facultatifs sur la localisation, uniquement à but informatif."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr ""
"Facultatif : tous les mouvements de retour créés à partir de ce mouvement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "Facultatif : mouvement de stock suivant quand ils sont chaînés."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Facultatif : mouvement de stock précédent quand ils sont chaînés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "Options"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order"
msgstr "Commander"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
msgid "Order To Max"
msgstr "Commander au max"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed"
msgstr "Bon signé"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed by %s"
msgstr "Bon signé par %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Ordered"
msgstr "Commandé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "Point de commande"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "Origine"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "Mouvements d'origine"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "Origine du retour"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "Mouvement original"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "Règle de réapprovisionnement originale"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "Autres informations"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"Nos factures sont payables dans les 21 jours ouvrables, à moins qu'un autre "
"délai ne soit précisé soit sur la facture ou sur la commande. En cas de non "
"paiement dans les temps impartis, My Company (Chicago) se réserve le droit "
"de réclamer un taux d'intérêt de 10% du montant restant dû. My Company "
"(Chicago) sera autorisée à suspendre toute prestation de services sans "
"avertissement en cas de retard de paiement."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "Type de sortie"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "Sortant"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Outgoing Draft Transfer"
msgstr "Brouillon de transfert sortant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "Mouvement de stock sortant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "Expéditions"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Output"
msgstr "Sortie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "Emplacement de sortie"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "Vue d'ensemble"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "Détenteur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "Propriétaire "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner:"
msgstr "Propriétaire :"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "P&L Qty"
msgstr "Qté pertes et profits"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Pack"
msgstr "Colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__pack_date
msgid "Pack Date"
msgstr "Date de colisage"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date Demo"
msgstr "Date de colisage démo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date:"
msgstr "Date de colisage :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "Type de colisage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "Colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package A"
msgstr "Colis A"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package B"
msgstr "Colis B"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "Code-barre du colis (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "Code-barre du colis (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Contents"
msgstr "Code-barre du colis avec contenu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "Capacité par colis"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_package_level.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Content"
msgstr "Contenu du colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Label"
msgstr "Étiquette de colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__package_label_to_print
msgid "Package Label to Print"
msgstr "Étiquette de colis à imprimer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "Niveau du colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "Détails de l'ID du niveau du colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Nom du colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Référence du colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Transferts de colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "Type de colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type Demo"
msgstr "Type de colis démo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type:"
msgstr "Type de colis :"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "Types de colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "Utilisation du colis"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Package manually relocated"
msgstr "Colis déplacé manuellement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__valid_sscc
msgid "Package name is valid SSCC"
msgstr "Le nom du colis est valide SSCC"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "Type de colis"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.actions.report,name:stock.action_report_picking_packages
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "Colis"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"Les colis sont généralement créés pendant les transferts (pendant une opération de colisage) et peuvent contenir plusieurs produits.\n"
"Une fois créés, un colis peut être transféré dans son entièreté ou peut être déballé et transféré par unités à nouveau"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "Conditionnement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "Hauteur du conditionnement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "Longueur du conditionnement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "Largeur du conditionnement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "Conditionnements"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Emplacement de colisage"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Packing Zone"
msgstr "Zone de colisage"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Pallet"
msgstr "Palette"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Emplacement parent"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "Chemin parent"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__parent_route_ids
msgid "Parent Routes"
msgstr "Itinéraires parents"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "Partiel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__partial_package_names
msgid "Partial Package Names"
msgstr "Noms du colis partial"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "Partiellement disponible"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Partenaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "Adresse du partenaire"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
msgid "Physical Inventory"
msgstr "Inventaire physique"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
msgid "Pick"
msgstr "Transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quant_id
msgid "Pick From"
msgstr "Enlever parmi"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "Type de transfert"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Pick then Deliver (2 steps)"
msgstr "Choisir puis livrer (2 étapes)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pick, Pack, then Deliver (3 steps)"
msgstr "Choisir, emballer, puis livrer (3 étapes)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picked
msgid "Picked"
msgstr "Transféré"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__picking_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Transfert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Listes de transferts"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Opérations de transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__picking_properties_definition
msgid "Picking Properties"
msgstr "Propriétés du transfert"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "Type de transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "Domaine de codes de types de transfert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Liste de transfert"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Planning Issue"
msgstr "Problème de planification"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "Problèmes de planification"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Please create a warehouse for company %s."
msgstr "Veuillez créer un entrepôt pour la société %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid ""
"Please put this document inside your return parcel.<br/>\n"
"                                Your parcel must be sent to this address:"
msgstr ""
"Veuillez placer ce document à l'intérieur de votre colis de retour.<br/>\n"
"                                Votre colis doit être envoyé à cette adresse :"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Please specify at least one non-zero quantity."
msgstr "Veuillez indiquer au moins une quantité non nulle."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Preceding operations"
msgstr "Opérations précédentes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_id
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__route_id
msgid "Preferred Route"
msgstr "Route préférée"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "Route préférée"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Presence depends on the type of operation."
msgstr "La présence dépend du type d'opération."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Press the \"New\" button to define the quantity for a product in your stock "
"or import quantities from a spreadsheet via the Actions menu"
msgstr ""
"Appuyez sur le bouton \"Nouveau\" pour définir la quantité d'un produit en "
"stock ou importer des quantités à partir d'un fichier via le menu Actions."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Imprimer"

#. module: stock
#: model:res.groups,name:stock.group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lot & Serial Numbers"
msgstr "Imprimer des codes-barres GS1 pour les numéros de lot et de série"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lots & Serial Numbers"
msgstr "Imprimer des codes-barres GS1 pour les lots et les numéros de série"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Print Label"
msgstr "Imprimer l'étiquette"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Print Labels"
msgstr "Imprimer les étiquettes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print label as:"
msgstr "Imprimer l'étiquette comme :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on \"Put in Pack\""
msgstr "Imprimer sur \"Mettre en colis\""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on Validation"
msgstr "Imprimer sur validation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "Imprimé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "Priorité"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "Traiter à cette date pour être à temps"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "Traiter les opérations plus rapidement en utilisant les codes-barres"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement
msgid "Procurement"
msgstr "Approvisionnement"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "Groupe d'approvisionnement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "Groupe d'approvisionnement"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Procurement: run scheduler"
msgstr "Approvisionnement : lancer le planificateur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "Ligne de production"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Produced Qty"
msgstr "Qté produite"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "Produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "Disponibilité des produits"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "Capacité par produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "Catégories de produits"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_category_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "Catégorie de produits"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Product Display Name"
msgstr "Nom d'affichage du produit"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "Label de produit (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__product_label_format
msgid "Product Label Format to auto-print"
msgstr "Format de l'étiquette du produit à imprimer automatiquement"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "Rapport d'étiquettes de produit"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__products
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Product Labels"
msgstr "Étiquettes de produits"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Filtre sur les numéros de série"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mouvements de produit (Ligne de mouvement de stock)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "Conditionnement des produits"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "Conditionnement du produit (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "Conditionnements de produits"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Confirmed"
msgstr "Quantité de produit confirmée"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Updated"
msgstr "Quantité de produit mise à jour"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Product Relocated"
msgstr "Produit relocalisé"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.js:0
#: model:ir.model,name:stock.model_product_replenish
msgid "Product Replenish"
msgstr "Réassort de produit"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Mixin du réassort de produits"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "Analyse des routes du produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "Modèle de produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "Modèle de produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "Suivi de produit"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unité de mesure du produit"

#. module: stock
#: model:ir.model,name:stock.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "Variantes de produit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Product barcode"
msgstr "Code-barres du produit"

#. module: stock
#. odoo-python
#: code:addons/stock/report/product_label_report.py:0
msgid "Product model not defined, Please contact your administrator."
msgstr "Modèle de produit non défini, veuillez contacter votre administrateur"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""
"Produit contenu dans ce lot/numéro de série. Vous ne pouvez plus le modifier"
" s'il a déjà été déplacé."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "Libellé de l'unité de mesure du produit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "Produit avec suivi"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "Production"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "Emplacement de production"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "Emplacements de production"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Products"
msgstr "Produits"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "État de disponibilité des produits"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr ""
"Les produits seront réservés en priorité aux transferts les plus "
"prioritaires."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Products: %(location)s"
msgstr "Produits : %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "Propager"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Propager les annulations et les fractionnements"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "Propagation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "Propagation du groupe d'approvisionnement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "Propagation du transporteur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__lot_properties
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_properties
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_properties
msgid "Properties"
msgstr "Propriétés"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "Tirer & Pousser"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "Tirer depuis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "Règle de flux tiré"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__push_domain
msgid "Push Applicability"
msgstr "Applicabilité push"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "Règle de flux poussé"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "Pousser vers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_detailed_operation_tree
msgid "Put in Pack"
msgstr "Mettre en colis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr ""
"Emballer vos produits (p. ex. dans des colis ou des boîtes) et les suivre"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "Statégie de rangement"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Putaway Rules"
msgstr "Stratégies de rangement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "Rangement :"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "Stratégies de rangement"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "Le qté multiple doit être supérieure ou égale à zéro."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "Qualité"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Quality Control"
msgstr "Contrôle qualité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Emplacement de contrôle qualité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__qc_type_id
msgid "Quality Control Type"
msgstr "Type de contrôle qualité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Feuille de travail qualité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "Qté"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's creation is restricted, you can't do this operation."
msgstr ""
"La création de quant n'est pas autorisée, vous ne pouvez pas réaliser cette "
"opération."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's editing is restricted, you can't do this operation."
msgstr ""
"L'édition de quant n'est pas autorisée, vous ne pouvez pas réaliser cette "
"opération."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities Already Set"
msgstr "Quantités déjà définies"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities To Reset"
msgstr "Quantités à remettre à 0"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities unpacked"
msgstr "Quantités déballées"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Quantity"
msgstr "Quantité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Quantité multiple"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "Quantité en stock"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity Received"
msgstr "Quantité reçue"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity Relocated"
msgstr "Quantité relocalisée"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "Quantité réservée"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "Quantity available too low"
msgstr "Quantité disponible trop faible"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_change_product_qty.py:0
msgid "Quantity cannot be negative."
msgstr "La quantité ne peut pas être négative."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "Des quantités ont été transférées depuis votre dernier compte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity_product_uom
msgid "Quantity in Product UoM"
msgstr "Quantité dans l'UdM du produit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr "Quantité en stock qui peut encore être réservée pour ce mouvement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Quantité dans l'UdM par défaut du produit"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"La quantité de produits entrants prévue.\n"
"Dans le cas d'un emplacement de stock unique, il s'agit des marchandises arrivant à cet emplacement ou à l'un de ses enfants.\n"
"Dans le cas d'un entrepôt unique, il s'agit des marchandises arrivant à l'emplacement de stock de cet entrepôt ou à l'un de ses enfants.\n"
"Autrement, il s'agit des marchandises arrivant à n'importe quel emplacement de stock de type 'interne'."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"La quantité de produits sortants prévue.\n"
"Dans le cas d'un emplacement de stock unique, il s'agit des marchandises quittant cet emplacement ou l'un de ses enfants.\n"
"Dans le cas d'un entrepôt unique, il s'agit des marchandises quittant l'emplacement de stock de cet entrepôt ou l'un de ses enfants.\n"
"Autrement, il s'agit des marchandises quittant n'importe quel emplacement de stock de type 'interne'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr ""
"Quantité de produits dans ce quant, dans l'unité de mesure par défaut du "
"produit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr ""
"La quantité de produits réservés dans ce quant, dans l'unité de mesure par "
"défaut du produit"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity or Reserved Quantity should be set."
msgstr "La quantité ou la quantité réservée doit être définie."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity per Lot"
msgstr "Quantité par lot"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "La quantité doit être positive"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__label_quantity
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_quantity
msgid "Quantity to print"
msgstr "Quantité à imprimer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity:"
msgstr "Quantité :"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "Quantités"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr ""
"Les quantités sont supprimées automatiquement le cas échéant. Si vous devez "
"les supprimer manuellement, demandez à un gestionnaire des stocks de le "
"faire."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quants cannot be created for consumables or services."
msgstr ""
"Impossible de créer des quants pour des produits consommables ou des "
"services."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "RETURN OF"
msgstr "RETOUR DE"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Prêt"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_ready_moves
msgid "Ready Moves"
msgstr "Mouvements prêts"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "Quantité réelle"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Reason"
msgstr "Motif"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__message
msgid "Reason for relocation"
msgstr "Motif du relocalisation"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
msgid "Receipt"
msgstr "Reçu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "Route de réception"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_incoming
#: model:ir.ui.menu,name:stock.in_picking
#: model:stock.picking.type,name:stock.picking_type_in
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Receipts"
msgstr "Réceptions"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "Recevoir de"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive and Store (1 step)"
msgstr "Recevoir et stocker (1 étape)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 1 step (stock)"
msgstr "Recevoir en 1 étape (stock)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 2 steps (input + stock)"
msgstr "Recevoir en 2 étapes (emplacement d'entrée + stock)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 3 steps (input + quality + stock)"
msgstr ""
"Recevoir en 3 étapes (emplacement d'entrée + contrôle qualité + stock)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive then Store (2 steps)"
msgstr "Recevoir puis stocker (2 étapes)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive, Quality Control, then Store (3 steps)"
msgstr "Recevoir, Contrôle qualité, puis stocker (3 étapes)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Received Qty"
msgstr "Qté reçue"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report"
msgstr "Rapport de réception"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "Étiquette de rapport de réception"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report Labels"
msgstr "Étiquettes du rapport de réception"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"Reduce stockouts with alerts, barcode app, replenishment propositions,\n"
"                        locations management traceability, quality control, etc."
msgstr ""
"Réduction des ruptures de stock grâce aux alertes, à l'application code-barres, aux propositions de réassort,\n"
"                        à la traçabilité de la gestion des emplacements, au contrôle de la qualité, etc."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "Référence"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "Séquence de référence"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "La référence doit être unique par société !"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "Référence du document"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "Référence :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "Mouvements de stock liés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Relocate"
msgstr "Relocaliser"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Relocate your stock"
msgstr "Relocaliser votre stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Parties restantes de transferts partiellement traités"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Enlèvement"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "Stratégie d'enlèvement"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Removal strategy %s not implemented."
msgstr "La stratégie d'enlèvement %s n'est pas implémentée."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Remove manually entered value and replace by the quantity to order based on "
"the forecasted quantities"
msgstr ""
"Supprimer la valeur saisie manuellement et la remplacer par la quantité à "
"commander en fonction des quantités prévues."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Qté de réapprovisionnement max."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Qté de réapprovisionnement min."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "Règle de réapprovisionnement"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Reordering Rules"
msgstr "Règles de réapprovisionnement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Recherche des règles de réapprovisionnement"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Replenish"
msgstr "Réapprovisionner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__replenish_location
msgid "Replenish Location"
msgstr "Emplacement de réassort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__should_replenish
msgid "Replenish Quantities"
msgstr "Réapprovisionner les quantités"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "Réapprovisionner sur commande (MTO)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "Assistant de réapprovisionnement"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Replenishment"
msgstr "Réassort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__replenishment_info_id
msgid "Replenishment Info"
msgstr "Information de réassort"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Replenishment Information"
msgstr "Information de réassort"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Information for %(product)s in %(warehouse)s"
msgstr "Information de réassort pour %(product)s dans %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Report"
msgstr "Rapport de réassorts"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "Recherche dans le Rapport de réassorts"

#. module: stock
#: model:ir.model,name:stock.model_ir_actions_report
msgid "Report Action"
msgstr "Action de rapport"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Report Printing Error"
msgstr "Erreur d'impression du rapport"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Reporting"
msgstr "Analyse"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "Demander un inventaire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Request your vendors to deliver to your customers"
msgstr "Demander à vos fournisseurs de livrer à vos clients"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "Demander une signature sur vos bons de livraison"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "Méthode de réservation"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "Réservations"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Reserve"
msgstr "Réserver"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "Réserver seulement des conditionnements entiers"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"Réserver seulement des conditionnements entiers : ne réservera pas des conditionnements partiels. Si un client commande 2 palettes de 1000 unités chacune et que vous n'avez que 1600 unités en stock, alors seulement 1000 seront réservées.\n"
"Réserver des conditionnements partiels : autorise la réservation de conditionnements partiels. Si le client commande 2 palettes de 1000 unités chacune et que vous n'avez que 1600 disponible, alors les 1600 seront réservées."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "Réserver des conditionnements"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "Réserver des conditionnements partiels"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
msgid "Reserved"
msgstr "Réservé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_packaging_qty
msgid "Reserved Packaging Quantity"
msgstr "Quantité de conditionnement réservée"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "Quantité réservée"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Reserving a negative quantity is not allowed."
msgstr "Réserver une quantité négative n'est pas permis."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "Réapprovisionner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "Réapprovisionner depuis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "Routes de réapprovisionnement"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Retour"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return All"
msgstr "Tout renvoyer"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Transfert de retour"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Ligne de transfert de retour"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Return Slip"
msgstr "Bon de retour"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return for Exchange"
msgstr "Retour pour échanger"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_id
msgid "Return of"
msgstr "Retour de"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Return of %(picking_name)s"
msgstr "Retour de %(picking_name)s"

#. module: stock
#: model:ir.actions.report,name:stock.return_label_report
msgid "Return slip"
msgstr "Bon de retour"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Returned Picking"
msgstr "Bon de retour"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_ids
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Returns"
msgstr "Retours"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Returns Type"
msgstr "Type de retour"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "Boîte réutilisable"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"Les boîtes réutilisables sont utilisées pour des transferts par lot et sont vidées par la suite afin d'être réutilisées. Dans l'application Code-barres, il suffit de scanner une boîte réutilisable pour ajouter les produits dans cette boîte.\n"
"Les boîtes jetables ne sont pas réutilisées. Quand on scanne une une boîte jetable dans Code-barres, les produits contenus sont ajoutés au transfert"

#. module: stock
#: model:ir.actions.server,name:stock.action_revert_inventory_adjustment
msgid "Revert Inventory Adjustment"
msgstr "Annuler l'ajustement de l'inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__route_id
#: model:ir.model.fields,field_description:stock.field_stock_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "Route"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "Société de la route"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "Séquence de route"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Routes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "Les routes ne peuvent être sélectionnées sur ce produit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""
"Des routes seront créées automatiquement pour réapprovisionner cet entrepôt "
"depuis les entrepôts choisis"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""
"Des routes seront créées pour ces entrepôts de réapprovisionnement et vous "
"pourrez les activer au niveau des produits et catégories de produits"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"Rule %(rule)s belongs to %(rule_company)s while the route belongs to "
"%(route_company)s."
msgstr ""
"Règle %(rule)s appartient à %(rule_company)s tandis que la route while the "
"route belongs to %(route_company)s."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "Message de règle"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "Règles"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "Règles sur les catégories"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "Règles sur les produits"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "Règles utilisées"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "S0001"
msgstr "S0001"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "Confirmation par SMS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC Demo"
msgstr "SSCC démo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC:"
msgstr "SSCC :"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "CONDITIONS GENERALES DE VENTE"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Sales History"
msgstr "Historique de ventes"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sample data"
msgstr "Données démo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_move_line__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "Date planifiée"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
#: model:ir.model.fields,help:stock.field_stock_move_line__scheduled_date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr ""
"Date planifiée tant que le mouvement n'est pas terminé, puis date de "
"l'exécution réelle du mouvement."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "Date programmée ou date de traitement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"Heure prévue de la première partie à expédier. Préciser une date ici revient"
" à spécifier la date prévue pour tous les mouvements de stock."

#. module: stock
#: model:ir.actions.server,name:stock.action_scrap
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap"
msgstr "Rebut"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "Emplacement de rebut"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "Ordres de mise au rebut"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap Products"
msgstr "Produits de rebut"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_reason_tag_ids
msgid "Scrap Reason"
msgstr "Raison de mise au rebut"

#. module: stock
#: model:ir.model,name:stock.model_stock_scrap_reason_tag
msgid "Scrap Reason Tag"
msgstr "Étiquette raison de mise au rebut"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_id
msgid "Scrap operation"
msgstr "Opération de rebut"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "Mettre au rebut"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "Mis au rebut"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"La mise au rebut d'un produit retirera ce dernier de votre stock. Le produit"
" sera placé dans un emplacement de rebut qui peut être utilisé pour produire"
" des rapports."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "Rebuts"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "Rechercher dans les approvisionnements"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "Recherche de rebuts"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Search not supported without a value."
msgstr "Recherche non prise en charge sans valeur."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Select Route"
msgstr "Sélectionner la route"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr ""
"Sélectionnez les endroits dans lesquels cette route pourra être sélectionnée"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
msgid ""
"Selected storage category does not exist in the 'store to' location or any "
"of its sublocations"
msgstr ""
"Le type de stockage sélectionné n'existe pas dans l'emplacement \" stocker "
"dans \" ou dans l'un de ses sous-emplacements."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Sélectionner l'option 'Avertissement' notifiera l'utilisateur avec le "
"Message. Sélectionner 'Message bloquant' lancera une exception avec le "
"message et bloquera le flux. Le message doit être encodé dans le champ "
"suivant."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Selection not supported."
msgstr "Sélection non prise en charge."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Acheter et vendre des produits dans des unités de mesure différentes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr ""
"Envoyer un sms de confirmation automatique à la validation des bons de "
"livraison"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr ""
"Envoyer un e-mail de confirmation automatique à la validation des bons de "
"livraison"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "Envoyer un e-mail"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Connecteur SendCloud"

#. module: stock
#: model:mail.template,description:stock.mail_template_data_delivery_confirmation
msgid ""
"Sent to the customers when orders are delivered, if the setting is enabled"
msgstr ""
"Envoyé aux clients lors de la livraison des commandes, si le paramètre est "
"activé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__barcode_separator
msgid "Separator"
msgstr "Séparateur"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "Septembre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Sequence"
msgstr "Séquence"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sequence %(code)s"
msgstr "Ordre %(code)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Sequence Prefix"
msgstr "Préfixe de la Séquence"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "Numéros de série"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Serial number (%(serial_number)s) already exists in location(s): "
"%(location_list)s. Please correct the serial number encoded."
msgstr ""
"Numéro de série (%(serial_number)s) déjà existant dans le(s) emplacement(s) "
": %(location_list)s.Veuillez corriger le numéro de série encodé."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"Le numéro de série (%(serial_number)s) ne se trouve pas dans %(source_location)s, mais se trouve dans le(s) emplacement(s) : %(other_locations)s.\n"
"\n"
"Veuillez corriger cela afin d'éviter des données incohérentes."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Source location for this move will be changed to %(recommended_location)s"
msgstr ""
"Le numéro de série (%(serial_number)s) ne se trouve pas dans %(source_location)s, mais se trouve dans l'/les emplacements : %(other_locations)s.\n"
"\n"
"L'emplacement source de ce mouvement devient %(recommended_location)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Serial numbers"
msgstr "Numéros de série"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "Copier"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "Copier la quantité actuelle"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "Définir les routes de l'entrepôt"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closest location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting).\n"
"Least Packages: FIFO but with the least number of packages possible when there are several packages containing the same product."
msgstr ""
"Définissez une stratégie d'enlèvement spécifique qui sera utilisée quel que soit l'emplacement d'origine pour cette catégorie de produits..\n"
"\n"
"FIFO : les produits/lots stockés en premier seront sortis en premier.\n"
"LIFO : les produits/lots stockés en dernier seront sortis en premier.\n"
"Emplacement le plus proche : les produits/lots les plus proches de l'emplacement cible seront déplacés en premier.\n"
"FEFO : les produits/lots dont la date de retrait est la plus proche seront retirés en premier (la disponibilité de cette méthode dépend du paramètre \"Dates d'expiration\").\n"
"Le moins de colis possible : FIFO mais avec le moins de colis possible lorsqu'il y a plusieurs colis contenant le même produit."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots & serial numbers"
msgstr "Définir des dates d'expiration sur les numéros de lot et de série"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "Définir un propriétaire sur les produits stockés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""
"Définir les attributs du produit (par ex. couleur, taille,...) pour gérer "
"les variantes"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_to_zero_quants_tree
msgid "Set to 0"
msgstr "Définir sur 0"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
msgid "Set to quantity on hand"
msgstr "Définir sur la quantité en stock"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "Paramètres"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Shelf A"
msgstr "Étagère A"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "Rayon (Y)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "Ship a lot to a customer."
msgstr "Expédier un lot à un client."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "Expéditions"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "Expédition"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Connecteurs d'expédition"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__move_type
msgid "Shipping Policy"
msgstr "Politique d'expédition"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Poids d'expédition"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"Les connecteurs d'expédition permettent de calculer les frais d'expédition "
"précis, d'imprimer des étiquettes d'expédition et de demander la "
"récupération du colis à l'entrepôt par le fournisseur pour l'envoyer au "
"client. Pour appliquer un connecteur d'expédition, accédez aux modes de "
"livraison."

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Shipping: Send by Email"
msgstr "Expédition : Envoyer par e-mail"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Connecteur Shiprocket"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "Nom court"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "Nom court utilisé pour identifier votre entrpôt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "Afficher l'allocation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "Afficher Vérifier la disponibilité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "Afficher Opérations détaillées"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "Bouton d'Affichage de la Qté prévue"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "Montrer les lots M2O"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "Afficher Texte des lots"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_next_pickings
msgid "Show Next Pickings"
msgstr "Afficher les transferts suivants"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "Montrer le Bouton de Statut de la Qté en stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__is_favorite
msgid "Show Operation in Overview"
msgstr "Afficher l'opération dans la vue d'ensemble"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_picking_type
msgid "Show Picking Type"
msgstr "Afficher le type de transfert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_quant
msgid "Show Quant"
msgstr "Afficher la quantité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_show_reception_report
msgid "Show Reception Report at Validation"
msgstr "Montrer le Rapport de réception à la validation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
msgid "Show Transfers"
msgstr "Montrer les transferts"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez tous les enregistrements pour lesquels la date des prochaines "
"actions est pour aujourd'hui ou avant. "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_m2o
msgid "Show lot_id"
msgstr "Afficher lot_id"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_text
msgid "Show lot_name"
msgstr "Afficher lot_name"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "Montrer les routes qui s'appliquent aux entrepôts sélectionnés"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "Signer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "Signature"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "Signé"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "Taille"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "Taille : Longueur x Largeur x Hauteur"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Snooze"
msgstr "Reporter"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "Date de report"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "Reporter le Point de commande"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "Reporter de"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "Reporté"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr ""
"Certaines lignes sélectionnées ont déjà une quantité comptée définie, elles "
"seront ignorées."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "Source"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "Document d'origine"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source Location"
msgstr "Emplacement d'origine"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_usage
msgid "Source Location Type"
msgstr "Type d'emplacement d'origine"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "Emplacement d'origine :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Source Name"
msgstr "Nom de source"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "Colis d'origine"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package:"
msgstr "Colis d'origine :"

#. module: stock
#: model:ir.actions.server,name:stock.stock_split_picking
msgid "Split"
msgstr "Fractionner"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "Favoris"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Starred Products"
msgstr "Produits favoris"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr "Connecteur Starshipit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
msgid "State"
msgstr "Statut"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Statut"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_state
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_product_stock_view
#: model:ir.ui.menu,name:stock.menu_product_stock
msgid "Stock"
msgstr "Stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode_barcodelookup
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Stock Barcode Database"
msgstr "Base de données stock code-barres"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Stock In Transit"
msgstr "Stock en transit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Emplacement de stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Emplacements de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "Mouvements de stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Analyse des mouvements de stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "Opération de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Emplacement de destination du colis"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "Niveau de colisage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "Colisage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "Quant de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Historique des quantités en stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_relocate
msgid "Stock Quantity Relocation"
msgstr "Relocalisation des quantités en stock"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "Rapport de quantité en stock"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Rapport de réception de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_forecasted_product_product
#: model:ir.model,name:stock.model_stock_forecasted_product_template
msgid "Stock Replenishment Report"
msgstr "Rapport de réassorts de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Le stock requière un inventaire"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "Règle de stock"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "Analyse des règles de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Analyse des règles de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "Confirmation du suivi de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "Ligne de suivi du stock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock move"
msgstr "Mouvement de stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "Les mouvements de stock qui sont disponiibles (prêts à traiter)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "Mouvements de stock confirmés, disponible ou en attente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Mouvements de stock ayant été traités"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "Type de colis de stock"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Rapport de règle de stock"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Informations sur le réassort des fournisseurs"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "Option de réassort de l'entrepôt"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Storage"
msgstr "Stockage"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid "Storage Capacities"
msgstr "Capacités d'emplacement"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "Catégories d'emplacement"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "Catégories d'emplacement"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "Capacité de la catégorie d'emplacement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "Emplacements de stockage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__store_type_id
msgid "Storage Type"
msgstr "Type de stockage"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Store To"
msgstr "Stocker dans"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid ""
"Store and retrieve information regarding every Lot/Serial Number (condition,"
" product info, ...)."
msgstr ""
"Stocker et récupérer des informations concernant chaque numéro de lot/série "
"(état, informations produit, ...)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""
"Stocker les produits dans des zones d'emplacement spécifiques dans votre "
"entrepôt (exemple: bacs, étagères) et suivez votre inventaire en fonction. "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Store to"
msgstr "Stocker dans"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "Ranger dans la sous-location de"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sublocation
msgid "Sublocation"
msgstr "Sous-emplacement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Entrepôt approvisionné"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "Méthode d'approvisionnement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Entrepôt d'approvisionement "

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_scrap_reason_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Ce nom d'étiquette existe déjà !"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "Prendre dans le stock"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "Prendre dans le stock, si pas disponible, déclencher une autre règle."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"Prendre dans le stock: les articles seront pris du stock disponible de l'emplacement d'origine.\n"
"Déclencher une autre règle: le système va essayer de trouver une règle de stock pour amener les articles dans l'emplacement d'origine. Le stock disponible sera ignoré.\n"
"Prendre dans le stock, si pas disponible,  déclencher une autre règle: les articles seront pris du stock disponible de l'emplacement d'origine. S'il n'y a pas de stock disponible, le système va essayer de trouver une règle de stock pour amener les articles dans l'emplacement d'origine."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Champ technique indiquant si le bouton Allocation doit être affiché ou non."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Informations techniques"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr ""
"Champ technique indiquant si le bouton \"Vérifier la disponibilité\" doit "
"être affiché ou non."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "Modèle"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The %s location is required by the Inventory app and cannot be deleted, but "
"you can archive it."
msgstr ""
"L'emplacement %s est requis par l'application Inventaire et ne peut pas être"
" supprimé. Vous pouvez toutefois l'archiver."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"La valeur 'Opération manuelle' permet de créer un mouvement de stock après "
"le mouvement actuel. Si 'Mouvement automatique sans ajout d'étape' est "
"sélectionné, l'emplacement de stock sera remplacé lors du mouvement initial."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "The Lot/Serial number (%s) is linked to another product."
msgstr "Le lot/numéro de série (%s) est lié à un autre produit."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"The Picking Operations report has been deleted so you cannot print at this "
"time unless the report is restored."
msgstr ""
"Le rapport des opérations de transfert a été supprimé. Vous ne pouvez donc "
"pas l'imprimer pour le moment, sauf si le rapport est restauré."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The Serial Number (%(serial_number)s) is already used in location(s): %(location_list)s.\n"
"\n"
"Is this expected? For example, this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"Le numéro de série (%(serial_number)s) est déjà utilisé dans cet emplacement : %(location_list)s.\n"
"\n"
"Comment est-ce possible? Par exemple si une livraison est validée avant que la livraison correspondante soit validée. Dans ce cas, le problème sera résolu automatiquement une fois que tout aura été validé. Autrement, les numéros de série doivent être corrigés pour empêcher les données inconsistantes."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "The backorder %s has been created."
msgstr "Le reliquat %s a été créé."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company!"
msgstr "Le code-barres pour un emplacement doit être unique par société !"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""
"Le client renonce expressément à ses propres conditions générales de vente, "
"même si celles-ci ont été établies postérieurement aux présentes conditions "
"générales de vente. Pour être valable, toute dérogation doit être "
"expressément convenue au préalable par écrit."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"The combination of lot/serial number and product must be unique within a company including when no company is defined.\n"
"The following combinations contain duplicates:\n"
"%(error_lines)s"
msgstr ""
"La combinaison du numéro de lot/série et du produit doit être unique au sein d'une entreprise, y compris lorsque aucune entreprise n'est définie.\n"
"Les combinaisons suivantes contiennent des doublons :\n"
"%(error_lines)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr ""
"La société est automatiquement définie d'après vos préférences "
"d'utilisateur."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "The day after tomorrow"
msgstr "Après-demain"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The deadline has been automatically updated due to a delay on %s."
msgstr ""
"La date limite a été automatiquement mise à jour en raison d'un retard de "
"%s."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr "La date prévue pour le transfert créé sera calculée selon ce délai."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "Le premier dans la séquence est celui par défaut."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "The following replenishment order have been generated"
msgstr "L'ordre de réassort suivant a été généré"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "The forecasted quantity of"
msgstr "La quantité prévue de"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "The forecasted stock on the"
msgstr "Le stock prévu pour le"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "The inter-warehouse transfers have been generated"
msgstr "Les transferts entre entrepôts ont été générés"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "The inventory adjustments have been reverted."
msgstr "Les ajustements d'inventaire ont été annulés."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr ""
"La fréquence de l'inventaire (jours) pour un emplacement ne peut être "
"négative"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"The minimum quantity must be less than or equal to the maximum quantity."
msgstr ""
"La quantité minimale doit être inférieure ou égale à la quantité maximale."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "Le nom de l'entrepôt doit être unique par société !"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr "Le nombre de numéros de série à générer doit être supérieur à zéro."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_final_id
msgid ""
"The operation brings the products to the intermediate location.But this "
"operation is part of a chain of operations targeting the final location."
msgstr ""
"L'opération amène les produits à l'emplacement intermédiaire. Cependant, "
"cette opération fait partie d'une chaîne d'opérations visant l'emplacement "
"final."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid "The operation takes and suggests products from this location."
msgstr ""
"L'opération consiste à prendre et à proposer des produits à partir de cet "
"emplacement."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""
"Le système des types d'opérations vous permet d'assigner à chaque opération\n"
"                de stock un type spécifique qui altérera ses vues en fonction.\n"
"                Sur le type d'opération, vous pourriez par exemple spécifier si le colis est requis par défaut,\n"
"                si le client doit être affiché."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "The operations brings product to this location"
msgstr ""
"Les opérations permettent d'acheminer le produit jusqu'à cet emplacement"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "Le colis contenant cette quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""
"L'emplacement parent qui comprend cet emplacement. Par exemple, la 'Zone "
"d'expédition' est l'emplacement parent de la 'Porte 1'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to a multiple of this field "
"quantity. If it is 0, it is not rounded."
msgstr ""
"La quantité à approvisionner sera arrondie à un multiple de la quantité de "
"ce champ. Si elle est égale à 0, elle ne sera pas arrondie."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "Ce produit n'est pas disponible en quantité suffisante"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "La quantité comptée du produit."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"The quantities selected do not all belong to the same location.\n"
"                    You may not assign them a package without moving them to a common location."
msgstr ""
"Les quantités sélectionnées n'appartiennent pas au même emplacement. \n"
"Vous ne pouvez pas leur attribuer un colis sans les déplacer vers un emplacement commun."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"The quantity done for the product \"%(product)s\" doesn't respect the "
"rounding precision defined on the unit of measure \"%(unit)s\". Please "
"change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"La quantité réalisée pour le produit \"%(product)s\" ne respecte pas les "
"règles d'arrondissement définies dans l'unité de mesure \"%(unit)s\". "
"Veuillez changer la quantité réalisée ou les règles d'arrondissement de "
"votre unité de mesure."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The quantity per lot should always be a positive value."
msgstr "La quantité par lot doit toujours être une valeur positive."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"Impossible de traiter l'opération demandée à cause d'une erreur de "
"programmation : on a utilisé le champ `product_qty` au lieu de "
"`product_uom_qty`."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The search does not support the %(operator)s operator or %(value)s value."
msgstr ""
"La recherche ne prend pas en charge l'opérateur %(operator)s ou la valeur "
"%(value)s."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr ""
"La fréquence de l'inventaire sélectionnée crée un date trop éloignée dans le"
" futur."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The serial number has already been assigned: \n"
" Product: %(product)s, Serial Number: %(serial_number)s"
msgstr ""
"Le numéro de série à déjà été assigné : \n"
" Produit : %(product)s, N° de série : %(serial_number)s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr "Le nom court de l'entrepôt doit être unique par société !"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"L'emplacement de stock utilisé comme destination quand vous envoyez des "
"biens à ce contact."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"L'emplacement de stock utilisé comme source quand vous recevez des "
"marchandises de ce contact."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "L'opération de stock où le colis a été fait."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "La règle d'approvisionnement qui a créé ce mouvement de stock"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"L'entrepôt à propager au mouvement/approvisionnement créé, qui peut être "
"différent de l'entrepôt pour lequel on fait cette règle (par ex. pour les "
"règles de réapprovisionnement depuis un autre entrepôt)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "There are no inventory adjustments to revert."
msgstr "Il n'y a aucun ajustement d'inventaire à annuler."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"There is nothing eligible to put in a pack. Either there are no quantities "
"to put in a pack or all products are already in a pack."
msgstr ""
"Il n'y a rien d'éligible à mettre en colis. Soit il n'y a pas de quantités à"
" mettre en colis, soit tous les produits sont déjà mis dans un colis."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "Il n'y a pas encore de mouvement de produit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "This SN is already in another location."
msgstr "Ce numéro de série se trouve déjà à un autre emplacement."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"Cela permet d'ajouter une route de dropshipping à appliquer aux produits "
"afin de demander à vos fournisseurs de livrer à vos clients. Un produit en "
"dropshipping génère une demande de prix d'achat une fois la commande client "
"confirmée. Il s'agit d'un flux à la demande. L'adresse de livraison demandée"
" est celle de votre client, et non de votre entrepôt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr ""
"Cette analyse vous donne un aperçu du niveau de stock actuel de vos "
"produits."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picked
msgid ""
"This checkbox is just indicative, it doesn't validate or generate any "
"product moves."
msgstr ""
"Cette case à cocher n'est qu'indicative, elle ne valide ni ne génère aucun "
"mouvement de produit."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr "Ce champ remplira l'origine du colisage et le nom de ses mouvements"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when this operation is manually "
"created. However, it is possible to change it afterwards or that the routes "
"use another one by default."
msgstr ""
"Il s'agit de l'emplacement de destination par défaut lorsque cette opération"
" est créée manuellement. Cependant, il est possible de le modifier par la "
"suite ou que les itinéraires utilisent un autre emplacement par défaut."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when this operation is manually created."
" However, it is possible to change it afterwards or that the routes use "
"another one by default."
msgstr ""
"Il s'agit de l'emplacement source par défaut lorsque cette opération est "
"créée manuellement. Cependant, il est possible de le modifier par la suite "
"ou que les itinéraires utilisent un autre emplacement par défaut."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "Il s'agit du propriétaire du quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of product that is planned to be moved.Lowering this "
"quantity does not generate a backorder.Changing this quantity on assigned "
"moves affects the product reservation, and should be done with care."
msgstr ""
"Il s'agit de la quantité de produits qu'il est prévu de déplacer. "
"L'abaissement de cette quantité ne génère pas de reliquat. La modification "
"de cette quantité sur les mouvements assignés affecte la réservation de "
"produits et doit être effectuée avec précaution."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr ""
"Cet emplacement (s'il est interne) et ses descendants filtrés par "
"type=interne"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr ""
"L'utilisation de cet emplacement ne peut pas être définie sur Vue car il "
"contient des produits."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr "Ce lot %(lot_name)s est incompatible avec ce produit %(product_name)s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "This lot/serial number is already in another location"
msgstr "Ce lot/numéro de série est déjà dans un autre emplacement"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Ce menu vous donne la traçabilité complète des opérations d'inventaire pour "
"un produit donné. Vous pouvez filtrer sur le produit pour voir tous les "
"déplacements passés et futurs de ce produit."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"Ce menu donne une traçabilité complète des opérations d'inventaire liées à "
"un produit spécifque. Vous pouvez filtrer sur le produit pour voir tous les "
"déplacements passés liés à ce produit."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "Cette note est ajoutée aux bons de livraison."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""
"Cette note est ajoutée aux transferts internes (par ex. où prendre les "
"produits dans l'entrepôt)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr ""
"Cette note est ajoutée aux bons de réception (par ex. où stocker les "
"produits dans l'entrepôt)."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"Ce produit a été utilisé dans au moins un mouvement de stock. Il n'est pas "
"conseillé de changer le type de produit, car cela peut entraîner des "
"incohérences. Une meilleure solution pourrait consister à archiver le "
"produit et à en créer un nouveau à la place."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr ""
"La société de ce produit ne peut être modifiée tant qu'il existe des "
"quantités de ce produit appartenant à une autre société."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr ""
"La société de ce produit ne peut pas être modifiée tant qu'il y a des "
"mouvements de stock de ce produit appartenant à une autre société."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr ""
"Cette quantité est exprimée dans l'unité de mesure par défaut du produit."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid "This record already exists."
msgstr "Cet enregistrement existe déjà."

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "This report cannot be used for done and not done %s at the same time"
msgstr ""
"Ce rapport ne peut être utilisé à la fois pour des %s terminés et non "
"terminés "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"This sequence prefix is already being used by another operation type. It is "
"recommended that you select a unique prefix to avoid issues and/or repeated "
"reference values or assign the existing reference sequence to this operation"
" type."
msgstr ""
"Le préfixe de cette séquence est déjà utilisé par un autre type d'opération."
" Il est recommandé de sélectionner un préfixe unique pour éviter les "
"problèmes et/ou les valeurs de référence répétées ou d'affecter la séquence "
"de référence existante à ce type d'opération."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Cet emplacement de stock sera utilisé, à la place de l'emplacement par "
"défaut, comme emplacement d'origine des mouvements de stock générés par les "
"ordres de fabrication."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Cet emplacement de stock sera utilisé, à la place de l'emplacement par "
"défaut, comme emplacement d'origine pour les mouvements de stock générés "
"quand vous faites un inventaire."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""
"Cet utilisateur sera responsable des prochaines activités afférentes aux "
"opérations logistiques pour ce produit."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr ""
"Ceci va ignorer tous les comptages non appliqués, est-ce vraiment ce que "
"vous voulez faire ?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Those products you added are tracked but lots/serials were not defined. Once applied those can't be changed.<br/>\n"
"                    Apply anyway?"
msgstr ""
"Les produits que vous avez ajoutés sont suivis mais les lots/séries n'ont pas été définis. Une fois appliqués, ils ne peuvent plus être modifiés.<br/>\n"
"Postuler quand même ?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Time Horizon"
msgstr "Horizon temporel"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_1
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid "Tip: Monitor Lot details"
msgstr "Conseil : Suivez les détails des lots"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "Astuce : Accélérez vos opérations d'inventaire grâce aux codes-barres"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "Vers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "À appliquer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "À mettre dans un reliquat"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "À compter"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Deliver"
msgstr "À livrer"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_graph
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "To Do"
msgstr "À faire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Location"
msgstr "À relocaliser"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "À commander"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_computed
msgid "To Order Computed"
msgstr "À commander Calculé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_manual
msgid "To Order Manual"
msgstr "À commander manuellement"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "To Order with Visibility Days"
msgstr "À commander avec des jours de visibilité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Package"
msgstr "À mettre en colis"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "À traiter"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr "À recevoir"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "À réapprovisionner"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__today
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today"
msgstr "Aujourd'hui"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "Activités du jour"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Tomorrow"
msgstr "Demain"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Available"
msgstr "Total disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Demand"
msgstr "Demande totale"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Forecasted"
msgstr "Total prévu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Free to Use"
msgstr "Total virtuellement disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Incoming"
msgstr "Total entrant"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total On Hand"
msgstr "Total en stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Outgoing"
msgstr "Total sortant"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Quantity"
msgstr "Quantité totale"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Reserved"
msgstr "Total réservé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "Total des routes"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"Poids total des colis et des articles non emballés. Les colis sans poids "
"spécifié utiliseront le total du poids des articles. Ce poids est utilisé "
"pour calculer le coût de l'expédition."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "Poids total de tous les produits non emballés."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Poids total du colis."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "Traçabilité"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.js:0
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Traceability Report"
msgstr "Rapport de traçabilité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__is_storable
#: model:ir.model.fields,field_description:stock.field_product_template__is_storable
#: model:ir.model.fields,field_description:stock.field_stock_move__is_storable
msgid "Track Inventory"
msgstr "Suivre l'inventaire"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"Les dates suivantes peuvent être suivies via les numéros de lot et de série : date de péremption, date de retrait, date d'expiration et date d'alerte. \n"
" Ces dates sont définies automatiquement lors de la création du lot/numéro de série en fonction de valeurs spécifiées pour chaque produit (en jours)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"Les dates suivantes peuvent être suivies via les numéros de lot et de "
"série : date de péremption, date de retrait, date d'expiration et date "
"d'alerte. Ces dates sont définies automatiquement lors de la création du "
"lot/numéro de série en fonction de valeurs spécifiées pour chaque produit "
"(en jours)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "Suivre les zones d'emplacement dans votre entrepôt"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr "Tracez vos quantités en stock en créant des produits stockables."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Tracked Products in Inventory Adjustment"
msgstr "Produits suivis dans l'ajustement d'inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "Suivi"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "Suivi de ligne"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "Transfert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "Transférer vers"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_ids
#: model:ir.ui.menu,name:stock.menu_stock_transfers
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "Transferts"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Transfers %s: Please add some items to move."
msgstr "Transferts %s : Ajoutez quelques articles à déplacer"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Les transferts vous permettent de déplacer des produits d'un emplacement à "
"un autre."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "Transferts pour groupes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr ""
"Transferts qui sont en retard par rapport à la date prévue ou pour lesquels "
"un des transferts sera en retard"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "Emplacement de transit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Emplacements de transit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Transport management: organize packs in your fleet, or carriers."
msgstr ""
"Gestion du transport : organiser le transport avec votre parc automobile ou "
"des transporteurs."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger"
msgstr "Déclencheur"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "Déclencher une autre règle"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "Déclencher une autre règle si il n'y a pas de stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger Manual"
msgstr "Déclencheur manuel"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_scrap__should_replenish
msgid "Trigger replenishment for scrapped products"
msgstr "Déclencher un réassort pour les produits mis au rebut"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
msgid "Try to add some incoming or outgoing transfers."
msgstr "Essayez d'ajouter des transferts entrants ou sortants"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
msgid "Type"
msgstr "Type"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Type a message..."
msgstr "Écrivez un message..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "Type d'opération"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "Connecteur UPS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "Connecteur USPS"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Unassign"
msgstr "Désassigner"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"Unavailable Serial numbers. Please correct the serial numbers encoded: "
"%(serial_numbers_to_locations)s"
msgstr ""
"Numéros de série non disponibles. Veuillez corriger les numéros de série "
"encodés : %(serial_numbers_to_locations)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "Unfold"
msgstr "Déplier"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__name
msgid "Unique Lot/Serial Number"
msgstr "Lot/numéro de série unique"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Unit"
msgstr "Unité"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "Prix unitaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__uom
msgid "Unit of Measure Name"
msgstr "Nom de l'unité de mesure"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Units"
msgstr "Unité(s)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "Unités de mesure"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "Unités de mesure"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "Unités de mesure"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unity of measure"
msgstr "Untié de mesure"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Unknown Pack"
msgstr "Colis inconnu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Unless previously specified by the source document, this will be the default"
" picking policy for this operation type."
msgstr ""
"Sauf indication contraire dans le document source, cela sera la politique de"
" prélèvement par défaut pour ce type d'opération."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Défaire le colis"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "Annuler la réservation"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Unreturned"
msgstr "Non retourné"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Unsafe unit of measure"
msgstr "Unité de mesure risquée"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__unwanted_replenish
msgid "Unwanted Replenish"
msgstr "Réassort indésirable"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "UoM"
msgstr "UdM"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "Catégories UdM"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__uom_id
msgid "Uom"
msgstr "UdM"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Mettre à jour la quantité de produits"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Update Quantities"
msgstr "Mettre à jour les quantités"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Quantity"
msgstr "Mettre la quantité à jour"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"Updating the location of this transfer will result in unreservation of the currently assigned items. An attempt to reserve items at the new location will be made and the link with preceding transfers will be discarded.\n"
"\n"
"To avoid this, please discard the source location change before saving."
msgstr ""
"La mise à jour de la localisation de ce transfert entraînera la déréservation des articles actuellement attribués. Une tentative de réservation des articles au nouvel emplacement sera effectuée et le lien avec les transferts précédents sera supprimé..\n"
"\n"
"Pour éviter cela, veuillez annuler le changement d'emplacement d'origine avant de l'enregistrer."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "Urgent"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "Utiliser les lots/numéros de série existants"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Use Existing ones"
msgstr "Utiliser les existants"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Use GS1 nomenclature datamatrix whenever barcodes are printed for lots and "
"serial numbers."
msgstr ""
"Utilisez la matrice de données de la nomenclature GS1 lorsque des codes-"
"barres sont imprimés pour des lots et des numéros de série."

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "Utiliser le rapport de réception"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "Utiliser vos propres routes"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Used by"
msgstr "Utilisé par"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Utilisé pour trier la vue kanban 'Toutes les opérations'"

#. module: stock
#: model:ir.model,name:stock.model_res_users
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "Utilisateur"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "Utilisateur assigné au comptage de produits."

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Valider"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
msgid "Validate Inventory"
msgstr "Valider l'inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "Nombre de variantes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Fournisseur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "Emplacement fournisseur"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Emplacements fournisseur"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "Vue"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "View Availability"
msgstr "Voir la disponibilité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "Voir le diagramme"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "Emplacement vue"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "Voir et allouer les quantités reçues"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__visibility_days
msgid "Visibility Days"
msgstr "Jours de visibilité"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Visibility days"
msgstr "Translation placeholder"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_volume
msgid "Volume for Shipping"
msgstr "Volume pour l'expédition"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/OUT/00001"
msgstr "WH/OUT/00001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "WH/OUT/0001"
msgstr "WH/OUT/0001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Outgoing"
msgstr "WH/Outgoing"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Stock"
msgstr "WH/Stock"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "En attente"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "En attente d'un autre mouvement"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "En attente d'une autre opération"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "En attente de disponibilité"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "Mouvements en attente"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "En attente de transferts"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Entrepôt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Configuration de l'entrepôt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "Domaine de l'entrepôt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Warehouse Location"
msgstr "Emplacement de l'entrepôt"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Gestion de l'entrepôt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "Vue d'entrepôt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Entrepôt à propager"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "Emplacement vue de l'entrepôt"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Warehouse's Routes"
msgstr "Routes de l'entrepôt"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_warehouse_filter.xml:0
msgid "Warehouse:"
msgstr "Entrepôt :"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Warehouses"
msgstr "Entrepôts"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "Prévenir en cas de quantité insuffisante"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "Avertir sur quantité de rebut insuffisante"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
msgid "Warning"
msgstr "Avertissement"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Warning Duplicated SN"
msgstr "Avertissement NS dupliqué"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warning_message
msgid "Warning Message"
msgstr "Message d'avertissement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "Avertissement sur le transfert"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Warning!"
msgstr "Avertissement !"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Warning: change source location"
msgstr "Avertissement : modifier l'emplacement d'origine"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "Avertissements"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "Avertissements pour Inventaire"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__base_weight
msgid "Weight"
msgstr "Poids"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Poids pour l'expédition"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__base_weight
msgid "Weight of the package type"
msgstr "Poids du type de colis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__weight_uom_name
msgid "Weight unit"
msgstr "Unité de poids"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Intitulé de l'unité de mesure de poids "

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Produit pesé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__wh_replenishment_option_ids
msgid "Wh Replenishment Option"
msgstr "Option de réassort de l'entrepôt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr ""
"Lorsqu'un entrepôt est sélectionné pour cet itinéraire, celui-ci doit être "
"considéré comme l'itinéraire par défaut lorsque les produits passent par cet"
" entrepôt."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__one
msgid "When all products are ready"
msgstr "Quand tous les articles sont prêts"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr ""
"Lorsqu'il est coché, l'itinéraire sera sélectionnable dans l'onglet "
"Inventaire du formulaire Produit."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr ""
"Une fois vérifié, l'itinéraire sera sélectionnable dans la catégorie de "
"produit."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr ""
"Une fois cochée, la route sera sélectionnable sur le conditionnement de "
"produit."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "Quand le produit arrive dans"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%(destination)s</b>, <br> "
"<b>%(operation)s</b> are created from <b>%(source_location)s</b> to fulfill "
"the need. %(suffix)s"
msgstr ""
"Lorsque des produits sont nécessaires en <b>%(destination)s</b>, <br> "
"<b>%(operation)s</b> sont créé(e)s depuis <b>%(source_location)s</b> pour "
"répondre au besoin. %(suffix)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products arrive in <b>%(source_location)s</b>, <br> "
"<b>%(operation)s</b> are created to send them to <b>%(destination)s</b>."
msgstr ""
"Lorsque les produits arrivent à <b>%(source_location)s</b>, des <br> "
"<b>%(operation)s</b> sont créé(e)s pour les envoyer à "
"<b>%(destination)s</b>."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__location_dest_from_rule
msgid ""
"When set to True the destination location of the stock.move will be the "
"rule.Otherwise, it takes it from the picking type."
msgstr ""
"Si la valeur définie est vrai, l'emplacement de destination du stock.move "
"deviendra la règle, sinon la règle sera déduite du type de transfert."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""
"Lorsque la préparation n'est pas terminée, cela permet de modifier la "
"demande initiale. Lorsque la préparation est terminée, cela permet de "
"modifier les quantités traitées."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity above of this"
" Min Quantity."
msgstr ""
"Quand le stock virtuel tombe en dessous de la quantité minimale indiquée "
"dans de champ, Odoo génère un approvisionnement pour ramener la quantité "
"prévue à cette quantité minimale."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity up to (or near to) the Max "
"Quantity specified for this field (or to Min Quantity, whichever is bigger)."
msgstr ""
"Quand le stock virtuel tombe en dessous de la quantité minimale, Odoo génère"
" un approvisionnement pour ramener la quantité prévue à la quantité maximale"
" (ou se rapprochant de celle-ci) spécifiée pour ce champ (ou la quantité "
"minimale, selon la valeur la plus élevée)."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propagated."
msgstr "Lorsque coché, le transporteur d'expédition sera propagé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr ""
"Si coché, le prochain mouvement créé par cette règle sera annulé dans le cas"
" où le mouvement source est annulé."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__create_backorder
msgid ""
"When validating a transfer:\n"
" * Ask: users are asked to choose if they want to make a backorder for remaining products\n"
" * Always: a backorder is automatically created for the remaining products\n"
" * Never: remaining products are cancelled"
msgstr ""
"Lors de la validation d'un transfert :\n"
"* Demander : les utilisateurs sont invités à choisir s'ils souhaitent effectuer une commande en attente pour les produits restants\n"
"* Toujours : une commande en attente est automatiquement créée pour les produits restants\n"
"* Jamais : les produits restants sont annulés"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr ""
"À la validation du transfert, les produits seront assignés à ce "
"propriétaire."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr ""
"À la validation du transfert, les produits seront pris de ce propriétaire."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr ""
"Si le mouvement a été ajouté après la confirmation du transfert ou non"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "Largeur"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "La largeur doit être positive"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "Assistant"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Write one lot/serial name per line, followed by the quantity."
msgstr "Écrivez un lot/numéro de série par ligne, suivi de la quantité."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Yesterday"
msgstr "Hier"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"You are about to move quantities in a package without moving the full package.\n"
"                    Those quantities will be removed from the following package(s):"
msgstr ""
"Vous êtes sur le point de déplacer des quantités dans un colis dans déplacer la totalité du colis. \n"
"Ces quantités seront supprimées du (des) colis suivant(s) :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid ""
"You are going to pick products that are not referenced\n"
"in this location. That leads to a negative stock."
msgstr ""
"Vous êtes sur le point de transférer des produits qui ne sont pas référencés\n"
"dans cet emplacement. Cela conduit à un stock négatif."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "Vous êtes bon, pas de réassort à effectuer !"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"Vous n'êtes pas autorisé à modifier le produit lié à un numéro de série ou "
"de lot si des mouvements de stock ont déjà été créés avec ce numéro. Cela "
"créerait des incohérences dans votre stock."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"Vous n'êtes pas autorisé à créer un numéro de lot ou de série avec ce type "
"d'opération. Pour modifier ceci, aller dans les types d'opérations et cochez"
" \"Créer nouveaux lots/numéros de série\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr ""
"Vous essayez de mettre dans le même colis des produits à acheminer vers des "
"emplacements différents"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"Vous utilisez une unité de mesure plus petite que celle que vous utilisez "
"pour stocker votre produit. Ceci peut mener à des problèmes d'arrondissement"
" de la quantité réservée. Vous devriez utiliser l'unité de mesure la plus "
"petite pour évaluer votre stock ou bien modifier le choix d'arrondissement "
"vers une valeur plus petite (exemple : 0.00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"Vous pouvez définir ici les routes principales qui parcourent \n"
"vos entrepôts et qui définissent les flux de vos produits.\n"
"Ces\n"
"routes peuvent être assignées à des produits, des catégories de produits ou être fixées \n"
"dans l'approvisionnement ou les commandes clients."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "Vous pouvez soit :"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that is currently "
"reserved on a stock move. If you need to change the inventory tracking, you "
"should first unreserve the stock move."
msgstr ""
"Vous ne pouvez pas modifier le suivi de l'inventaire d'un produit "
"actuellement réservé pour un mouvement de stock. Si vous devez modifier le "
"suivi de l'inventaire, commencez par annuler la réservation du mouvement de "
"stock."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that was already "
"used."
msgstr ""
"Vous ne pouvez pas modifier le suivi de l'inventaire d'un produit qui a déjà"
" été utilisé."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can not create a snoozed orderpoint that is not manually triggered."
msgstr ""
"Vous ne pouvez pas créer de point de commande reporté qui ne se déclenche "
"pas manuellement."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You can not delete moves linked to another operation"
msgstr ""
"Vous ne pouvez pas supprimer des mouvements liés à une autre opération"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""
"Vous ne pouvez pas supprimer un mouvement de produit si le transfert est "
"terminé. Vous pouvez uniquement corriger les quantités faites."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can not enter negative quantities."
msgstr "Vous ne pouvez pas saisir des quantités négatives."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You can only enter positive quantities."
msgstr "Vous ne pouvez saisir que des quantités positives."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You can only move a lot/serial to a new location if it exists in a single "
"location."
msgstr ""
"Vous ne pouvez déplacer un lot/série vers un nouvel emplacement que s'il "
"existe en un seul endroit."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You can only move positive quantities stored in locations used by a single "
"company per relocation."
msgstr ""
"Vous ne pouvez déplacer que des quantités positives stockées dans des "
"emplacements utilisés par une seule société par relocalisation."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr ""
"Vous pouvez uniquement traiter 1.0 %s des produits avec un numéro de série "
"unique."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can only snooze manual orderpoints. You should rather archive 'auto-"
"trigger' orderpoints if you do not want them to be triggered."
msgstr ""
"Vous pouvez uniquement reporter les points de commande manuels. Archivez les"
" points de commande déclenchés automatiquement si vous ne souhaitez pas "
"qu'ils soient déclenchés."

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You can't deactivate the multi-location if you have more than once warehouse"
" by company"
msgstr ""
"Vous ne pouvez pas désactiver les emplacements multiples si vous avez plus "
"d'un entrepôt par société"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "You can't disable locations %s because they still contain products."
msgstr ""
"Vous ne pouvez pas désactiver les emplacements %s parce qu'ils contiennent "
"encore des produits."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot archive location %(location)s because it is used by warehouse "
"%(warehouse)s"
msgstr ""
"Vous ne pouvez pas archiver l'emplacement %(location)s car il est utilisé "
"par votre entrepôt %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr ""
"Vous ne pouvez pas annuler un transfert marqué comme \"Terminé\". Créez un "
"retour pour inverser les mouvements qui ont été effectués."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr ""
"Vous ne pouvez pas modifier un mouvement de stock annulé, créez plutôt une "
"nouvelle ligne."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr ""
"Vous ne pouvez modifier la date prévue sur un transfert qui est terminé ou "
"annulé."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr ""
"Vous ne pouvez pas changer l'UdM pour un mouvement de stock qui a déjà été "
"terminé."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You cannot change the company of a lot/serial number currently in a location"
" belonging to another company."
msgstr ""
"Vous ne pouvez pas changer la société d'un numéro de lot/série qui se trouve"
" actuellement dans un emplacement appartenant à une autre société."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""
"Vous ne pouvez pas changer le facteur de cette unité de mesure car certains "
"produits dans cette unité de mesure ont déjà été déplacés ou sont "
"actuellement réservés."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"Vous ne pouvez pas modifier l'unité de mesure car il y a déjà des mouvements"
" de stock pour ce produit. Si vous souhaitez modifier l'unité de mesure, "
"vous devez archiver ce produit et en créer un nouveau."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You cannot delete a scrap which is done."
msgstr "Vous ne pouvez pas supprimer une mise au rebut terminée."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot directly pack quantities from different transfers into the same "
"package through this view. Try adding them to a batch picking and pack it "
"there."
msgstr ""
"Cette vue ne permet pas de regrouper directement des quantités provenant de "
"différents transferts dans le même colis. Essayez de les ajouter à un "
"transfert par lot et de les regrouper à ce niveau."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot duplicate stock quants."
msgstr "Vous ne pouvez pas dupliquer de quants."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot modify inventory loss quantity"
msgstr "Vous ne pouvez modifier la quantité de perte d'inventaire"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""
"Vous ne pouvez pas transférer le même contenu du colis plus d'une fois pour "
"un même transfert ou diviser le même colis sur deux emplacements."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they are from different "
"transfers with different operation types."
msgstr ""
"Il n'est pas possible de regrouper des produits dans un même colis s'ils "
"proviennent de différents transferts avec des types d'opération différents."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot perform moves because their unit of measure has a different "
"category from their product unit of measure."
msgstr ""
"Vous ne pouvez pas effectuer de transferts parce que leur unité de mesure "
"appartient à une catégorie différente de celle du produit."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot set a location as a scrap location when it is assigned as a "
"destination location for a manufacturing type operation."
msgstr ""
"Vous ne pouvez pas définir un emplacement comme emplacement de rebut "
"lorsqu'il est assigné comme emplacement de destination pour une opération de"
" type fabrication."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot set a scrap location as the destination location for a "
"manufacturing type operation."
msgstr ""
"Vous ne pouvez pas définir un emplacement de rebut comme emplacement de "
"destination pour une opération de type fabrication."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""
"Impossible de diviser un mouvement en brouillon. Il doit d'abord être "
"confirmé."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr ""
"Vous ne pouvez pas diviser un mouvement de stock qui est défini sur "
"'Terminé' ou 'Annuler'."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr ""
"Vous ne pouvez pas récupérer ou livrer des produits dans un emplacement de "
"type \"vue\" (%s)."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr "Vous ne pouvez pas décommander un mouvement de stock terminé."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""
"Vous ne pouvez pas utiliser le même numéro de série plusieurs fois. Veuillez"
" corriger les numéros de série encodés."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot validate a transfer if no quantities are reserved. To force the "
"transfer, encode quantities."
msgstr ""
"Vous ne pouvez pas valider un transfert si aucune quantité n'est réservée. "
"Pour forcer le transfert, encodez des quantités."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You can’t validate an empty transfer. Please add some products to move "
"before proceeding."
msgstr ""
"Vous ne pouvez pas valider un transfert vide. Veuillez ajouter certains "
"produits à déplacer avant de continuer."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "Vous avez traité moins de produits que la demande initiale. "

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"Vous avez des produits en stock pour lesquels le suivi par lot/numéro de série est activé. \n"
"Désactivez le suivi sur tous les produits avant de désactiver ce paramètre."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"Vous avez des produits en stock qui n'ont pas de lot/numéro de série. Vous "
"pouvez leur en assigner via un ajustement d'inventaire."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr ""
"Vous devez sélectionner une unité de mesure pour ce produit qui soit dans la"
" même catégorie que l'unité de mesure par défaut de ce produit."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return Done pickings."
msgstr "Vous ne pouvez renvoyer que des transferts terminés."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return one picking at a time."
msgstr "Vous ne pouvez renvoyer qu'un seul transfert à la fois."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr ""
"Vous devez activer les emplacements de stockage pour faire des opérations de"
" type interne."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "You need to select a route to replenish your products"
msgstr "Vous devez sélectionner une route pour réapprovisionner vos produits"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You need to supply a Lot/Serial Number for product:\n"
"%(products)s"
msgstr ""
"Vous devez fournir un numéro de lot/série pour le produit :\n"
"%(products)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "Vous devez fournir un lot/numéro de série pour le produit %s."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr "Vous devriez mettre à jour ce document pour refléter vos CGV "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"You still have ongoing operations for operation types %(operations)s in "
"warehouse %(warehouse)s"
msgstr ""
"Vous avez des opérations en cours pour les types de transfert %(operations)s"
" dans l'entrepôt %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""
"Ce produit est encore associé à des règles de réapprovisionnement. Commencez"
" par les archiver ou les supprimer."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid ""
"You tried to create a record that already exists. The existing record was "
"modified instead."
msgstr ""
"Vous avez essayé de créer un enregistrement qui existe déjà. "
"L'enregistrement existant a été modifié à la place."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"Vous trouverez ici des propositions de réassort basées sur vos projections d'inventaire.\n"
"             Choisissez la quantité à acheter ou produire et créez des commandes en un click.\n"
"              Pour gagner du temps à l'avenir, automatisez les règles."

#. module: stock
#: model_terms:res.company,lunch_notify_message:stock.res_company_1
msgid ""
"Your lunch has been delivered.\n"
"Enjoy your meal!"
msgstr ""
"Votre déjeuner a été livré.\n"
"Bon appétit !"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Your stock is currently empty"
msgstr "Votre stock est actuellement vide"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zpl
msgid "ZPL Labels"
msgstr "Étiquettes ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_lots
msgid "ZPL Labels - One per lot/SN"
msgstr "Étiquettes ZPL - Une par lot/NS"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_units
msgid "ZPL Labels - One per unit"
msgstr "Étiquettes ZPL - Une par unité"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zplxprice
msgid "ZPL Labels with price"
msgstr "Étiquettes ZPL avec prix"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>min :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "below the inventory"
msgstr "en-dessous de l'inventaire actuel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "Connecteur Bpost"

#. module: stock
#: model:product.removal,method:stock.removal_closest
msgid "closest"
msgstr "le plus près"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "jours"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "jours avant si prioritaire"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "jours avant/"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "par ex. EC"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "par ex. Entrepôt Central"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "e.g. LOT-PR-00012"
msgstr "par ex. LOT-PR-00012"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "par ex. LOT/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. Lumber Inc"
msgstr "par ex. Lumber Inc"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "par ex. PACK000000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "par ex. PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "Par ex. Emplacements physiques"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "e.g. Receptions"
msgstr "par ex. Réceptions"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "e.g. SN000001"
msgstr "par ex. SN000001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "par ex. Stock de réserve"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "par ex. Réception en 2 étapes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "par ex. d7vctmiv2rwgenebha8bxq7irooudn"

#. module: stock
#: model:product.removal,method:stock.removal_fifo
msgid "fifo"
msgstr "fifo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "depuis l'emplacement"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "in"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "in barcode."
msgstr "dans code-barres."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "is"
msgstr "est"

#. module: stock
#: model:product.removal,method:stock.removal_least_packages
msgid "least_packages"
msgstr "least_packages"

#. module: stock
#: model:product.removal,method:stock.removal_lifo
msgid "lifo"
msgstr "lifo"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "minimum of"
msgstr "minimum de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "de"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "planned on"
msgstr "planifié le"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "traité à la place de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "reserved"
msgstr "réservé"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "should be replenished"
msgstr "doit être réapprovisionné"

#. module: stock
#: model:ir.actions.server,name:stock.click_dashboard_graph
msgid "stock.click_dashboard_graph"
msgstr "stock.click_dashboard_graph"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_incoming
msgid "stock.method_action_picking_tree_incoming"
msgstr "stock.method_action_picking_tree_incoming"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_internal
msgid "stock.method_action_picking_tree_internal"
msgstr "stock.method_action_picking_tree_internal"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_outgoing
msgid "stock.method_action_picking_tree_outgoing"
msgstr "stock.method_action_picking_tree_outgoing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__filter_for_stock_putaway_rule
msgid "stock.putaway.rule"
msgstr "stock.putaway.rule"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "the barcode app"
msgstr "l'application code-barres"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"the warehouse to consider for the route selection on the next procurement "
"(if any)."
msgstr ""
"l'entrepôt à considérer pour la sélection de la route lors du prochain "
"approvisionnement (le cas échéant)."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "to reach the maximum of"
msgstr "Pour atteindre le maximum de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "units"
msgstr "unité(s)"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Bon de livraison (Ref {{ object.name or 'n/a' "
"}})"
