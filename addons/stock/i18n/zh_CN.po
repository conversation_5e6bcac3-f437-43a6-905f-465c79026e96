# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# W<PERSON>, 2024
# <PERSON>, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: 何彬 <<EMAIL>>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"\n"
"\n"
"Transfers %(transfer_list)s: You need to supply a Lot/Serial number for products %(product_list)s."
msgstr ""
"\n"
"\n"
"调拨 %(transfer_list)s：您需要提供产品 %(product_list)s 的批次/序号。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"(%(serial_number)s) exists in location %(location)s"
msgstr ""
"\n"
"（%(serial_number)s）存在于位置 %(location)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"The quantity done for the product %(product)s doesn't respect the rounding precision defined on the unit of measure %(unit)s.\n"
"Please change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"\n"
"产品 %(product)s 的完成数量不符合计量单位 %(unit)s 上定义的舍入精度。\n"
"请更改已完成的数量，或计量单位的舍入精度。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
" * 草稿：调拨尚未确认。 预订不应用。\n"
" * 等待另一个作业:在准备好之前，该调拨正在等待另一个作业。\n"
" * 等待：调拨正在等待某些产品的可用性。\n"
"(a) 调拨政策是“尽快”：没有产品可以预留。\n"
"(b) 调拨政策是“当所有产品准备就绪”时：并非所有产品都可以预留。\n"
" * 就绪：调拨已准备好进行处理。\n"
"(a) 调拨政策是“尽快”：至少预留了一种产品。\n"
"(b) 调拨政策是“当所有产品准备就绪”时：所有产品都已预订。\n"
" * 完成：已处理调拨。\n"
" * 已取消：调拨已取消。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid " - Product: %(product)s, Lot/Serial Number: %(lot)s"
msgstr " - 产品：%(product)s，序列号码：%(lot)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,help:stock.field_stock_quant__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr "当与0不同时，储存在这个位置的产品的库存清点日期将按定义的频率自动设置。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_count
msgid "# Returns"
msgstr "# 退货"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s (copy)(%(id)s)"
msgstr "%(name)s (copy)(%(id)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence cross dock"
msgstr "%(name)s越库序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence in"
msgstr "%(name)s入库序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence internal"
msgstr "%(name)s内部序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence out"
msgstr "%(name)s出库序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence packing"
msgstr "%(name)s包装序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking"
msgstr "%(name)s顺序挑选"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence quality control"
msgstr "%(name)s质控序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence storage"
msgstr "%(name)s存储序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"%(operations)s have default source or destination locations within warehouse"
" %(warehouse)s, therefore you cannot archive it."
msgstr "操作 %(operations)s 在仓库 %(warehouse)s 内设有预设来源位置或目标位置，因此不可封存。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "%(product)s: Insufficient Quantity To Scrap"
msgstr "%(product)s: 无充足的数量用来报废"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"%(product_name)s --> Product UoM is %(product_uom)s "
"(%(product_uom_category)s) - Move UoM is %(move_uom)s "
"(%(move_uom_category)s)"
msgstr ""
"%(product_name)s --> 产品计量单位：%(product_uom)s (%(product_uom_category)s） - "
"移动计量单位： %(move_uom)s（%(move_uom_category)s）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%(warehouse)s Sequence %(code)s"
msgstr "%(warehouse)s序列%(code)s"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid ""
"%(warehouse)s can only provide %(free_qty)s %(uom)s, while the quantity to "
"order is %(qty_to_order)s %(uom)s."
msgstr ""
"%(warehouse)s 只能提供 %(free_qty)s %(uom)s，而订购数量为 %(qty_to_order)s %(uom)s。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s: 供应产品来自 %(supplier)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_rule.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%s (copy)"
msgstr "%s（副本）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "%s [reverted]"
msgstr "%s[恢复]"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "%s days"
msgstr "%s 天"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%s: Can't split: quantities done can't be above demand"
msgstr "%s：不能拆分：已完成的数量不能超过需求量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split, all demand is done. For split you need at least one "
"line not fully fulfilled"
msgstr "%s：无需拆分，所有需求都已满足。若要拆分，至少需要一行未完全满足的需求"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split. Fill the quantities you want in a new transfer in the "
"done quantities"
msgstr "%s：没有要分割的内容。请在已完成的数量中填写新转账中需要的数量"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "'计数表'"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr "'送货单 - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'位置 - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'批次序列 - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'作业-类型 - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_packages
msgid "'Packages - %s' % (object.name)"
msgstr "'包裹 - %s' % (object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr "'拣货作业 - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "(copy of) %s"
msgstr "(副本) %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(document barcode)"
msgstr "（文件条形码）"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(package barcode)"
msgstr "（包裹条形码）"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(product barcode)"
msgstr "（产品条形码）"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(serial barcode)"
msgstr "（系列条形码）"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: The stock move is created but not confirmed.\n"
"* Waiting Another Move: A linked stock move should be done before this one.\n"
"* Waiting Availability: The stock move is confirmed but the product can't be reserved.\n"
"* Available: The product of the stock move is reserved.\n"
"* Done: The product has been transferred and the transfer has been confirmed."
msgstr ""
"* 新建：库存移动已创建但未确认。\n"
"* 等待下一步移动：在此之前应完成关联的库存移动。\n"
"* 等待中：库存移动已确认，但产品无法预留。\n"
"* 可移动：库存移动产品已预留。\n"
"* 完成： 产品已调拨，已完成确认。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move__location_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* 供应商位置：虚拟位置，表示来自供应商的产品的源位置\n"
"* 视图：虚拟位置，用于为仓库创建层次结构，聚集其子位置; 不能直接包含产品\n"
"* 内部位置：您自己仓库内的物理位置，\n"
"* 客户位置：虚拟位置，表示发送给客户的产品的目标位置\n"
"* 库存损失：虚拟位置作为库存作业的对应物，用于纠正库存水平（物理库存）\n"
"* 生产：生产作业的虚拟对应位置：此位置消耗组件并生产成品\n"
"* 中转站：应在公司间或仓库间运营中使用的对应地点"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d天(s)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", 最大:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"           可能需要手动动作。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 天"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 个月"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 周"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "12.0"
msgstr "12.0"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
msgid "1234567890"
msgstr "1234567890"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "12345678901"
msgstr "12345678901"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 含价格"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "2021-9-01"
msgstr "2021 年 9 月 1 日"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "2023-01-01"
msgstr "2023-01-01"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "2023-09-24"
msgstr "2023 年 9 月 24 日"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "3.00"
msgstr "3.00"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__4x12
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_lots
msgid "4 x 12 - One per lot/SN"
msgstr "4 x 12 - 每个批次/SN 一个"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_units
msgid "4 x 12 - One per unit"
msgstr "4 x 12 - 每个单元一个"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 含价格"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 含价格"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "54326786758"
msgstr "54326786758"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>当前库存: </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"<br/>\n"
"                Want to speed up operations?"
msgstr ""
"<br/>\n"
"                想要加快运行速度？"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr "<br>需求产生于 <b>%s</b> 为满足这个需求，规则将会被触发。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring the missing quantity in this location."
msgstr "<br>如果<b>%s</b>里的产品不可用，规则将被触发来把缺失的数量带入此位置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>The products will be moved towards <b>%(destination)s</b>, <br/> as "
"specified from <b>%(operation)s</b> destination."
msgstr ""
"<br>这些产品将会移至 <b>%(destination)s</b>，<br/>即 <b>%(operation)s</b>操作指定的目的地。"

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>您好，<br/><br/>\n"
"        我们很高兴地通知您，您的订单已发货。\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            您的追踪参考号码是\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"         详情请见随附的送货单。<br/><br/>\n"
"        谢谢，\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                所有产品都无法预留。 单击“检查可用性”按钮以尝试预留产品。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Back Orders</span>"
msgstr "<span class=\"col-6\">缺货订单</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">迟到</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Operations</span>"
msgstr "<span class=\"col-6\">操作</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr "<span class=\"col-6\">等待中</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Scannable Package Contents</span>"
msgstr "<span class=\"o_form_label\">可扫描包装内容</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Allocation</span>"
msgstr "<span class=\"o_stat_text\">分配</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">预测</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">In:</span>"
msgstr "<span class=\"o_stat_text\">入：</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Location</span>"
msgstr "<span class=\"o_stat_text\">位置</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "<span class=\"o_stat_text\">Lot/Serial Numbers</span>"
msgstr "<span class=\"o_stat_text\">批次/序列号</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Max:</span>"
msgstr "<span class=\"o_stat_text\">最大值：</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Min:</span>"
msgstr "<span class=\"o_stat_text\">最小值：</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Moves</span>"
msgstr "<span class=\"o_stat_text\">移动</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Next Transfer</span>"
msgstr "<span class=\"o_stat_text\">下一次调拨</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">在手</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">作业</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Out:</span>"
msgstr "<span class=\"o_stat_text\">出：</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">产品移动</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Putaway Rules</span>"
msgstr "<span class=\"o_stat_text\">上架规则</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "<span class=\"o_stat_text\">Routes</span>"
msgstr "<span class=\"o_stat_text\">路线</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Storage Capacities</span>"
msgstr "<span class=\"o_stat_text\">存储容量</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">追踪</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>交货地址 :</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "<span>OBTRETU</span>"
msgstr "<span>OBTRETU</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "<span>On Hand: </span>"
msgstr "<span>随手可得： </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>包裹类型: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>未分配包裹的产品</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>剩余的数量尚未交付。</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "<span>days</span>"
msgstr "<span> 天</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                已完成的调拨行被纠正。\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Customer Address</strong>"
msgstr "<strong>客户地址</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Delivery address</strong>"
msgstr "<strong>送货地址</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr "<strong>由于在您最初更新数量和现在之间做了一些库存调拨，数量的差异不再是一致的。</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>来自</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>批次/序列号码</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty:</strong>"
msgstr "<strong>最大数量：</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty:</strong>"
msgstr "<strong>最小数量：</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order</strong>"
msgstr "<strong>订单</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Pack Date:</strong>"
msgstr "<strong>包装日期:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>包裹类型:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>产品条码</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>产品</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>数量</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Recipient address</strong>"
msgstr "<strong>收件人地址</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>安排的日期 </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date</strong>"
msgstr "<strong>发货日期</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>签名</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status</strong>"
msgstr "<strong>状态</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>初始需求已被更新。</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>目的</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>追踪的产品(s)。</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Vendor Address</strong>"
msgstr "<strong>供应商地址</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse Address</strong>"
msgstr "<strong>仓库地址</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse address</strong>"
msgstr "<strong>仓库地址</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products?</strong>"
msgstr "<strong>您希望将产品送往何处？</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "？这可能会导致库存不一致。"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type!"
msgstr "一个条形码只能分配给一种包裹类型！"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "A replenishment rule already exists for this product on this location."
msgstr "此位置上已存在本产品的补货规则。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__is_storable
#: model:ir.model.fields,help:stock.field_product_template__is_storable
#: model:ir.model.fields,help:stock.field_stock_move__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr "可储存产品是您管理库存的产品。"

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "警告消息可以针对合作伙伴设置(库存)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "操作"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "所需操作"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__replenish_location
msgid ""
"Activate this function to get all quantities to replenish at this particular"
" location"
msgstr "激活此功能，可以获取在该位置补充的所有数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_route__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "有效"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
msgid "Activities"
msgstr "活动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常标示"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_view_activity
msgid "Activity view"
msgstr "活动视图"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "添加产品"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "增加批号/序列号"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "增加新位置"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "增加新路线"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "增加一个新的存储类别"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr "添加将在“拣货作业”工作表上打印的内部注释"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"添加并自定义路线作业以处理您库存中的产品调拨：例如：卸货>品控>进货，拣货>包装>发运。\n"
"您也可以在仓位置置设置上架策略，以便直接将进货产品发送到特定的子位置（例如：特定的货位、货架）。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"添加并自定义路线作业以处理您库存中的产品调拨：例如：卸货>品控>进货，拣货>包装>发运。您也可以在仓位置置设置上架策略，以便直接将进货产品发送到特定的子位置（例如：特定的货位、货架）。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/fields/stock_move_line_x2_many_field.js:0
msgid "Add line: %s"
msgstr "添加行：%s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "为您的调拨作业增加质量检查"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "额外的信息"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "其它信息"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__address
#: model:ir.model.fields,field_description:stock.field_stock_picking__warehouse_address_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Address"
msgstr "地址"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "货物的运送地址。可选填。"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_adjustments
msgid "Adjustments"
msgstr "调整"

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "管理员"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "高级调度"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "高级：应用补货规则"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__after
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "After"
msgstr "之后"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "全部"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Categories"
msgstr "所有类别"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "All Companies"
msgstr "所有公司"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Products"
msgstr "所有产品"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "所有调拨"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
msgid "All Warehouses"
msgstr "所有仓库"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "一次性全部"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr "我们所有的合作关系都将受国家法律管辖."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "全部退回移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "允许新产品"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "允许混合产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "允许的位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__allowed_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__allowed_route_ids
msgid "Allowed Route"
msgstr "允许的路线"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__always
msgid "Always"
msgstr "总是"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Andrwep"
msgstr "Andrwep"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "年度盘点日和月"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "年度库存月"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr "对于不在有周期性库存日期的地点的产品的年度库存月。如果没有自动年度库存，则设置为无月。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"Another parent/sub replenish location %s exists, if you wish to change it, "
"uncheck it first"
msgstr "另一个父/子补充位置 %s 存在，如果您希望更改它，请先取消选中"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Any Category"
msgstr "任意类别"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "适用性"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "可应用于"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "可应用于包装"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_selectable
msgid "Applicable on Product"
msgstr "可应用于产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "可应用于产品类别"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "可应用于仓库"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Apply"
msgstr "应用"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply All"
msgstr "应用全部"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_id
#: model:ir.model.fields,help:stock.field_stock_replenish_mixin__route_id
msgid ""
"Apply specific route for the replenishment instead of product's default "
"routes."
msgstr "使用特定路线进行补货，而非使用产品默认路线。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "四月"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "已归档"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Are you sure you want to cancel this transfer?"
msgstr "你真的想取消此库存调拨吗？"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__direct
msgid "As soon as possible"
msgstr "尽快"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__ask
msgid "Ask"
msgstr "提问"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Assign"
msgstr "分配"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Assign All"
msgstr "全部指定"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "指定所有者"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "指派的移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "分派给"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "确认时"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "At Customer"
msgstr "给客户"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "属性"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "八月"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "自动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_delivery_slip
msgid "Auto Print Delivery Slip"
msgstr "自动打印送货单"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_lot_labels
msgid "Auto Print Lot/SN Labels"
msgstr "自动打印批次/SN 标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_package_label
msgid "Auto Print Package Label"
msgstr "自动打印包装标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_packages
msgid "Auto Print Packages"
msgstr "自动打印套餐"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_product_labels
msgid "Auto Print Product Labels"
msgstr "自动打印产品标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report
msgid "Auto Print Reception Report"
msgstr "自动打印接收报表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid "Auto Print Reception Report Labels"
msgstr "自动打印接收报表标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_return_slip
msgid "Auto Print Return Slip"
msgstr "自动打印回单"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate"
msgstr "自动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "自动移动"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "自动并不增加步骤"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Available"
msgstr "可用"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "可用的产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "可用数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Available quantity should be set to zero before changing inventory tracking"
msgstr "更改库存追踪之前，可用数量应设置为零"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "欠单"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
msgid "Back Orders"
msgstr "欠单"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "欠单确认"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "欠单确认行"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "欠单确认明细行"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "欠单创建"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "欠单"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "条码"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Barcode Demo"
msgstr "条形码演示"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "条码命名规则"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "条码规则"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "条码扫描器"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__valid_ean
msgid "Barcode is valid EAN"
msgstr "条码是有效 EAN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch, Wave & Cluster Transfers"
msgstr "批量、波次和集群调拨"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__before
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Before"
msgstr "之前"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "在安排日期之前"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr "以下文字仅供参考，Odoo S.A.不承担责任。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "分组消息"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Blocking: %s"
msgstr "阻止：%s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "散装内容"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "散货重量"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "按批次"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "By Quantity"
msgstr "按数量"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "按唯一序列号"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"默认情况下，系统从源位置的库存中取货，并被动等待产品入库。另外一个可能是允许您在源位置上直接创建一个补货单（从而忽略其当前库存）来获取产品。如果我们想把库存调拨连接起来，并让库存调拨等待上一次调拨，您应该选择第二种方法。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr "通过取消勾选有效字段，您能隐藏一个位置而不是删除它。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "COPY"
msgstr "复制"

#. module: stock
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "理线盒"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "日历视图"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any customer or supplier location."
msgstr "找不到任何客户或供应商位置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any generic route %s."
msgstr "找不到通用路线 %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "取消"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "取消下一步移动"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot move an empty package"
msgstr "不可移动空的包装"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot remove the location of a non empty package"
msgstr "无法移除非空包裹的位置。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "能力"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "按包裹的能力"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "按产品分类的能力"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.product_search_form_view_stock_report
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "类别"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "类别路线"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"某些国家根据其国内立法，对结算单金额实行源头预扣。任何来源的预扣款将由客户支付给税务机关。在任何情况下，我公司（芝加哥）都不能涉及与一个国家的立法有关的费用。因此，结算单金额将全部由我公司（芝加哥）承担，不包括与客户所在国家的立法有关的任何费用。"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "更改产品数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Changing the Lot/Serial number for move lines with different products is not"
" allowed."
msgstr "不同产品之间不可以更改批次/序号"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr "此时禁止更改此记录的公司，您应该将其归档并创建一个新记录。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Changing the operation type of this record is forbidden at this point."
msgstr "此时禁止更改此记录的作业类型。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "只允许在“草稿”状态下更改产品。"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__barcode_separator
msgid ""
"Character(s) used to separate data contained within an aggregate barcode "
"(i.e. a barcode containing multiple barcode encodings)"
msgstr "用于分隔集合条形码（即包含多种条形码编码的条形码）中所含数据的字符"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "检查可用量"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr "检查调拨行上的目的地包裹的存在"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "检查拣货时是否需要进行包装作业"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid ""
"Check this box if you want to generate shipping label in this operation."
msgstr "若想在此操作中生成装运标签，请选中此复选框。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr "勾取此选项以允许此位置放置已报废/已毁坏的物资。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "选择标签布局"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose Type of Labels To Print"
msgstr "请选择要打印的标签类型"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "选择一个日期以获得该日期的库存"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose destination location"
msgstr "选择目的位置"

#. module: stock
#: model:ir.model,name:stock.model_lot_label_layout
msgid "Choose the sheet layout to print lot labels"
msgstr "请选择纸张布局格式用于打印批次标签"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "选择表格布局来打印标签"

#. module: stock
#: model:ir.model,name:stock.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "清选择【打印产品标签】/【批次标签】"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "选择您的日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "清除"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Close"
msgstr "关闭"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__closest_location
#: model:product.removal,name:stock.removal_closest
msgid "Closest Location"
msgstr "最近位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__color
msgid "Color"
msgstr "颜色"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "公司"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Company"
msgstr "公司"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "用来计算运输成本"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "计算运输成本并用DHL装运"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "计算运输成本并用Easypost装运"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "计算运输成本并用FedEX装运"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "计算运费并使用 Sendcloud 发货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "使用 Shiprocket 计算运输成本并发货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "计算运输成本，并通过 Starshipit 送货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "计算运输成本并用UPS装运"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "计算运输成本并用USPS装运"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "计算运输成本并用bpost装运"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid "Computes when a move should be reserved"
msgstr "判断何时产品移动会被预留"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "配置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Confirm"
msgstr "确认"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "已确认"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "库存的冲突"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Conflict in Inventory Adjustment"
msgstr "库存调整中的冲突"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Conflicts"
msgstr "冲突"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__visibility_days
msgid ""
"Consider product forecast these many days in the future upon product replenishment, set to 0 for just-in-time.\n"
"The value depends on the type of the route (Buy or Manufacture)"
msgstr "判断产品补货后未来几天的数量预测，数值为0表示即时生效。该值取决于路线类型（购买或制造）。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "寄售"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "消耗行"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "联系人"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "包含"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "内容"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Contents"
msgstr "内容"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "继续"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
msgid "Control panel buttons"
msgstr "控制面板按钮"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "仅同类别的计量单位可以互相转换。根据比例进行转换。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "通道(X)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_move_ready
msgid "Count Move Ready"
msgstr "就绪移动计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "拣货计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "拣货欠单计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "草稿拣货计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "迟到拣货计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "就绪拣货计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "等待拣货计数"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "计数表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "盘点数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "对应位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "创建欠单"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Create Backorder?"
msgstr "创建欠单？"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Create New"
msgstr "创建新的"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "创建新批次/序列号"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Create Stock"
msgstr "创建库存"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                            products later. Do not create a backorder if you will not\n"
"                            process the remaining products."
msgstr ""
"如果您希望稍后再处理剩余的\n"
"                            产品。如果您不会\n"
"                            处理剩余的产品。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "创建新作业类型"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "创建新包裹"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "为您的质量检查创建可定制的工作表"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr "创建新的上架规则，以便在收到货后自动将特定产品发送到其适当的目的地。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create products easily by scanning using"
msgstr "轻松扫描资料创建产品，只需使用："

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "Create some storable products to see their stock info in this view."
msgstr "在此视图可查看产品类型为【可库存】的产品的库存信息。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "创建人"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "创建日期"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr "创建新仓库将自动激活库存位置设置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "创建时间"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "创建日期，通常是订单的时间"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Creation date"
msgstr "创建日期"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__date
msgid ""
"Creation date of this move line until updated due to: quantity being "
"increased, 'picked' status has updated, or move line is done."
msgstr "此移动行的创建日期，直到由于以下原因而更新：数量增加、“已拣货” 状态已更新或移动行完成。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross Dock"
msgstr "越库"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__xdock_type_id
msgid "Cross Dock Type"
msgstr "越库类型"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross-Dock"
msgstr "越库"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "越库路线"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "当前库存"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"当前产品数量。 \n"
"对单一库存位置来说，包括了此位置或其任何子位置所存储的产品。 \n"
"对单一库存来说，包括了此仓位置置或其任何子位置所存储的产品。  \n"
"另外，这包括了所有'内部'类型的任何库存位置所存储的产品。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "自定义"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "客户"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "客户前置时间"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "客户位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "客户位置"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot_report
msgid "Customer Lot Report"
msgstr "客户批次报表"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lot_report
msgid "Customer lots"
msgstr "客户批次"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Customizable Desk"
msgstr "可定制化桌子"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Counting"
msgstr "循环计数"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_DATE"
msgstr "演示日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_ORIGIN_DISPLAY_NAME"
msgstr "演示源显示名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PARTNER_NAME"
msgstr "演示伙伴名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PRODUCT_DISPLAY_NAME"
msgstr "产品展示名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_QUANTITY"
msgstr "DEMO_QUANTITY"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_SOURCE_DISPLAY_NAME"
msgstr "演示源显示名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_UOM"
msgstr "DEMO_UOM"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL 快递连接器"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "日期"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__search_date_category
msgid "Date Category"
msgstr "日期类别"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "日期处理"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "安排的日期"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "补货需要完成的日期。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "调拨动作确认或者取消的日期。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr "基于周期性时间表的下一次计划库存日期。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "调拨日期"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "在这个地点的最后一次清点日期。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "预约日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "年度库存清点应在哪一天和哪一月进行。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "日期"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"年度盘点应该发生在哪个月的哪一天。如果是零或负数，那么将选择该月的第一天来代替。\n"
"        如果大于一个月的最后一天，那么将选择该月的最后一天来代替。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Day(s)"
msgstr "天"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "天"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Days To Order"
msgstr "订购天数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "担任主演的日子"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "截止日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "超过或/和按计划的截止日期"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Deadline updated due to delay on %s"
msgstr "截止日期%s因延迟而更新"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "十二月"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Barcode Name"
msgstr "默认条形码名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Name"
msgstr "默认名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default OBTRETU Barcode"
msgstr "默认 OBTRETU 条形码"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Return Name"
msgstr "默认返回名称"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "默认入向路线"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "默认出向路线"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr "所有库存作业的默认单位。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "默认：从库存获取"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "通过此仓库的默认路线"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo automatically creates requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr "设置一个最小库存规则，odoo会依据规则自动创建报价单或确认生产单，以满足最小库存量。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "定义新仓库"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"自定义位置来反映库存的结构\n"
"和组织。系统能管理实体位置\n"
"（库存、货架、储位等等），\n"
"业务伙伴位置（客户、\n"
"供应商）和虚拟位置，即是对方的库存作业，如：制造订单\n"
"的消耗、盘点等。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"定义默认的方法，用于建议产品的确切位置（货架），该位置的批次等。这个方法可以在产品类别层面上强制执行，如果这里没有设置，则在父级位置上进行回退。\n"
"\n"
"先进先出：先入库的产品/批次将先被移出。\n"
"后进先出：最后进货的产品/批号将被先移出。\n"
"壁橱位置：最接近目标位置的产品/批次将被首先移出。\n"
"FEFO：最接近移出日期的产品/批次将被首先移出（这种方法的可用性取决于 \"到期日 \"的设置）。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "延迟警报日期"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Delay on %s"
msgstr "延迟 %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver (1 step)"
msgstr "送货（1步）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 1 step (ship)"
msgstr "1步出货（发货）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 2 steps (pick + ship)"
msgstr "2步出货（拣货+发货）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "3步出货（拣货+包装+发货）"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Delivered"
msgstr "已发货"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Delivered Qty"
msgstr "已交货数量"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_outgoing
#: model:ir.ui.menu,name:stock.out_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deliveries"
msgstr "交货"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
msgid "Delivery"
msgstr "交货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "送货地址"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__delivery_date
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery Date"
msgstr "交付日期"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "交货方式"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_out
msgid "Delivery Orders"
msgstr "交货单"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "交货路线"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Delivery Slip"
msgstr "交货单"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "交货类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery date"
msgstr "送货日期"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr "交货提前期， 依日计算。此为承诺给客户的交期， 即从下单到交货所需要的天数。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_count
msgid "Delivery order count"
msgstr "交货单数"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Delivery orders of %s"
msgstr "%s 的交货订单"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "需求"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Demo Address and Name"
msgstr "演示地址和名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Demo Display Name"
msgstr "演示显示名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Lot/SN"
msgstr "演示批次/序号"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Product"
msgstr "演示产品"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr "根据所安装的模块，这将允许您定义产品在这个包装中的路线：是否会被购买、制造、按订单补充，等等。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr "根据所安装的模块，这将允许您定义路线产品:是否会购买，制造，补充订单，等。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__note
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "描述"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "出库单的说明"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "内部调拨的说明"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "收货说明"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "拣货说明"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "出库单说明"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "拣货说明"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "收货说明"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Description on transfer"
msgstr "调拨说明"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "拣货说明"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_location_id
msgid "Dest Location"
msgstr "目的库位"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id
msgid "Dest Package"
msgstr "目的包装"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id_domain
msgid "Dest Package Id Domain"
msgstr "Dest 包装 ID 域名"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "目的地地址 "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Destination Location"
msgstr "目的位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_usage
msgid "Destination Location Type"
msgstr "目的位置类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "目的位置:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "目的移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "目的地包裹"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package:"
msgstr "目的地包裹："

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "目的位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_from_rule
msgid "Destination location origin from rule"
msgstr "使用规则的目标位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "目的路线"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Detailed Operations"
msgstr "作业详情"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "详情可见"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "差异"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "丢弃"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "取消并手动解决冲突"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_fleet
msgid "Dispatch Management System"
msgstr "配送管理系统"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "显示分配序列号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_complete
msgid "Display Complete"
msgstr "显示完整"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_import_lot
msgid "Display Import Lot"
msgstr "显示导入批次"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "在送货单上显示批次 / 序列号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__display_name
#: model:ir.model.fields,field_description:stock.field_picking_label_type__display_name
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Display Name"
msgstr "显示名称"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "用于在出库单上显示的批次 / 序列号"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "显示包裹内容"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "一次性包装盒"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "您确定要报废吗"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Documentation"
msgstr "文档"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Done"
msgstr "已完成"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
msgid "Done By"
msgstr "完成人员"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_quantity
msgid "Done Packaging Quantity"
msgstr "已完成 包装数量"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "草稿"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "草稿移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "代发货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Due to receipts scheduled in the future, you might end up with "
"excessive stock . Check the Forecasted Report  before reordering"
msgstr "由于收货安排在未来，您可能会出现库存过多的情况。重新订购前查看预测报告"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "Duplicated SN Warning"
msgstr "重复 SN 警告"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__sn_duplicated
msgid "Duplicated Serial Number"
msgstr "重复的序列号码"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost 连接器"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Edit Product"
msgstr "编辑产品"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr "禁止在存货调整地点编辑计数，其修正计数时，地点作为对照。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "实际日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "邮件确认"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "邮件确认拣货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "确认拣货邮件模版"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr "订单完成后，将Email发送给客户。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Empty Locations"
msgstr "空的位置"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"享受Odoo条码应用带来的快节奏体验。它的速度快得惊人，即使没有稳定的网络连接也能工作。它支持所有流程：盘点、批量拣货、移动批次或托盘、低库存检查等。进入"
" \"应用 \"菜单，激活条码界面。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "确保可追溯到仓库中的产品。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"系统中每一个库存作业都是将产品从一个\n"
"位置移至另一位置。例如，若果您从供应商那边收到产品，\n"
"Odoo将会把产品从供应商位置调拨到仓位置置。每个报告可用在\n"
"实体位置、业务伙伴位置或者虚拟位置进行。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "拣货作业出现异常"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "异常:"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Exp"
msgstr "预计"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Exp %s"
msgstr "预计%s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "预计"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "预计交付时间"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "到期日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "外部备注..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_favorite
msgid "Favorite"
msgstr "收藏夹"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__favorite_user_ids
msgid "Favorite User"
msgstr "喜好的用户"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Favorites"
msgstr "收藏"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "二月"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx 连接器"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "筛选的位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "筛选"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_final_id
msgid "Final Location"
msgstr "最终位置"

#. module: stock
#: model:product.removal,name:stock.removal_fifo
msgid "First In First Out (FIFO)"
msgstr "先进先出(FIFO)"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Lot Number"
msgstr "首个批次号码"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN/Lot"
msgstr "首个序号/批次号码"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Serial Number"
msgstr "首个序号"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "固定的"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "固定的补货组"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome图标，例如：fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "强制下架策略"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Forecast"
msgstr "预测"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "预测可用性"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Forecast Description"
msgstr "预测说明"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Forecast Report"
msgstr "预估报表"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"预测数量(计算为手上数量 - 出货 + 进货) \n"
"对于单一库存位置来说，这包括了存储在此位置及其子位置的货物。\n"
"对于单一库存来说，这包括了存储在此库存的库存位置及其子位置的货物。\n"
"否则，这包括存储在任何“内部”类型的任何库存位置的货物。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"预测数量(以一手数量计算-预留数量)\n"
"在具有单一库存位置的上下文中，这包括存储在此位置的货物，或其任何子位置。\n"
"在具有单个仓库的上下文中，这包括存储在该仓库的库存位置的货物，或者它的任何子仓库。\n"
"否则，这包括存储在‘内部’类型的任何库存位置的货物。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
msgid "Forecasted"
msgstr "预测"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date"
msgstr "预测的日期"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date + Visibility Days"
msgstr "预测日期 + 可见天数"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
msgid "Forecasted Deliveries"
msgstr "预测的交货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "预测的预计日期"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted Inventory"
msgstr "预测库存"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecasted_quantity
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
msgid "Forecasted Quantity"
msgstr "预测数量"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
msgid "Forecasted Receipts"
msgstr "预测的收货"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_forecasted_product_product_action
#: model:ir.actions.client,name:stock.stock_forecasted_product_template_action
msgid "Forecasted Report"
msgstr "预测报告"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
msgid "Forecasted Stock"
msgstr "预测库存"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "预测的重量"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted with Pending"
msgstr "包含待定的预测"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__print_format
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "格式"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__free_qty
msgid "Free Qty"
msgstr "可用数量"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock"
msgstr "可用库存"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock in Transit"
msgstr "中转可用库存"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "可用数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Free to Use"
msgstr "可用"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "来自"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "来自所有者"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "完整的位置名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "未来活动"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Deliveries"
msgstr "未来交货"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future P&L"
msgstr "未来的损益"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Productions"
msgstr "未来生产"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Receipts"
msgstr "未来收货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "General"
msgstr "常规"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate"
msgstr "生成"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Lot numbers"
msgstr "生成批次号码"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Serial numbers"
msgstr "生成序列号码"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate Serials/Lots"
msgstr "生成序列号/批次号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Generate Shipping Labels"
msgstr "生成运输标签"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr "从供应商到客户获得全面的追溯性"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "基于合作伙伴获取信息或者阻塞警告"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr "给予更专业的类别，更高的优先级让它们显示在列表的顶部。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "给出显示仓库时这一行的顺序。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Go to Warehouses"
msgstr "前往仓库"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "分组方式"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "分组…"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "HTML reports cannot be auto-printed, skipping report: %s"
msgstr "无法自动打印 HTML 报告，跳过报告：%s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Hardware"
msgstr "硬件"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "有消息"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "有包裹作业"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "有包裹"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__has_return
msgid "Has Return"
msgstr "有退货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "有报废移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "有追溯"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "有变体"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "拥有类别"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "高度"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "高度(Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "高度必须是正数"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "下一次调度前隐藏。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__hide_reservation_method
msgid "Hide Reservation Method"
msgstr "隐藏保留方法"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "History"
msgstr "历史"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
msgid "Horizon"
msgstr "范围"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr "该作业类型的调拨中的产品应如何预留。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__id
#: model:ir.model.fields,field_description:stock.field_picking_label_type__id
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_route__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr "如果在到期付款日期后超过六十（60）天仍未完成款，我公司（芝加哥）预留要求债务追讨公司提供服务的权利。所有法律费用将由客户支付。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"If a separator is defined, a QR code containing all serial numbers contained"
" in package will be generated, using the defined character(s) to separate "
"each numbers"
msgstr "如果定义了分隔符，则将生成包含软件包中所有序列号的二维码，并使用定义的字符分隔每个号码"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "如果所有产品都是一样的"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr "如果勾选此项，在取消本移动时也取消关联的移动"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "如果设置，将对动作进行组合"

#. module: stock
#: model:ir.model.fields,help:stock.field_lot_label_layout__label_quantity
msgid ""
"If the UoM of a lot is not 'units', the lot will be considered as a unit and"
" only one label will be printed for this lot."
msgstr "如果批次的计量单位不是系统设定单位，则该批次将被视为一个单位，并且该批次只打印一个标签。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr "如果有效字段被设置为否，系统将会允许您隐藏订货点而不是删除。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr "如果有效字段被设置为否，系统将会允许您隐藏路线而不是删除。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "如果该位置是空的"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__sn_duplicated
msgid "If the same SN is in another Quant"
msgstr "如果相同的序列号已被记录于系统库存中"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_delivery_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the delivery slip "
"of a picking when it is validated."
msgstr "如果勾选该复选框，Odoo 将在拣货验证后自动打印交货单。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_lot_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN labels "
"of a picking when it is validated."
msgstr "如果勾选该复选框，Odoo 将在验证拣货时自动打印拣货的批次/SN 标签。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_package_label
msgid ""
"If this checkbox is ticked, Odoo will automatically print the package label "
"when \"Put in Pack\" button is used."
msgstr "如果勾选该复选框，Odoo 将在使用 \"放入包装 \"按钮时自动打印包装标签。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_packages
msgid ""
"If this checkbox is ticked, Odoo will automatically print the packages and "
"their contents of a picking when it is validated."
msgstr "如果勾选该复选框，Odoo 将在采样验证时自动打印采样包及其内容。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a picking when it is validated."
msgstr "如果勾选该复选框，Odoo 将在采摘验证时自动打印产品标签。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report labels of a picking when it is validated."
msgstr "如果勾选该复选框，Odoo 将在采摘验证时自动打印采摘的接收报告标签。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report of a picking when it is validated and has assigned moves."
msgstr "如果勾选该复选框，Odoo 将在采摘验证并分配移动后自动打印采摘的接收报告。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_return_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the return slip of"
" a picking when it is validated."
msgstr "如果勾选该复选框，Odoo 将在验证拣货时自动打印退货单。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_show_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically show the reception "
"report (if there are moves to allocate to) when validating."
msgstr "如果勾选此复选框，Odoo将在验证时自动显示接收报表（如果有要分配的库存移动）。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr "如果勾选此框，则拣货线将表示详细的库存作业。否则，拣选线将代表详细的库存作业的总和。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr "如果仅选中此项，则会假设您要创建新的批次/序列号，以便您可以在文本字段中提供它们。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr "如果选中此项，您将可以选择批次/序列号。 您也可以决定不要在这种作业类型中投入很多。 这意味着它将创造出没有多少的库存，或者不会限制措施。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__return_id
msgid ""
"If this picking was created as a return of another picking, this field links"
" to the original picking."
msgstr "如果此拣货单因另一个拣货单退货而创建，该字段将链接到原始拣货单。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr "如果这个运输被拆分，该字段连接到包括了已经处理的部分的运输。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr "如果勾选此项，你将被允许可以选择整个包装来进行移动"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr "如果不勾选，允许您隐藏规则而无需删除。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
msgid "Immediate Transfer"
msgstr "立即调拨"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Lots"
msgstr "导入批次"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Serials"
msgstr "导入序列号"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Import Serials/Lots"
msgstr "导入序列号/批次"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Import Template for Inventory Adjustments"
msgstr "库存调整导入模版"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "In Stock"
msgstr "有现货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "入库类型"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid ""
"In case of outgoing flow, validate the transfer before this date to allow to deliver at promised date to the customer.\n"
"        In case of incoming flow, validate the transfer before this date in order to have these products in stock at the date promised by the supplier"
msgstr ""
"在出货情况下，请在该日期前验证转账，以确保能按承诺的日期向客户交货。\n"
"        在进货情况下，请在该日期前验证转账，以确保能在供应商承诺的日期前将这些产品存入库存。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "In internal locations"
msgstr "在内部位置"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr "为使其可被受理，我公司（芝加哥）必须在交付货物或提供服务后8天内通过记录递送的方式将任何索赔通知到其注册办公室。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "进货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "进货日期"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Incoming Draft Transfer"
msgstr "进货的草稿调拨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "进货移动行"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "入向运输"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Incorrect type of action submitted as a report, skipping action"
msgstr "作为报告提交的操作类型不正确，跳过操作"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr "表示产品的理论计数和其计算计数之间的差距。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "初始需求"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Input"
msgstr "进货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "进货位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Install"
msgstr "安装"

#. module: stock
#: model:ir.actions.server,name:stock.action_install_barcode
msgid "Install Barcode"
msgstr "安装条码"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_company.py:0
msgid "Inter-warehouse transit"
msgstr "仓库间中转"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
msgid "Intermediate Location"
msgstr "中间位置"

#. module: stock
#: model:ir.ui.menu,name:stock.int_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Internal"
msgstr "内部"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "内部位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "内部位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__ref
msgid "Internal Reference"
msgstr "内部参考号"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "内部调拨"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_internal
#: model:stock.picking.type,name:stock.picking_type_internal
msgid "Internal Transfers"
msgstr "内部调拨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "内部中转位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "内部类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations among descendants"
msgstr "下级的内部位置"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "Internal locations having stock can't be converted"
msgstr "有库存的内部位置无法转移"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr "内部参考号码，它不同于制造商的批次/序列号码"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Invalid domain left operand %s"
msgstr "非法的域左作业符 %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain operator %s"
msgstr "非法的域操作符 %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr "无效位域右移运算 '%s'。必须为整数或浮点数"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr "无效规则配置，以下规则导致无限循环：%s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "盘点的数量"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "库存"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory Adjustment"
msgstr "库存调整"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "库存调整参考/原因"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "库存调整警告"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Inventory Adjustments"
msgstr "库存调整"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "库存清点表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "盘点日期"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,field_description:stock.field_stock_quant__cyclic_inventory_frequency
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Inventory Frequency"
msgstr "库存频率"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "盘点位置"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "库存位置"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "库存损失"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_view_inherit_stock
msgid "Inventory Management"
msgstr "库存管理"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Inventory On Hand"
msgstr "在手库存"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "库存概览"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "库存数量集"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Reason"
msgstr "库存调整原因"

#. module: stock
#: model:ir.model,name:stock.model_stock_route
msgid "Inventory Routes"
msgstr "库存路线"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Inventory Valuation"
msgstr "库存估值"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_at_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory at Date"
msgstr "某日库存"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__is_empty
msgid "Is Empty"
msgstr "为空"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "是新鲜包裹"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "是锁定"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_multi_location
msgid "Is Multi Location"
msgstr "是否多库位"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_partial_package
msgid "Is Partial Package"
msgstr "是部分包装"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "已签署"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "是一个报废位置？"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "初始需求是否可以编辑"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "迟到"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr "迟到或将迟到，具体取决于截止日期和预定日期"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "完成数量是否可以编辑"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr "不能取消预留超过库存数量的 %s 产品。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "指定货物是部分交货，还是一次性交货"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__move_type
msgid "It specifies goods to be transferred partially or all at once"
msgstr "指定货物是部分转移，还是全部转移"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "弹出式窗口小部件的JSON数据"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "一月"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "John Doe"
msgstr "张三"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "Json提前天数弹窗"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.js:0
msgid "Json Popup"
msgstr "JSON格式弹窗"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "Json补货历史"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "七月"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "六月"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "看板仪表板图表"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "保持计数的计数"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "保持差异"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Keep current lines"
msgstr "保留当前明细行"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr "保持<strong>计算的计数</strong>（差额将被更新）。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr "保留<strong>差额</strong>（计算的计数将被更新，以反映与您计算时相同的差额）。"

#. module: stock
#: model:ir.actions.server,name:stock.action_print_labels
msgid "Labels"
msgstr "标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__label_type
msgid "Labels to print"
msgstr "待打印标签"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Laptop"
msgstr "笔记本电脑"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "过去12个月"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "最近3个月"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "最近30天"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__last_count_date
msgid "Last Count Date"
msgstr "最后统计日期"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "最后的交付伙伴"

#. module: stock
#: model:product.removal,name:stock.removal_lifo
msgid "Last In First Out (LIFO)"
msgstr "后进先出（LIFO）"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Inventory"
msgstr "最后库存"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__last_used
msgid "Last Used"
msgstr "上次使用"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__last_count_date
msgid "Last time the Quantity was Updated"
msgstr "最近更新数量的时间"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "迟到"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "最近活动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Availability"
msgstr "迟到的可用性"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "迟到的调拨"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "拣货的最新产品供应状况"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "提前期日期"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__lead_time
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "前置时间"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Lead Times"
msgstr "前置时间"

#. module: stock
#: model:product.removal,name:stock.removal_least_packages
msgid "Least Packages"
msgstr "最少包裹"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "留空"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr "如果此路线为所有公司共享, 那么此字段留空"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "图例"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "长度"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "长度必须为正"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "‎计量标签的长度单位‎"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
#: model:ir.model.fields,help:stock.field_stock_quant_relocate__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr "如果这个位置是公司间共享的，则此字段留空"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "链接的移动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of detailed operations"
msgstr "详细操作的列表视图"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of next transfers"
msgstr "下一转移列表视图模式"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "作业的列表视图"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "位置"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "位置条码"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "位置名称"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "库存位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Location Type"
msgstr "位置类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "位置：储存"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "位置：到达"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model:ir.ui.menu,name:stock.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "地点"

#. module: stock
#: model:ir.actions.server,name:stock.action_toggle_is_locked
msgid "Lock/Unlock"
msgstr "锁定/解锁"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "物流"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "批次"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_lot_customer_report_view_list
msgid "Lot / Serial Number"
msgstr "批次/序列号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__lot_label_format
msgid "Lot Label Format to auto-print"
msgstr "自动打印的批次标签格式"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_lot_template_view
msgid "Lot Label Report"
msgstr "批次标签报告"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__lot_properties_definition
msgid "Lot Properties"
msgstr "批次属性"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Lot numbers"
msgstr "批次号码"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__lots
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Lot/SN Labels"
msgstr "批号/序号 标签"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Lot/SN:"
msgstr "批号/序列号"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "批次/序列号"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Lot/Serial #"
msgstr "批号/序列号 #"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Lot/Serial Number"
msgstr "批次/序列号码"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "批次/序列号 （PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "批次/序列号 (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "批次/序列号 名称"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Lot/Serial Number Relocated"
msgstr "批次/序列号 搬迁"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial:"
msgstr "批次/序列号："

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "批次 / 序列号"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the delivery slip"
msgstr "批号 / 序列号会显示在送货单上"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
msgid "Lots / Serial Numbers"
msgstr "批次/序列号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "批次可见"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr "没有提供批次或序列号用来追溯产品"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "批次/序列号码"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"批号/序列号可帮助您跟踪产品遵循的路径。 \n"
" 从其可追溯性报告中，您将看到其使用的完整历史记录以及它们的组成。"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_replenish
msgid "Low on stock? Let's replenish."
msgstr "库存不足？让我们补充库存吧"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "MTO规则"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "管理不同的库存所有者"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "管理批次/序列号"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "管理多个库存位置"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "管理多个仓库"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "管理包裹"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "管理推式以及拉式库存流"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr "管理产品包装（例如6瓶每包，10件每盒）"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "手动"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "手动作业"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "Manual Replenishment"
msgstr "手动补给"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "手动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "制造"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "三月"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "标记为待办"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Max"
msgstr "最大"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "最大数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight"
msgstr "最大重量"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "最大重量必须是正数"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "最大重量应该是一个正数。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr "应在预定日期前预留优先拣货产品的最大天数。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr "应在预定日期前预留产品的最大天数。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "这种包装可装运的最大重量"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "五月"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "库存拣货单消息"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "消息"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "方法"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Min"
msgstr "最小"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "最小数量"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "最小库存规则"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "最小库存规则"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_ids
msgid "Move"
msgstr "分录"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "移动详情"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "移动整个包裹"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "分录行"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "移动明细行"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "移动明细行数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_quantity
msgid "Move Quantity"
msgstr "移动数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "创建退回移动的移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "凭证"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.ui.menu,name:stock.stock_move_menu
msgid "Moves Analysis"
msgstr "移动分析"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_pivot
msgid "Moves History"
msgstr "移动历史"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr "通过此订购补货点创建的库存调拨将放在此补货组中。如果没有被提供，由补货规则生成的库存调拨将被组合到一个大拣货组中。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "多步路线"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "倍数"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "一种包裹类型的多种能力规则。"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "一个产品有多种能力规则。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"我公司（芝加哥）承诺尽最大努力按照商定的时间框架在适当的时候提供有效的服务。然而，它的任何义务都不能被认为是取得成果的义务。在任何情况下，我公司（芝加哥）不能被客户要求作为第三方出现在最终消费者对客户提出的任何损害索赔中。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "我的计数"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "我的调拨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Name Demo"
msgstr "名称演示"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "移动进数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "移动出数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "负数预测数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "负数库存"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "净重量"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__never
msgid "Never"
msgstr "从不"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__never_product_template_attribute_value_ids
msgid "Never attribute Values"
msgstr "从不赋予值"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "New"
msgstr "新建"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "New Move: %(product)s"
msgstr "新的移动: %(product)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "新的在手数量"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "新调拨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一个活动日历事件"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected"
msgstr "下一次预期"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Next Transfers"
msgstr "其后转移"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "应计算下一个日期的在手计数。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "影响到的下一步调拨:"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__no
msgid "No"
msgstr "否"

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "No %s selected or a delivery order selected"
msgstr "未选择%s  或已选择交货单"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "没有欠单"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "无消息"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "No Stock On Hand"
msgstr "无在手库存"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found."
msgstr "无需预留库存"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "No data yet!"
msgstr "还没有数据耶！"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No delivery to do!"
msgstr "暂无运输！"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "No negative quantities allowed"
msgstr "不允许负数"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "No operation made on this lot."
msgstr "此批号没有作业"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a transfer!"
msgstr "没有作业。让我们创建一个调拨！"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "No product found to generate Serials/Lots for."
msgstr "未找到可生成序列号/批次的产品。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "现在还没有产品， 我们先创建一个!"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr "没有可退回产品（只有处于完成状态并且没有全部退回的行才可以退回产品）。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "还没有上架规则。 让我们创建一个！"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No receipt yet! Create a new one."
msgstr "暂无收据！创建新收据。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "没有重订货规则"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"No rule has been found to replenish \"%(product)s\" in \"%(location)s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"找不到产品 %(product)s 在位置 %(location)s 的补货规则。\n"
"请检查产品的路径配置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "No source location defined on stock rule: %s!"
msgstr "库存规则:%s没有定义来源位置!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "没有库存移动"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "No stock to show"
msgstr "没有库存可供显示"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No transfer found. Let's create one!"
msgstr "没有调拨。 让我们创建一个！"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "正常"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Not Available"
msgstr "不可用"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "没有延后"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "备注"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "备注"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Nothing to check the availability for."
msgstr "没有产品需要检查可用量。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "十一月"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "操作数量"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Number of SN"
msgstr "SN编码"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN/Lots"
msgstr "序号/批次号码"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "在过去12个月中，进货的数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息的数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "在过去的12个月里，出货库存移动数"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Numbers of days  in advance that replenishments demands are created."
msgstr "创建补货需求所需的前置天数。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "十月"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Odoo opens a PDF preview by default. If you (Enterprise users only) want to print instantly,\n"
"                                        install the IoT App on a computer that is on the same local network as the\n"
"                                        barcode operator and configure the routing of the reports.\n"
"                                        <br/>"
msgstr ""
"Odoo 默认开启 PDF 预览。如果你（仅限企业用戶）想立即打印，\n"
"                                        请在与条码操作员位于同一本地网络的电脑上，\n"
"                                        安装物联网（IoT）应用程序，并配置报告的路由。\n"
"                                        <br/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Office Chair"
msgstr "办公椅子"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "On Hand"
msgstr "在手"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_qty
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "在手数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "On hand Quantity"
msgstr "在手数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr "在调拨中未预留的现有数量，以产品的默认计量单位"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "在手："

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__lots
msgid "One per lot/SN"
msgstr "每批次/序列号一个"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__units
msgid "One per unit"
msgstr "每单元一个"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Open"
msgstr "开启"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__move
msgid "Operation Quantities"
msgstr "操作数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Operation Type"
msgstr "作业类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "退回的作业类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "作业类型"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_quant.py:0
msgid "Operation not supported"
msgstr "不支持的操作"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "作业类型"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "作业类型（PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "作业类型(ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "作业"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "作业类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "无包裹作业"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Optimize your transfers by grouping operations together and assigning jobs "
"to workers"
msgstr "通过将作业进行分组并将任务分配给工人来优化您的调拨。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr "用于交货的可选地址，专门用于配发"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "可选的定位细节，仅供参考"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr "可选: 所有从此移动创建的退货移动"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "可选: 当链接它们时下一库存移动."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "可选: 当链接它们时前一库存移动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "选项"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order"
msgstr "下单"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
msgid "Order To Max"
msgstr "订购至最大值"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed"
msgstr "已签署订单"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed by %s"
msgstr "订单由%s签名"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Ordered"
msgstr "已订购"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "订货点"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "原始"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "原始移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "原始退回移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "原始移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "原始重订货规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "其他信息"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"我们的结算单应在21个工作日内支付，除非结算单或订单上注明了其他付款时限。如果在到期日没有付款，我公司（芝加哥）预留要求支付固定利息的权利，金额为剩余款项的10%"
" o%。如果逾期付款，我公司（芝加哥）将被授权暂停提供任何服务，而无需事先警告。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "出库类型"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "出货"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Outgoing Draft Transfer"
msgstr "出货草稿调拨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "出货调拨行"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "出向运输"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Output"
msgstr "出货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "出货位置"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "概述"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "所有者"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "所有者 "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner:"
msgstr "所有者："

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "P&L Qty"
msgstr "P&L 数量"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Pack"
msgstr "包裹"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__pack_date
msgid "Pack Date"
msgstr "包装日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date Demo"
msgstr "包装日期 演示"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date:"
msgstr "包装日期："

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "包装类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "服务包"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package A"
msgstr "包裹 A"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package B"
msgstr "包裹 B"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "包裹条码 (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "包裹条码 (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Contents"
msgstr "有内容的包装条码"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "包裹能力"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_package_level.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Content"
msgstr "包裹内容"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Label"
msgstr "包装标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__package_label_to_print
msgid "Package Label to Print"
msgstr "要打印的包装标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "包裹层级"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "包裹层级ids 详情"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "包裹名称"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "包裹参考"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "包裹调拨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "包裹类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type Demo"
msgstr "软件包类型 演示"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type:"
msgstr "包裹类型："

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "包裹类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "包裹使用"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Package manually relocated"
msgstr "包装已手动转移位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__valid_sscc
msgid "Package name is valid SSCC"
msgstr "包裹名称不符合SSCC"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "包裹类型"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.actions.report,name:stock.action_report_picking_packages
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "包裹"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"包裹通常是通过转移（在包裹作业期间）创建的，可以包含不同的产品。 \n"
" 创建完成后，整个包裹可以立即调拨，或者产品可以拆开包裹并再次作为单个单元调拨。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "包装"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "包装高度"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "包装长度"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "包装宽度"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "包装"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "包装位置"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Packing Zone"
msgstr "包装区域"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Pallet"
msgstr "托盘"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "上级位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "父级路径"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__parent_route_ids
msgid "Parent Routes"
msgstr "上级路线"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "部分"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__partial_package_names
msgid "Partial Package Names"
msgstr "部分软件包名称"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "部分可用"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "合作伙伴"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "业务伙伴地址"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
msgid "Physical Inventory"
msgstr "实物库存"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
msgid "Pick"
msgstr "拣货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quant_id
msgid "Pick From"
msgstr "选自"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "拣货类型"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Pick then Deliver (2 steps)"
msgstr "拣货、配送（2步）"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pick, Pack, then Deliver (3 steps)"
msgstr "拣货，打包，配送（3步）"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picked
msgid "Picked"
msgstr "已拣货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__picking_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "拣货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "拣货清单"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "拣货作业"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__picking_properties_definition
msgid "Picking Properties"
msgstr "拣货属性"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "拣货类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "作业类型代码域"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "拣货清单"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Planning Issue"
msgstr "规划问题"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "计划问题"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Please create a warehouse for company %s."
msgstr "请为公司%s创建一个仓库。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid ""
"Please put this document inside your return parcel.<br/>\n"
"                                Your parcel must be sent to this address:"
msgstr ""
"请将此文件放在退货包裹内。<br/>\n"
"                                您的包裹必须寄往此地址："

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Please specify at least one non-zero quantity."
msgstr "请指定至少一个非0的数值。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Preceding operations"
msgstr "前置作业"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_id
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__route_id
msgid "Preferred Route"
msgstr "首选路线"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "首选路线"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Presence depends on the type of operation."
msgstr "是否存在取决于作业类型。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Press the \"New\" button to define the quantity for a product in your stock "
"or import quantities from a spreadsheet via the Actions menu"
msgstr "点击【新增】按钮，为产品设定库存数量，或通过【操作】菜单从电子表格导入数量。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "打印"

#. module: stock
#: model:res.groups,name:stock.group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lot & Serial Numbers"
msgstr "使用GS1编码格式打印批号和序列号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lots & Serial Numbers"
msgstr "使用GS1编码格式打印批号和序列号"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Print Label"
msgstr "打印标签"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Print Labels"
msgstr "标签打印"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print label as:"
msgstr "打印标签为："

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on \"Put in Pack\""
msgstr "在 \"放入包装 \"上打印"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on Validation"
msgstr "验证时打印"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "已打印"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "优先级"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "在此日期处理以保证准时"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "使用条码更快的处理作业"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement
msgid "Procurement"
msgstr "补货"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "补货组"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "补货组"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Procurement: run scheduler"
msgstr "补货: 运行调度器"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "生产明细行"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Produced Qty"
msgstr "生产的数量"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "产品可用性"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "产品能力"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "产品类别"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_category_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "产品类别"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Product Display Name"
msgstr "产品显示名称"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "产品标签 (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__product_label_format
msgid "Product Label Format to auto-print"
msgstr "自动打印的产品标签格式"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "产品标签报表"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__products
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Product Labels"
msgstr "产品标签"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "产品批次筛选"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "产品移动(移库明细)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "产品包装"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "产品包装 (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "产品包装"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Confirmed"
msgstr "确认的产品数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Updated"
msgstr "更新的产品数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Product Relocated"
msgstr "产品迁移"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.js:0
#: model:ir.model,name:stock.model_product_replenish
msgid "Product Replenish"
msgstr "补料"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "产品补货混入程序"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "产品路线报表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "产品模板"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "产品模板"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "产品追溯"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "产品计量单位"

#. module: stock
#: model:ir.model,name:stock.model_product_product
msgid "Product Variant"
msgstr "产品变体"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "产品变体"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Product barcode"
msgstr "产品条形码"

#. module: stock
#. odoo-python
#: code:addons/stock/report/product_label_report.py:0
msgid "Product model not defined, Please contact your administrator."
msgstr "产品型号未定义，请联系您的管理员。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr "此批号/序列号包含的产品。产品产生转以后，则不能修改批号/序列号。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "产品单位标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "追溯的产品"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "生产"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "生产位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "生产位置"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Products"
msgstr "产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "产品可用状态"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr "产品将会首先为最高优先级的调拨预留。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Products: %(location)s"
msgstr "产品: %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "传播"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "传播取消以及拆分"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "传播"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "补货组的传播"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "承运商的传播"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__lot_properties
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_properties
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_properties
msgid "Properties"
msgstr "权益"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "拉推"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "拉"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "拉规则"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__push_domain
msgid "Push Applicability"
msgstr "推动适用性"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "推式规则"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "推"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_detailed_operation_tree
msgid "Put in Pack"
msgstr "放入包裹"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr "把您的产品放入包装（例如包裹，包装盒）并追溯它们"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "上架规则"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Putaway Rules"
msgstr "上架规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "上架："

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "上架规则"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "数量倍数必须大于或等于零。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "质量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Quality Control"
msgstr "质量管理"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "质量管理位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__qc_type_id
msgid "Quality Control Type"
msgstr "品质控制类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "质量工作表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "量化"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's creation is restricted, you can't do this operation."
msgstr "量化创建是被限制的，您不能做此作业。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's editing is restricted, you can't do this operation."
msgstr "量化的编辑受限，您无法执行此作业。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities Already Set"
msgstr "数量已设定"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities To Reset"
msgstr "要重置的数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities unpacked"
msgstr "未包装数量"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Quantity"
msgstr "数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "数量倍数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "在手数量"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity Received"
msgstr "已收货数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity Relocated"
msgstr "搬迁数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "数量已预留"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "Quantity available too low"
msgstr "可用数量过低"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_change_product_qty.py:0
msgid "Quantity cannot be negative."
msgstr "数量不能为负数。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "自上次统计以来，已经移动的计数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity_product_uom
msgid "Quantity in Product UoM"
msgstr "产品数量 UoM"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr "库存中的数量仍然可以预留。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "基于产品默认计量单位的数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"计划进货产品的数量。\n"
"在单个库存位置的情况下，这包括离开该位置或其任何子位置的货品。\n"
"在单个库存的情况下，这包括离开库存的库存位置或其任何子位置的货物。\n"
"否则，这包括任何离开“内部”类型库存位置的货物。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"计划出货的产品数量。\n"
"对于一个位置来说，这包括了将要到达此位置或其子位置的货物。\n"
"对于一个库存来说，这包括了将要到达这个库存的的这个位置,或其子位置的货物。\n"
"另外，这包括了所有“内部”类型位置要到达的货物。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr "产品数量，以产品的默认单位为准"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr "产品数量，以产品的默认单位为准"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity or Reserved Quantity should be set."
msgstr "应设置 \"数量 \"或 \"保留数量\"。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity per Lot"
msgstr "每批数量"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "数量应该是一个正数。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__label_quantity
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_quantity
msgid "Quantity to print"
msgstr "打印数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity:"
msgstr "数量："

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "量化"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr "配额会适时自动删除。如果必须手动删除，请让库存经理来做。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quants cannot be created for consumables or services."
msgstr "不可对消耗产品或服务进行数量统计。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "RETURN OF"
msgstr "退回"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__rating_ids
msgid "Ratings"
msgstr "评分"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "就绪"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_ready_moves
msgid "Ready Moves"
msgstr "就绪移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "实际数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Reason"
msgstr "原因"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__message
msgid "Reason for relocation"
msgstr "搬迁原因"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
msgid "Receipt"
msgstr "收货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "收货路线"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_incoming
#: model:ir.ui.menu,name:stock.in_picking
#: model:stock.picking.type,name:stock.picking_type_in
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Receipts"
msgstr "收据"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "接收自"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive and Store (1 step)"
msgstr "收货并入库（1步）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 1 step (stock)"
msgstr "单步接收（库存）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 2 steps (input + stock)"
msgstr "2步接收(收货+库存）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "3步接收(收货+质检+入库)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive then Store (2 steps)"
msgstr "收货再入库（2步）"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive, Quality Control, then Store (3 steps)"
msgstr "收货, 质检, 入库（3步）"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Received Qty"
msgstr "已接收数量"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report"
msgstr "库存接收报表"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "接收报表标签"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report Labels"
msgstr "接收报表标签"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"Reduce stockouts with alerts, barcode app, replenishment propositions,\n"
"                        locations management traceability, quality control, etc."
msgstr ""
"通过警报、条形码应用程序、补货建议、\n"
"                        位置管理可追溯性、品质控制等，减少缺货的情况。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "参考号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "参考序列"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "每个公司的参考必须是唯一！"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "文档的参照"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "参考："

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "相关的库存移动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Relocate"
msgstr "迁移"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Relocate your stock"
msgstr "重新定位您的库存"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "部分已处理的拣货后剩余部分"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "下架"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "下架策略"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Removal strategy %s not implemented."
msgstr "下架策略 %s 没有实现。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Remove manually entered value and replace by the quantity to order based on "
"the forecasted quantities"
msgstr "移除手动输入的值，改为基于预测数量的待订购数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "重订货最大数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "重订货最小数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "重订货规则"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Reordering Rules"
msgstr "重订货规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "重订货规则搜索"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Replenish"
msgstr "执行补货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__replenish_location
msgid "Replenish Location"
msgstr "补充位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__should_replenish
msgid "Replenish Quantities"
msgstr "补充数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "按订单补给(MTO)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "补给向导"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Replenishment"
msgstr "补货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__replenishment_info_id
msgid "Replenishment Info"
msgstr "补货信息"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Replenishment Information"
msgstr "补货信息"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Information for %(product)s in %(warehouse)s"
msgstr "%(product)s 在 %(warehouse)s 中的补货信息"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Report"
msgstr "补货报表"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "补货报表搜索"

#. module: stock
#: model:ir.model,name:stock.model_ir_actions_report
msgid "Report Action"
msgstr "报表动作"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Report Printing Error"
msgstr "报表打印错误"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Reporting"
msgstr "报表"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "要求计数"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Request your vendors to deliver to your customers"
msgstr "请您的供应商直接将货物交付给您的客户"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "请求在送货单上签字"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "保留方式"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "预留"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Reserve"
msgstr "预留"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "只预留完整包装"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"只预留完整的包装：将不预留部分包装。如果客户订购了2个托盘，每个托盘有1000个单位，而您只有1600个库存，那么只有1000个将被预留。\n"
"预留部分包装：允许预留部分包装。如果客户订购了2个各1000件的托盘，而您只有1600件的库存，那么将预留1600件。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "预留包装"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "预留部分包装"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
msgid "Reserved"
msgstr "已预留"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_packaging_qty
msgid "Reserved Packaging Quantity"
msgstr "预留包装数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "预留数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Reserving a negative quantity is not allowed."
msgstr "不允许保留负数。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "负责人"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "补给"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "补给 自"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "补给路线"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "退回"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return All"
msgstr "退回全部"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "退回拣货"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "退回明细行"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Return Slip"
msgstr "退货单"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return for Exchange"
msgstr "退换货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_id
msgid "Return of"
msgstr "退回"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Return of %(picking_name)s"
msgstr "退回%(picking_name)s"

#. module: stock
#: model:ir.actions.report,name:stock.return_label_report
msgid "Return slip"
msgstr "退货单"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Returned Picking"
msgstr "退回的拣货"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_ids
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Returns"
msgstr "退货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Returns Type"
msgstr "退货类型"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "可重复使用的盒子"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"可重复使用的盒子用于批量拣选，之后被清空以重复使用。在条形码应用程序中，扫描一个可重复使用的盒子将添加该盒子中的产品。\n"
"        一次性包装盒不能重复使用，当在条码应用中扫描一次性包装盒时，所装的产品会被添加到调拨中。"

#. module: stock
#: model:ir.actions.server,name:stock.action_revert_inventory_adjustment
msgid "Revert Inventory Adjustment"
msgstr "还原库存调整"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__route_id
#: model:ir.model.fields,field_description:stock.field_stock_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "路线"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "路线公司"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "路线序列"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "路线"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "可以在此产品上选择路线"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr "勾选的项目会自动建立补货路线"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr "将会为这些补充仓库创建路线，而且您可以在产品和产品类别选择它们"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"Rule %(rule)s belongs to %(rule_company)s while the route belongs to "
"%(route_company)s."
msgstr "规则%(rule)s属于%(rule_company)s，而路由属于%(route_company)s。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "规则消息"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "基于类别的规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "基于产品的规则"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "使用的规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "S0001"
msgstr "S0001"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "短信息确认"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC Demo"
msgstr "SSCC 演示"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC:"
msgstr "SSCC:"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "标准销售条款及条件"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Sales History"
msgstr "销售历史"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sample data"
msgstr "示例数据"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_move_line__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "安排的日期"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
#: model:ir.model.fields,help:stock.field_stock_move_line__scheduled_date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr "预定日期，直到完成调拨为止，然后是实际调拨处理的日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "计划日期或移库日期"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr "计划时间的第一部分运输即将进行。在此手动设定一个值表示所有库存调拨的期望时间。"

#. module: stock
#: model:ir.actions.server,name:stock.action_scrap
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap"
msgstr "报废"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "报废位置"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "报废单"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap Products"
msgstr "废品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_reason_tag_ids
msgid "Scrap Reason"
msgstr "报废原因"

#. module: stock
#: model:ir.model,name:stock.model_stock_scrap_reason_tag
msgid "Scrap Reason Tag"
msgstr "报废原因标签"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_id
msgid "Scrap operation"
msgstr "报废操作"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "报废产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "已报废"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"报废产品将从您的库存中移除。 该产品将\n"
"出现于可用于报告目的之报废位置。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "报废"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "搜索补货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "搜索报废"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Search not supported without a value."
msgstr "不支持无值搜索。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Select Route"
msgstr "选择路线"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "选择可以应用此路线的地方"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
msgid ""
"Selected storage category does not exist in the 'store to' location or any "
"of its sublocations"
msgstr "选择的储存类别，在“储存至”位置或其任何子位置中，都不存在"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr "选择该“警告”选项，将通过消息通知用户，选择“阻塞消息”选项，将发送异常通知消息，并阻断流。须将该消息写入下一字段。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Selection not supported."
msgstr "不支持选择。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "允许产品使用多计量单位进行采购与销售"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr "发送一个自动确认短信息短信息时，交付订单完成"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr "发送一个自动确认电子邮件时，交货订单完成"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "发送邮件"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Sendcloud 连接器"

#. module: stock
#: model:mail.template,description:stock.mail_template_data_delivery_confirmation
msgid ""
"Sent to the customers when orders are delivered, if the setting is enabled"
msgstr "如果启用设置，将在订单交付后给客户送货。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__barcode_separator
msgid "Separator"
msgstr "分隔符"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "九月"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Sequence"
msgstr "序列"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sequence %(code)s"
msgstr "序列 %(code)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Sequence Prefix"
msgstr "序列前缀"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "序列号"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Serial number (%(serial_number)s) already exists in location(s): "
"%(location_list)s. Please correct the serial number encoded."
msgstr "序号（%(serial_number)s）已存在于以下位置：%(location_list)s。请更正编码的序列号。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"序号（%(serial_number)s）并不位于%(source_location)s中，而是在位置%(other_locations)s。\n"
"\n"
"请更正，以防数据不一致。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Source location for this move will be changed to %(recommended_location)s"
msgstr ""
"序号（%(serial_number)s）并不位于 %(source_location)s中，而是在位置%(other_locations)s。\n"
"\n"
"此移动的来源位置将变更为%(recommended_location)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Serial numbers"
msgstr "序号"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "设置"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "设置当前值"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "设置仓库路线"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closest location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting).\n"
"Least Packages: FIFO but with the least number of packages possible when there are several packages containing the same product."
msgstr ""
"设置特定的删除策略，无论该产品类别的来源位置如何，都将使用该策略。\n"
"\n"
"先进先出（FIFO）：先入库的产品/批次将先移出。\n"
"后进先出（LIFO）：最后入库的产品/批次将先被移出。\n"
"最接近位置：最接近目标位置的产品/批次将首先移出。\n"
"临期先出（FEFO）：最接近移除日期的产品/批次将首先移除（此方法的可用性取决于 “有效期” 设置）。\n"
"最少包装：先进先出，但在有多个包装包含相同产品时，尽可能减少包装数量。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots & serial numbers"
msgstr "为批次 / 序列号设置有效期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "在储存的产品上设置所有者"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr "设置产品变体（例如颜色，大小）来管理产品变体"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_to_zero_quants_tree
msgid "Set to 0"
msgstr "设置为 0"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
msgid "Set to quantity on hand"
msgstr "设置为库存数量"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "设置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Shelf A"
msgstr "货架 A"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "货架(Y)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "Ship a lot to a customer."
msgstr "向客户运送大量货物"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "运输"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "送货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "物流对接"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__move_type
msgid "Shipping Policy"
msgstr "送货策略"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "发货重量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr "运输连接器允许计算准确的运输成本，打印运输标签和请求在您的仓库运送给客户的承运商拣选。 从交付方式申请运送连接器。"

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Shipping: Send by Email"
msgstr "发货：通过电子邮件发送"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Shiprocket 连接器"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "简称"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "利用简称来标识您的仓库"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "显示分配"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "显示检查可用"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "显示作业详情"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "显示预测的数量状态按钮"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "显示批次M2O"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "显示批次文本"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_next_pickings
msgid "Show Next Pickings"
msgstr "显示下一个拣货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "在状态按钮显示在手数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__is_favorite
msgid "Show Operation in Overview"
msgstr "在概览中显示操作"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_picking_type
msgid "Show Picking Type"
msgstr "显示拣货类型"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_quant
msgid "Show Quant"
msgstr "显示数量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_show_reception_report
msgid "Show Reception Report at Validation"
msgstr "在验证时显示接待报表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
msgid "Show Transfers"
msgstr "显示调拨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr "显示下一操作日期早于今天的所有记录"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_m2o
msgid "Show lot_id"
msgstr "显示批号"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_text
msgid "Show lot_name"
msgstr "显示批次名称"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "显示所选仓库的可用路线。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "签署"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "签名"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "已签署"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "尺寸"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "尺寸：长×宽×高"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Snooze"
msgstr "延后"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "延后日期"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "延后订单点"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "延后"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "已延后"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr "一些选定的行已经设置了数量，它们将被忽略。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "来源"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "源单据"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source Location"
msgstr "源位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_usage
msgid "Source Location Type"
msgstr "源位置类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "源位置："

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Source Name"
msgstr "源名称"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "源包裹"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package:"
msgstr "源软件包："

#. module: stock
#: model:ir.actions.server,name:stock.stock_split_picking
msgid "Split"
msgstr "拆分"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "星标消息"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Starred Products"
msgstr "标星的产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr "Starshipit 连接"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
msgid "State"
msgstr "状态"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "状态"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_state
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：超出到期日期\n"
"今天：活动日期是今天\n"
"计划：未来活动。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_product_stock_view
#: model:ir.ui.menu,name:stock.menu_product_stock
msgid "Stock"
msgstr "库存"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode_barcodelookup
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Stock Barcode Database"
msgstr "库存条形码数据库"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Stock In Transit"
msgstr "中转库存"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "库存地点"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "库存位置"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
msgid "Stock Move"
msgstr "库存移动"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "库存移动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "库存移动分析"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "库存作业"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "包裹目的地"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "库存包裹层级"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "库存拣货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "库存量"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "库存数量历史"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_relocate
msgid "Stock Quantity Relocation"
msgstr "库存数量重新定位"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "库存数量报表"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "库存接收报表"

#. module: stock
#: model:ir.model,name:stock.model_stock_forecasted_product_product
#: model:ir.model,name:stock.model_stock_forecasted_product_template
msgid "Stock Replenishment Report"
msgstr "库存补货报表"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "库存要求盘点"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "库存规则"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "库存规则报表"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "库存规则报表"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "库存追溯确认"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "库存追溯行"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock move"
msgstr "库存移动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "可用的库存调拨（准备处理）"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "库存移动处于已确认、可用或者在等待的状态"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "已经处理调拨库存"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "库存包裹类型"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "库存规则报表"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "库存供应商补货信息"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "仓库库存补货选项"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Storage"
msgstr "存储"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid "Storage Capacities"
msgstr "储存能力"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "存储类别"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "存储类别"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "存储类别能力"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "储存位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__store_type_id
msgid "Storage Type"
msgstr "存储类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Store To"
msgstr "储存到"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid ""
"Store and retrieve information regarding every Lot/Serial Number (condition,"
" product info, ...)."
msgstr "保存和查询有关每个批次/序列号（状态、产品信息等）的信息。"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr "将产品存储在您仓库的特定位置（如：箱柜、货架）并据此追溯库存。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Store to"
msgstr "储存到"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "存储到子位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sublocation
msgid "Sublocation"
msgstr "子位置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "供应的仓库"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "供应方法"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "供应仓库"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_scrap_reason_tag_name_uniq
msgid "Tag name already exists!"
msgstr "标签名称已存在！"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "从库存获取"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "从库存调取，如不可用，触发其他规则"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"从库存调取：产品将从源位置的可用库存调取\n"
"触发其他规则：系统将尝试找到库存规则将产品调入源位置。可用库存将会被忽略。\n"
"从库存调取，如不可用，触发其他规则：产品将会从源位置的可用库存调取。如果没有可用库存，系统系统将尝试找到库存规则将产品调入源位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr "用于决定是否应显示 \"分配 \"按钮的技术字段。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "技术信息"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr "用于计算是否应显示按钮“检查可用性”的技术字段。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "模板"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The %s location is required by the Inventory app and cannot be deleted, but "
"you can archive it."
msgstr "位置%s是应用程序所必须，无法删除，但可以将其存档。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr "“手动作业”的值会在当前作业之后创建库存调拨。当“自动无步骤添加”时，位置将在原来的调拨中被替换。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "The Lot/Serial number (%s) is linked to another product."
msgstr "批次/序列号（%s）与另一个产品有关联。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"The Picking Operations report has been deleted so you cannot print at this "
"time unless the report is restored."
msgstr "拣选操作报告已被删除，除非恢复该报告，否则目前无法打印。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The Serial Number (%(serial_number)s) is already used in location(s): %(location_list)s.\n"
"\n"
"Is this expected? For example, this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"序号（%(serial_number)s）已在以下位置使用： %(location_list)s。\n"
"\n"
"预计会出现这种情况吗？例如，如果送货操作在其相应的收货操作验证之前就已验证，就会出现这种情况。在这种情况下，一旦完成所有步骤，问题就会自动解决。否则，应更正序列号，以防止数据不一致。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "The backorder %s has been created."
msgstr "欠单 %s已被创建. "

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company!"
msgstr "每个公司的位置条码必须是唯一的！"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr "客户明确放弃自己的标准期限和条件，即使这些期限和条件是在这些标准销售期限和条件之后制定的。为了有效，任何减损必须事先以书面形式明确同意。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"The combination of lot/serial number and product must be unique within a company including when no company is defined.\n"
"The following combinations contain duplicates:\n"
"%(error_lines)s"
msgstr ""
"批号/序列号与产品的组合在公司内部（含未指定公司）必须是唯一的。 \n"
"以下组合包含重复项：\n"
"%(error_lines)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr "该公司是从您的用户偏好自动设置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "The day after tomorrow"
msgstr "后天"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The deadline has been automatically updated due to a delay on %s."
msgstr "由于 %s的延期，截止日期已自动更新。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr "创建的调拨的预计日期将基于该提前期进行计算。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "第一个序列中是默认的"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "The following replenishment order have been generated"
msgstr "已生成以下补货订单"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "The forecasted quantity of"
msgstr "预测数量的"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "The forecasted stock on the"
msgstr "预测库存"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "The inter-warehouse transfers have been generated"
msgstr "已生成仓库间的库存移动"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "The inventory adjustments have been reverted."
msgstr "库存调整已被还原"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr "一个地点的库存频率（天数）必须是非负的"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"The minimum quantity must be less than or equal to the maximum quantity."
msgstr "最小数量必须小于或等于最大数量。"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "每个公司的仓库名称必须唯一!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr "要生成的序列号的数量必须大于零。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_final_id
msgid ""
"The operation brings the products to the intermediate location.But this "
"operation is part of a chain of operations targeting the final location."
msgstr "该操作将产品带到中间位置。但是，此操作是针对最终位置的操作链的一部分。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid "The operation takes and suggests products from this location."
msgstr "该操作会从此位置获取并推荐产品。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""
"作业类型系统允许您分配每个库存\n"
"            作业一个特定类型，该类型将相应地改变其视图。\n"
"            在作业类型上，你可以指定默认情况下是否需要包装，\n"
"            如果它应该显示给客户。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "The operations brings product to this location"
msgstr "该操作会将产品送至此位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "该包裹包含此数量"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr "包含此位置的父位置。示例： '调度区域' 是 '1号门' 的父位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to a multiple of this field "
"quantity. If it is 0, it is not rounded."
msgstr "采购数量将四舍五入为该字段数量的倍数。如果为 0，则不四舍五入。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "产品数量不足"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "该产品的实盘数量。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"The quantities selected do not all belong to the same location.\n"
"                    You may not assign them a package without moving them to a common location."
msgstr ""
"所选数量并不都属于同一位置。\n"
"                    如果不将它们移动到共同位置，就不能为它们分配货包。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"The quantity done for the product \"%(product)s\" doesn't respect the "
"rounding precision defined on the unit of measure \"%(unit)s\". Please "
"change the quantity done or the rounding precision of your unit of measure."
msgstr "产品“%(product)s”的完成数量不符合计量单位“%(unit)s”上定义的舍入精确度。请更改已完成的数量，或计量单位的舍入精确度。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The quantity per lot should always be a positive value."
msgstr "每批次数量应始终为正值。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr "请求的作业无法处理，因为一个程序错误，设置`product_qty`字段取代了 `product_uom_qty`。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The search does not support the %(operator)s operator or %(value)s value."
msgstr "搜索不支持%(operator)s操作员或%(value)s值。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr "所选的库存频率（天数）创建的日期过于遥远。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The serial number has already been assigned: \n"
" Product: %(product)s, Serial Number: %(serial_number)s"
msgstr ""
"序列号已分配：\n"
"产品：%(product)s，序号：%(serial_number)s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr "仓库的简称在每个公司中必须是唯一的！"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr "当发送产品到此联系人时， 此预定义的库存将用做目的位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr "当从此联系人接收产品时， 此预定义的库存将用做源位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "打包作业执行的库存作业"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "建立此移库动作的相应规则"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr "在已创建的调拨/补货传播的仓库，它可以与此规则所针对的仓库不同 (例如：从另外仓库补货的规则)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "There are no inventory adjustments to revert."
msgstr "没有库存调整可以还原"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"There is nothing eligible to put in a pack. Either there are no quantities "
"to put in a pack or all products are already in a pack."
msgstr "没有符合条件的产品可以放入包装。要么是没有数量可放入包装，要么是所有产品都已放入包装。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "还没有产品移动"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "This SN is already in another location."
msgstr "该序列号已存在于其他库存位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"这增加一个直销路线来应用于产品，以请求您的供应商交付给您的客户。 一旦销售订单被确认，要直销的产品将会产生报价的采购申请。这是按需流量。 "
"请求的送货地址将是客户送货地址，而不是您的仓库。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr "此分析为您提供了产品当前库存水平的概述。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picked
msgid ""
"This checkbox is just indicative, it doesn't validate or generate any "
"product moves."
msgstr "该复选框只是指示性的，不会验证或生成任何产品移动。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr "此字段将填写库存作业来源和其调拨的名称"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when this operation is manually "
"created. However, it is possible to change it afterwards or that the routes "
"use another one by default."
msgstr "这是手动创建操作时的默认目标位置。不过，您可以在之后更改，或由系统路线设置其他默认位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when this operation is manually created."
" However, it is possible to change it afterwards or that the routes use "
"another one by default."
msgstr "这是手动创建操作时的默认来源位置。不过，您可以在之后更改，或由系统路线设置其他默认位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "这是存货的货主"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of product that is planned to be moved.Lowering this "
"quantity does not generate a backorder.Changing this quantity on assigned "
"moves affects the product reservation, and should be done with care."
msgstr "在指定的移动中更改该数量会影响产品预订，因此应谨慎操作。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr "这个位置（如果它是内部的）和它所有的后代，通过type=Internal过滤。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr "该位置的使用情况不能更改为视图，因为其包含产品。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr "该批号 %(lot_name)s 与 %(product_name)s 不兼容"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "This lot/serial number is already in another location"
msgstr "该批次/序列号已存在于其他库存位置"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"此菜单能让你完整的追踪某一特定产品的盘点\n"
"作业。你可以在产品上筛选此产品过去和将来的的所有的移动。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"此菜单为您提供特定产品的库存作业的完整可追溯性。\n"
"您可以对产品进行过滤，查看产品过去的所有动作。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "此通知已添加到送货单中。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr "此说明添加到内部调拨订单中(例如：在仓库中何处提取产品)。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr "此说明添加到收货订单中(例如：产品在仓库的存储位置)。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr "该产品已至少在库存调拨中使用过一次。 不建议更改产品类型，因为它可能导致不一致。 更好的解决方案是将产品存档，然后创建一个新产品。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr "如果此产品有部分数量属于另一家公司，则不能更改产品的所属公司。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr "如果此产品有部分属于另一家公司，则不能更改产品的所属公司。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr "此数量以该产品的默认计量单位表示。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid "This record already exists."
msgstr "此记录早已存在。"

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "This report cannot be used for done and not done %s at the same time"
msgstr "报表不能同时存在已完成和未完成%s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"This sequence prefix is already being used by another operation type. It is "
"recommended that you select a unique prefix to avoid issues and/or repeated "
"reference values or assign the existing reference sequence to this operation"
" type."
msgstr "此序列前缀已被其他操作类型使用。建议您选择一个唯一的前缀，以避免出现问题和/或重复参考值，或将现有参考序列分配给此操作类型。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr "该位置将被使用以替换默认值，同样，库存调拨的源位置将根据制造单生成。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr "此库存位置将被使用以替换默认的，当您做盘点时，同样的作为根据制造单生成的库存调拨之源位置。"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr "该用户将负责与该产品物流作业相关的下一个活动。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr "这将放弃所有未应用的盘点，您想继续吗？"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Those products you added are tracked but lots/serials were not defined. Once applied those can't be changed.<br/>\n"
"                    Apply anyway?"
msgstr ""
"您添加的那些产品将被跟踪，但批次/序列号未定义。一旦应用，这些就无法更改。<br/>\n"
"                    确认操作吗?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Time Horizon"
msgstr "时间范围"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_1
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid "Tip: Monitor Lot details"
msgstr "提示：监控Lot详情"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "提示：使用条形码加快库存作业"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "至"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "待应用"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "待欠单"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "安排超期"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Deliver"
msgstr "待送货"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_graph
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "To Do"
msgstr "待办"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Location"
msgstr "位置:"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "待下单"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_computed
msgid "To Order Computed"
msgstr "待订货计算"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_manual
msgid "To Order Manual"
msgstr "待手动订货"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "To Order with Visibility Days"
msgstr "依可见天数排序"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Package"
msgstr "到包装"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "待处理"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr "待接收"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "重新订购"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__today
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today"
msgstr "今天"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "今日活动"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Tomorrow"
msgstr "明天"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Available"
msgstr "合计可用"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Demand"
msgstr "总需求"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Forecasted"
msgstr "总预测数"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Free to Use"
msgstr "总计可用"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Incoming"
msgstr "总计进货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total On Hand"
msgstr "在手总计"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Outgoing"
msgstr "总计出货"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Quantity"
msgstr "数量总计"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Reserved"
msgstr "保留总计"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "路线合计"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr "包裹和不在包裹中的产品的总重量。未指定运输重量的包裹将默认为其产品的总重量。该重量用于计算运费。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "非包装内产品的总重量。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "包裹的总重量。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "追溯"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.js:0
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Traceability Report"
msgstr "追溯报表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__is_storable
#: model:ir.model.fields,field_description:stock.field_product_template__is_storable
#: model:ir.model.fields,field_description:stock.field_stock_move__is_storable
msgid "Track Inventory"
msgstr "追踪库存"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"追溯以下日期的批次 / 序列号：最佳日期、删除、生命尽头、警报。\n"
"这些日期是根据产品上在批次/序列号创建时自动设置的值（以天为单位）。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr "追溯以下日期的批次 / 序列号：最佳日期、删除、生命尽头、警报。 这些日期是根据产品设置的值（以天为单位）在批次/序列号创建时自动设置的。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "追溯您仓库内的产品位置"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr "通过创建可存储的产品来跟踪您的库存数量。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Tracked Products in Inventory Adjustment"
msgstr "库存调整中的产品追溯"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "追溯"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "追溯明细"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "调拨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "调拨到"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_ids
#: model:ir.ui.menu,name:stock.menu_stock_transfers
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "调拨"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Transfers %s: Please add some items to move."
msgstr "调拨%s：请添加一些要移动的项目。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Transfers allow you to move products from one location to another."
msgstr "调拨允许您将产品从一个位置调拨到另外一个位置。"

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "组的调拨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr "计划日期延迟的调拨或将会延迟的拣货"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "中转位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "中转位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Transport management: organize packs in your fleet, or carriers."
msgstr "运输管理：安排您的车队或外部承运商运送包裹。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger"
msgstr "触发方式"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "触发其他规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "如果没有库存，触发另一个规则"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger Manual"
msgstr "手动触发"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_scrap__should_replenish
msgid "Trigger replenishment for scrapped products"
msgstr "报废产品触发补货"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
msgid "Try to add some incoming or outgoing transfers."
msgstr "尝试添加一些进货或出货的调拨。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
msgid "Type"
msgstr "类型"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Type a message..."
msgstr "输入消息......"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "作业类型"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录中异常活动的类型。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPS 连接器"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPS 连接器"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Unassign"
msgstr "取消分配"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"Unavailable Serial numbers. Please correct the serial numbers encoded: "
"%(serial_numbers_to_locations)s"
msgstr "序列号不可用。请更正已编码的序列号：%(serial_numbers_to_locations)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "Unfold"
msgstr "展开"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__name
msgid "Unique Lot/Serial Number"
msgstr "唯一批次/序列号码"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Unit"
msgstr "单位"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "单价"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "计量单位"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__uom
msgid "Unit of Measure Name"
msgstr "计量单位名称"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Units"
msgstr "单位"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "计量单位"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "计量单位"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "计量单位"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unity of measure"
msgstr "计量单位"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Unknown Pack"
msgstr "未知包裹"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Unless previously specified by the source document, this will be the default"
" picking policy for this operation type."
msgstr "除非源文档中另有规定，否则这将是此类操作的默认选择策略。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "拆包"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "取消保留"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Unreturned"
msgstr "未交还"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Unsafe unit of measure"
msgstr "非安全计量单位"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__unwanted_replenish
msgid "Unwanted Replenish"
msgstr "不需要的补货"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "UoM"
msgstr "计量单位"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "计量单位类别"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__uom_id
msgid "Uom"
msgstr "单位"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "更新产品数量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Update Quantities"
msgstr "更新数量"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Quantity"
msgstr "更新数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"Updating the location of this transfer will result in unreservation of the currently assigned items. An attempt to reserve items at the new location will be made and the link with preceding transfers will be discarded.\n"
"\n"
"To avoid this, please discard the source location change before saving."
msgstr ""
"更新此转移的位置，将导致取消保留当前已指派的项目。系统会尝试在新位置预订物品，先前传输的链接将被丢弃。\n"
"\n"
"为了避免这种情况，请在保存之前，放弃来源位置变更。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "紧急"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "使用已有批次/序列号"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Use Existing ones"
msgstr "使用现有的"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Use GS1 nomenclature datamatrix whenever barcodes are printed for lots and "
"serial numbers."
msgstr "每当打印批次 / 序列号的条形码时，请使用 GS1 命名法 datamatrix。"

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "使用接收报表"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "使用您自己的路线"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Used by"
msgstr "用于"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "用于订购“所有作业”看板视图"

#. module: stock
#: model:ir.model,name:stock.model_res_users
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "用户"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "被指派做产品盘点的用户。"

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "验证"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
msgid "Validate Inventory"
msgstr "验证库存"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "变体计数"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "供应商"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "供应商位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "供应商位置"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "视图"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "View Availability"
msgstr "查看可用性"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "查看图表"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "视图位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "查看和分配收到的数量。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__visibility_days
msgid "Visibility Days"
msgstr "可见的天数"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Visibility days"
msgstr "可见天数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_volume
msgid "Volume for Shipping"
msgstr "运输体积"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/OUT/00001"
msgstr "WH/OUT/00001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "WH/OUT/0001"
msgstr "WH/OUT/0001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Outgoing"
msgstr "仓库/出库"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Stock"
msgstr "仓库/库存"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "正在等待"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "等待其它移动"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "等待其它作业"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "等待可用量"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "等待移动"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "等待调拨"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "仓库"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "仓库配置"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "仓库 域"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Warehouse Location"
msgstr "仓库位置"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "仓库管理"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "仓库视图"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "传播的仓库"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "仓库视图位置"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Warehouse's Routes"
msgstr "仓库的路线"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_warehouse_filter.xml:0
msgid "Warehouse:"
msgstr "仓库:"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Warehouses"
msgstr "仓库"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "库存不足时发出警告"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "报废数量不足发出警告"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
msgid "Warning"
msgstr "警告"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Warning Duplicated SN"
msgstr "警告序列号重复"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warning_message
msgid "Warning Message"
msgstr "警告消息"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "拣货的警告"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Warning!"
msgstr "警告！"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Warning: change source location"
msgstr "警告：更改源位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "警告"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "库存警报"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "网站沟通记录"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__base_weight
msgid "Weight"
msgstr "重量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "物流重量"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__base_weight
msgid "Weight of the package type"
msgstr "某包装类型的重量"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__weight_uom_name
msgid "Weight unit"
msgstr "重量单位"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "重量单位标签"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "称重的产品"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__wh_replenishment_option_ids
msgid "Wh Replenishment Option"
msgstr "WH 补货规则 "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr "当为该路线选择仓库时，当产品通过该仓库时，该路线应被视为默认路线。"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__one
msgid "When all products are ready"
msgstr "当所有产品就绪时"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr "选中此选项后，可以在产品表单的存货选项卡中选择路线。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr "选中此选项后，将在产品类别上选择该路线。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr "勾选后，该路线将可在产品包装上进行选择。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "当产品到达"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%(destination)s</b>, <br> "
"<b>%(operation)s</b> are created from <b>%(source_location)s</b> to fulfill "
"the need. %(suffix)s"
msgstr ""
"当 <b>%(destination)s</b> 需要产品时，<br>会自动创建<b>%(operation)s</b> 操作，由 "
"<b>%(source_location)s</b> 调配产品，以满足需求。%(suffix)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products arrive in <b>%(source_location)s</b>, <br> "
"<b>%(operation)s</b> are created to send them to <b>%(destination)s</b>."
msgstr ""
"产品到达<b>%(source_location)s</b>时，<br>会自动建立<b>%(operation)s</b>操作，将产品运往<b>%(destination)s</b>。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__location_dest_from_rule
msgid ""
"When set to True the destination location of the stock.move will be the "
"rule.Otherwise, it takes it from the picking type."
msgstr "设置为 True（真）时，stock.move 的目标位置会依照规则。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr "当拣货进行时，允许改变初始需求。当拣选完成时，只允许改变已完成数量。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity above of this"
" Min Quantity."
msgstr "当虚拟库存低于此字段指定的最小数量时，Odoo 会生成采购，使预测数量高于此最小数量。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity up to (or near to) the Max "
"Quantity specified for this field (or to Min Quantity, whichever is bigger)."
msgstr "当虚拟库存低于最小数量时，Odoo 会生成采购，使预测数量达到（或接近）该字段指定的最大数量（或最小数量，以较大者为准）。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propagated."
msgstr "当被打勾时，运输承运商将被传播。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr "当勾选此项时，如果此规则创建的调拨被取消，下一个调拨也将被取消。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__create_backorder
msgid ""
"When validating a transfer:\n"
" * Ask: users are asked to choose if they want to make a backorder for remaining products\n"
" * Always: a backorder is automatically created for the remaining products\n"
" * Never: remaining products are cancelled"
msgstr ""
"在验证调拨时:\n"
" *询问:要求用户选择是否要对剩余产品进行延期订购\n"
" * 总是:为剩余的产品自动创建延迟订单\n"
" * 从不:剩余的产品被取消"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr "在验证调拨时，这些产品将分配给此所有者。"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr "在验证调拨时，产品将从该所有者处获得。"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr "是否在拣货确认后添加了调拨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "宽度"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "宽度必须是正数"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "向导"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Write one lot/serial name per line, followed by the quantity."
msgstr "每行写一个批次/序列名称，然后是数量。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Yesterday"
msgstr "昨天"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"You are about to move quantities in a package without moving the full package.\n"
"                    Those quantities will be removed from the following package(s):"
msgstr ""
"您要移动一个软件包中的数量，但不移动整个软件包。\n"
"                    这些数量将从以下货包中移除："

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid ""
"You are going to pick products that are not referenced\n"
"in this location. That leads to a negative stock."
msgstr "你要挑选的产品在该位置未被引用。这将导致库存为负。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "您太棒了，无需补货！"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr "当一个序号已有产生库存调拨时，您不能修改此已连接到序号或者批号的产品。 因这样会导致库存记录不完整。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr "在此作业类型下不能创建批次或者序列号。要更改此设置，请在相应的作业类型下勾选“创建新的批次 / 序列号” 选项。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr "你正在将发货到不同地方的产品装进同一个包装内"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"您使用的计量单位小于在储存产品时需要用到的计量单位。 "
"这可能导致已预留数量上的舍入问题！您应该使用尽可能小的计量单位来管理库存或将其舍入精度更改为较小值（例如：0.00001）。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"您可以在这里定义贯穿于整个仓库的主要路线，\n"
"与此同时这也决定了产品的流向。\n"
"这些路径可以被设置在产品、产品类别上，\n"
" 或者应用于补货和销售订单上。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "您可以选择:"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that is currently "
"reserved on a stock move. If you need to change the inventory tracking, you "
"should first unreserve the stock move."
msgstr "您不能更改当前已在库存移动中保留的产品的库存追踪。如果需要更改库存追踪，应首先取消库存移动。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that was already "
"used."
msgstr "您不能更改已使用产品的库存追踪。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can not create a snoozed orderpoint that is not manually triggered."
msgstr "您不能创建非手动触发的延后订单点。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You can not delete moves linked to another operation"
msgstr "不能删除与其他操作关联的移动"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr "如果拣货已完成，不能删除产品调拨。您可以纠正完成数量。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can not enter negative quantities."
msgstr "不能输入负数数量。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You can only enter positive quantities."
msgstr "只能输入正数。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You can only move a lot/serial to a new location if it exists in a single "
"location."
msgstr "只有在某个位置有批次/序列号的情况下，你才可以将其迁移到新位置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You can only move positive quantities stored in locations used by a single "
"company per relocation."
msgstr "每次搬迁只能搬迁存放在单个公司使用的地点的正数数量。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr "只能处理1.0%s具有唯一序列号的产品。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can only snooze manual orderpoints. You should rather archive 'auto-"
"trigger' orderpoints if you do not want them to be triggered."
msgstr "您只能暂停手动订单点。如果不想触发订单点，则应将其存档。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You can't deactivate the multi-location if you have more than once warehouse"
" by company"
msgstr "如果公司有多个仓库，则不能停用多位置"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "You can't disable locations %s because they still contain products."
msgstr "您不能禁用位置 %s，因为它们仍然包含产品。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot archive location %(location)s because it is used by warehouse "
"%(warehouse)s"
msgstr "您不可封存位置%(location)s，因为仓库%(warehouse)s正使用它"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr "您无法取消已设置为“完成”的库存调拨。 创建退货以撤消已发生的调拨。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr "不能更改已取消的库存移动，应创建新行。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr "您不能更改已完成或已取消调拨的预定日期。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr "您无法改变已标记为“完成”的库存调拨的状态。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You cannot change the company of a lot/serial number currently in a location"
" belonging to another company."
msgstr "您不能更改当前位置属于其他公司的批号/序列号的公司。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr "您无法更改此度量单位的比率，因为具有此UoM的某些产品已被调拨或当前已预留。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr "您无法更改计量单位，因为此产品已有库存变动。如果要更改计量单位，则应该归档此产品并创建新产品。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You cannot delete a scrap which is done."
msgstr "不能删除已完成的报废。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot directly pack quantities from different transfers into the same "
"package through this view. Try adding them to a batch picking and pack it "
"there."
msgstr "您不能通过此视图，将不同转移的数量直接打包到同一个货包中。请尝试将它们添加到批次拣选中，然后在该处打包。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot duplicate stock quants."
msgstr "您不可复制库存数量。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot modify inventory loss quantity"
msgstr "不能修改盘点损失数量"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr "您不能在同一调拨中多次调拨同一包裹内容，或将同一包裹拆分到两个位置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they are from different "
"transfers with different operation types."
msgstr "如果产品来自不同的转移，且操作类型不同，则不能将产品装入同一包裹。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot perform moves because their unit of measure has a different "
"category from their product unit of measure."
msgstr "您不可执行移动，因为其计量单位与产品计量单位的类别不同。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot set a location as a scrap location when it is assigned as a "
"destination location for a manufacturing type operation."
msgstr "当某个位置已被指定为生产类型操作的目的地位置时，不能将该位置设置为报废位置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot set a scrap location as the destination location for a "
"manufacturing type operation."
msgstr "不能将报废位置设置为生产类型作业的目标位置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr "您不能拆分一个草稿移动。您需要先确认它。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr "您不能拆分已设置为'完成'或'取消'的库存移动。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr "您不能从“视图”（%s）类型的位置获取产品或将产品交付到该位置。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr "您无法取消预留状态已标记为“完成”的库存移动。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr "您不能使用两次相同的序列号。 请更正序列号编码。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot validate a transfer if no quantities are reserved. To force the "
"transfer, encode quantities."
msgstr "如果没有保留数量，则无法验证调拨。要强制调拨，请对数量进行编码。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You can’t validate an empty transfer. Please add some products to move "
"before proceeding."
msgstr "您无法验证空调拨。请在继续之前添加一些要调拨的产品。"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "您已经处理了初始需求的部分产品。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"您有启用批次/序列号跟踪的库存产品。\n"
"在关闭此设置之前，请先关闭所有产品的跟踪功能。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr "您有库存的产品没有批号/序列号。您可以通过库存调整来分配批次/序列号。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr "您必须选择与产品默认计量单位相同类别的产品计量单位"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return Done pickings."
msgstr "您只能退回已完成的拣货。"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return one picking at a time."
msgstr "您每次只能退回一次拣货。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr "您需要激活存储位置，以便能够执行内部作业类型。"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "You need to select a route to replenish your products"
msgstr "您需要选择补货的路线"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You need to supply a Lot/Serial Number for product:\n"
"%(products)s"
msgstr ""
"您需要提供产品的批次/序列号：\n"
"%(products)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "您需要提供产品%s的批号/序列号。"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr "您应该更新此文档以反映您的T &amp; C。"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"You still have ongoing operations for operation types %(operations)s in "
"warehouse %(warehouse)s"
msgstr "库存仍有%(operations)s类型的操作正在进行。相关仓库： %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr "您对此产品仍然有一些取消归档的重新排序规则。 请先归档或删除它们。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid ""
"You tried to create a record that already exists. The existing record was "
"modified instead."
msgstr "您试图创建的记录已存在，因此直接修改了现有记录。"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"在odoo系统可以自动补货，目的是减少人工干预，降低人力成本。在这里您可以找到基于智能化的库存预测的补货建议。\n"
"            然后您就选择要购买或生产的数量，然后单击启动订单。\n"
"            为了节省将来的时间，请将规则设置为“automated自动补货”。"

#. module: stock
#: model_terms:res.company,lunch_notify_message:stock.res_company_1
msgid ""
"Your lunch has been delivered.\n"
"Enjoy your meal!"
msgstr ""
"你的午餐已经送到了。\n"
"祝您用餐愉快！"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Your stock is currently empty"
msgstr "您的库存目前是空的"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zpl
msgid "ZPL Labels"
msgstr "ZPL标签"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_lots
msgid "ZPL Labels - One per lot/SN"
msgstr "ZPL 标签 - 每批次/SN 一个"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_units
msgid "ZPL Labels - One per unit"
msgstr "ZPL 标签 - 每个单位一个"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zplxprice
msgid "ZPL Labels with price"
msgstr "带价格的ZPL标签"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>分钟:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "below the inventory"
msgstr "在库存以下"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpost 连接器"

#. module: stock
#: model:product.removal,method:stock.removal_closest
msgid "closest"
msgstr "closest"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "天"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "在星光闪耀的前几天"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "收货前几天"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "例如：CW"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "例如：中央仓库"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "e.g. LOT-PR-00012"
msgstr "例：LOT-PR-00012"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "例如：批次/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. Lumber Inc"
msgstr "例：Lumber Inc"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "例如：PACK0000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "例如：PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "例如：物理位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "e.g. Receptions"
msgstr "例如:接待"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "e.g. SN000001"
msgstr "例如：SN000001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "例如：备用库存"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "例如：两步接收"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "例：d7vctmiv2rwgenebha8bxq7irooudn"

#. module: stock
#: model:product.removal,method:stock.removal_fifo
msgid "fifo"
msgstr "fifo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "来自位置"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "在"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "in barcode."
msgstr "在条形码内。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "is"
msgstr "是"

#. module: stock
#: model:product.removal,method:stock.removal_least_packages
msgid "least_packages"
msgstr "least_packages"

#. module: stock
#: model:product.removal,method:stock.removal_lifo
msgid "lifo"
msgstr "lifo"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "minimum of"
msgstr "最小日期"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "的"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "planned on"
msgstr "计划于"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "已处理，替换"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "reserved"
msgstr "已预留"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "should be replenished"
msgstr "应补足"

#. module: stock
#: model:ir.actions.server,name:stock.click_dashboard_graph
msgid "stock.click_dashboard_graph"
msgstr "stock.click_dashboard_graph"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_incoming
msgid "stock.method_action_picking_tree_incoming"
msgstr "stock.method_action_picking_tree_incoming"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_internal
msgid "stock.method_action_picking_tree_internal"
msgstr "stock.method_action_picking_tree_internal"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_outgoing
msgid "stock.method_action_picking_tree_outgoing"
msgstr "stock.method_action_picking_tree_outgoing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__filter_for_stock_putaway_rule
msgid "stock.putaway.rule"
msgstr "stock.putaway.rule"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "the barcode app"
msgstr "条形码应用程序"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"the warehouse to consider for the route selection on the next procurement "
"(if any)."
msgstr "仓库在下一次采购（如有）时考虑路线选择。"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "to reach the maximum of"
msgstr "以达到最大的"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "units"
msgstr "单位"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr "${object.company_id.name} 交货单 (Ref ${object.name or 'n/a' })"
