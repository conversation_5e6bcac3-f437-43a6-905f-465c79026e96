# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* lunch
# 
# Translators:
# <PERSON><PERSON>ian<PERSON><PERSON>z<PERSON>, 2024
# <PERSON><PERSON>, 2024
# eriiik<PERSON>, 2024
# <PERSON><PERSON> <car<PERSON><EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# jabiri7, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<PERSON><EMAIL>>, 2024
# Harcogourmet, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# Noem<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Noemi Pla, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_temaki
msgid "1 Avocado - 1 Salmon - 1 Eggs - 1 Tuna"
msgstr "1 alvocat - 1 Salmó - 1 préssecs - 1 Tonyina"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_chirashi
msgid "2 Tempuras, Cabbages, Onions, Sesame Sauce"
msgstr "2 Tempures, Cols, Cebes, Salsa de Sèsam"

#. module: lunch
#: model:lunch.product,name:lunch.product_4formaggi
msgid "4 Formaggi"
msgstr "4 Formaggi"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_salmon
msgid "4 Sushi Salmon - 6 Maki Salmon - 4 Sashimi Salmon"
msgstr "4 Salmó Sushi - 6 Maki Salmon - 4 Salmó Sashimi"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_maki
msgid "6 Maki Salmon - 6 Maki Tuna - 6 Maki Shrimp/Avocado"
msgstr "6 Maki de salmó - 6 Maki de tonyina - 6 Maki de gambes/alvocat"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Receive button\"/>"
msgstr ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Receive button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Send notification\" "
"title=\"Send notification\"/>"
msgstr ""
"<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Send notification\" "
"title=\"Send notification\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Amount\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Amount\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Money\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Money\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-paper-plane\" role=\"img\" aria-label=\"Send button\" "
"title=\"Send button\"/>"
msgstr ""
"<i class=\"fa fa-paper-plane\" role=\"img\" aria-label=\"Send button\" "
"title=\"Send button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Order button\"/>"
msgstr ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Order button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel button\"/>"
msgstr ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel button\"/>"

#. module: lunch
#: model:mail.template,body_html:lunch.lunch_order_mail_supplier
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Lunch Order</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not user.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"    <div>\n"
"        <t t-set=\"lines\" t-value=\"ctx.get('lines', [])\"/>\n"
"        <t t-set=\"order\" t-value=\"ctx.get('order')\"/>\n"
"        <t t-set=\"currency\" t-value=\"user.env['res.currency'].browse(order.get('currency_id'))\"/>\n"
"        <p>\n"
"        Dear <t t-out=\"order.get('supplier_name', '')\">Laurie Poiret</t>,\n"
"        </p><p>\n"
"        Here is, today orders for <t t-out=\"order.get('company_name', '')\">LunchCompany</t>:\n"
"        </p>\n"
"\n"
"        <t t-if=\"sites\">\n"
"            <br/>\n"
"            <p>Location</p>\n"
"            <t t-foreach=\"site\" t-as=\"site\">\n"
"                <p><t t-out=\"site['name'] or ''\"/> : <t t-out=\"site['address'] or ''\"/></p>\n"
"            </t>\n"
"            <br/>\n"
"        </t>\n"
"\n"
"        <table>\n"
"            <thead>\n"
"                <tr style=\"background-color:rgb(233,232,233);\">\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Product</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Comments</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Person</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Site</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Qty</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Price</strong></th>\n"
"                </tr>\n"
"            </thead>\n"
"            <tbody>\n"
"                <tr t-foreach=\"lines\" t-as=\"line\">\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['product'] or ''\">Sushi salmon</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\">\n"
"                    <t t-if=\"line['toppings']\">\n"
"                        <t t-out=\"line['toppings'] or ''\">Soy sauce</t>\n"
"                    </t>\n"
"                    <t t-if=\"line['note']\">\n"
"                        <div style=\"color: rgb(173,181,189);\" t-out=\"line['note'] or ''\">With wasabi.</div>\n"
"                    </t>\n"
"                    </td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['username'] or ''\">lap</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['site'] or ''\">Office 1</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"line['quantity'] or ''\">10</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"format_amount(line['price'], currency) or ''\">$ 1.00</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\"><strong>Total</strong></td>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\" align=\"right\"><strong t-out=\"format_amount(order['amount_total'], currency) or ''\">$ 10.00</strong></td>\n"
"                </tr>\n"
"            </tbody>\n"
"        </table>\n"
"\n"
"        <p>Do not hesitate to contact us if you have any questions.</p>\n"
"    </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <t t-if=\"user.company_id.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <t t-if=\"user.company_id.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Comanda de dinar</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not user.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"    <div>\n"
"        <t t-set=\"lines\" t-value=\"ctx.get('lines', [])\"/>\n"
"        <t t-set=\"order\" t-value=\"ctx.get('order')\"/>\n"
"        <t t-set=\"currency\" t-value=\"user.env['res.currency'].browse(order.get('currency_id'))\"/>\n"
"        <p>\n"
"        Benvolgut/da <t t-out=\"order.get('supplier_name', '')\">Laurie Poiret</t>:\n"
"        </p><p>\n"
"        A continuació es mostren les comandes d'avui per a <t t-out=\"order.get('company_name', '')\">EmpresaDeDinars</t>:\n"
"        </p>\n"
"\n"
"        <t t-if=\"sites\">\n"
"            <br/>\n"
"            <p>Ubicació</p>\n"
"            <t t-foreach=\"site\" t-as=\"site\">\n"
"                <p><t t-out=\"site['name'] or ''\"/>: <t t-out=\"site['address'] or ''\"/></p>\n"
"            </t>\n"
"            <br/>\n"
"        </t>\n"
"\n"
"        <table>\n"
"            <thead>\n"
"                <tr style=\"background-color:rgb(233,232,233);\">\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Producte</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Comentaris</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Persona</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Lloc web</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Quant.</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Preu</strong></th>\n"
"                </tr>\n"
"            </thead>\n"
"            <tbody>\n"
"                <tr t-foreach=\"lines\" t-as=\"line\">\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['product'] or ''\">Sushi de salmó</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\">\n"
"                    <t t-if=\"line['toppings']\">\n"
"                        <t t-out=\"line['toppings'] or ''\">Salsa de soja</t>\n"
"                    </t>\n"
"                    <t t-if=\"line['note']\">\n"
"                        <div style=\"color: rgb(173,181,189);\" t-out=\"line['note'] or ''\">Amb wasabi.</div>\n"
"                    </t>\n"
"                    </td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['username'] or ''\">lap</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['site'] or ''\">Oficina 1</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"line['quantity'] or ''\">10</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"format_amount(line['price'], currency) or ''\">1 €</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\"><strong>Total</strong></td>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\" align=\"right\"><strong t-out=\"format_amount(order['amount_total'], currency) or ''\">10 €</strong></td>\n"
"                </tr>\n"
"            </tbody>\n"
"        </table>\n"
"\n"
"        <p>No dubti a contactar amb nosaltres si té alguna pregunta.</p>\n"
"    </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">LaSevaEmpresa</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <t t-if=\"user.company_id.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <t t-if=\"user.company_id.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Desenvolupat per <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid ""
"A cashmove can either be an expense or a payment.<br>\n"
"            An expense is automatically created at the order receipt.<br>\n"
"            A payment represents the employee reimbursement to the company."
msgstr ""
"Un moviment en efectiu pot ser una despesa o un pagament.<br>\n"
"            Es crea automàticament una despesa al rebut de comanda.<br>\n"
"            Un pagament representa el reemborsament de l'empleat a l'empresa."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "A product is defined by its name, category, price and vendor."
msgstr "Un producte es defineix pel seu nom, categoria, preu i venedor."

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__am
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__am
msgid "AM"
msgstr "AM"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__active
#: model:ir.model.fields,field_description:lunch.field_lunch_order__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__active
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__active
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Active"
msgstr "Actiu"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Add To Cart"
msgstr "Afegir a la cistella"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__address
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Address"
msgstr "Adreça"

#. module: lunch
#: model:res.groups,name:lunch.group_lunch_manager
msgid "Administrator"
msgstr "Administrador"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__name
msgid "Alert Name"
msgstr "Nom de l'alerta"

#. module: lunch
#: model:lunch.alert,name:lunch.alert_office_3
msgid "Alert for Office 3"
msgstr "Avís per a l'Office 3"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__alert
msgid "Alert in app"
msgstr "Alerta en l'aplicació"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_alert_menu
msgid "Alerts"
msgstr "Alertes"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "Already Paid"
msgstr "Ja pagat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__amount
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__amount
msgid "Amount"
msgstr "Import"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_search
msgid "Archived"
msgstr "Arxivat"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_1
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_2
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_3
msgid "Are extras available for this product"
msgstr "Hi ha extres disponibles per a aquest producte"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_supplier_automatic_email_time_range
msgid "Automatic Email Sending Time should be between 0 and 12"
msgstr ""
"El temps d'enviament automàtic de correu electrònic ha de ser entre 0 i 12"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Availability"
msgstr "Disponibilitat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_on_date
msgid "Available On Date"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Available Today"
msgstr "Disponible avui"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_1
msgid "Available Toppings 1"
msgstr "Toppings disponibles 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_2
msgid "Available Toppings 2"
msgstr "Toppings disponibles 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_3
msgid "Available Toppings 3"
msgstr "Toppings disponibles 3"

#. module: lunch
#: model:lunch.product,name:lunch.product_bacon
#: model:lunch.product,name:lunch.product_bacon_0
msgid "Bacon"
msgstr "Bacon"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_bacon
#: model_terms:lunch.product,description:lunch.product_bacon_0
msgid "Beef, Bacon, Salad, Cheddar, Fried Onion, BBQ Sauce"
msgstr ""
"Carn de boví, bacon, amanida, formatge cheddar, ceba fregida, salsa barbacoa"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_burger_0
#: model_terms:lunch.product,description:lunch.product_cheeseburger
msgid "Beef, Cheddar, Salad, Fried Onions, BBQ Sauce"
msgstr ""
"Carn de boví, formatge cheddar, amanida, cebes fregides, salsa barbacoa"

#. module: lunch
#: model:lunch.product,name:lunch.product_Bolognese
msgid "Bolognese Pasta"
msgstr "Pasta bolonyesa"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_country
msgid "Brie, Honey, Walnut Kernels"
msgstr "Brie, mel i nous en gra"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_burger
msgid "Burger"
msgstr "Hamburguesa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "By Employee"
msgstr "Per empleat/da"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "By User"
msgstr "Per usuari"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Cancel"
msgstr "Cancel·la"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__cancelled
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_supplier.py:0
msgid "Cannot send an email to this supplier!"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_action_payment
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_payment
msgid "Cash Moves"
msgstr "Moviments d'efectiu"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_cashmove_report
msgid "Cashmoves report"
msgstr "Informe sobre els moviments de caixa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Categories"
msgstr "Categories"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Category"
msgstr "Categoria"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__chat
msgid "Chat notification"
msgstr "Notificació de xat"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_ham
msgid "Cheese And Ham"
msgstr "Formatge i pernil"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_burger_0
#: model:lunch.product,name:lunch.product_cheeseburger
msgid "Cheese Burger"
msgstr "Hamburguesa de formatge"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_ham
msgid "Cheese, Ham, Salad, Tomatoes, cucumbers, eggs"
msgstr "Formatge, pernil, amanida, tomàquets, cogombres, ous"

#. module: lunch
#: model:lunch.product,name:lunch.product_chicken_curry
msgid "Chicken Curry"
msgstr "Curri de pollastre"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_chirashi
msgid "Chirashi"
msgstr "Chirashi"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__city
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "City"
msgstr "Ciutat"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid ""
"Click on the <span class=\"fa fa-phone text-success\" title=\"Order button\"></span> to announce that the order is ordered.<br>\n"
"            Click on the <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> to announce that the order is received.<br>\n"
"            Click on the <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> red X to announce that the order isn't available."
msgstr ""
"Feu clic a <span class=\"fa fa-phone text-success\" title=\"Order button\"></span>per a anunciar que l'ordre està ordenada.<br>\n"
"            Feu clic a <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> per a anunciar que s'ha rebut la comanda.<br>\n"
"            Feu clic a <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span>X vermella per a anunciar que la comanda no està disponible. "

#. module: lunch
#: model:lunch.product,name:lunch.product_club
#: model:lunch.product,name:lunch.product_club_0
msgid "Club"
msgstr "Club"

#. module: lunch
#: model:lunch.product,name:lunch.product_coke_0
msgid "Coca Cola"
msgstr "Coca Cola"

#. module: lunch
#: model:ir.model,name:lunch.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__company_id
msgid "Company"
msgstr "Empresa"

#. module: lunch
#: model:ir.model,name:lunch.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_config
msgid "Configuration"
msgstr "Configuració"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/mixins/lunch_renderer_mixin.js:0
msgid "Configure Your Order"
msgstr "Configura la comanda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Confirm"
msgstr "Confirmar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Confirm Orders"
msgstr "Confirma les comandes"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_control_accounts
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_control_accounts
msgid "Control Accounts"
msgstr "Control de Comptes"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_control_suppliers
#: model:ir.ui.menu,name:lunch.lunch_order_menu_control_suppliers
msgid "Control Vendors"
msgstr "Control de Proveïdors"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__country_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Country"
msgstr "País"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid "Create a new payment"
msgstr "Crea un pagament nou"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Create a new product category"
msgstr "Crea una categoria de producte nova"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "Create a new product for lunch"
msgstr "Crear un nou producte per a menjar"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_alert_action
msgid "Create new lunch alerts"
msgstr "Crear nuevas alertas para el menjar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_date
msgid "Created on"
msgstr "Creat el"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__cron_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__cron_id
msgid "Cron"
msgstr "Cron"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__currency_id
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Currently inactive"
msgstr "Actualment inactiu"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__date
msgid "Date"
msgstr "Data"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__delivery
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__delivery
msgid "Delivery"
msgstr "Lliurament"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__description
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__description
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_description
#: model:ir.model.fields,field_description:lunch.field_lunch_product__description
msgid "Description"
msgstr "Descripció"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Discard"
msgstr "Descartar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mode
msgid "Display"
msgstr "Mostrar "

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_add_button
msgid "Display Add Button"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_location__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_reorder_button
msgid "Display Reorder Button"
msgstr "Mostrar botó de tornar a demanar"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_drinks
msgid "Drinks"
msgstr "Begudes"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "Edit order"
msgstr "Modifica la comanda"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__mail
msgid "Email"
msgstr "Correu electrònic"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_month
msgid "Employee who ordered last month"
msgstr "Empleat que va ordenar el mes passat"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_week
msgid "Employee who ordered last week"
msgstr "Empleat que va ordenar la setmana passada"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_year
msgid "Employee who ordered last year"
msgstr "Empleat que va ordenar l'any passat"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__everyone
msgid "Everyone"
msgstr "Tothom"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_1
msgid "Extra 1 Label"
msgstr "Etiqueta extra 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_1
msgid "Extra 1 Quantity"
msgstr "Quantitat extra 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_2
msgid "Extra 2 Label"
msgstr "Etiqueta extra 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_2
msgid "Extra 2 Quantity"
msgstr "Quantitat extra 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_3
msgid "Extra 3 Label"
msgstr "Etiqueta extra 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_3
msgid "Extra 3 Quantity"
msgstr "Quantitat extra 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_toppings
msgid "Extras"
msgstr "Extres"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_1
msgid "Extras 1"
msgstr "Extres 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_2
msgid "Extras 2"
msgstr "Extres 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_3
msgid "Extras 3"
msgstr "Extres 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__favorite_lunch_product_ids
msgid "Favorite Lunch Product"
msgstr "Producte favorit per a l'esmorzar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__favorite_user_ids
msgid "Favorite User"
msgstr "Usuari preferit"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "Adreça e-mail formatada \"Nom <email@domain>\""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email_formatted
msgid "Formatted Email"
msgstr "Correu electrònic formatat"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_italiana
msgid "Fresh Tomatoes, Basil, Mozzarella"
msgstr "Tomàquets frescos, alfàbrega, mozzarella"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__fri
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__fri
msgid "Fri"
msgstr "Dv"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Friday"
msgstr "Divendres"

#. module: lunch
#: model:lunch.product,name:lunch.product_gouda
msgid "Gouda Cheese"
msgstr "Formatge Gouda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_club
#: model_terms:lunch.product,description:lunch.product_club_0
msgid "Ham, Cheese, Vegetables"
msgstr "Pernil, formatge, verdures"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: lunch
#: model:ir.module.category,description:lunch.module_lunch_category
msgid ""
"Helps you handle your lunch needs, if you are a manager you will be able to "
"create new products, cashmoves and to confirm or cancel orders."
msgstr ""
"Us ajuda a gestionar les vostres necessitats de dinar, si sou un gestor "
"podreu crear nous productes, caixers automàtics i confirmar o cancel·lar "
"comandes."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Here you can access all categories for the lunch products."
msgstr "Aquí pot accedir a totes les categories de productes per menjar."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Here you can see today's orders grouped by vendors."
msgstr "Aquí podeu veure les ordres d'avui agrupades pels venedors."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid ""
"Here you can see your cash moves.<br>A cash move can either be an expense or a payment.\n"
"            An expense is automatically created when an order is received while a payment is a reimbursement to the company encoded by the manager."
msgstr ""
"Aquí podeu veure els vostres moviments en efectiu.<br>Un moviment en efectiu pot ser una despesa o un pagament.\n"
"            Una despesa es crea automàticament quan es rep una comanda mentre que un pagament és un reemborsament a l'empresa codificada pel gestor."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__id
#: model:ir.model.fields,field_description:lunch.field_lunch_location__id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__id
msgid "ID"
msgstr "ID"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1920
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1920
msgid "Image"
msgstr "Imatge"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1024
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1024
msgid "Image 1024"
msgstr "Imatge 1024"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_128
msgid "Image 128"
msgstr "Imatge 128"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_1920
msgid "Image 1920"
msgstr "Imatge 1920"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_256
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_256
msgid "Image 256"
msgstr "Image 256"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_512
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_512
msgid "Image 512"
msgstr "Imatge 512"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Information, allergens, ..."
msgstr "Informació, al·lèrgens... "

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__available_today
msgid "Is Displayed Today"
msgstr "Es mostra avui"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_favorite
msgid "Is Favorite"
msgstr "És preferit"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_new
msgid "Is New"
msgstr "És nou"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__last_lunch_location_id
msgid "Last Lunch Location"
msgstr "Darrera ubicació de la lonxa"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__last_order_date
msgid "Last Order Date"
msgstr "Data de l'última comanda"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__location_ids
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_location_ids
msgid "Location"
msgstr "Ubicació"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__name
msgid "Location Name"
msgstr "Nom ubicació"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_location_menu
msgid "Locations"
msgstr "Ubicacions"

#. module: lunch
#: model:ir.module.category,name:lunch.module_lunch_category
#: model:ir.ui.menu,name:lunch.menu_lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Lunch"
msgstr "Menjar"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_alert
msgid "Lunch Alert"
msgstr "Alerta de menjar"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_alert_action
msgid "Lunch Alerts"
msgstr "Alertes per menjar"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_cashmove.py:0
#: code:addons/lunch/report/lunch_cashmove_report.py:0
#: model:ir.model,name:lunch.model_lunch_cashmove
msgid "Lunch Cashmove"
msgstr "Lunch Cashmove"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_topping
msgid "Lunch Extras"
msgstr "Extres de menjar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__lunch_location_id
msgid "Lunch Location"
msgstr "Ubicació de menjar"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_location_action
#: model:ir.model,name:lunch.model_lunch_location
msgid "Lunch Locations"
msgstr "Ubicacions d'ancoratge"

#. module: lunch
#: model:lunch.product,name:lunch.product_maki
msgid "Lunch Maki 18pc"
msgstr "Esmorzar Maki 18pc"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_company__lunch_minimum_threshold
msgid "Lunch Minimum Threshold"
msgstr "Llindar mínim de menjar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_company__lunch_notify_message
msgid "Lunch Notify Message"
msgstr "Missatge de notificació de desconnexió"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_order
msgid "Lunch Order"
msgstr "Comanda menjar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Lunch Overdraft"
msgstr ""

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product
msgid "Lunch Product"
msgstr "Producte de menjar"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product_category
msgid "Lunch Product Category"
msgstr "Categoria del producte d'anul·lació"

#. module: lunch
#: model:lunch.product,name:lunch.product_salmon
msgid "Lunch Salmon 20pc"
msgstr "Menjar Salmó 20pc"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_supplier
msgid "Lunch Supplier"
msgstr "Proveïdor de menjar"

#. module: lunch
#: model:lunch.product,name:lunch.product_temaki
msgid "Lunch Temaki mix 3pc"
msgstr "Menjar Temaki mix 3pc"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
msgid "Lunch notification"
msgstr "Notificació d'ancoratge"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__company_lunch_notify_message
msgid "Lunch notification message"
msgstr "Missatge de notificació d'anul·lació"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_cancel
msgid "Lunch: Cancel meals"
msgstr "Lunch: cancel·la els àpats"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_confirm
msgid "Lunch: Receive meals"
msgstr "Lunch: rep els àpats"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_notify
msgid "Lunch: Send notifications"
msgstr "Lunch: envia notificacions"

#. module: lunch
#: model:mail.template,name:lunch.lunch_order_mail_supplier
msgid "Lunch: Supplier Order"
msgstr "Lunch: Comanda del proveïdor"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_alert_cron_sa_228
msgid "Lunch: alert chat notification (Alert for Office 3)"
msgstr "Lunch: alerta la notificació del xat (avís per a l'Office 3)"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_229
msgid "Lunch: send automatic email to Coin gourmand"
msgstr "Lunch: envia un correu electrònic automàtic a Coin gourmand"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_224
msgid "Lunch: send automatic email to Lunch Supplier"
msgstr "Lunch: envia un correu electrònic automàtic al proveïdor de Lunch"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_230
msgid "Lunch: send automatic email to Pizza Inn"
msgstr "Lunch: envia un correu electrònic automàtic a Pizza Inn"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_232
msgid "Lunch: send automatic email to Sushi Shop"
msgstr "Lunch: envia un correu electrònic automàtic a la botiga Sushi"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_231
msgid "Lunch: send automatic email to The Corner"
msgstr "Lunch: envia un correu electrònic automàtic a la cantonada"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr "Moneda principal de la companyia."

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_admin
msgid "Manager"
msgstr "Gestor"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__company_lunch_minimum_threshold
msgid "Maximum Allowed Overdraft"
msgstr "Sobregir màxim permès"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Maximum overdraft that your employees can reach"
msgstr "Despeses màximes que els empleats poden arribar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__message
msgid "Message"
msgstr "Missatge"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__moment
msgid "Moment"
msgstr "Moment"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mon
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__mon
msgid "Mon"
msgstr "Dl"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Monday"
msgstr "Dilluns"

#. module: lunch
#: model:lunch.product,name:lunch.product_mozzarella
msgid "Mozzarella"
msgstr "Mozzarella"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_mozzarella
msgid "Mozzarella, Pesto, Tomatoes"
msgstr "Mozzarella, Pesto, Tomàquets"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_account
msgid "My Account"
msgstr "El meu compte"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_form
msgid "My Account History"
msgstr "Historial del meu compte"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "My Account grouped"
msgstr "El meu compte agrupat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_title
msgid "My Lunch"
msgstr "El meu esmorzar"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_tree
msgid "My Order History"
msgstr "Història de la meva ordre"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "My Orders"
msgstr "Les meves comandes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__name
msgid "Name"
msgstr "Nom"

#. module: lunch
#: model:lunch.product,name:lunch.product_Napoli
msgid "Napoli Pasta"
msgstr "Pasta Napoli"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_product_kanban_order
msgid "New"
msgstr "Nou"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_form
msgid "New Order"
msgstr "Nova ordre"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__new_until
msgid "New Until"
msgstr "Nou fins"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__no_delivery
msgid "No Delivery"
msgstr "No hi ha lliurament"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid "No cash move yet"
msgstr "Encara no hi ha cap moviment en efectiu"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/views/no_content_helper.xml:0
msgid "No location found"
msgstr ""

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "No lunch location available."
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "No lunch order yet"
msgstr "Encara no s'ha demanat menjar"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid "No previous order found"
msgstr "No s'ha trobat cap ordre anterior"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__0_more
msgid "None or More"
msgstr "Cap o més"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Not Received"
msgstr "No s'ha rebut"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__note
msgid "Notes"
msgstr "Notes"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Nothing to order today"
msgstr "Avui no hi ha res per a demanar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_moment
msgid "Notification Moment"
msgstr "Moment de notificació"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_time
msgid "Notification Time"
msgstr "Temps de notificació"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_alert_notification_time_range
msgid "Notification time must be between 0 and 12"
msgstr "El temps de notificació ha d'estar entre 0 i 12"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__notified
msgid "Notified"
msgstr "Notificat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1_more
msgid "One or More"
msgstr "Un o més"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1
msgid "Only One"
msgstr "Només un"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "Order"
msgstr "Ordre"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__date
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Order Date"
msgstr "Data comanda"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__order_deadline_passed
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__order_deadline_passed
msgid "Order Deadline Passed"
msgstr "S'ha superat la data límit de la comanda"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "Order Now"
msgstr "Ordena ara"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__automatic_email_time
msgid "Order Time"
msgstr "Temps de comanda"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action_order
msgid "Order Your Lunch"
msgstr "Demani el seu esmorzar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Order lines List"
msgstr ""

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__ordered
msgid "Ordered"
msgstr "Encarregat"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Orders"
msgstr "Comandes"

#. module: lunch
#: model:mail.template,subject:lunch.lunch_order_mail_supplier
msgid "Orders for {{ ctx['order']['company_name'] }}"
msgstr "Comandes per {{ ctx['ordre']['nomempresa'] }}"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Overdraft"
msgstr "S'ha excedit"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__pm
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__pm
msgid "PM"
msgstr "PM"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pasta
msgid "Pasta"
msgstr "Pasta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
msgid "Payment"
msgstr "Pagament"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""
"Els pagaments s'utilitzen per registrar els moviments líquids. Podeu "
"processar aquests pagaments segons els vostres mitjans o usant "
"instal·lacions instal· lades."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__phone
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__phone
msgid "Phone"
msgstr "Telèfon"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pizza
msgid "Pizza"
msgstr "Pizza"

#. module: lunch
#: model:lunch.product,name:lunch.product_funghi
msgid "Pizza Funghi"
msgstr "Pizza Funghi"

#. module: lunch
#: model:lunch.product,name:lunch.product_italiana
msgid "Pizza Italiana"
msgstr "Pizza Italiana"

#. module: lunch
#: model:lunch.product,name:lunch.product_margherita
#: model:lunch.product,name:lunch.product_pizza_0
msgid "Pizza Margherita"
msgstr "Pizza Margherita"

#. module: lunch
#: model:lunch.product,name:lunch.product_vege
msgid "Pizza Vegetarian"
msgstr "Pizza Vegetariana"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/views/no_content_helper.xml:0
msgid "Please create a location to start ordering."
msgstr ""

#. module: lunch
#: model_terms:lunch.alert,message:lunch.alert_office_3
msgid "Please order"
msgstr "Si us plau, demani"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__price
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__price
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Price"
msgstr "Preu"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product"
msgstr "Producte"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_available_at
msgid "Product Availability"
msgstr "Disponibilitat de producte"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_category_action
#: model:ir.ui.menu,name:lunch.lunch_product_category_menu
msgid "Product Categories"
msgstr "Categories de producte"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
msgid "Product Categories Form"
msgstr "Formulari de categories de productes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__name
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
msgid "Product Category"
msgstr "Categoria del producte"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__product_count
msgid "Product Count"
msgstr "Nombre de productes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__product_image
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_product_kanban
msgid "Product Image"
msgstr "Imatge de Producte"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__name
msgid "Product Name"
msgstr "Nom producte"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product Search"
msgstr "Cerca de productes"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
msgid "Product is no longer available."
msgstr "El producte ja no està disponible."

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action
#: model:ir.actions.act_window,name:lunch.lunch_product_action_statbutton
#: model:ir.ui.menu,name:lunch.lunch_product_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_kanban
msgid "Products"
msgstr "Productes"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
msgid "Products Form"
msgstr "Formulari de productes"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_tree
msgid "Products List"
msgstr "Llista de productes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__quantity
msgid "Quantity"
msgstr "Quantitat"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Re-order"
msgstr "Tornar a demanar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Receive"
msgstr "Rebre"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Received"
msgstr "Rebuda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Reception notification"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__recipients
msgid "Recipients"
msgstr "Destinataris"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid "Register a payment"
msgstr "Registrar un pagament"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Reset"
msgstr "Reinicia"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__responsible_id
msgid "Responsible"
msgstr "Responsable"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: lunch
#: model:lunch.product,name:lunch.product_chirashi
msgid "Salmon and Avocado"
msgstr "Salmó i alvocat"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sandwich
msgid "Sandwich"
msgstr "Sandwich"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sat
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sat
msgid "Sat"
msgstr "Ds"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Saturday"
msgstr "Dissabte"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Search"
msgstr "Cercar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "Send"
msgstr "Enviar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Send Notification"
msgstr "Envia una notificació"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__send_by
msgid "Send Order By"
msgstr "Envia la comanda per"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Send Orders"
msgstr "Envia comandes"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Send this message to your users when their order has been delivered."
msgstr ""
"Envieu aquest missatge als vostres usuaris quan s'hagi lliurat la comanda."

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__sent
msgid "Sent"
msgstr "Enviat"

#. module: lunch
#: model:mail.template,description:lunch.lunch_order_mail_supplier
msgid "Sent to vendor with the order of the day"
msgstr "Enviat al proveïdor amb l'ordre del dia"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_config_settings_action
#: model:ir.ui.menu,name:lunch.lunch_settings_menu
msgid "Settings"
msgstr "Configuració"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__show_confirm_button
msgid "Show Confirm Button"
msgstr "Mostra el botó de confirmació"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__show_order_button
msgid "Show Order Button"
msgstr "Mostra el botó de comanda"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__until
msgid "Show Until"
msgstr "Mostra fins"

#. module: lunch
#: model:lunch.product,name:lunch.product_spicy_tuna
msgid "Spicy Tuna"
msgstr "Tonyina picant"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__state_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "State"
msgstr "Estat"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__state
msgid "Status"
msgstr "Estat"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street
msgid "Street"
msgstr "Carrer"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street 2..."
msgstr "Carrer 2..."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street..."
msgstr "Carrer..."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street2
msgid "Street2"
msgstr "2a adreça"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "Summary of all lunch orders, grouped by vendor and by date."
msgstr ""
"Resum de totes les comandes de menjar, agrupats per proveïdor i per data."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sun
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sun
msgid "Sun"
msgstr "Dg"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Sunday"
msgstr "Diumenge"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__supplier_id
msgid "Supplier"
msgstr "Proveïdor"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sushi
msgid "Sushi"
msgstr "Sushi"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_temaki
msgid "Temaki"
msgstr "Temaki"

#. module: lunch
#: model:lunch.product,name:lunch.product_country
msgid "The Country"
msgstr "El País"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_product.py:0
msgid ""
"The following product categories are archived. You should either unarchive the categories or change the category of the product.\n"
"%s"
msgstr ""
"Les categories de producte següents estan arxivades. Hauríeu de desarchivar les categories o bé canviar la categoria del producte.\n"
"%s"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_product.py:0
msgid ""
"The following suppliers are archived. You should either unarchive the suppliers or change the supplier of the product.\n"
"%s"
msgstr ""
"S'arxiven els proveïdors següents. Cal desarxivar els proveïdors o canviar el proveïdor del producte.\n"
"%s"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_product_category__product_count
msgid "The number of products related to this category"
msgstr "El nombre de productes relacionats amb aquesta categoria"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "The orders for this vendor have already been sent."
msgstr "Les comandes d'aquest proveïdor ja s'han enviat."

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_supplier.py:0
msgid "The orders have been confirmed!"
msgstr "Les ordres han estat confirmades!"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_supplier.py:0
msgid "The orders have been sent!"
msgstr "S'han enviat les ordres!"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__responsible_id
msgid ""
"The responsible is the person that will order lunch for everyone. It will be"
" used as the 'from' when sending the automatic email."
msgstr ""
"El responsable és la persona que demanarà menjar per tothom. S'utilitzarà "
"per a omplir el \"de:\" quan s'enviï el correu electrònic automàtic."

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
msgid ""
"The vendor related to this order is not available at the selected date."
msgstr ""

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
msgid "The vendor related to this order is not available today."
msgstr "El venedor relacionat amb aquesta comanda no està disponible avui."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid ""
"There is no previous order recorded. Click on \"My Lunch\" and then create a"
" new lunch order."
msgstr ""
"No hi ha cap comanda anterior enregistrada. Feu clic a \"El meu dinar\" i "
"després creeu una nova comanda de dinar."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid "There is no product available today"
msgstr "Avui no hi ha cap producte disponible"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__recurrency_end_date
msgid "This field is used in order to "
msgstr "Aquest camp s'utilitza per a"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_today
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_today
msgid "This is True when if the supplier is available today"
msgstr "Això és cert quan si el proveïdor està disponible avui"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__thu
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__thu
msgid "Thu"
msgstr "Dj"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Thursday"
msgstr "Dijous"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tz
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tz
msgid "Timezone"
msgstr "Zona horària"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__new
msgid "To Order"
msgstr "Quantitat a demanar"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "To Pay"
msgstr "A pagar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_payment_dialog
msgid "To add some money to your wallet, please contact your lunch manager."
msgstr ""
"Per afegir una mica de diners a la teva cartera, posa't en contacte amb el "
"teu gerent de dinar."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_location_action
msgid "To see some locations, create one using the create button"
msgstr ""
"Per a veure algunes ubicacions, creeu-ne una utilitzant el botó de creació"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid ""
"To see some products, check if your vendors are available today and that you"
" have configured some products"
msgstr ""
"Per a veure alguns productes, comproveu si els proveïdors estan disponibles "
"avui i que heu configurat alguns productes."

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Today"
msgstr "Avui"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_by_supplier
#: model:ir.ui.menu,name:lunch.lunch_order_menu_by_supplier
msgid "Today's Orders"
msgstr "Ordres d'avui"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_4formaggi
msgid "Tomato sauce, Olive oil, Fresh Tomatoes, Onions, Vegetables, Parmesan"
msgstr ""
"Salsa de tomàquet, Oli d'oliva, Tomàquets frescos, Cebes, Verdures, Parmesà"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_Napoli
msgid "Tomatoes, Basil"
msgstr "Tomàquets, alfàbrega"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_margherita
#: model_terms:lunch.product,description:lunch.product_pizza_0
msgid "Tomatoes, Mozzarella"
msgstr "Tomàquets, Mozzarella"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_vege
msgid "Tomatoes, Mozzarella, Mushrooms, Peppers, Olives"
msgstr "Tomàquets, mozzarella, xampinyons, pebrots, olives"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_funghi
msgid "Tomatoes, Mushrooms, Mozzarella"
msgstr "Tomàquets, xampinyons, mozzarella"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__topping_category
msgid "Topping Category"
msgstr "Categoria superior"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_1
msgid "Topping Ids 1"
msgstr "Id. superior 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_2
msgid "Topping Ids 2"
msgstr "Id. superior 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_3
msgid "Topping Ids 3"
msgstr "Ids superiors 3"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Total"
msgstr "Total"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__price
msgid "Total Price"
msgstr "Preu total"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tue
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tue
msgid "Tue"
msgstr "Dt"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Tuesday"
msgstr "Dimarts"

#. module: lunch
#: model:lunch.product,name:lunch.product_tuna
msgid "Tuna"
msgstr "Tonyina"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_tuna
msgid "Tuna, Mayonnaise"
msgstr "Tonyina, maionesa"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__recurrency_end_date
msgid "Until"
msgstr "Fins"

#. module: lunch
#: model:ir.model,name:lunch.model_res_users
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__user_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "User"
msgstr "Usuari"

#. module: lunch
#: model:res.groups,name:lunch.group_lunch_user
msgid "User : Order your meal"
msgstr "Usuari: comanda el menjar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__partner_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Vendor"
msgstr "Proveïdor "

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Vendor Orders by Date"
msgstr "Comandes del proveïdor per data"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_vendors_action
#: model:ir.ui.menu,name:lunch.lunch_vendors_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Vendors"
msgstr "Proveïdors "

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__wed
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__wed
msgid "Wed"
msgstr "Dc"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Wednesday"
msgstr "Dimecres"

#. module: lunch
#. odoo-python
#: code:addons/lunch/controllers/main.py:0
msgid ""
"You are trying to impersonate another user, but this can only be done by a "
"lunch manager"
msgstr ""
"Estàs intentant suplantar a un altre usuari, però això només ho pot fer un "
"gestor de menjars"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
msgid "You have to order one and only one %s"
msgstr "Heu de demanar un i solament un %s"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
msgid "You should order at least one %s"
msgstr "Hauríeu de demanar almenys un %s"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "Your Account"
msgstr "El vostre compte"

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "Your Cart ("
msgstr "El vostre cotxe ("

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_alert.py:0
msgid "Your Lunch Order"
msgstr ""

#. module: lunch
#. odoo-javascript
#: code:addons/lunch/static/src/components/lunch_dashboard.xml:0
msgid "Your Order"
msgstr "La Teva Comanda"

#. module: lunch
#. odoo-python
#: code:addons/lunch/models/lunch_order.py:0
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid ""
"Your wallet does not contain enough money to order that. To add some money "
"to your wallet, please contact your lunch manager."
msgstr ""
"La cartera no conté prou diners per a demanar-ho. Per afegir una mica de "
"diners a la teva cartera, posa't en contacte amb el teu gerent de dinar."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "ZIP"
msgstr "C.P."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__zip_code
msgid "Zip"
msgstr "Codi Postal"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "alert form"
msgstr "formulari d'alerta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_form
msgid "cashmove form"
msgstr "formulari de moviment"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
msgid "cashmove list"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "e.g. Order before 11am"
msgstr "p. ex. Ordre abans de 11am"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "e.g. The Pizzeria Inn"
msgstr "p. ex. La Pizzería Inn"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "lunch cashmove"
msgstr "dinar en efectiu"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "lunch employee payment"
msgstr "dinar el pagament dels empleats"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "on"
msgstr "en"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "to"
msgstr "fins"
