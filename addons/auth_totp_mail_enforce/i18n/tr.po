# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail_enforce
# 
# Translators:
# emre <PERSON>tem, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
".\n"
"                <br/>"
msgstr ""
".\n"
"                <br/>"

#. module: auth_totp_mail_enforce
#: model:mail.template,body_html:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    Dear <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"    <p>Someone is trying to log in into your account with a new device.</p>\n"
"    <ul>\n"
"        <t t-set=\"not_available\">N/A</t>\n"
"        <li>Location: <t t-out=\"ctx.get('location') or not_available\"/></li>\n"
"        <li>Device: <t t-out=\"ctx.get('device') or not_available\"/></li>\n"
"        <li>Browser: <t t-out=\"ctx.get('browser') or not_available\"/></li>\n"
"        <li>IP address: <t t-out=\"ctx.get('ip') or not_available\"/></li>\n"
"    </ul>\n"
"    <p>If this is you, please enter the following code to complete the login:</p>\n"
"    <t t-set=\"code_expiration\" t-value=\"object._get_totp_mail_code()\"/>\n"
"    <t t-set=\"code\" t-value=\"code_expiration[0]\"/>\n"
"    <t t-set=\"expiration\" t-value=\"code_expiration[1]\"/>\n"
"    <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <span t-out=\"code\" style=\"background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;\"/>\n"
"    </div>\n"
"    <small>Please note that this code expires in <t t-out=\"expiration\"/>.</small>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        If you did NOT initiate this log-in,\n"
"        you should immediately change your password to ensure account security.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        We also strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"            Activate my two-factor authentication\n"
"        </a>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    Sayın <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"    <p>Birisi hesabınıza yeni bir cihazla giriş yapmaya çalışıyor.</p>\n"
"    <ul>\n"
"        <t t-set=\"not_available\">Bilgi yok</t>\n"
"        <li>Konum: <t t-out=\"ctx.get('location') or not_available\"/></li>\n"
"        <li>Cihaz: <t t-out=\"ctx.get('device') or not_available\"/></li>\n"
"        <li>Tarayıcı: <t t-out=\"ctx.get('browser') or not_available\"/></li>\n"
"        <li>IP adresi: <t t-out=\"ctx.get('ip') or not_available\"/></li>\n"
"    </ul>\n"
"    <p>Giriş yapmaya çalışan sizseniz, giriş işlemini tamamlamak için lütfen aşağıdaki kodu girin:</p>\n"
"    <t t-set=\"code_expiration\" t-value=\"object._get_totp_mail_code()\"/>\n"
"    <t t-set=\"code\" t-value=\"code_expiration[0]\"/>\n"
"    <t t-set=\"expiration\" t-value=\"code_expiration[1]\"/>\n"
"    <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <span t-out=\"code\" style=\"background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;\"/>\n"
"    </div>\n"
"    <small>Bu kodun geçerliliğini yitireceği süre:<t t-out=\"expiration\"/>.</small>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        Bu oturum açma işlemini siz başlatmadıysanız\n"
"        hesap güvenliğinizi sağlamak için derhal şifrenizi değiştirmelisiniz.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        Ayrıca, hesabınızın güvenliğini sağlamaya yardımcı olması için bir kimlik doğrulayıcı uygulaması kullanarak iki faktörlü kimlik doğrulamayı etkinleştirmenizi şiddetle tavsiye ederiz.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"            İki faktörlü kimlik doğrulamasını etkinleştirme\n"
"        </a>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"<i class=\"fa fa-envelope-o\"/>\n"
"                To login, enter below the six-digit authentication code just sent via email to"
msgstr ""
"<i j=\"0/\">Giriş yapmak için, e-posta yoluyla gönderilen altı haneli kimlik"
" doğrulama kodunu aşağıya girin</i>"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__all_required
msgid "All users"
msgstr "Tüm kullanıcılar"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
msgid "Cannot send email: user %s has no email address."
msgstr ""
"Eposta gönderilemedi: %s isimli kullanıcının eposta adresi bulunmuyor."

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__code_check
msgid "Code Checking"
msgstr "Kod Kontrolü"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__employee_required
msgid "Employees only"
msgstr "Yalnızca çalışanlar"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.res_config_settings_view_form
msgid ""
"Enforce the two-factor authentication by email for employees or for all "
"users (including portal users) if they didn't enable any other two-factor "
"authentication method."
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_enforce
msgid "Enforce two-factor authentication"
msgstr "İki faktörlü kimlik doğrulamayı zorunlu kılma"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__id
msgid "ID"
msgstr "ID"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__ip
msgid "Ip"
msgstr "Ip"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Learn More"
msgstr "Daha Fazla Bilgi"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__limit_type
msgid "Limit Type"
msgstr "Limit türü"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Re-send email"
msgstr "E-postayı yeniden gönder"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__scope
msgid "Scope"
msgstr "Kapsam"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__send_email
msgid "Send Email"
msgstr "E-posta Gönder"

#. module: auth_totp_mail_enforce
#: model:mail.template,name:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Settings: 2Fa New Login"
msgstr "Ayarlar: 2Fa Yeni Giriş"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_auth_totp_rate_limit_log
msgid "TOTP rate limit logs"
msgstr "TOTP hız sınırı kayıtları"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_policy
msgid "Two-factor authentication enforcing policy"
msgstr "İki faktörlü kimlik doğrulama uygulama politikası"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_users
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__user_id
msgid "User"
msgstr "Kullanıcı"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
msgid "Verification failed, please double-check the 6-digit code"
msgstr "Doğrulama başarısız oldu, lütfen 6 haneli kodu tekrar kontrol edin"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"We strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"                <br/>"
msgstr ""
"Hesabınızın güvenliğini sağlamaya yardımcı olmak için bir kimlik doğrulayıcı"
" uygulaması kullanarak iki faktörlü kimlik doğrulamayı etkinleştirmenizi "
"kesinlikle öneririz.                <br/>"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
msgid "You reached the limit of authentication mails sent for your account"
msgstr ""
"Hesabınız için gönderilen kimlik doğrulama postalarının sınırına ulaştınız"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
msgid "You reached the limit of code verifications for your account"
msgstr "Hesabınız için kod doğrulama sınırına ulaştınız"

#. module: auth_totp_mail_enforce
#: model:mail.template,subject:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Your two-factor authentication code"
msgstr "İki faktörlü kimlik doğrulama kodunuz"
