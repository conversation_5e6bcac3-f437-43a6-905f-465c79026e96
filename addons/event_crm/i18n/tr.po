# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_crm
# 
# Translators:
# emre oktem, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_event__lead_count
#: model:ir.model.fields,field_description:event_crm.field_event_registration__lead_count
msgid "# Leads"
msgstr "# Adaylar"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__registration_count
msgid "# Registrations"
msgstr "# Kayıtlar"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_registration.py:0
msgid "(updated)"
msgstr "(güncellendi)"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_view_form
msgid "<span class=\"o_stat_text\"> Attendees</span>"
msgstr "<span class=\"o_stat_text\"> Katılımcılar</span>"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_registration_view_form
msgid "<span class=\"o_stat_text\"> Leads</span>"
msgstr "<span class=\"o_stat_text\"> Adaylar</span>"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__active
msgid "Active"
msgstr "Etkin"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__create
msgid "Attendees are created"
msgstr "Katılımcılar oluşturuldu"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__confirm
msgid "Attendees are registered"
msgstr "Katılımcılar kaydedildi"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__done
msgid "Attendees attended"
msgstr "Katılan katılımcılar"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_tag_ids
msgid "Automatically add these tags to the created leads."
msgstr "Oluşturulan adaylara bu etiketleri otomatik olarak ekleyin."

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_sales_team_id
msgid "Automatically assign the created leads to this Sales Team."
msgstr "Oluşturulan adayları bu Satış Ekibine otomatik olarak atayın."

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_user_id
msgid "Automatically assign the created leads to this Salesperson."
msgstr "Oluşturulan adayları bu Satış Görevlisine otomatik olarak atayın."

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Aww! No Leads created, check your Lead Generation Rules and try again."
msgstr ""
"Üzgünüz! Hiç Müşteri Adayı oluşturulmadı, Aday Oluşturma Kurallarınızı "
"kontrol edin ve tekrar deneyin."

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__company_id
msgid "Company"
msgstr "Firma"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__registration_count
msgid "Counter for the registrations linked to this lead"
msgstr "Bu adayla bağlantılı kayıtlar için sayaç"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_creation_basis
msgid "Create"
msgstr "Oluştur"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_lead_rule_action
msgid "Create a Lead Generation Rule"
msgstr "Bir Aday Oluşturma Kuralı Oluşturun"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_ids
msgid "Created Leads"
msgstr "Oluşturulmuş Adaylar"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Creation Type"
msgstr "Oluşturma Türü"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_creation_trigger
msgid ""
"Creation: at attendee creation;\n"
"Registered: at attendee registration, manually or automatically;\n"
"Attended: when attendance is confirmed and registration set to done;"
msgstr ""
"Oluşturulma: katılımcı oluşturulurken;\n"
"Kayıtlı: katılımcı kaydedildiğinde (manuel veya otomatik);\n"
"Katıldı: katılım onaylandığında ve kayıt tamamlandığında;"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_type
msgid "Default lead type when this rule is applied."
msgstr "Bu kural uygulandığında varsayılan aday türü."

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__display_name
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_event
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__event_id
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_id
msgid "Event"
msgstr "Etkinlik"

#. module: event_crm
#: model:ir.actions.server,name:event_crm.ir_cron_generate_leads_ir_actions_server
msgid "Event CRM: Generate Leads based on Rules"
msgstr "Etkinlik CRM: Kurallara göre aday oluşturun"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_lead_request
msgid "Event Lead Request"
msgstr "Etkinlik Aday Talebi"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_lead_rule
msgid "Event Lead Rules"
msgstr "Etkinlik Aday Kuralları"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_registration
msgid "Event Registration"
msgstr "Etkinlik Kaydı"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_type_ids
msgid "Event Templates"
msgstr "Etkinlik Şablonları"

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.event_registration_action_from_lead
msgid "Event registrations"
msgstr "Etkinlik kayıtları"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__event_id
msgid "Event triggering the rule that created this lead"
msgstr "Bu adayı oluşturan kuralı tetikleyen etkinlik"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_merge_summary_inherit_event_crm
msgid "Event:"
msgstr "Etkinlik:"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_registration_filter
msgid "Filter the attendees that will or not generate leads."
msgstr "Adayları oluşturacak veya oluşturmayacak katılımcıları filtreleyin."

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_type_ids
msgid ""
"Filter the attendees to include those of this specific event category. If "
"not set, no event category restriction will be applied."
msgstr ""
"Katılımcıları bu belirli etkinlik kategorisindekileri içerecek şekilde "
"filtreleyin. Ayarlanmaması durumunda hiçbir etkinlik kategorisi sınırlaması "
"uygulanmayacaktır."

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_id
msgid ""
"Filter the attendees to include those of this specific event. If not set, no"
" event restriction will be applied."
msgstr ""
"Katılımcıları yalnızca bu belirli etkinliğe ait olanlarla sınırlamak için "
"filtreleyin. Ayarlanmazsa, herhangi bir etkinlik kısıtlaması uygulanmaz."

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "For any of these Events"
msgstr "Bu Etkinliklerden herhangi biri için"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_view_form
msgid "Generate Leads"
msgstr "Generate Leads"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Got it! We've noted your request. Your leads will be created soon!"
msgstr ""
"Anlaşıldı! Talebinizi not ettik. Müşteri adaylarınız yakında oluşturulacak! "

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__id
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__id
msgid "ID"
msgstr "ID"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "If the Attendees meet these Conditions"
msgstr "Katılımcıların bu Koşulları karşılaması halinde"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_type__lead
msgid "Lead"
msgstr "Aday"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_tree
msgid "Lead Creation Type"
msgstr "Aday Oluşturma Türü"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "Lead Default Values"
msgstr "Aday Öntanımlı Değerleri"

#. module: event_crm
#: model:ir.ui.menu,name:event_crm.event_lead_rule_menu
msgid "Lead Generation"
msgstr "Adayı Oluşturma"

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.event_lead_rule_action
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "Lead Generation Rule"
msgstr "Aday Oluşturma Kuralı"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_tree
msgid "Lead Generation Rules"
msgstr "Aday Oluşturma Kuralları"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_type
msgid "Lead Type"
msgstr "Teslim Türü"

#. module: event_crm
#: model:ir.model,name:event_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Aday/Fırsat"

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.crm_lead_action_from_event
#: model:ir.actions.act_window,name:event_crm.crm_lead_action_from_registration
#: model:ir.model.fields,field_description:event_crm.field_event_event__lead_ids
#: model:ir.model.fields,field_description:event_crm.field_event_registration__lead_ids
#: model_terms:ir.ui.view,arch_db:event_crm.event_view_form
msgid "Leads"
msgstr "Adaylar"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_event__lead_ids
msgid "Leads generated from this event"
msgstr "Bu etkinlikten oluşturulan adaylar"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Name"
msgstr "Adı"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_lead_rule.py:0
msgid "New registrations"
msgstr "Yeni kayıtlar"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.crm_lead_action_from_event
#: model_terms:ir.actions.act_window,help:event_crm.crm_lead_action_from_registration
msgid "No leads found"
msgstr "Aday bulunamadı"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_registration_action_from_lead
msgid "No registration found"
msgstr "Kayıt bulunamadı"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_event__has_lead_request
msgid "Ongoing Generation Request"
msgstr "Devam Eden Oluşturma Talebi"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Only Event Managers are allowed to re-generate all leads."
msgstr ""
"Tüm adayları yeniden oluşturma yetkisi yalnızca Etkinlik Yöneticilerindedir."

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "Fırsat"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_registration.py:0
msgid "Participants"
msgstr "Katılımcılar"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_basis__attendee
msgid "Per Attendee"
msgstr "Katılımcı Başına"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_creation_basis
msgid ""
"Per Attendee: A Lead is created for each Attendee (B2C).\n"
"Per Order: A single Lead is created per Ticket Batch/Sale Order (B2B)"
msgstr ""
"Katılımcı Başına: Her Katılımcı için bir Müşteri Adayı oluşturulur (B2C).\n"
"Sipariş Başına: Bilet Grubu/Satış Siparişi başına tek bir Müşteri Adayı oluşturulur (B2B)"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_basis__order
msgid "Per Order"
msgstr "Sipariş Başına"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__processed_registration_id
msgid "Processed Registration"
msgstr "İşlenmiş Kayıt"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__event_lead_rule_id
msgid "Registration Rule"
msgstr "Kayıt Kuralı"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_merge_summary_inherit_event_crm
msgid "Registration Rule:"
msgstr "Kayıt Kuralı:"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_registration_filter
msgid "Registrations Domain"
msgstr "Domain Kayıtları"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__registration_ids
msgid "Registrations triggering the rule that created this lead"
msgstr "Bu adayı oluşturan kuralı tetikleyen kayıtlar"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__company_id
msgid ""
"Restrict the trigger of this rule to events belonging to a specific company.\n"
"If not set, no company restriction will be applied."
msgstr ""
"Bu kuralın tetikleyicisini belirli bir şirkete ait etkinliklerle sınırlayın.\n"
"Ayarlanmaması durumunda hiçbir şirket sınırlaması uygulanmayacaktır."

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__name
msgid "Rule Name"
msgstr "Kural Adı"

#. module: event_crm
#: model:event.lead.rule,name:event_crm.event_lead_rule_0
msgid "Rule on @example.com"
msgstr "@example.com'da kural"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__event_lead_rule_id
msgid "Rule that created this lead"
msgstr "Bu adayı oluşturan kural"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_sales_team_id
msgid "Sales Team"
msgstr "Satış Ekibi"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_user_id
msgid "Salesperson"
msgstr "Satış Temsilcisi"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Search Lead Generation Rules"
msgstr "Adayları Oluşturma Kurallarını Arayın"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_event__has_lead_request
msgid "Set to True when a Lead Generation Request is currently running."
msgstr "Bir Aday Oluşturma Talebi çalışırken Doğru olarak ayarlanır."

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__event_id
msgid "Source Event"
msgstr "Etkinlik Kaynağı"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__registration_ids
msgid "Source Registrations"
msgstr "Kayıtlar kaynağı"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_tag_ids
msgid "Tags"
msgstr "Etiketler"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_request__processed_registration_id
msgid ""
"The ID of the last processed event.registration, used to know where to "
"resume."
msgstr ""
"Nereden devam edileceğini belirlemek için kullanılan, en son işlenen "
"event.registration kimliği. "

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_lead_rule_action
msgid "Those automatically create leads when attendees register."
msgstr ""
"Bunlar, katılımcılar kaydolduğunda otomatik olarak adayları oluşturur."

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Trigger Type"
msgstr "Tetikleyici Türü"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_registration.py:0
msgid "Updated registrations"
msgstr "Güncellenen kayıtlar"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_creation_trigger
msgid "When"
msgstr "Ne zaman"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Yee-ha, %(leads_count)s Leads have been created!"
msgstr "Yaşasın, %(leads_count)s Müşteri Adayı oluşturuldu!"

#. module: event_crm
#: model:ir.model.constraint,message:event_crm.constraint_event_lead_request_uniq_event
msgid "You can only have one generation request per event at a time."
msgstr ""
"Her etkinlik için aynı anda yalnızca bir oluşturma talebiniz olabilir. "

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "e.g. B2B Fairs"
msgstr "örn. B2B Fuarları"
