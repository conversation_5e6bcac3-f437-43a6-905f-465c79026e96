<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- This view should only be used from the product o2m because the required field product_tmpl_id has to be automatically set. -->
    <record id="view_product_image_form" model="ir.ui.view">
        <field name="name">product.image.view.form</field>
        <field name="model">product.image</field>
        <field name="arch" type="xml">
            <form string="Product Images">
                <field name="sequence" invisible="1"/>
                <div class="row o_website_sale_image_modal">
                    <div class="col-md-6 col-xl-5">
                        <label for="name" string="Image Name"/>
                        <h2><field name="name" placeholder="Image Name"/></h2>
                        <label for="video_url" string="Video URL"/><br/>
                        <field name="video_url"/><br/>
                    </div>
                    <div class="col-md-6 col-xl-7 text-center o_website_sale_image_modal_container">
                        <div class="row">
                            <div class="col">
                                <field name="image_1920" widget="image"/>
                            </div>
                            <div class="col" invisible="video_url in ['', False]">
                                <div class="o_video_container p-2">
                                    <span>Video Preview</span>
                                    <field name="embed_code" class="mt-2" widget="video_preview"/>
                                    <h4 class="o_invalid_warning text-muted text-center" invisible="embed_code">
                                        Please enter a valid Video URL.
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </field>
    </record>

    <record id="product_image_view_kanban" model="ir.ui.view">
        <field name="name">product.image.view.kanban</field>
        <field name="model">product.image</field>
        <field name="arch" type="xml">
            <kanban string="Product Images" default_order="sequence">
                <field name="video_url" invisible="1"/>
                <field name="sequence" widget="handle"/>
                <templates>
                    <t t-name="card" class="p-0 border-0">
                        <div class="card">
                            <div class="o_squared_image">
                                <field class="card-img-top" name="image_1920" widget="x2_many_image"/>
                            </div>
                            <div class="p-2 m-0 bg-200" t-attf-title="#{record.name.value}">
                                <field name="name" class="small fs-5 text-truncate d-block"/>
                            </div>
                            <!-- below 100 Kb: good -->
                            <t t-if="record.image_1920.raw_value.length &lt; 100*1000">
                                <t t-set="size_status" t-value="'text-bg-success'"/>
                                <t t-set="message">Acceptable file size</t>
                            </t>
                            <!-- below 1000 Kb: decent -->
                            <t t-elif="record.image_1920.raw_value.length &lt; 1000*1000">
                                <t t-set="size_status" t-value="'text-bg-warning'" />
                                <t t-set="message">Huge file size. The image should be optimized/reduced.</t>
                            </t>
                            <!-- above 1000 Kb: bad -->
                            <t t-else="1">
                                <t t-set="size_status" t-value="'text-bg-danger'"/>
                                <t t-set="message">Optimization required! Reduce the image size or increase your compression settings.</t>
                            </t>
                            <span t-attf-class="badge #{size_status} o_product_image_size" t-att-title="message"></span>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>
