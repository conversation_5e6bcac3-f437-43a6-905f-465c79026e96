<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="product_product_view_form_normalized_website_sale" model="ir.ui.view">
        <field name="name">product.product.view.form.normalized.website.sale.inherit</field>
        <field name="model">product.product</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="product.product_product_view_form_normalized"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="js_class">website_new_content_form</attribute>
            </xpath>
            <xpath expr="//field[@name='company_id']" position="before">
                <field name="website_url" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="product_product_view_form_normalized" model="ir.ui.view">
        <field name="name">product.product.view.form.normalized.website.sale</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_view_form_normalized"/>
        <field name="arch" type="xml">
            <div name="tax_info" position="after">
                <field name="public_categ_ids" string="Website Category" class="oe_inline" widget="many2many_tags" placeholder="Unpublished"/>
            </div>
        </field>
    </record>

    <record id="product_product_action_add" model="ir.actions.act_window">
        <field name="name">New Product</field>
        <field name="res_model">product.product</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context" eval="{'dialog_size': 'medium', 'website_published': True}"/>
        <field name="view_id" ref="product_product_view_form_normalized_website_sale"/>
    </record>

</odoo>
