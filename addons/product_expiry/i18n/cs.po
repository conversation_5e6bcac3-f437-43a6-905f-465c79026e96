# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_expiry
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid ""
"<span class=\"badge text-bg-danger\" invisible=\"not "
"product_expiry_alert\">Expiration Alert</span>"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_production_lot_view_kanban
msgid ""
"<span title=\"Alert\" class=\"fa fa-exclamation-triangle text-danger me-2\" "
"invisible=\"product_qty &lt;= 0 or not product_expiry_alert or not "
"expiration_date\"/>"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span> days after receipt</span>"
msgstr "<span> dnů po účtence</span>"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span> days before expiration date</span>"
msgstr "<span> dnů před datem vypršení</span>"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__alert_time
#: model:ir.model.fields,field_description:product_expiry.field_stock_lot__alert_date
msgid "Alert Date"
msgstr "Datum výstrahy"

#. module: product_expiry
#: model:mail.activity.type,name:product_expiry.mail_activity_type_alert_date_reached
msgid "Alert Date Reached"
msgstr "Bylo dosaženo výstražného data"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "B.b."
msgstr "B.b."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_time
msgid "Best Before Date"
msgstr "Minimální trvanlivost"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_lot__use_date
msgid "Best before Date"
msgstr "Minimální trvanlivost"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Confirm"
msgstr "Potvrdit"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_expiry_picking_confirmation
msgid "Confirm Expiry"
msgstr "Potvrzení vypršení platnosti"

#. module: product_expiry
#. odoo-python
#: code:addons/product_expiry/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Confirmation"
msgstr "Potvrzení"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_lot__alert_date
msgid ""
"Date to determine the expired lots and serial numbers using the filter "
"\"Expiration Alerts\"."
msgstr ""
"Datum k určení šarží a sériových čísel, jejichž platnost vypršela, pomocí "
"filtru „Upozornění na vypršení platnosti“."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Dates"
msgstr "Datumy"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__description
msgid "Description"
msgstr "Popis"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Discard"
msgstr "Zrušit"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings__group_expiry_date_on_delivery_slip
msgid "Display Expiration Dates on Delivery Slips"
msgstr "Zobrazit data expirace na dodacích listech"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Exp."
msgstr "Exp."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.quant_search_view_inherit_product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.search_product_lot_filter_inherit_product_expiry
msgid "Expiration Alerts"
msgstr "Výstražné upozornění na expiraci"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__expiration_time
#: model:ir.model.fields,field_description:product_expiry.field_stock_lot__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__expiration_date
#: model_terms:ir.ui.view,arch_db:product_expiry.stock_report_delivery_document_inherit_product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Expiration Date"
msgstr "Datum platnosti"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.res_config_settings_view_form_stock
msgid "Expiration dates will appear on the delivery slip"
msgstr "Datum expirace bude uvedeno na dodacím listu."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_production_lot_view_kanban
msgid "Expiration:"
msgstr "Expirace:"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Expired Lot(s)"
msgstr "Prošlá šarže"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_lot__product_expiry_reminded
msgid "Expiry has been reminded"
msgstr "Bylo připomenuto vypršení platnosti"

#. module: product_expiry
#: model:product.removal,name:product_expiry.removal_fefo
msgid "First Expiry First Out (FEFO)"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__id
msgid "ID"
msgstr "ID"

#. module: product_expiry
#: model:res.groups,name:product_expiry.group_expiry_date_on_delivery_slip
msgid "Include expiration dates on delivery slip"
msgstr "Na dodacím listu uvést datum expirace."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__lot_ids
msgid "Lot"
msgstr "Dávka"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_lot
msgid "Lot/Serial"
msgstr "Šarže/sériové číslo"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,help:product_expiry.field_product_template__expiration_time
msgid ""
"Number of days after the receipt of the products (from the vendor or in "
"stock after production) after which the goods may become dangerous and must "
"not be consumed. It will be computed on the lot/serial number."
msgstr ""
"Počet dní po přijetí produktů (od prodejce nebo na skladě po výrobě), po "
"kterých se zboží může stát nebezpečným a nesmí být spotřebováno. Bude "
"vypočítáno na šarži / sériovém čísle."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,help:product_expiry.field_product_template__alert_time
msgid ""
"Number of days before the Expiration Date after which an alert should be "
"raised on the lot/serial number. It will be computed on the lot/serial "
"number."
msgstr ""
"Počet dní před datem vypršení platnosti, po kterém by mělo být upozorněno na"
" šarži / sériové číslo. Bude vypočítáno na šarži / sériovém čísle."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,help:product_expiry.field_product_template__removal_time
msgid ""
"Number of days before the Expiration Date after which the goods should be "
"removed from the stock. It will be computed on the lot/serial number."
msgstr ""
"Počet dní před datem vypršení platnosti, po kterém by mělo být zboží "
"odebráno ze skladu. Bude vypočítáno na šarži / sériovém čísle."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_time
#: model:ir.model.fields,help:product_expiry.field_product_template__use_time
msgid ""
"Number of days before the Expiration Date after which the goods starts "
"deteriorating, without being dangerous yet. It will be computed on the "
"lot/serial number."
msgstr ""
"Počet dní před datem vypršení platnosti, po kterém se zboží začne zhoršovat,"
" aniž by to ještě bylo nebezpečné. Bude vypočítáno na šarži / sériovém "
"čísle."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__picking_ids
msgid "Picking"
msgstr "Dodání"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Proceed except for expired one"
msgstr "Pokračovat s výjimkou vypršelého"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_procurement_group
msgid "Procurement Group"
msgstr "Skupina zásobování"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_lot__product_expiry_alert
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__is_expired
msgid "Product Expiry Alert"
msgstr "Upozornění na expiraci produktu"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Pohyby produktu (položka pohybu zásob)"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_product
msgid "Product Variant"
msgstr "Produktová varianta"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr "Kvanty"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__removal_time
#: model:ir.model.fields,field_description:product_expiry.field_stock_lot__removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__removal_date
msgid "Removal Date"
msgstr "Datum odebrání"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_production_lot_view_kanban
msgid "Removal:"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__show_lots
msgid "Show Lots"
msgstr "Zobrazit šarže"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move
msgid "Stock Move"
msgstr "Pohyb zásob"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_lot__product_expiry_alert
#: model:ir.model.fields,help:product_expiry.field_stock_move_line__is_expired
msgid "The Expiration Date has been reached."
msgstr "Datum vypršení platnosti bylo dosaženo."

#. module: product_expiry
#. odoo-python
#: code:addons/product_expiry/models/production_lot.py:0
msgid "The alert date has been reached for this lot/serial number"
msgstr "Pro tuto šarži/sériové číslo bylo dosaženo data výstrahy"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_lot__expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__expiration_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""
"Datum, kdy se zboží s tímto seriovým číslem může stát nebezpečným a nesmí "
"být požíváno."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_lot__removal_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock. This date will be used in FEFO removal strategy."
msgstr ""
"Toto je datum, kdy by mělo být zboží s tímto sériovým číslem odebráno ze "
"skladu. Toto datum bude použito ve strategii odstranění FEFO."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_lot__use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""
"Datum, kdy se zboží s tímto seriovým číslem začne kazit bez toho, aby ještě "
"bylo nebezpečné."

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_picking
msgid "Transfer"
msgstr "Převod"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_lot__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__use_expiration_date
msgid "Use Expiration Date"
msgstr "Použít datum platnosti"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_lot__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_move_line__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__use_expiration_date
msgid ""
"When this box is ticked, you have the possibility to specify dates to manage"
" product expiration, on the product and on the corresponding lot/serial "
"numbers"
msgstr ""
"Když je toto políčko zaškrtnuto, máte možnost určit data pro správu vypršení"
" platnosti produktu, na produktu a na odpovídajících číslech šarží / "
"sériových čísel"

#. module: product_expiry
#. odoo-python
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
msgid ""
"You are going to deliver some product expired lots.\n"
"Do you confirm you want to proceed?"
msgstr ""

#. module: product_expiry
#. odoo-python
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
msgid ""
"You are going to deliver the product %(product_name)s, %(lot_name)s which is expired.\n"
"Do you confirm you want to proceed?"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid "^A0N,44,33^FDUse by:"
msgstr "^A0N,44,33^FDPoužít od:"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^BCN,100,Y,N,N\n"
"^FD"
msgstr ""
"^BCN,100,Y,N,N\n"
"^FD"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid "^FO100,150"
msgstr "^FO100,150"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^FO100,150\n"
"^A0N,44,33^FDBest before:"
msgstr ""
"^FO100,150\n"
"^A0N,44,33^FDMinimální trvanlivost:"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^FO100,150\n"
"^A0N,44,33^FDUse by:"
msgstr ""
"^FO100,150\n"
"^A0N,44,33^FDPoužít od:"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid "^FO100,200"
msgstr "^FO100,200"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^FO100,200\n"
"^A0N,44,33^FDUse by:"
msgstr ""
"^FO100,200\n"
"^A0N,44,33^FDPoužít od:"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid "^FO100,200^BY3"
msgstr "^FO100,200^BY3"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^FO100,50\n"
"^A0N,44,33^FD"
msgstr ""
"^FO100,50\n"
"^A0N,44,33^FD"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid "^FS"
msgstr "^FS"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^FS\n"
"^FO100,100\n"
"^A0N,44,33^FDLN/SN:"
msgstr ""
"^FS\n"
"^FO100,100\n"
"^A0N,44,33^FDLN/SN:"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^FS\n"
"^FO100,200^BY3\n"
"^BCN,100,Y,N,N\n"
"^FD"
msgstr ""
"^FS\n"
"^FO100,200^BY3\n"
"^BCN,100,Y,N,N\n"
"^FD"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.label_lot_template_view_expiry
msgid ""
"^FS\n"
"^FO100,250^BY3"
msgstr ""
"^FS\n"
"^FO100,250^BY3"

#. module: product_expiry
#: model:product.removal,method:product_expiry.removal_fefo
msgid "fefo"
msgstr "fefo"
