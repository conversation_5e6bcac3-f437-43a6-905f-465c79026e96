# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2025
# NumerSpiral HBG, 2025
# <PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$50"
msgstr "$50"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$500"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.report,print_report_name:purchase_requisition.action_report_purchase_requisitions
msgid "'Purchase Agreement - %s' % (object.name)"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "02/16/2024"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "12/25/2024"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "2023-09-15"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>From</strong></span>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>to</strong></span>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Contact:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Reference:</strong><br/>"
msgstr "<strong>Referência:</strong><br/>"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction
msgid "Action Needed"
msgstr "Ação Necessária"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__active
msgid "Active"
msgstr "Ativo"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_ids
msgid "Activities"
msgstr "Atividades"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Marcador da Exceção na Atividade"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_state
msgid "Activity State"
msgstr "Estado da Atividade"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone de Tipo de Atividade"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__name
msgid "Agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_type
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__requisition_type
msgid "Agreement Type"
msgstr "Tipo de Acordo"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Agreement Validity"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Agreement Validity:"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__alternative_po_ids
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__alternative_po_ids
msgid "Alternative POs"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternative Purchase Order"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Alternative Warning"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternatives"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "An example of a purchase agreement is a blanket order."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribuição da Analítica"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_precision
msgid "Analytic Precision"
msgstr "Precisão analítica"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Archived"
msgstr "Arquivados"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_attachment_count
msgid "Attachment Count"
msgstr "Número de Anexos"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "BO00004"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__blanket_order
msgid "Blanket Order"
msgstr "Pedido genérico"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Blanket Orders"
msgstr "Pedidos genéricos"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Buyer"
msgstr "Comprador"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel"
msgstr "Cancelar"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Cancel Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__cancel
msgid "Cancelled"
msgstr "Cancelada"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Cancelled by the agreement associated to this quotation."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid "Category"
msgstr "Categoria"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Choose"
msgstr "Escolher"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
msgid "Choose a vendor for alternative PO"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear"
msgstr "Limpar"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear Selected"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close"
msgstr "Encerrar"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__done
msgid "Closed"
msgstr "Encerrado"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Code"
msgstr "Código"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__company_id
msgid "Company"
msgstr "Empresa"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__company_currency_id
msgid "Company Currency"
msgstr "Moeda da Empresa"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__price_total_cc
msgid "Company Subtotal"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Company Total"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Compare Order Lines"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Compare Product Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr "Confirmar"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__confirmed
msgid "Confirmed"
msgstr "Confirmado"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Conversões entre Unidades de Medida só podem ocorrer se pertencerem à mesma "
"categoria. A conversão será feita com base nos coeficientes."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid "Copy Products"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
msgid "Create Alternative"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid ""
"Create a call for tender by adding alternative requests for quotation to different vendors.\n"
"                            Make your choice by selecting the best combination of lead time, OTD and/or total amount.\n"
"                            By comparing product lines you can also decide to order some products from one vendor and others from another vendor."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Create alternative"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_date
msgid "Created on"
msgstr "Criado em"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid "Creation Blocked"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Date"
msgstr "Data"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Demo Reference"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__description
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_description_variants
msgid "Description"
msgstr "Descrição"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Discard"
msgstr "Descartar"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Done"
msgstr "Concluído"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__draft
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Draft"
msgstr "Rascunho"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_end
msgid "End Date"
msgstr "Data Final"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"End date cannot be earlier than start date. Please check dates for "
"agreements: %s"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Expected on"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Parceiros)"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ícone do Font awesome ex. fa-tasks"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specific period\n"
"            (e.g. a year) and you order products within this agreement to benefit\n"
"            from the negotiated prices."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Future Activities"
msgstr "Atividades Futuras"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Agrupar Por"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__has_alternatives
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_kpis_tree_inherit_purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_tree_inherit_purchase_requisition
msgid "Has Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__has_message
msgid "Has Message"
msgstr "Tem Mensagem"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__id
msgid "ID"
msgstr "Id."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ícone para indicar uma exceção na atividade."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se marcado, há novas mensagens que requerem a sua atenção."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se estiver marcado, algumas mensagens têm um erro na entrega."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid ""
"If the chosen vendor or if any of the products in the original PO have a "
"blocking warning then we prevent creation of alternative PO. This is because"
" normally these fields are cleared w/warning message within form view, but "
"we cannot recreate that in this case."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid ""
"If this is checked, the product quantities of the original PO will be copied"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_is_follower
msgid "Is Follower"
msgstr "É Seguidor"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Keep Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Late Activities"
msgstr "Atividades em Atraso"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.res_config_settings_view_form_purchase_requisition
msgid "Link RFQs together and compare them"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Link to Existing RfQ"
msgstr ""

#. module: purchase_requisition
#: model:res.groups,name:purchase_requisition.group_purchase_alternatives
msgid "Manage Purchase Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error
msgid "Message Delivery error"
msgstr "Erro na Entrega de Mensagem"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Mitchell Admin"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo da Minha Atividade"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "My Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Name, TIN, Email, or Reference"
msgstr "Nome, NIF, e-mail ou referência"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "New"
msgstr "Novo"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr "Novos Acordos"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr "Novo Orçamento"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Próximo Evento do Calendário de Atividades"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data Limite Para a Próxima Atividade"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_summary
msgid "Next Activity Summary"
msgstr "Resumo da Próxima Atividade"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da Atividade Seguinte"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Nothing to clear"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Ações"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__order_count
msgid "Number of Orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensagens que requerem uma ação"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com um erro na entrega"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__order_ids
msgid "Order"
msgstr "Ordem"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__qty_ordered
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Ordered"
msgstr "Pedido"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr "Data de Ordem"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Orders"
msgstr "Encomendas"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "Origin Po"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__alternative_po_ids
msgid "Other potential purchase orders for purchasing products"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__po_ids
msgid "POs to Confirm"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__product_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Product"
msgstr "Artigo"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_id
msgid "Product Unit of Measure"
msgstr "Unidade de Medida de Artigo"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_product
msgid "Product Variant"
msgstr "Categoria do Artigo"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Artigos"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__line_ids
msgid "Products to Purchase"
msgstr "Artigos a Comprar"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__requisition_id
msgid "Purchase Agreement"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Agreement:"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr "Acordos de Compra"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_res_config_settings__group_purchase_alternatives
msgid "Purchase Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__purchase_group_id
msgid "Purchase Group"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr "Notas de encomenda"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Linha de Encomenda"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Purchase Order Lines"
msgstr "Linhas de Encomenda"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__purchase_ids
msgid "Purchase Orders"
msgstr "Notas de encomenda"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr "Notas de encomenda com requisição"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Reference"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Representative"
msgstr "Comprador"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Requisição de compra"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_line_id
msgid "Purchase Requisition Line"
msgstr "Linha de Requisição da compra"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__purchase_template
msgid "Purchase Template"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Templates"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Quantity"
msgstr "Quantidade"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "RFQ"
msgstr "RFQ"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__rating_ids
msgid "Ratings"
msgstr "Classificações"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__reference
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Reference"
msgstr "Referência"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr "Pedido de Orçamento"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr "Pedidos de Orçamento"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr "Requisição"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Mudar para Rascunho"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_user_id
msgid "Responsible User"
msgstr "Utilizador Responsável"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro de Envio de SMS"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Show all records which has next action date is before today"
msgstr "Mostrar todos os registos cuja data de ação é anterior à atual"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Some not cleared"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid ""
"Some quantities were not cleared because their status is not a RFQ status."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_start
msgid "Start Date"
msgstr "Data Inicial"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Start a new purchase agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Estado"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estados baseados nas atividades\n"
"Vencida: Ultrapassada a data planeada\n"
"Hoje: Data da atividade é a de hoje\n"
"Planeado: Atividades futuras."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__supplier_info_ids
msgid "Supplier Info"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Lista de Preços de Fornecedor"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_group
msgid "Technical model to group PO for call to tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Termos e condições"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "The original PO that this alternative PO is being created for."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"The vendor you have selected or at least one of the products you are copying"
" from the original order has a blocking warning on it and cannot be selected"
" to create an alternative."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "There are no quantities to clear."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"There is already an open blanket order for this supplier. We suggest you "
"complete this open blanket order, instead of creating a new one."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid "This is a blocking warning!\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"To close this purchase requisition, cancel related Requests for Quotation.\n"
"\n"
"Imagine the mess if someone confirms these duplicates: double the order, double the trouble :)"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Today Activities"
msgstr "Atividades do Dia"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Total"
msgstr "Total"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividade excecional registada."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit"
msgstr "Unidade"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit Price"
msgstr "Preço Unitário"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "UoM"
msgstr "UoM"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__vendor_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Vendor"
msgstr "Fornecedor"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__purchase_warn_msg
msgid "Warning Messages"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(partner)s:\n"
"%(warning_message)s\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(product)s:\n"
"%(warning_message)s\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Warning for %s"
msgstr "Aviso para %s"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do Website"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do website"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "What about the alternative Requests for Quotations?"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__has_alternatives
msgid ""
"Whether or not this purchase order is linked to another purchase order as an"
" alternative."
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_alternative_warning
msgid "Wizard in case PO still has open alternative requests for quotation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_create_alternative
msgid "Wizard to preset values for alternative PO"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You can only delete draft or cancelled requisitions."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot change the Agreement Type or Company of a not draft purchase "
"agreement."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a price."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a quantity."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot confirm agreement '%(agreement)s' because it does not contain any"
" product lines."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot have a negative or unit price of 0 for an already confirmed "
"blanket order."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr "por exemplo, PO0025"
