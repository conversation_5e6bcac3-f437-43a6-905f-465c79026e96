<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tax_report" model="account.report">
        <field name="name">VAT Report (KMD)</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ee"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <!-- Valid until 2025-06-31 -->
            <record id="tax_report_line_1" model="account.report.line">
                <field name="name">1 - Acts and transactions subject to tax at a rate of 22%</field>
                <field name="code">l10n_ee_vat_1</field>
                <field name="hide_if_zero" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_1_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_base</field>
                    </record>
                    <record id="tax_report_line_1_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_tax</field>
                    </record>
                </field>
            </record>
            <!-- Valid from 2025-07-01 -->
            <record id="tax_report_line_1_24" model="account.report.line">
                <field name="name">1 - Acts and transactions subject to tax at a rate of 24%</field>
                <field name="code">l10n_ee_vat_1_24</field>
                <field name="hide_if_zero" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_1_24_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_base_24</field>
                    </record>
                    <record id="tax_report_line_1_24_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_tax_24</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_1_1" model="account.report.line">
                <field name="name">1¹ - Acts and transactions subject to tax at a rate of 20%</field>
                <field name="code">l10n_ee_vat_1_1</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_1_1_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_1_base</field>
                    </record>
                    <record id="tax_report_line_1_1_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_1_tax</field>
                    </record>
                </field>
            </record>
            <!-- Valid from 2025-07-01 -->
            <record id="tax_report_line_1_2" model="account.report.line">
                <field name="name">1² - Acts and transactions subject to tax at a rate of 22%</field>
                <field name="code">l10n_ee_vat_1_2</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_1_2_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_2_base</field>
                    </record>
                    <record id="tax_report_line_1_2_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">1_2_tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_2" model="account.report.line">
                <field name="name">2 - Acts and transactions subject to tax at a rate of 9%</field>
                <field name="code">l10n_ee_vat_2</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_2_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">2_base</field>
                    </record>
                    <record id="tax_report_line_2_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">2_tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_2_1" model="account.report.line">
                <field name="name">2¹ - Acts and transactions subject to tax at a rate of 5%</field>
                <field name="code">l10n_ee_vat_2_1</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_2_1_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">2_1_base</field>
                    </record>
                    <record id="tax_report_line_2_1_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">2_1_tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_2_2" model="account.report.line">
                <field name="name">2² - Acts and transactions subject to tax at a rate of 13%</field>
                <field name="code">l10n_ee_vat_2_2</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_2_2_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">2_2_base</field>
                    </record>
                    <record id="tax_report_line_2_2_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">2_2_tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_3" model="account.report.line">
                <field name="name">3 - Acts and transactions subject to tax at a rate of 0%, incl.</field>
                <field name="code">l10n_ee_vat_3</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_3_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">l10n_ee_vat_3.tax_tags + l10n_ee_vat_3_1.balance + l10n_ee_vat_3_2.balance</field>
                    </record>
                    <record id="tax_report_line_3_tag" model="account.report.expression">
                        <field name="label">tax_tags</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">3</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="tax_report_line_3_1" model="account.report.line">
                        <field name="name">3.1 - Intra-Community supply of goods and services provided to a taxable person or taxable person with limited liability of another Member State, total, incl.</field>
                        <field name="code">l10n_ee_vat_3_1</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_3_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_ee_vat_3_1.tax_tags + l10n_ee_vat_3_1_1.balance</field>
                            </record>
                            <record id="tax_report_line_3_1_tag" model="account.report.expression">
                                <field name="label">tax_tags</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">3_1</field>
                            </record>
                            <!-- Not visible, but used for EC Sales Report -->
                            <record id="tax_report_line_ec_services_tag" model="account.report.expression">
                                <field name="label">ec_services</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">3_1_S</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="tax_report_line_3_1_1" model="account.report.line">
                                <field name="name">3.1.1 - Intra-Community supply of goods</field>
                                <field name="code">l10n_ee_vat_3_1_1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_3_1_1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">3_1_1</field>
                                    </record>
                                    <!-- Not visible, but used for EC Sales Report -->
                                    <record id="tax_report_line_ec_goods_tag" model="account.report.expression">
                                        <field name="label">ec_goods</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">3_1_1_G</field>
                                    </record>
                                    <record id="tax_report_line_ec_triangular_tag" model="account.report.expression">
                                        <field name="label">ec_triangular</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">3_1_1_T</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_3_2" model="account.report.line">
                        <field name="name">3.2 - Exportation of goods, incl.</field>
                        <field name="code">l10n_ee_vat_3_2</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_3_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_ee_vat_3_2.tax_tags + l10n_ee_vat_3_2_1.balance</field>
                            </record>
                            <record id="tax_report_line_3_2_tag" model="account.report.expression">
                                <field name="label">tax_tags</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">3_2</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="tax_report_line_3_2_1" model="account.report.line">
                                <field name="name">3.2.1 - Sale to passengers with return of value added tax</field>
                                <field name="code">l10n_ee_vat_3_2_1</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_3_2_1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">3_2_1</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_4" model="account.report.line">
                <field name="name">4 - Total amount of value added tax</field>
                <field name="code">l10n_ee_vat_4</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_4_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">l10n_ee_vat_1_24.balance * 0.24 + l10n_ee_vat_1.balance * 0.22 + l10n_ee_vat_1_2.balance * 0.22 + l10n_ee_vat_1_1.balance * 0.2 + l10n_ee_vat_2.balance * 0.09 + l10n_ee_vat_2_1.balance * 0.05 + l10n_ee_vat_2_2.balance * 0.13</field>
                    </record>
                    <record id="tax_report_line_4_tag_no_rounding" model="account.report.expression">
                        <field name="label">balance_from_tags</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">l10n_ee_vat_1_24.tax + l10n_ee_vat_1.tax + l10n_ee_vat_1_1.tax + l10n_ee_vat_1_2.tax + l10n_ee_vat_2.tax + l10n_ee_vat_2_1.tax + l10n_ee_vat_2_2.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_4_1" model="account.report.line">
                <field name="name">4¹ - Value added tax payable upon the import of the goods</field>
                <field name="code">l10n_ee_vat_4_1</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_4_1_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">4_1_tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_5" model="account.report.line">
                <field name="name">5 - Total amount of input VAT subject to deduction pursuant to law, incl.</field>
                <field name="code">l10n_ee_vat_5</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_5_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">l10n_ee_vat_5.tax_tags + l10n_ee_vat_5_1.balance + l10n_ee_vat_5_2.balance + l10n_ee_vat_5_3.balance + l10n_ee_vat_5_4.balance</field>
                    </record>
                    <record id="tax_report_line_5_tag" model="account.report.expression">
                        <field name="label">tax_tags</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">5_tax</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="tax_report_line_5_1" model="account.report.line">
                        <field name="name">5.1 - VAT paid or payable on import</field>
                        <field name="code">l10n_ee_vat_5_1</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_5_1_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">5_1_tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_5_2" model="account.report.line">
                        <field name="name">5.2 - VAT paid or payable on acquisition of fixed assets</field>
                        <field name="code">l10n_ee_vat_5_2</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_5_2_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">5_2_tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_5_3" model="account.report.line">
                        <field name="name">5.3 - VAT paid or payable on acquisition of a car used for business purposes (100%), and on acquisition of goods and receipt of services for such car</field>
                        <field name="code">l10n_ee_vat_5_3</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_5_3_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">5_3_tax</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="tax_report_line_5_3_cars" model="account.report.line">
                                <field name="name">Number of cars</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_5_3_cars_value" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">most_recent</field>
                                        <field name="subformula">editable;rounding=0</field>
                                        <field name="figure_type">integer</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_5_4" model="account.report.line">
                        <field name="name">5.4 - VAT paid or payable on acquisition of a car used partially for business purposes, and on acquisition of goods and receipt of services for such car</field>
                        <field name="code">l10n_ee_vat_5_4</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_5_4_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">5_4_tax</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="tax_report_line_5_4_cars" model="account.report.line">
                                <field name="name">Number of cars</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_5_4_cars_value" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">most_recent</field>
                                        <field name="subformula">editable;rounding=0</field>
                                        <field name="figure_type">integer</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_6" model="account.report.line">
                <field name="name">6 - Intra-Community acquisitions of goods and services received from a taxable person of another Member State, total, incl.</field>
                <field name="code">l10n_ee_vat_6</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_6_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">l10n_ee_vat_6.tax_tags + l10n_ee_vat_6_1.balance</field>
                    </record>
                    <record id="tax_report_line_6_tag" model="account.report.expression">
                        <field name="label">tax_tags</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">6</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="tax_report_line_6_1" model="account.report.line">
                        <field name="name">6.1 - Intra-Community acquisitions of goods</field>
                        <field name="code">l10n_ee_vat_6_1</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_6_1_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">6_1</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_7" model="account.report.line">
                <field name="name">7 - Acquisition of other goods and services subject to VAT, incl.</field>
                <field name="code">l10n_ee_vat_7</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_7_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">l10n_ee_vat_7.tax_tags + l10n_ee_vat_7_1.balance</field>
                    </record>
                    <record id="tax_report_line_7_tag" model="account.report.expression">
                        <field name="label">tax_tags</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">7</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="tax_report_line_7_1" model="account.report.line">
                        <field name="name">7.1 - Acquisition of immovables, scrap metal, precious metal and metal products subject to value added tax under the special arrangements (VAT Act §41¹)</field>
                        <field name="code">l10n_ee_vat_7_1</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_7_1_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">7_1</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_8" model="account.report.line">
                <field name="name">8 - Supply exempt from tax</field>
                <field name="code">l10n_ee_vat_8</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_8_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">8</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_9" model="account.report.line">
                <field name="name">9 - Supply of immovables, scrap metal, precious metal and metal products subject to value added tax under the special arrangements (VAT Act §41¹) and taxable value of goods to be installed or assembled in another Member State</field>
                <field name="code">l10n_ee_vat_9</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_9_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">9</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_10" model="account.report.line">
                <field name="name">10 - Adjustments (+)</field>
                <field name="code">l10n_ee_vat_10</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_10_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">10</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_11" model="account.report.line">
                <field name="name">11 - Adjustments (-)</field>
                <field name="code">l10n_ee_vat_11</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_11_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">11</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_12" model="account.report.line">
                <field name="name">12 - Value added tax payable</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_12_formula" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">l10n_ee_vat_4.balance + l10n_ee_vat_4_1.balance - l10n_ee_vat_5.balance + l10n_ee_vat_10.balance - l10n_ee_vat_11.balance</field>
                        <field name="subformula">if_above(EUR(0))</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_13" model="account.report.line">
                <field name="name">13 - Overpaid value added tax</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_line_13_formula" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">-(l10n_ee_vat_4.balance + l10n_ee_vat_4_1.balance - l10n_ee_vat_5.balance + l10n_ee_vat_10.balance - l10n_ee_vat_11.balance)</field>
                        <field name="subformula">if_above(EUR(0))</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
