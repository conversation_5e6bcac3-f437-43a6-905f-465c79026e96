# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# <PERSON><PERSON> <kari.lind<PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "(recording keyboard)"
msgstr "(näppäimistöä tallennetaan)"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "(run:"
msgstr "(aja:"

#. module: web_tour
#: model:ir.model.constraint,message:web_tour.constraint_web_tour_tour_uniq_name
msgid "A tour already exists with this name . Tour's name must be unique!"
msgstr ""
"Tällä nimellä on jo olemassa esittelykiertue. Esittelykiertueen nimen on "
"oltava yksilöllinen!"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
msgid "Click the top left corner to navigate across apps."
msgstr "Voit navigoida sovellusten välillä napsauttamalla vasenta yläkulmaa."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__content
msgid "Content"
msgstr "Sisältö"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__create_uid
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__create_date
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__create_date
msgid "Created on"
msgstr "Luotu"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__custom
msgid "Custom"
msgstr "Mukautettu"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.js:0
msgid "Custom tour '%s' couldn't be saved!"
msgstr "Mukautettua esittelykierrosta '%s' ei voitu tallentaa!"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.js:0
msgid "Custom tour '%s' has been added."
msgstr "Mukautettu esittelykierros '%s' on lisätty."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: web_tour
#: model:ir.actions.server,name:web_tour.tour_export_js_action
msgid "Export JS"
msgstr "Vie JS"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__write_uid
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__write_date
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_list
msgid "Menu"
msgstr "Valikko"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Name"
msgstr "Nimi"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Name:"
msgstr "Nimi:"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_service.js:0
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
#: model:ir.model.fields,field_description:web_tour.field_res_users__tour_enabled
msgid "Onboarding"
msgstr "Perehdytys"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__rainbow_man_message
msgid "Rainbow Man Message"
msgstr "Säihkyvän sateenkaarisankarin viesti"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "Rainbow Man Message..."
msgstr "Säihkyvän sateenkaarisankarin viesti..."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
#: code:addons/web_tour/static/src/views/tour_controller.xml:0
msgid "Record"
msgstr "Tietue"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/views/tour_controller.xml:0
msgid "Record Tour"
msgstr "Tallenna esittelykiertue"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__run
msgid "Run"
msgstr "Suorita"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Save"
msgstr "Tallenna"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll down to reach the next step."
msgstr "Vieritä alaspäin päästäksesi seuraavaan vaiheeseen."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll left to reach the next step."
msgstr "Vieritä hiirellä vasemmalta siirtyäksesi seuraavaan vaiheeseen."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll right to reach the next step."
msgstr "Vieritä hiirellä oikealle siirtyäksesi seuraavaan vaiheeseen."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll up to reach the next step."
msgstr "Siirry seuraavaan vaiheeseen selaamalla ylöspäin."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__sequence
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__sharing_url
msgid "Sharing URL"
msgstr "Jaetaan URL-osoite"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Start Tour"
msgstr "Aloita esittelykierros"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__url
msgid "Starting URL"
msgstr "Aloitetaan URL-osoite"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__step_ids
msgid "Step"
msgstr "Vaihe"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "Steps"
msgstr "Vaiheet"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Steps:"
msgstr "Vaiheet:"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Test Tour"
msgstr "Testaa esittelykiertue"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Testing"
msgstr "Testaus"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_search
msgid "Tip"
msgstr "Vinkki"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__tour_id
msgid "Tour"
msgstr "Kierros"

#. module: web_tour
#: model:ir.model,name:web_tour.model_web_tour_tour_step
msgid "Tour's step"
msgstr "Esittelykiertueen askel"

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr "Esittelykiertueet"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__trigger
msgid "Trigger"
msgstr "Liipaisin"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Url:"
msgstr "URL:"

#. module: web_tour
#: model:ir.model,name:web_tour.model_res_users
msgid "User"
msgstr "Käyttäjä"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_consumed_ids
msgid "User Consumed"
msgstr "Käyttäjä kuluttanut"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "e.g. My_Tour"
msgstr "esim. My_Tour"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "name_of_the_tour"
msgstr "name_of_the_tour"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "trigger"
msgstr "trigger"
