# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock_account
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>ILMAZ <<EMAIL>>, 2024
# emre oktem, 2024
# <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>_<PERSON>do<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "3D printing pen"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "447 out of 1,856"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "76,437 out of 188,071"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock qty by category and creation date"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock value by product and creation date"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Quantity"
msgstr "Mevcut Miktarı"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Value"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top locations)"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top propducts)"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top locations)"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top propducts)"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Bluetooth-enabled LED light strip"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Compact espresso machine"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Count of products with negative stock"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Electric standing desk"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ergonomic office chair"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "KPI"
msgstr "Temel Performans Göstergesi"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Lines with negative stock"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Location"
msgstr "Konum"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Lot/Serial"
msgstr "Lot/Seri"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Post-production"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Pre-production"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product"
msgstr "Ürün"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product Category"
msgstr "Ürün Kategorisi"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Products"
msgstr "Ürünler"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved"
msgstr "Rezerve"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Quantity"
msgstr "Ayrılan Miktar"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Value"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock Value"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock qty"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Qty"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Value"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Smart air purifier"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Solar-powered phone charger"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Top 10 products with negative stock"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total"
msgstr "Toplam"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total inventory value"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Output"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock"
msgstr "WH/Stok"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock/Shelf 10"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Warehouse"
msgstr "Depo"

#. module: spreadsheet_dashboard_stock_account
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock_account.spreadsheet_dashboard_warehouse_metrics
msgid "Warehouse Metrics"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Waterproof hiking backpack"
msgstr ""
