# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock_account
# 
# Translators:
# Wil <PERSON>doo, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "3D printing pen"
msgstr "3D-Druck-Stift"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "447 out of 1,856"
msgstr "447 von 1.856"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "76,437 out of 188,071"
msgstr "76.437 von 188.071"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock qty by category and creation date"
msgstr "Menge an Altbestand nach Kategorie und Erstellungsdatum"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock value by product and creation date"
msgstr "Wert des Altbestandes nach Produkt und Erstellungsdatum"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Quantity"
msgstr "Verfügbare Menge"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Value"
msgstr "Verfügbarer Wert"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top locations)"
msgstr "Menge des verfügbaren und reservierten Bestands (Top-Lagerorte)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top propducts)"
msgstr "Menge des verfügbaren und reservierten Bestands (Top-Produkte)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top locations)"
msgstr "Wert des verfügbaren und reservierten Bestands (Top-Lagerorte)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top propducts)"
msgstr "Wert des verfügbaren und reservierten Bestands (Top-Produkte)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Bluetooth-enabled LED light strip"
msgstr "LED-Leuchtband mit Bluetooth-Verbindung"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Compact espresso machine"
msgstr "Kompakte Espressomaschine"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Count of products with negative stock"
msgstr "Anzahl Produkte mit negativem Bestand"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Electric standing desk"
msgstr "Elektrisches Stehpult"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ergonomic office chair"
msgstr "Ergonomischer Bürostuhl"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Lines with negative stock"
msgstr "Zeilen mit negativem Bestand"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Location"
msgstr "Lagerort"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Lot/Serial"
msgstr "Los/Serie"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Post-production"
msgstr "Nachproduktion"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Pre-production"
msgstr "Vorproduktion"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product"
msgstr "Produkt"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product Category"
msgstr "Produktkategorie"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Products"
msgstr "Produkte"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved"
msgstr "Reserviert"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Quantity"
msgstr "Reservierte Menge"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Value"
msgstr "Reservierter Wert"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock Value"
msgstr "Wertanteil des reservierten Bestands"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock qty"
msgstr "Mengenanteil des reservierten Bestands"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Qty"
msgstr "Mengenanteil des reservierten Bestands"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Value"
msgstr "Wertanteil des reservierten Bestands"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Smart air purifier"
msgstr "Intelligenter Luftreiniger"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Solar-powered phone charger"
msgstr "Solarbetriebener Handylader"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Top 10 products with negative stock"
msgstr "Die besten 10 Produkte mit negativem Bestand"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total"
msgstr "Gesamt"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total inventory value"
msgstr "Gesamtbestandswert"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Output"
msgstr "WH/Ausgang"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock"
msgstr "WH/Stock"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock/Shelf 10"
msgstr "WH/Bestand/Regal 10"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Warehouse"
msgstr "Lagerhaus"

#. module: spreadsheet_dashboard_stock_account
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock_account.spreadsheet_dashboard_warehouse_metrics
msgid "Warehouse Metrics"
msgstr "Lagerhauskennzahlen"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Waterproof hiking backpack"
msgstr "Wasserfester Wanderrucksack"
