# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Возможно понадобится ручная действие."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_inherited_form_purchase
msgid "<span class=\"o_stat_text\">Purchase</span>"
msgstr "<span class=\"o_stat_text\">Закупка</span>"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.purchase_order_inherited_form_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr "<span class=\"o_stat_text\">Продажа</span>"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_cancel_view_form
msgid ""
"<span id=\"display_invoice_alert\" position=\"after\">\n"
"                <span id=\"display_purchase_orders_alert\" invisible=\"not display_purchase_orders_alert\" groups=\"purchase.group_purchase_user\">\n"
"                    There are active purchase orders linked to this sale order that are not cancelled automatically! <br/>\n"
"                </span>\n"
"            </span>"
msgstr ""
"<span id=\"display_invoice_alert\" position=\"after\">\n"
"                <span id=\"display_purchase_orders_alert\" invisible=\"not display_purchase_orders_alert\" groups=\"purchase.group_purchase_user\">\n"
"                   К этому заказу на продажу привязаны активные заказы на закупку, которые не отменяются автоматически! <br/>\n"
"                </span>\n"
"            </span>"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "Исключения произошли в заказах на закупку:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Исключения произошли в заказах на продажу:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s):"
msgstr "Исключения:"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_ids
msgid "Generated Purchase Lines"
msgstr "Сгенерированные строки закупок"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_template__service_to_purchase
msgid ""
"If ticked, each time you sell this product through a SO, a RfQ is "
"automatically created to buy the product. Tip: don't forget to set a vendor "
"on the product."
msgstr ""
"Если флажок установлен, каждый раз, когда вы продаете этот продукт через СЦ,"
" автоматически создается RfQ для покупки продукта. Совет: не забудьте "
"установить продавца для продукта."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Manual actions may be needed."
msgstr "Могут потребоваться ручные действия."

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order__purchase_order_count
msgid "Number of Purchase Order Generated"
msgstr "Количество сгенерированных заказов на поставку"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order__sale_order_count
msgid "Number of Source Sale"
msgstr "Количество источников продажи"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_count
msgid "Number of generated purchase items"
msgstr "Количество сгенерированных элементов покупки"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order_line.py:0
msgid "Ordered quantity decreased!"
msgstr "Заказанное количество уменьшилось!"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_line_id
msgid "Origin Sale Item"
msgstr "Происхождения Товаров"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/product_template.py:0
msgid ""
"Please define the vendor from whom you would like to purchase this service "
"automatically."
msgstr ""
"Укажите поставщика, у которого вы хотели бы приобретать эту услугу "
"автоматически."

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_product_template
msgid "Product"
msgstr "Товар"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/product_template.py:0
msgid "Product that is not a service can not create RFQ."
msgstr "Продукт, который не является услугой, не может создавать RFQ."

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Заказ на покупку"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_cancel__display_purchase_orders_alert
msgid "Purchase Order Alert"
msgstr "Оповещение о заказах на поставку"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Позиция заказа на покупку"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order.py:0
msgid "Purchase Order generated from %s"
msgstr "Заказ на поставку, сформированный из %s"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_sale_order_line__purchase_line_ids
msgid ""
"Purchase line generated by this Sales item on order confirmation, or when "
"the quantity was increased."
msgstr ""
"Строка закупки, созданная для данного элемента продажи при подтверждении "
"заказа или при увеличении количества."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.product_template_form_view_inherit
msgid "Reordering"
msgstr "Восполнение"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_order_id
msgid "Sale Order"
msgstr "Заказ на продажу"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Отмена заказа на продажу"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/purchase_order.py:0
msgid "Sources Sale Orders %s"
msgstr "Источники Заказы на продажу %s"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_template__service_to_purchase
msgid "Subcontract Service"
msgstr "Субподрядные услуги"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order_line.py:0
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr ""
"Нет поставщика, связанного с ТМЦ %s. Пожалуйста, определите поставщика для "
"этого ТМЦ."

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order_line.py:0
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the purchase order if needed."
msgstr ""
"Вы уменьшаете заказанное количество! Не забудьте вручную обновить заказ на "
"покупку, если это необходимо."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "cancelled"
msgstr "отменен"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "of"
msgstr "из"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "ordered instead of"
msgstr "заказано вместо"
