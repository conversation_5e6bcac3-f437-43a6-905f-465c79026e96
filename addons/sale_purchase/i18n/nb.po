# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase
# 
# Translators:
# <PERSON>e Restad, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: <PERSON> Bokmål (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Manuelle handlinger kan være nødvendig."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_inherited_form_purchase
msgid "<span class=\"o_stat_text\">Purchase</span>"
msgstr ""

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.purchase_order_inherited_form_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr "<span class=\"o_stat_text\"> Salg </span>"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_cancel_view_form
msgid ""
"<span id=\"display_invoice_alert\" position=\"after\">\n"
"                <span id=\"display_purchase_orders_alert\" invisible=\"not display_purchase_orders_alert\" groups=\"purchase.group_purchase_user\">\n"
"                    There are active purchase orders linked to this sale order that are not cancelled automatically! <br/>\n"
"                </span>\n"
"            </span>"
msgstr ""

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "Ett eller flere unntak oppstod med innkjøpsordren(e):"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Ett eller flere unntak oppstod med salgsordren(e):"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s):"
msgstr "Unntak:"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_ids
msgid "Generated Purchase Lines"
msgstr "Genererte innkjøpslinjer"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_template__service_to_purchase
msgid ""
"If ticked, each time you sell this product through a SO, a RfQ is "
"automatically created to buy the product. Tip: don't forget to set a vendor "
"on the product."
msgstr ""
"Hvis huket av, vil en RFQ (innkjøpsforespørsel) bli automatisk opprettet for"
" innkjøp av produktet, hver gang du selger det i en salgsordre. PS: Ikke "
"glem å sette leverandør på produktet."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Manual actions may be needed."
msgstr "Manuel håndtering kan være nødvendig"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order__purchase_order_count
msgid "Number of Purchase Order Generated"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order__sale_order_count
msgid "Number of Source Sale"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_count
msgid "Number of generated purchase items"
msgstr "Antall genererte innkjøpsvarer"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order_line.py:0
msgid "Ordered quantity decreased!"
msgstr "Bestilt antall er redusert!"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_line_id
msgid "Origin Sale Item"
msgstr "Kilde for salgsvare"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/product_template.py:0
msgid ""
"Please define the vendor from whom you would like to purchase this service "
"automatically."
msgstr ""

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/product_template.py:0
msgid "Product that is not a service can not create RFQ."
msgstr "Et produkt som ikke er en tjeneste, kan ikke opprette en RFQ."

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Innkjøpsordre"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_cancel__display_purchase_orders_alert
msgid "Purchase Order Alert"
msgstr ""

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Innkjøpsordrelinje"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order.py:0
msgid "Purchase Order generated from %s"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_sale_order_line__purchase_line_ids
msgid ""
"Purchase line generated by this Sales item on order confirmation, or when "
"the quantity was increased."
msgstr ""
"Innkjøpslinje generert av dette salgselementet ved ordrebekreftelse, eller "
"da antallet økte."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.product_template_form_view_inherit
msgid "Reordering"
msgstr "Bestilling"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_order_id
msgid "Sale Order"
msgstr "Salgsordre"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order
msgid "Sales Order"
msgstr "Salgsordre"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Salgsordre kansellere"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_line
msgid "Sales Order Line"
msgstr "Salgsordrelinje"

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/purchase_order.py:0
msgid "Sources Sale Orders %s"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_template__service_to_purchase
msgid "Subcontract Service"
msgstr ""

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order_line.py:0
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr ""
"Det er ikke satt en leverandør på produktet %s. Du må angi en leverandør for"
" for dette produktet."

#. module: sale_purchase
#. odoo-python
#: code:addons/sale_purchase/models/sale_order_line.py:0
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the purchase order if needed."
msgstr ""
"Du er i ferd med å redusere bestilt antall. Ikke glem å manuelt oppdatere "
"innkjøpsordren, om nødvendig."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "cancelled"
msgstr "kansellert"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "of"
msgstr "av"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "ordered instead of"
msgstr "bestilt istedenfor"
