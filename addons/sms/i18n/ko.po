# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# <PERSON><PERSON>, 2025
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "# 잘못된 수신자"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "# 유효한 수신자"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid ""
"%(count)s out of the %(total)s selected SMS Text Messages have successfully "
"been resent."
msgstr "선택한 %(count)s개의 SMS 문자 메시지 중 %(total)s를 성공적으로 재전송했습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "%s invalid recipients"
msgstr "%s 잘못된 수신자"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "******-555-555"
msgstr "******-555-555"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid ", fits in"
msgstr ","

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> An error occurred with your account. Please "
"contact the support for more information..."
msgstr "<i class=\"fa fa-warning\"/> 계정에 오류가 발생했습니다. 자세한 내용은 지원팀에 문의하세요..."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> You cannot send SMS while your account is not "
"registered."
msgstr "<i class=\"fa fa-warning\"/> 계정이 등록되지 않은 상태에서는 SMS를 보낼 수 없습니다."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Register"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            등록하기"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Set Sender Name"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            발신자 이름 설정"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">추가하기</span>\n"
"                                <span class=\"o_stat_text\">상황별 작업</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "<span class=\"o_stat_text\">Preview</span>"
msgstr "<span class=\"o_stat_text\">미리 보기</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">제거하기</span>\n"
"                                <span class=\"o_stat_text\">상황별 작업</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "<span class=\"text-warning\" invisible=\"not no_record\">No records</span>"
msgstr "<span class=\"text-warning\" invisible=\"not no_record\">기록 없음</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                The message will be sent as an SMS to the recipients of the template\n"
"                                and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                The SMS will not be sent, it will only be posted as an\n"
"                                internal note in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                The SMS will be sent as an SMS to the recipients of the\n"
"                                template and it will also be posted as an internal note\n"
"                                in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                메시지는 서식을 수신하는 대상에게 SMS로 전송되며\n"
"                                메시지 이력에는 표시되지 않습니다.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                SMS는 전송되지 않고 메시지 내역에서 내부 사용자만 볼 수 있도록\n"
"                                메모로 게시됩니다.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                메시지는 서식을 수신하는 대상에게 SMS로 전송되며\n"
"                                메시지 이력에서 내부 사용자만 볼 수 있도록\n"
"                                메모로 게시됩니다.\n"
"                            </span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "<span> or to specify the country code.</span>"
msgstr "<span> 또는 국가 코드를 지정합니다.</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"<strong>Invalid number:</strong>\n"
"                        <span> make sure to set a country on the </span>"
msgstr ""
"<strong>잘못된 번호:</strong>\n"
"                        <span> 다음에 국가를 설정해야 합니다. </span>"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_tracker_sms_uuid_unique
msgid "A record for this UUID already exists"
msgstr "이 UUID에 대한 레코드가 이미 존재합니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__account_id
msgid "Account"
msgstr "계정"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr "이 서식을 사용하여 SMS 작성기를 열려면 관련 모델에 대한 상황별 작업을 추가하십시오."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__uuid
msgid "Alternate way to identify a SMS record, used for delivery reports"
msgstr "배송 보고서에 사용되는 SMS 레코드를 식별할 수 있는 다른 방법"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS"
msgstr "SMS 발송 중 오류가 발생했습니다."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS on “%(record_name)s”"
msgstr "“%(record_name)s”으로 SMS 메시지를 보내는 중 오류가 발생했습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"An unknown error occurred. Please contact Odoo support if this error "
"persists."
msgstr "알 수 없는 오류가 발생했습니다. 문제가 지속되면 Odoo 지원팀에 문의하세요."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "적용 대상"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid ""
"Are you sure you want to reset these sms templates to their original "
"configuration? Changes and translations will be lost."
msgstr "이 SMS 서식을 원래의 구성으로 초기화하시겠습니까? 변경 사항 및 번역한 내용은 저장되지 않습니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: sms
#: model:ir.model,name:sms.model_base
msgid "Base"
msgstr "기준액"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "수신 거부"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "수신 거부 연락처가 휴대폰입니다"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "수신 거부 연락처가 전화번호입니다"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "본문"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "크레딧 구매하기"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Buy credits."
msgstr "크레딧을 구매하십시오."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_cancel
msgid "Can Cancel"
msgstr "취소 가능"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_resend
msgid "Can Resend"
msgstr "재발송 가능"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "취소"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Cancelled"
msgstr "취소됨"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr "언어 선택 :"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid "Choose a template..."
msgstr "서식을 선택하세요."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "예제 선택"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_code.py:0
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Choose your sender name"
msgstr "보낸 사람 이름 선택"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "닫기"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr "작성 모드"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "연락처"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "내용"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_country_not_supported
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_country_not_supported
msgid "Country Not Supported"
msgstr "지원되지 않는 국가입니다."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_registration_needed
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_registration_needed
msgid "Country-specific Registration Required"
msgstr "국가별로 등록해야 합니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Country-specific registration required."
msgstr "국가별로 등록해야 합니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_uid
msgid "Created by"
msgstr "작성자"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_date
msgid "Created on"
msgstr "작성일자"

#. module: sms
#: model:iap.service,unit_name:sms.iap_service_sms
msgid "Credits"
msgstr "크레딧"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "고객"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr "고객 : 자동화된 SMS"

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr "안녕하세요 {{ object.display_name }} 본 SMS는 자동발송되는 문자입니다."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Delivered"
msgstr "배송완료"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "취소"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__display_name
#: model:ir.model.fields,field_description:sms.field_sms_tracker__display_name
msgid "Display Name"
msgstr "표시명"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "문서 팔로워"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "문서 ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr "문서 ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model_description
msgid "Document Model Description"
msgstr "문서 모델 설명"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "문서 모델명"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "복사"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "협력사 편집하기"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "이메일 스레드"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/phone_field/phone_field.js:0
msgid "Enable SMS"
msgstr "SMS 활성화"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Enter a phone number to get an SMS with a verification code."
msgstr "SMS로 인증 코드를 받을 휴대폰 번호를 입력하세요."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "오류"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Error Message"
msgstr "오류 메시지"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_expired
msgid "Expired"
msgstr "만료됨"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr "실패 유형"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
msgid "Failure type"
msgstr "실패 유형"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "삭제된 전화 번호를 저장하는 데 사용되는 필드입니다. 검색 및 비교 속도를 높이는 데 도움이 됩니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr "서식이 만들어진 원래의 파일입니다. 서식이 손상된 경우 다시 제작하는데 사용합니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Following numbers are not correctly encoded: %s"
msgstr "다음 숫자는 올바르게 인코딩되지 않았습니다 : %s"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr "크레딧 부족함"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr "SMS 오류임"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr "등록되지 않은 계정 있음"

#. module: sms
#: model:ir.model,name:sms.model_iap_account
msgid "IAP Account"
msgstr "IAP 계정"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__id
#: model:ir.model.fields,field_description:sms.field_sms_tracker__id
msgid "ID"
msgstr "ID"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"검증한 전화번호가 블랙리스트에 있는 번호인 경우, 어느 목록에서도 해당 연락처에 대해서는 더 이상 대량 발송 SMS를 보내지 않게 "
"됩니다. "

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr "전체 무시"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "대기열"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"해당 핸드폰 번호가 블랙리스트에 오른 번호인지 여부를 나타냅니다. 모델에 핸드폰과 유선전화 필드 양쪽이 있는 경우 블랙리스트에 올라 있는"
" 번호를 구분하는데 사용합니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"해당 전화번호가 블랙리스트에 오른 번호인지 여부를 나타냅니다. 모델에 핸드폰과 유선전화 필드 양쪽이 있는 경우 블랙리스트에 올라 있는 "
"번호를 구분하는데 사용합니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr "SMS 작성기가 단일 특정 수신자를 대상으로 하는지 여부를 나타냅니다."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "크레딧 부족"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_invalid_destination
msgid "Invalid Destination"
msgstr "대상이 잘못되었습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Invalid phone number. Please make sure to follow the international format, "
"i.e. a plus sign (+), then country code, city code, and local phone number. "
"For example: ******-555-555"
msgstr ""
"입력한 전화번호가 잘못되었습니다. 더하기 기호(+) 다음에 국가 번호, 도시 번호, 지역 전화번호가 오는 국제 형식을 따르는지 "
"확인하세요. 예를 들면 다음과 같습니다: ******-555-555."

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Invalid recipient number. Please update it."
msgstr "수신자 번호가 잘못되었습니다. 변경하시기 바랍니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr "유효함"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr "문서에 노트 유지"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "사용 언어"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: sms
#: model:ir.model,name:sms.model_sms_tracker
msgid "Link SMS to mailing/sms tracking models"
msgstr "SMS를 메일링 및 SMS 추적 모델에 연결합니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr "메일 메시지"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__mail_notification_id
msgid "Mail Notification"
msgstr "메일 알림"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr "메일 스레드 SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__to_delete
msgid "Marked for deletion"
msgstr "삭제 표시"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "메시지"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "메시지 알림"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "메시지"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "누락된 번호"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "모델"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "이름"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr "기록 없음"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_allowed
msgid "Not Allowed"
msgstr "허용되지 않음"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_delivered
msgid "Not Delivered"
msgstr "미배송"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__note
msgid "Note only"
msgstr "메모만"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Note that this is not required, if you don't set a sender name, your SMS "
"will be sent from a short code."
msgstr "발신자 이름을 지정하지 않으면 SMS가 짧은 코드로 전송되므로 반드시 필요한 사항은 아닙니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "알림"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "알림 유형"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "숫자"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr "숫자 필드"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr "활성화된 도메인 값을 적용하지 않고 대량 모드로 전송할 경우에 SMS를 수신할 수신자 수"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "선택하지 않음"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"이메일 전송 시 번역할 언어 (ISO code)를 선택할 수 있습니다. 설정하지 않을 경우, 영어가 기본 언어로 사용됩니다. 적절한 "
"언어를 선택할 수 있도록 다음과 같은 예시용 메시지를 사용합니다. {{ object.partner_id.lang }}."

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "발신 SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "협력사"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "수신 거부 전화번호"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__phone_number
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
msgid "Phone Number"
msgstr "전화번호"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"Phone number of the recipient. If changed, it will be recorded on "
"recipient's profile."
msgstr "수신자의 전화 번호입니다. 변경하면, 수신자의 프로필에 저장됩니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "전화번호/휴대전화"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr "문서에 게시"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "다음 미리보기"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid "Proceed"
msgstr "진행"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__process
msgid "Processing"
msgstr "처리 중"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "대기열에 넣기"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "사유"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "수신인"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
msgid "Recipient Name"
msgstr "수신인 이름"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr "수신자 번호"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "수신인"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr "수신인 (번호)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr "수신인 (협력사)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr "레코드 참조"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
msgid "Register"
msgstr "등록"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_phone.py:0
msgid "Register Account"
msgstr "계정 등록"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Register now."
msgstr "지금 등록하세요."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Register your SMS account"
msgstr "SMS 계정 등록"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_rejected
msgid "Rejected"
msgstr "거부됨"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "관련 문서 모델"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr "관련된 모델의 상황별 작업 제거"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "렌더링 모델"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
msgid "Resend"
msgstr "재전송"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr "알림 재전송"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_reset_action
msgid "Reset SMS Template"
msgstr "SMS 서식 초기화"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Reset Template"
msgstr "서식 재설정"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "재시도"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.xml:0
#: code:addons/sms/static/src/core/notification_model_patch.js:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "SMS"
msgstr "SMS"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS ("
msgstr "SMS ("

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__comment
msgid "SMS (with note)"
msgstr "SMS (메모 포함)"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__sms
msgid "SMS (without note)"
msgstr "SMS (메모 포함하지 않음)"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_phone
msgid "SMS Account Registration Phone Number Wizard"
msgstr "SMS 계정 등록 전화번호 마법사"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_sender
msgid "SMS Account Sender Name Wizard"
msgstr "SMS 계정 발신자 이름 마법사"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_code
msgid "SMS Account Verification Code Wizard"
msgstr "SMS 계정 인증 코드 마법사"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failure: %(modelName)s"
msgstr "SMS 실패: %(modelName)s"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failures"
msgstr "SMS 전송 실패"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id_int
msgid "SMS ID"
msgstr "SMS ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr "SMS 번호"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr "SMS 미리보기"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS Pricing"
msgstr "SMS 요금"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr "SMS 재전송"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr "SMS 상태"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "SMS 서식"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr "SMS 서식 미리보기"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_reset
msgid "SMS Template Reset"
msgstr "SMS 서식 초기화"

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr "SMS 서식"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_template_reset.py:0
msgid "SMS Templates have been reset"
msgstr "SMS 서식이 초기화되었습니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_tracker_ids
msgid "SMS Trackers"
msgstr "SMS 추적기"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr "SMS 내용"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "SMS template model of %(action_name)s does not match action model."
msgstr "%(action_name)s의 SMS 서식 모델이 작업 모델과 일치하지 않습니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__sms_tracker_id
msgid "SMS trackers"
msgstr "SMS 추적기"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__sms_uuid
msgid "SMS uuid"
msgstr "SMS uuid"

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
msgid "SMS: SMS Queue Manager"
msgstr "SMS : SMS  대기열 관리자"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr "제거된 번호"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr "SMS 서식 검색"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Send & Close"
msgstr "전송 후 닫기"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "지금 보내기"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "SMS 보내기"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "Send SMS (%s)"
msgstr "SMS 보내기 (%s)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_method
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_method
msgid "Send SMS As"
msgstr "다른 이름으로 SMS 보내기"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "SMS 전송 마법사"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr "SMS 일괄 전송하기"

#. module: sms
#: model:iap.service,description:sms.iap_service_sms
msgid "Send SMS to your contacts directly from your database."
msgstr "데이터베이스에 있는 연락처로 직접 SMS를 보낼 수 있습니다."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "SMS 보내기"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr "직접 보내기"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr "번호로 보내기"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Send verification code"
msgstr "인증 코드 전송"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_iap_account__sender_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__sender_name
msgid "Sender Name"
msgstr "발신자 이름"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr "전송 실패"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "Sending SMS can only be done on a not transient mail.thread model"
msgstr "SMS 전송은 임시 메일 스레드 모델이 아닌 경우에만 가능합니다."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__pending
msgid "Sent"
msgstr "전송됨"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "서버작업"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "서버 오류"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Set sender name"
msgstr "발신자 이름 설정"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr "계정 만들기"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "사이드바 동작"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr "관련 문서 모델의 레코드에서 이 서식을 사용할 수 있도록 하는 사이드바 동작"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr "단일 모드"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Skip for now"
msgstr "지금 건너뛰기"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr "SMS 재전송"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr "SMS 서식"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr "저장된 수신자 번호"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Success"
msgstr "성공"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__template_ids
msgid "Template"
msgstr "서식"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__template_fs
msgid "Template Filename"
msgstr "서식 파일명"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "서식 미리보기"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "서식 미리보기 언어"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "템플릿(서식)"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"The SMS Service is currently unavailable for new users and new accounts "
"registrations are suspended."
msgstr "현재 신규 사용자 및 계정에 대한 신규 SMS 서비스 등록이 일시 중단되었습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "The SMS Text Messages could not be resent."
msgstr "SMS 문자 메시지는 다시 전송할 수 없습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The content of the message violates rules applied by our providers."
msgstr "메시지 내용이 트위터에서 적용하는 규칙을 위반합니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The destination country is not supported."
msgstr "대상 국가는 지원되지 않습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The number you're trying to reach is not correctly formatted."
msgstr "연락 번호 형식이 올바르지 않습니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "이 서식을 사용할 수 있는 문서 유형"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The verification code is incorrect."
msgstr "인증 코드가 잘못되었습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "There are no SMS Text Messages to resend."
msgstr "재전송할 SMS 문자 메시지가 없습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This SMS has been removed as the number was already used."
msgstr "이 번호는 이미 사용되었기 때문에 삭제되었습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"This account already has an existing sender name and it cannot be changed."
msgstr "이 계정에는 이미 수정할 수 없는 발신자 이름이 있습니다."

#. module: sms
#: model:ir.model.fields,help:sms.field_iap_account__sender_name
msgid "This is the name that will be displayed as the sender of the SMS."
msgstr "SMS 발신자로 표시되는 이름입니다."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr "이 전화번호는 SMS 마케팅에서 블랙리스트에 올라있습니다. 블랙리스트를 해제하려면 클릭하세요."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This phone number/account has been banned from our service."
msgstr "이 전화번호 또는 계정은 서비스 이용이 금지되었습니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
msgid "Try Again"
msgstr "다시 시도하세요"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "유형"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"서버 작업 유형입니다. 다음 값을 사용할 수 있습니다:\n"
"- '레코드 업데이트': 레코드 값을 업데이트합니다.\n"
"- '활동 만들기': 활동을 생성합니다. (토론)\n"
"- '이메일 보내기': 메시지, 메모를 게시하거나 이메일을 전송합니다. (토론)\n"
"- 'SMS 보내기': SMS를 보내고, 문서에 기록합니다. (SMS)- '팔로워 추가/제거': 레코드에 팔로워를 추가하거나 제거합니다 .(토론)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__uuid
msgid "UUID"
msgstr "UUID"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_sms_uuid_unique
msgid "UUID must be unique"
msgstr "UUID는 고유해야 합니다."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__unknown
msgid "Unknown error"
msgstr "알 수 없는 오류"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "등록되지 않은 계정"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr "서식 사용"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr "수신 거부 이용"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"메세지 작성을 분류하는 데 사용됩니다.\n"
"'email': 수신 이메일에 의해 작성됩니다. (예: 메일 게이트웨이)\n"
"'comment': 토론 또는 작성란을 통해 사용자가 입력할 수 있습니다.\n"
"'email_outgoing': 메일링에 의해 작성됩니다.\n"
"'notification': 시스템에서 생성됩니다. (예: 메시지 추적)\n"
"'auto_comment': 자동화된 알림 메커니즘에 의해 생성됩니다. (예: 승인)\n"
"'user_notification': 특정 수신자를 위해 생성됩니다."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__verification_code
msgid "Verification Code"
msgstr "인증 코드"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr "보이는 레코드 수"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Warning"
msgstr "주의"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "We were not able to find your account in our database."
msgstr "데이터베이스에서 귀하의 계정을 찾을 수 없습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"We were not able to reach you via your phone number. If you have requested "
"multiple codes recently, please retry later."
msgstr "귀하의 휴대폰 번호로 연결할 수 없습니다. 최근에 여러 번 코드를 요청한 경우 나중에 다시 시도하세요."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr "이 모델이 SMS를 통한 메시지 및 알림을 지원하는지 여부"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__to_delete
msgid ""
"Will automatically be deleted, while notifications will not be deleted in "
"any case."
msgstr "자동으로 삭제되며, 알림은 그 어떤 경우에도 삭제되지 않습니다."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "잘못된 번호 형식"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_resend.py:0
msgid "You do not have access to the message and/or related document."
msgstr "메시지 또는 관련 문서에 접근할 수 없습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have an eligible IAP account."
msgstr "적합한 IAP 계정이 없습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have enough credits on your IAP account."
msgstr "IAP 계정에 크레딧이 충분하지 않습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You tried too many times. Please retry later."
msgstr "시도 횟수가 너무 많습니다. 나중에 다시 시도하세요."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr "SMS 문자 메시지에는 공백이 아닌 문자가 하나 이상 있어야 합니다."

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_account_code.py:0
msgid "Your SMS account has been successfully registered."
msgstr "SMS 계정 등록이 완료되었습니다."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain "
"alphanumeric characters."
msgstr "발신자 이름은 3~11자 사이여야 하며 영숫자만 포함해야 합니다."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain alphanumeric characters.\n"
"                        It must fit your company name, and you aren't allowed to modify it once you registered one, choose it carefully."
msgstr ""
"발신자 이름은 3~11자 사이여야 하며 영숫자로만 구성되어야 합니다.\n"
"                        회사명과 일치해야 하며, 한 번 등록하면 변경할 수 없으므로 신중하게 선택하세요."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Your sms account has not been activated yet."
msgstr "SMS 계정이 아직 활성화되지 않았습니다."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "characters"
msgstr "문자"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr "예. +1 415 555 0100"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr "예: 캘린더 알림"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr "예. 연락처"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr "예: en_US or {{ object.partner_id.lang }} "

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "out of"
msgstr "다음에서"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients have an invalid phone number and will not receive this text "
"message."
msgstr "수신자 전화번호가 잘못되었기 때문에 이 문자 메시지를 수신할 수 없습니다."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr "레코드 :"
