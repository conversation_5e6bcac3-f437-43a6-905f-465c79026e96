# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_facturae
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-21 14:26+0000\n"
"PO-Revision-Date: 2025-05-21 14:26+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.corrective_type
msgid "01"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.account_invoice_facturae_export
msgid "3.2.2"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__27
msgid "6-Packs"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.view_partner_form_inherit_l10n_es_edi_facturae
msgid ""
"<span>Administrative Center for Spain Public Administrations. Used in "
"Spanish electronic invoices.</span>"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__05
msgid "Accepted bill of exchange"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_account_move_reversal
msgid "Account Move Reversal"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_account_move_send
msgid "Account Move Send"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_partner__type
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_users__type
msgid "Address Type"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_l10n_es_edi_facturae_ac_role_type
msgid "Administrative Center Role Type"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__13
msgid "Applicable Date/Period"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__12
msgid "Applicable Tax Amount"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__11
msgid "Applicable Tax Rate"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__10
msgid "Bags"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__17
msgid "Banker’s draft"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__08
msgid "Barrels"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__08
msgid "Bill of exchange"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__17
msgid "Bins"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__12
msgid "Bottles, non-protected, cylindrical"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__06
msgid "Boxes"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__24
msgid "Bunches"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_04
msgid "Buyer"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__81
msgid "Calculation of tax inputs"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__80
msgid "Calculation of tax outputs"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__13
msgid "Canisters"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__23
msgid "Cans, rectangular"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__11
msgid "Carboys, non-protected"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__19
msgid "Cases"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__18
msgid "Cash on delivery"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__15
msgid "Centiliters"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__16
msgid "Centimeters"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_certificate_certificate
msgid "Certificate"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.ui.menu,name:l10n_es_edi_facturae.menu_l10n_es_edi_facturae_root_certificates
msgid "Certificates"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.actions.act_window,name:l10n_es_edi_facturae.l10n_es_edi_facturae_certificate_action
msgid "Certificates for Facturae EDI invoices on Spain"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__16
msgid "Certified cheque"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__11
msgid "Cheque"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__code
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_center_code
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_center_code
msgid "Code"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_logical_operational_point
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_logical_operational_point
msgid ""
"Code identifying the company. Barcode of 13 standard positions. Codes are "
"registered in Spain by AECOC. The code is made up of the country code (2 "
"positions) Spain is '84' + Company code (5 positions) + the remaining "
"positions. The last one is the product + check digit."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_center_code
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_center_code
msgid "Code of the issuing department."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_08
msgid "Collection Receiver"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_05
msgid "Collector"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__07
msgid "Contract award"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__29
msgid "Corporation Tax"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid ""
"Could not retrieve currency: %s. Did you enable the multicurrency option and"
" activate the currency?"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "Could not retrieve the tax: %(tax_rate)s %% for line '%(line)s'."
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.actions.act_window,help:l10n_es_edi_facturae.l10n_es_edi_facturae_certificate_action
msgid "Create the first certificate"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "Created from attachment in %s"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__04
msgid "Credit transfer"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__33
msgid "Cubic meter"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid ""
"Customer/Vendor could not be found and could not be created due to missing "
"data in the XML."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__20
msgid "Demijohns, non-protected"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__02
msgid "Direct debit"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__06
msgid "Documentary credit"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__18
msgid "Dozens"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__31
msgid "Envelopes"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move_send.py:0
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__26
msgid "Excise duty applied to manufactured tobacco in Canaries"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__res_partner__type__facturae_ac
msgid "FACe Center"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.view_move_form
msgid "Factura-e"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__certificate_certificate__scope__facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__res_partner__invoice_edi_format__es_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.certificate_certificate_view_search
msgid "Facturae"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_bank_statement_line__l10n_es_edi_facturae_xml_id
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_move__l10n_es_edi_facturae_xml_id
msgid "Facturae Attachment"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_company__l10n_es_edi_facturae_residence_type
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_residence_type
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_residence_type
msgid "Facturae EDI Residency Type Code"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_company__l10n_es_edi_facturae_certificate_ids
msgid "Facturae EDI signing certificate"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_bank_statement_line__l10n_es_edi_facturae_xml_file
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_move__l10n_es_edi_facturae_xml_file
msgid "Facturae File"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.certificate_certificate_view_search
msgid "Facturae certificates"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_01
msgid "Fiscal"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__21
msgid "Grams"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__02
msgid "Hours"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__12
msgid "ICIO: Tax on construction, installation and works"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__id
msgid "ID"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__25
msgid "IDEC: Tax on bank deposits"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__07
msgid "IE: Excise duties and consumption taxes"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__10
msgid "IECDPCAC: Excise duties on oil derivates in Canaries"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__27
msgid "IGFEI: Tax on Fluorinated Greenhouse Gases"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__03
msgid "IGIC: Canaries General Indirect Tax"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__09
msgid "IGTECM: Sales tax in Ceuta and Melilla"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__11
msgid ""
"IIIMAB: Tax on premises that affect the environment in the Balearic Islands"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__15
msgid "IMGSN: Local sumptuary tax in Navarre"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__16
msgid "IMPN: Local tax on advertising in Navarre"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__14
msgid "IMSN: Local tax on building plots in Navarre"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__13
msgid "IMVDN: Local tax on unoccupied homes in Navarre"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__20
msgid "IPS: Insurance premiums Tax"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__28
msgid "IRNR: Non-resident Income Tax"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__04
msgid "IRPF: Personal Income Tax"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__06
msgid "ITPAJD: Tax on wealth transfers and stamp duty"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__22
msgid "IVPEE: Tax on the value of electricity generation"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_physical_gln
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_physical_gln
msgid ""
"Identification of the connection point to the VAN EDI (Global Location "
"Number). Barcode of 13 standard positions. Codes are registered in Spain by "
"AECOC. The code is made up of the country code (2 positions) Spain is '84' +"
" Company code (5 positions) + the remaining positions. The last one is the "
"product + check digit."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__01
msgid "In cash"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__14
msgid "Invoice Class"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_bank_statement_line__l10n_es_invoicing_period_end_date
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_move__l10n_es_invoicing_period_end_date
msgid "Invoice Period End Date"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_bank_statement_line__l10n_es_invoicing_period_start_date
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_move__l10n_es_invoicing_period_start_date
msgid "Invoice Period Start Date"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "Invoice imported from Factura-E XML file."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__01
msgid "Invoice number"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__02
msgid "Invoice serial number"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__03
msgid "Issue date"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_09
msgid "Issuer"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__06
msgid "Issuer's Tax Identification Number"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__08
msgid "Issuer's address"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_role_type_ids
#: model:ir.model.fields,help:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_role_type_ids
msgid ""
"It indicates the role played by the Operational Point defined as a Workplace/Department.\n"
"These functions are:\n"
"- Receiver: Workplace associated to the recipient's tax identification number where the invoice will be received.\n"
"- Payer: Workplace associated to the recipient's tax identification number responsible for paying the invoice.\n"
"- Buyer: Workplace associated to the recipient's tax identification number who issued the purchase order.\n"
"- Collector: Workplace associated to  the issuer's tax identification number responsible for handling the collection.\n"
"- Fiscal: Workplace associated to the recipient's tax identification number, where an Operational Point mailbox is shared by different client companies with different tax identification numbers and it is necessary to differentiate between where the message is received (shared letterbox) and the workplace where it must be stored (recipient company)."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__10
msgid "Item line"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__09
msgid "Jerricans, cylindrical"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__03
msgid "Kilograms"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__22
msgid "Kilometers"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__36
msgid "Kilowatt-hour"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__15
msgid "Legal literals"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__04
msgid "Liters"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_logical_operational_point
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_logical_operational_point
msgid "Logical Operational Point"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__25
msgid "Meters"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__26
msgid "Millimeters"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_l10n_es_edi_facturae_ac_role_type__name
msgid "Name"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__04
msgid "Name and surnames/Corporate name - Issuer (Sender)"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__05
msgid "Name and surnames/Corporate name - Receiver"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "No valid certificate found"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid ""
"No valid certificate found for this company, Facturae EDI file will not be "
"signed.\n"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__10
msgid "Non transferable promissory note"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.template_xades_signature
msgid "Ohixl6upD6av8N7pEvDABhEL6hM="
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__12
msgid "Open account reimbursement"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__05
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__05
msgid "Other"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__28
msgid "Packages"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_03
msgid "Payer"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_bank_statement_line__l10n_es_payment_means
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_move__l10n_es_payment_means
msgid "Payment Means"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_07
msgid "Payment Receiver"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__19
msgid "Payment by card"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__15
msgid "Payment by postgiro"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_physical_gln
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_physical_gln
msgid "Physical GLN"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__29
msgid "Portions"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__08
msgid "RA: Customs duties"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__18
msgid "REIGIC: Special IGIC: for travel agencies"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__19
msgid "REIPSI: Special IPSI for travel agencies"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__17
msgid "REIVA: Special VAT for travel agencies"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.view_account_move_reversal_inherit_l10n_es_edi_facturae
msgid "Reason"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__03
msgid "Receipt"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_02
msgid "Receiver"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__07
msgid "Receiver's Tax Identification Number"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__09
msgid "Receiver's address"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.corrective_type
msgid "Rectificación modelo íntegro."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_partner__l10n_es_edi_facturae_ac_role_type_ids
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_users__l10n_es_edi_facturae_ac_role_type_ids
msgid "Roles"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__30
msgid "Rolls"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__21
msgid "SWUA: Surcharge for Winding Up Activity"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__34
msgid "Second"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:l10n_es_edi_facturae.ac_role_type,name:l10n_es_edi_facturae.ac_role_type_06
msgid "Seller"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__14
msgid "Set-off by reciprocal credits"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.ui.menu,name:l10n_es_edi_facturae.menu_l10n_es_edi_facturae_root
msgid "Spain Facturae EDI"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_uom_uom__l10n_es_edi_facturae_uom_code
msgid "Spanish EDI Units"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_bank_statement_line__l10n_es_edi_facturae_reason_code
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_move__l10n_es_edi_facturae_reason_code
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_move_reversal__l10n_es_edi_facturae_reason_code
msgid "Spanish Facturae EDI Reason Code"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_account_tax__l10n_es_edi_facturae_tax_type
msgid "Spanish Facturae EDI Tax Type"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.product_uom_categ_form_view_inherit_l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.product_uom_form_view_inherit_l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.product_uom_tree_view_inherit_l10n_es_edi_facturae
msgid "Spanish Facturae EDI type"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.view_tax_tree_inherit_l10n_es_edi_facturae
msgid "Spanish Tax Type"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__13
msgid "Special payment"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model,name:l10n_es_edi_facturae.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__23
msgid ""
"Tax on the production of spent nuclear fuel and radioactive waste from the "
"generation of nuclear electric power"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__24
msgid ""
"Tax on the storage of spent nuclear energy and radioactive waste in "
"centralised facilities"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__16
msgid "Taxable Base"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__83
msgid "Taxable Base modified due to discounts and rebates"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__84
msgid ""
"Taxable Base modified due to firm court ruling or administrative decision"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__82
msgid ""
"Taxable Base modified due to return of packages and packaging materials"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_edi_facturae_reason_code__85
msgid ""
"Taxable Base modified due to unpaid outputs where there is a judgement "
"opening insolvency proceedings"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__02
msgid "Taxes on production, services and imports in Ceuta and Melilla"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__14
msgid "Tetra Briks"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/res_partner.py:0
msgid "The Logical Operational Point entered is not valid."
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/res_partner.py:0
msgid "The Physical GLN entered is not valid."
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "The company needs a set tax identification number or VAT number"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid ""
"The credit note/refund appears to have been issued manually. For the purpose"
" of generating a Facturae document, it's necessary that the credit "
"note/refund is created directly from the associated invoice/bill."
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "The partner needs a set country"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "The partner needs a set tax identification number or VAT number"
msgstr ""

#. module: l10n_es_edi_facturae
#. odoo-python
#: code:addons/l10n_es_edi_facturae/models/account_move.py:0
msgid "The product '%s' could not be found."
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_move__l10n_es_payment_means__09
msgid "Transferable promissory note"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__07
msgid "Trays, one layer no cover, plastic"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__32
msgid "Tubs"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__01
msgid "Units"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__account_tax__l10n_es_edi_facturae_tax_type__01
msgid "Value-Added Tax"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae.selection__uom_uom__l10n_es_edi_facturae_uom_code__35
msgid "Watt"
msgstr ""

#. module: l10n_es_edi_facturae
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_partner__invoice_edi_format
#: model:ir.model.fields,field_description:l10n_es_edi_facturae.field_res_users__invoice_edi_format
msgid "eInvoice format"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.template_xades_signature
msgid "text/xml"
msgstr ""

#. module: l10n_es_edi_facturae
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae.template_xades_signature
msgid "urn:oid:1.2.840.10003.5.109.10"
msgstr ""
