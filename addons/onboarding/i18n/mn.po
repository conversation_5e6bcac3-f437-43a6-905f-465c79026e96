# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onboarding
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:57+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "#{alt}"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__progress_ids
msgid "All Onboarding Progress Records (across companies)."
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "All done!"
msgstr "Бүгд дууссан!"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "All related Onboarding Progress Step Records (across companies)"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image_alt
msgid "Alt Text for the Step Image"
msgstr ""

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
msgid ""
"An \"Opening Action\" is required for the following steps to be linked to an"
" onboarding panel: %(step_titles)s"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Are you sure you want to hide these configuration steps?"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__button_text
msgid "Button text"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Cancel"
msgstr "Цуцлах"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close"
msgstr "Хаах"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close Panel"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close the onboarding panel"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Closing action"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__company_id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__company_id
msgid "Company"
msgstr "Компани"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_onboarding_state
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_step_state
msgid "Completion State"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__description
msgid "Description"
msgstr "Тайлбар"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__done
msgid "Done"
msgstr "Дууссан"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_icon
msgid "Font Awesome Icon when completed"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Get them out of my sight!"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Hide Onboarding Tips"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__id
msgid "ID"
msgstr "ID"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__is_per_company
msgid "Is per company"
msgstr ""

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__just_done
msgid "Just done"
msgstr "Дөнгөж дууссан"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "Let's do it"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__text_completed
msgid "Message at completion"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__name
msgid "Name of the onboarding"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Name of the onboarding model action to execute when closing the panel."
msgstr ""

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid ""
"Name of the onboarding step model action to execute when opening the step, "
"e.g. action_open_onboarding_1_step_1"
msgstr ""

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding.py:0
msgid "Nice work! Your configuration is done."
msgstr ""

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__not_done
msgid "Not done"
msgstr "Дуусаагүй"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding
msgid "Onboarding"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__progress_ids
msgid "Onboarding Progress Records"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "Onboarding Progress Step Records"
msgstr ""

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress_step
msgid "Onboarding Progress Step Tracker"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Onboarding Progress Step for the current context (company)."
msgstr ""

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress
msgid "Onboarding Progress Tracker"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress for the current context (company)."
msgstr ""

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding_step
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_id
msgid "Onboarding Step"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_state
msgid "Onboarding Step Progress"
msgstr ""

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_step
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_tree
msgid "Onboarding Steps"
msgstr ""

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_onboarding_route_name_uniq
msgid "Onboarding alias must be unique."
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_state
msgid "Onboarding progress"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__step_ids
msgid "Onboarding steps"
msgstr ""

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__onboarding_ids
#: model:ir.ui.menu,name:onboarding.menu_onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Onboardings"
msgstr ""

#. module: onboarding
#: model:ir.ui.menu,name:onboarding.menu_onboarding_step
msgid "Onboardings Steps"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__route_name
msgid "One word name"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid "Opening action"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__progress_step_ids
msgid "Progress Steps Trackers"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__progress_ids
msgid "Related Onboarding Progress Tracker"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_id
msgid "Related onboarding tracked"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__sequence
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__sequence
msgid "Sequence"
msgstr "Дугаарлалт"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_per_company
msgid "Should be done per company?"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__step_image_alt
msgid "Show when impossible to load the image"
msgstr ""

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
msgid "Step Completed!"
msgstr "Алхам дууссан!"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image
msgid "Step Image"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image_filename
msgid "Step Image Filename"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Step Progress"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
msgid "Steps"
msgstr "Алхам"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__button_text
msgid "Text on the panel's button to start this step"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__text_completed
msgid "Text shown on onboarding when completed"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_text
msgid "Text to show when step is completed"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__title
msgid "Title"
msgstr "Гарчиг"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Toggle visibility"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_onboarding_closed
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__is_onboarding_closed
msgid "Was panel closed?"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "o_onboarding_confetti"
msgstr ""

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_panel
msgid "onboarding.onboarding.step"
msgstr ""
