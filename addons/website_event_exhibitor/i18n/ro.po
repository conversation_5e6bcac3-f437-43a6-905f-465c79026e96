# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_exhibitor
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>il <PERSON>o, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:05+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
")\n"
"                    to meet them!"
msgstr ""
")\n"
"                   pentru a-i întâlni!"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>Nepublicat"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<small class=\"badge text-bg-danger\">Unpublished</small>"
msgstr "<small class=\"badge text-bg-danger\">Nepublicat</small>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "<span class=\"fa fa-plus me-1\"/> Add Exhibitors"
msgstr "<span class=\"fa fa-plus me-1\"/> Adăugați expozanți"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<span class=\"h5 m-3\">Other exhibitors</span>"
msgstr "<span class=\"h5 m-3\">Alți expozanți</span>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is currently closed</span><br/>\n"
"                    Come back between"
msgstr ""
"<span>Oops! Această cameră este în prezent închisă</span><br/>\n"
"Reveniți între"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is full</span><br/>Come back later to have a chat with"
" us!"
msgstr ""
"<span>Oops! Această cameră este plină</span><br/> Reveniți mai târziu pentru"
" a discuta cu noi!"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"A non-profit international educational and scientific\n"
"                    organisation, hosting three departments (aeronautics and\n"
"                    aerospace, environmental and applied fluid dynamics, and\n"
"                    turbomachinery and propulsion)."
msgstr ""
"O organizație internațională educațională și științifică non-profit\n"
"                     care găzduiește trei departamente (aeronautică și\n"
"                     aerospațial, mediu și dinamica fluidelor aplicate și\n"
"                     turbomașini și propulsie)."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "About"
msgstr "Despre"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "Action Needed"
msgstr "Acțiune necesară"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__active
msgid "Active"
msgstr "Activ"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorator Excepție Activitate"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid "All Countries"
msgstr "Toate țările"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_exhibitor_main
msgid "All Exhibitors"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid "All Levels"
msgstr "Toate Nivelurile"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Archived"
msgstr "Arhivat"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "As a team, we are happy to contribute to this event."
msgstr "Ca echipă, suntem bucuroși să contribuim la acest eveniment."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Attendees will be able to join to meet"
msgstr "Participanții vor putea să se întâlnească cu"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Available from"
msgstr "Disponibil de la"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_exhibitor_main
msgid "Back to all Exhibitors"
msgstr ""

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type1
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__bronze
msgid "Bronze"
msgstr "Bronze"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid "By Country"
msgstr "După țară"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid "By Level"
msgstr "Pe nivel"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__can_publish
msgid "Can Publish"
msgstr "Poate publica"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__chat_room_id
msgid "Chat Room"
msgstr "Cameră de conversație"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Close"
msgstr "Închide"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
msgid "Come back between"
msgstr "Reveniți între"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Come see us live, we hope to meet you!"
msgstr "Veniți să ne vedeți live, sperăm să vă cunoaștem!"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Connect"
msgstr "Conectare"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Countries"
msgstr "Țări"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_id
msgid "Country"
msgstr "Țară"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_flag_url
msgid "Country Flag"
msgstr "Steagul țării"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid "Create a Sponsor / Exhibitor"
msgstr "Creați un sponsor / expozant"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid "Create a Sponsor Level"
msgstr "Creați un nivel de sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Customer Relationship Management"
msgstr "Gestionarea relațiilor cu clienții"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Deco Addict designs, develops, integrates and supports HR and Supply\n"
"                Chain processes in order to make our customers more productive,\n"
"                responsive and profitable."
msgstr ""
"Deco Addict proiectează, dezvoltă, integrează și sprijină procesele HR și Supply Chain\n"
"                pentru a face clienții noștri mai productivi,\n"
"                receptivi și profitabili."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Deco Addict integrates ERP for Global Companies and supports PME\n"
"                with Open Sources software to manage their businesses. Our\n"
"                consultants are experts in the following areas:"
msgstr ""
"Deco Addict integrează ERP pentru companiile globale și sprijină IMM-urile\n"
"                cu software Open Sources pentru a-și gestiona afacerile.\n"
"Consultanții noștri sunt experți în următoarele domenii:"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_description
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Description"
msgstr "Descriere"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Discover more"
msgstr "Descoperă mai mult"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_type__exhibitor_menu
msgid ""
"Display exhibitors on website, in the footer of every page of the event."
msgstr ""
"Afișați expozanții pe site, în subsolul fiecărei pagini a evenimentului."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Display in footer"
msgstr "Afișați în subsolul paginii"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Email"
msgstr "E-mail"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_to
msgid "End hour"
msgstr "Ora de sfârșit"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model:ir.model,name:website_event_exhibitor.model_event_event
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Event"
msgstr "Eveniment"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Event Page"
msgstr "Pagină eveniment"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor
msgid "Event Sponsor"
msgstr "Sponsor al evenimentului"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor_type
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_tree
msgid "Event Sponsor Level"
msgstr "Nivelul sponsorului evenimentului"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_form
msgid "Event Sponsor Levels"
msgstr "Niveluri de sponsorizare a evenimentului"

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action_from_event
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Event Sponsors"
msgstr "Sponsori ai evenimentului"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_type
msgid "Event Template"
msgstr "Șablon Eveniment"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__exhibitor
msgid "Exhibitor"
msgstr "Expozant"

#. module: website_event_exhibitor
#. odoo-python
#: code:addons/website_event_exhibitor/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Exhibitors"
msgstr "Expozanți"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_type_view_form
msgid "Exhibitors Menu Item"
msgstr "Element meniu expozanți"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu_ids
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__website_event_menu__menu_type__exhibitor
msgid "Exhibitors Menus"
msgstr "Meniuri expozanți"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Filters"
msgstr "Filtre"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_follower_ids
msgid "Followers"
msgstr "Urmăritori"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă Font awesome, de ex. fa-sarcini"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__sponsor
msgid "Footer Logo Only"
msgstr "Doar logo-ul din subsol"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type3
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__gold
msgid "Gold"
msgstr "Gold"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Happy to be Sponsor"
msgstr "Mă bucur să fiu sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__id
msgid "ID"
msgstr "ID"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon"
msgstr "Pictogramă"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma care indică o activitate de excepție."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Dacă este bifat, mesaje noi necesită atenția dumneavoastră."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, există mesaje cu eroare de livrare."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_128
msgid "Image 128"
msgstr "Imagine 128"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_256
msgid "Image 256"
msgstr "Imagine 256"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_image_url
msgid "Image URL"
msgstr "URL Imagine "

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__event_date_tz
msgid ""
"Indicates the timezone in which the event dates/times will be displayed on "
"the website."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Inventory and Warehouse management"
msgstr "Gestionarea stocurilor și a depozitului"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_published
msgid "Is Published"
msgstr "Este publicat"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"It provides post-graduate education in fluid dynamics\n"
"                    (research master in fluid dynamics, former \"Diploma\n"
"                    Course\", doctoral program, stagiaire program and lecture\n"
"                    series) and encourages \"training in research through\n"
"                    research\"."
msgstr ""
"Acesta oferă învățământ postuniversitar în domeniul dinamicii fluidelor\n"
"                   (master de cercetare în dinamica fluidelor, fostul \"Diploma\n"
"curs\", program de doctorat, program de stagiere și serie de prelegeri\n"
"serii de conferințe) și încurajează \"formarea în cercetare prin\n"
"cercetare\"."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"It undertakes and promotes research in the field of fluid\n"
"                    dynamics. It possesses about fifty different wind tunnels,\n"
"                    turbomachinery and other specialized test facilities, some\n"
"                    of which are unique or the largest in the world. Extensive\n"
"                    research on experimental, computational and theoretical\n"
"                    aspects of gas and liquid flows is carried out under the\n"
"                    direction of the faculty and research engineers, sponsored\n"
"                    mainly by governmental and international agencies as well\n"
"                    as industries."
msgstr ""
"Acesta întreprinde și promovează cercetarea în domeniul dinamicii fluidelor\n"
"                    Deține aproximativ cincizeci de tuneluri de vânt diferite,\n"
"turbomașini și alte instalații de testare specializate, unele\n"
"dintre care unele sunt unice sau cele mai mari din lume. Cercetări extinse\n"
"cercetări ample privind aspectele experimentale, computaționale și teoretice\n"
"ale fluxurilor de gaze și lichide se desfășoară sub conducerea facultății și a inginerilor de cercetare, sponsorizate\n"
"în principal de agenții guvernamentale și internaționale, precum și\n"
"de industrii."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Jitsi Name"
msgstr "Nume Jitsi"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Join us next time to meet"
msgstr "Alătură-te nouă data viitoare pentru a ne întâlni"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Join us there to meet"
msgstr "Vino alături de noi pentru a ne întâlni"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_lang_id
msgid "Language"
msgstr "Limba"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_last_activity
msgid "Last activity"
msgstr "Ultima activitate"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Level"
msgstr "Nivel"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Level:"
msgstr "Nivel:"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Live"
msgstr "Live"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_512
msgid "Logo"
msgstr "Sigla"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Materials Management"
msgstr "Managementul materialelor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_capacity
msgid "Max capacity"
msgstr "Capacitate maximă"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "Numărul maxim de participanți ajunși în cameră în același timp"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Tip Meniu"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error
msgid "Message Delivery error"
msgstr "Eroare de livrare a mesajului"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_mobile
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Mobile"
msgstr "Mobil"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "More info"
msgstr "Alte informații"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_name
msgid "Name"
msgstr "Nume"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_id
msgid "Next Activity Type"
msgstr "Următorul tip de activitate"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__no_ribbon
msgid "No Ribbon"
msgstr "Fără panglică"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "No exhibitor found."
msgstr "Nu a fost găsit niciun expozant."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of errors"
msgstr "Număr de erori"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numărul de mesaje care necesită acțiune"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Online"
msgstr "Activ"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__online
msgid "Online Exhibitor"
msgstr "Expoziție online"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Opening Hours"
msgstr "Ore de deschidere"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_from
msgid "Opening hour"
msgstr "Oră de deschidere"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Our experts invent, imagine and develop solutions which meet\n"
"                your business requirements. They build a new technical\n"
"                environment for your company, but they always take the already\n"
"                installed IT software into account. That is why Idealis\n"
"                Consulting delivers excellence in HR and SC Management."
msgstr ""
"Experții noștri inventează, imaginează și dezvoltă soluții care îndeplinesc\n"
"                cerințele dvs. de afaceri. Ei construiesc un nou mediu tehnic\n"
"pentru compania dumneavoastră, dar întotdeauna iau în considerare\n"
"software-ul IT deja instalat. Acesta este motivul pentru care Idealis\n"
"Consulting oferă excelență în managementul HR și SC."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_participant_count
msgid "Participant count"
msgstr "Număr de participanți"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Partner"
msgstr "Partener"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Peak participants"
msgstr "Participanți de vârf"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Personnel Administration"
msgstr "Administrarea personalului"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Phone"
msgstr "Telefon"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Published"
msgstr "Publicat"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid ""
"Rank your sponsors based on your own grading system (e.g. \"Gold, Silver, "
"Bronze\")."
msgstr ""
"Clasificați-vă sponsorii pe baza propriului sistem de clasificare (de "
"exemplu, \"Aur, Argint, Bronz\")."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__rating_ids
msgid "Ratings"
msgstr "Ratings"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Reporting"
msgstr "Raportare"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_ribbon_style
msgid "Ribbon Style"
msgstr "Stil panglică"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_is_full
msgid "Room Is Full"
msgstr "Camera este plină"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_name
msgid "Room Name"
msgstr "Nume Cameră"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Sales and Distribution"
msgstr "Vânzări și distribuție"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Search an exhibitor ..."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sequence
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_type__exhibitor_menu
msgid "Showcase Exhibitors"
msgstr "Expozanți de prezentare"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type2
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__silver
msgid "Silver"
msgstr "Silver"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__subtitle
msgid "Slogan"
msgstr "Slogan"

#. module: website_event_exhibitor
#. odoo-python
#: code:addons/website_event_exhibitor/models/event_sponsor.py:0
msgid "Sponsor"
msgstr "Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_count
msgid "Sponsor Count"
msgstr "Număr sponsori"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__email
msgid "Sponsor Email"
msgstr "E-mail sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__name
msgid "Sponsor Level"
msgstr "Nivel sponsor"

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_type_action
#: model:ir.ui.menu,name:website_event_exhibitor.menu_event_sponsor_type
msgid "Sponsor Levels"
msgstr "Nivele Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__mobile
msgid "Sponsor Mobile"
msgstr "Mobil sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__name
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Sponsor Name"
msgstr "Numele sponsorului"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__phone
msgid "Sponsor Phone"
msgstr "Telefonul sponsorului"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__exhibitor_type
msgid "Sponsor Type"
msgstr "Tip sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__url
msgid "Sponsor Website"
msgstr "Site-ul web al sponsorului"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Sponsor image"
msgstr "Imaginea sponsorului"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Sponsors"
msgstr "Sponsori"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid ""
"Sponsors are advertised on your event pages.<br>\n"
"    Exhibitors have a dedicated page a with chat room for people to connect with them."
msgstr ""
"Sponsorii sunt anunțați pe paginile evenimentului dvs.<br>\n"
"    Expozanții au o pagină dedicată, cu o cameră de chat, pentru ca oamenii să se conecteze cu ei."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Sponsorship"
msgstr "Sponsorizare"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sponsor_type_id
msgid "Sponsorship Level"
msgstr "Nivel sponsorizare"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data activitații este deja trecută\n"
"Astăzi: data activității este astăzi\n"
"Planificate: activități viitoare."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Talent Management"
msgstr "Managementul talentelor"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"URL-ul complet pentru accesarea documentului prin intermediul site-ului web."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "The sponsor website description is missing."
msgstr "Lipsește descrierea sponsorului de pe site."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_date_tz
msgid "Timezone"
msgstr "Fus orar"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Top Bar Filter"
msgstr "Filtru pentru bara de sus"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul de activitate de excepție înregistrată."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_thumb_details
msgid "Unpublished"
msgstr "Nepublicat"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_published
msgid "Visible on current website"
msgstr "Vizibil pe site-ul curent"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "We could not find any exhibitor at this moment."
msgstr "Nu am putut găsi niciun expozant în acest moment."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "We could not find any exhibitor matching your search for:"
msgstr ""
"Nu am putut găsi niciun expozant care să corespundă căutării dvs. pentru:"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_tree
msgid "Website"
msgstr "Site web"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_website_event_menu
msgid "Website Event Menu"
msgstr "Meniul evenimentului site-ului web"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website Messages"
msgstr "Mesaje de pe site-ul web"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_url
msgid "Website URL"
msgstr "URL website"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website communication history"
msgstr "Istoricul comunicării pe site-ul web"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_in_opening_hours
msgid "Within opening hours"
msgstr "În cadrul orelor de deschidere"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "a few seconds"
msgstr "câteva secunde "

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "e.g. \"Openwood specializes in home decoration...\""
msgstr "de exemplu, \"Openwood este specializată în decorațiuni pentru casă...\""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : OpenWood Decoration"
msgstr "de exemplu : Decorațiune OpenWood"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : https://www.odoo.com"
msgstr "de exemplu. : https://www.odoo.com"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : <EMAIL>"
msgstr "de exemplu. : <EMAIL>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. Your best choice for your home"
msgstr "de exemplu. Cea mai bună alegere pentru casa ta"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
msgid "is not available right now."
msgstr "nu este disponibil acum."

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
msgid "is over."
msgstr "s-a încheiat."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""
"s-a terminat.\n"
"                <br/>"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "starts in"
msgstr "începe în"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "starts on"
msgstr "începe pe"
