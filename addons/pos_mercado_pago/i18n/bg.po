# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercado_pago
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "1494126963"
msgstr ""

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "APP_USR-..."
msgstr ""

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "Do not have access to fetch token from Mercado Pago"
msgstr ""

#. module: pos_mercado_pago
#: model:ir.model.fields,help:pos_mercado_pago.field_pos_payment_method__mp_id_point_smart
msgid ""
"Enter your Point Smart terminal serial number written on the back of your "
"terminal (after the S/N:)"
msgstr ""

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "Force PDV"
msgstr ""

#. module: pos_mercado_pago
#: model:ir.model.fields,help:pos_mercado_pago.field_pos_payment_method__mp_bearer_token
msgid ""
"Mercado Pago customer production user token: "
"https://www.mercadopago.com.mx/developers/en/reference"
msgstr ""

#. module: pos_mercado_pago
#: model:ir.model.fields,help:pos_mercado_pago.field_pos_payment_method__mp_webhook_secret_key
msgid ""
"Mercado Pago production secret key from integration application: "
"https://www.mercadopago.com.mx/developers/panel/app"
msgstr ""

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_id_point_smart_complet
msgid "Mp Id Point Smart Complet"
msgstr ""

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has been canceled"
msgstr ""

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has been processed"
msgstr ""

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has been rejected"
msgstr ""

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment has to be canceled on terminal"
msgstr ""

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment not found (canceled/finished on terminal)"
msgstr ""

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Payment status could not be confirmed"
msgstr ""

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "Please verify your production user token as it was rejected"
msgstr ""

#. module: pos_mercado_pago
#: model:ir.model,name:pos_mercado_pago.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Методи на плащане за точка на продажба (POS)"

#. module: pos_mercado_pago
#: model:ir.model,name:pos_mercado_pago.model_pos_session
msgid "Point of Sale Session"
msgstr "Сесия на център за продажби"

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_webhook_secret_key
msgid "Production secret key"
msgstr ""

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_bearer_token
msgid "Production user token"
msgstr ""

#. module: pos_mercado_pago
#: model:ir.model.fields,field_description:pos_mercado_pago.field_pos_payment_method__mp_id_point_smart
msgid "Terminal S/N"
msgstr ""

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "The terminal serial number is not registered on Mercado Pago"
msgstr ""

#. module: pos_mercado_pago
#. odoo-python
#: code:addons/pos_mercado_pago/models/pos_payment_method.py:0
msgid "Unexpected Mercado Pago response: %s"
msgstr ""

#. module: pos_mercado_pago
#. odoo-javascript
#: code:addons/pos_mercado_pago/static/src/app/payment_mercado_pago.js:0
msgid "Unknown payment status"
msgstr ""

#. module: pos_mercado_pago
#: model_terms:ir.ui.view,arch_db:pos_mercado_pago.pos_payment_method_view_form_inherit_pos_mercado_pago
msgid "c2f3662..."
msgstr ""
