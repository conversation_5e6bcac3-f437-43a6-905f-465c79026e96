# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_tr_nilvera_einvoice
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 15:09+0000\n"
"PO-Revision-Date: 2025-03-26 15:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model,name:l10n_tr_nilvera_einvoice.model_account_move_send
msgid "Account Move Send"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/wizard/account_move_send.py:0
#, python-format
msgid "Check data on Partner(s)"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields.selection,name:l10n_tr_nilvera_einvoice.selection__account_move__l10n_tr_nilvera_send_status__error
msgid "Error (check chatter)"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera_einvoice.account_journal_dashboard_kanban_view
msgid "Fetch Nilvera invoice status"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera_einvoice.account_journal_dashboard_kanban_view
msgid "Fetch from Nilvera"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model,name:l10n_tr_nilvera_einvoice.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model,name:l10n_tr_nilvera_einvoice.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_move_send__l10n_tr_nilvera_einvoice_enable_xml
msgid "L10N Tr Nilvera Einvoice Enable Xml"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_move_send__l10n_tr_nilvera_warnings
msgid "L10N Tr Nilvera Warnings"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_bank_statement_line__l10n_tr_nilvera_uuid
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_move__l10n_tr_nilvera_uuid
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_payment__l10n_tr_nilvera_uuid
msgid "Nilvera Document UUID"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_bank_statement_line__l10n_tr_nilvera_send_status
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_move__l10n_tr_nilvera_send_status
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_payment__l10n_tr_nilvera_send_status
msgid "Nilvera Status"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid "Nilvera document has been received successfully"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.actions.server,name:l10n_tr_nilvera_einvoice.ir_cron_nilvera_get_invoice_status_ir_actions_server
msgid "Nilvera: retrieve invoice status"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.actions.server,name:l10n_tr_nilvera_einvoice.ir_cron_nilvera_get_new_documents_ir_actions_server
msgid "Nilvera: retrieve new documents"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields.selection,name:l10n_tr_nilvera_einvoice.selection__account_move__l10n_tr_nilvera_send_status__not_sent
msgid "Not sent"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid ""
"Oops, seems like you're unauthorised to do this. Try another API key with "
"more rights or contact Nilvera."
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields,field_description:l10n_tr_nilvera_einvoice.field_account_move_send__l10n_tr_nilvera_einvoice_checkbox_xml
msgid "Send E-Invoice to Nilvera"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields.selection,name:l10n_tr_nilvera_einvoice.selection__account_move__l10n_tr_nilvera_send_status__sent
msgid "Sent and waiting response"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid "Server error from Nilvera, please try again later."
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields.selection,name:l10n_tr_nilvera_einvoice.selection__account_move__l10n_tr_nilvera_send_status__succeed
msgid "Successful"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/wizard/account_move_send.py:0
#, python-format
msgid ""
"The following partner(s) are either not Turkish or are missing one of those "
"fields: city, state and street."
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid "The invoice couldn't be sent due to the following errors:\n"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid "The invoice couldn't be sent to the recipient."
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid "The invoice has been successfully sent to Nilvera."
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid "The invoice status couldn't be retrieved from Nilvera."
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model,name:l10n_tr_nilvera_einvoice.model_account_edi_xml_ubl_tr
msgid "UBL-TR 1.2"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields,help:l10n_tr_nilvera_einvoice.field_account_bank_statement_line__l10n_tr_nilvera_uuid
#: model:ir.model.fields,help:l10n_tr_nilvera_einvoice.field_account_move__l10n_tr_nilvera_uuid
#: model:ir.model.fields,help:l10n_tr_nilvera_einvoice.field_account_payment__l10n_tr_nilvera_uuid
msgid "Universally unique identifier of the Invoice"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields.selection,name:l10n_tr_nilvera_einvoice.selection__account_move__l10n_tr_nilvera_send_status__unknown
msgid "Unknown"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/wizard/account_move_send.py:0
#, python-format
msgid "View Partner(s)"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#: model:ir.model.fields.selection,name:l10n_tr_nilvera_einvoice.selection__account_move__l10n_tr_nilvera_send_status__waiting
msgid "Waiting"
msgstr ""

#. module: l10n_tr_nilvera_einvoice
#. odoo-python
#: code:addons/l10n_tr_nilvera_einvoice/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft an entry that has been sent to Nilvera."
msgstr ""
