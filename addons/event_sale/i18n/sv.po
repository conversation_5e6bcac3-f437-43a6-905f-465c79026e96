# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
# 
# Translators:
# 03992e16f8df6e39b9d1cc0ff635887e, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Lasse L, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid "%(event_name)s - Tickets"
msgstr "%(event_name)s - Biljetter"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""
".\n"
"            <span>Manuella åtgärder kan behövas.</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Configure Events &amp; Tickets"
msgstr "<i class=\"fa fa-arrow-right\"/> Konfigurera Evenemang &amp; Biljetter"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr "<span class=\"o_stat_text\">Säljorder</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Försäljning</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr "<span>Förändring i registrering för deltagare:</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Add"
msgstr "Lägg till"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr "Alla orderrader pekar på detta event"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__done
msgid "Attended"
msgstr "Närvarade"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr "Antal deltagare"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_name
msgid "Attendee Name"
msgstr "Deltagarnamn"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "Deltagare"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before updating the linked registrations of"
msgstr "Innan uppdatering av de länkade registreringarna av"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Booked by"
msgstr "Bokad av"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "Kampanj"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__cancel
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__cancel
msgid "Cancelled"
msgstr "Avbruten"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr "Välj ett event så skapas det automatiskt en registrering för eventet."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""
"Välj en eventbiljett och det skapas automatiskt en registrering för "
"biljetten."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Close"
msgstr "Stäng"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "Come back once tickets have been sold to overview your sales income."
msgstr ""
"Kom tillbaka när biljetterna har sålts för att se över dina "
"försäljningsintäkter."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__company_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__company_id
msgid "Company"
msgstr "Företag"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__open
msgid "Confirmed"
msgstr "Bekräftad"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/product_template.py:0
msgid "Create an Attendee for the selected Event."
msgstr "Skapa en deltagare för det valda evenemanget."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Create/Update registrations"
msgstr "Skapa/uppdatera registreringar"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Customer"
msgstr "Kund"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Discard"
msgstr "Avbryt"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr "Ändra deltagarens uppgifter vid bekräftelsen"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr "Ändra deltagarens rad på bekräftelsen"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "Redaktör"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "E-post"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event"
msgstr "Evenemang"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr "Eventkonfigurator"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_end
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event End Date"
msgstr "Evenemangets slutdatum"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_id
msgid "Event Registration"
msgstr "Anmälan"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "Anmälningar"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Sales Analysis"
msgstr "Analys av försäljning av evenemang"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr "Försäljningsrapport för evenemang"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_begin
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Start Date"
msgstr "Evenemangsstartdatum"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
msgid "Event Ticket"
msgstr "Biljett"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_type_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Type"
msgstr "Evenemangstyp"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Events that have ended"
msgstr "Händelser som har avslutats"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr "Undantag:"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__free
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__free
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Free"
msgstr "Gratis"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid "Get Your Tickets"
msgstr "Skaffa dina biljetter"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Group By"
msgstr "Grupp Av"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__has_available_tickets
msgid "Has Available Tickets"
msgstr "Har tillgängliga biljetter"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "ID"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/wizard/event_configurator.py:0
msgid "Invalid ticket choice \"%(ticket_name)s\" for event \"%(event_name)s\"."
msgstr "Ogiltigt biljettval \"%(ticket_name)s\" för evenemang \"%(event_name)s\"."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__invoice_partner_id
msgid "Invoice Address"
msgstr "Fakturaadress"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__active
msgid "Is registration active (not archived)?"
msgstr "Är registreringen aktiv (inte arkiverad)?"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "Medium"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "Namn"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "No Event Revenues yet!"
msgstr "Inga evenemangsinkomster ännu!"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Non-free tickets"
msgstr "Inga gratisbiljetter"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__to_pay
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__to_pay
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Not Sold"
msgstr "Ej såld"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_date
msgid "Order Date"
msgstr "Orderdatum"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "Orginalregistrering"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Participant"
msgstr "Deltagare"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Past Events"
msgstr "Tidigare evenemang"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_status
msgid "Payment Status"
msgstr "Betalningsstatus"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Pending payment"
msgstr "I väntan på betalning"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "Telefon"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid ""
"Please make sure all your event related lines are configured before "
"confirming this order:%s"
msgstr ""
"Vänligen kontrollera att alla dina eventrelaterade rader är konfigurerade "
"innan du bekräftar denna order:%s"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Product"
msgstr "Produkt"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__draft
msgid "Quotation"
msgstr "Offert"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sent
msgid "Quotation Sent"
msgstr "Offert skickad"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "Registrering"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_create_date
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Date"
msgstr "Anmälningsdatum"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Status"
msgstr "Registreringsstatus"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Registration revenues"
msgstr "Intäkter från registrering"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__registration_ids
msgid "Registrations"
msgstr "Anmälan"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "Anmälningar att ändra"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_sale_report_action
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price
#: model:ir.ui.menu,name:event_sale.menu_action_show_revenues
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_graph
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_pivot
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_tree
msgid "Revenues"
msgstr "Intäkter"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Sale Order"
msgstr "Kundorder"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_line_id
msgid "Sale Order Line"
msgstr "Kundorderrad"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sale Order Status"
msgstr "Kundorderstatus"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_status
msgid "Sale Status"
msgstr "Försäljningsstatus"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "Försäljning"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr "Försäljning (ex moms)"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sale
msgid "Sales Order"
msgstr "Försäljningsorder"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "Försäljningsorderrad"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_user_id
msgid "Salesperson"
msgstr "Säljare"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Select an Event"
msgstr "Välj ett Evenemang"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__sold
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__sold
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sold"
msgstr "Såld"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "Källa"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__state
msgid "Status"
msgstr "Status"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order_line.py:0
msgid ""
"The sale order line with the product %(product_name)s needs an event and a "
"ticket."
msgstr ""
"Försäljningsorderraden med produkten %(product_name)s behöver ett evenemang "
"och en biljett."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Ticket"
msgstr "Biljett"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
msgid "Ticket Type"
msgstr "Biljettyp"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr "Biljett ändrad från"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_price
msgid "Ticket price"
msgstr "Biljettpris"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr "Totalt försäljning för detta evenemang"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "Transaktion"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__draft
msgid "Unconfirmed"
msgstr "Obekräftad"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_registration__state
msgid ""
"Unconfirmed: registrations in a pending state waiting for an action (specific case, notably with sale status)\n"
"Registered: registrations considered taken by a client\n"
"Attended: registrations for which the attendee attended the event\n"
"Cancelled: registrations cancelled manually"
msgstr ""
"Obekräftade: registreringar i vänteläge som väntar på en åtgärd (specialfall, särskilt med försäljningsstatus)\n"
"Registrerade: registreringar som anses ha tagits av en kund\n"
"Närvarade: registreringar för vilka deltagaren närvarade vid evenemanget\n"
"Avbokade: registreringar som avbokats manuellt"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price_untaxed
msgid "Untaxed Revenues"
msgstr "Oskattade intäkter"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming events from today"
msgstr "Kommande evenemang från idag"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming/Running"
msgstr "Kommande/Pågående"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "We could not find a matching event ticket for this product. <br/>"
msgstr "Försäljningsorderraden med produkten. <br/>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please provide attendee details."
msgstr "vänligen ange deltagarinformation."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "till"
