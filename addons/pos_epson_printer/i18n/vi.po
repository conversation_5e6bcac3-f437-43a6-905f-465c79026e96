# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Ngăn két"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"Kiểm tra cấu hình máy in để biết cài đặt 'ID thiết bị'. Nên đặt thành:"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "IP máy in Epson"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Epson Printer IP Address"
msgstr "Địa chỉ IP máy in Epson"

#. module: pos_epson_printer
#. odoo-python
#: code:addons/pos_epson_printer/models/pos_printer.py:0
msgid "Epson Printer IP Address cannot be empty."
msgstr "Không thể bỏ trống địa chỉ IP máy in Epson."

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "Địa chỉ IP máy in biên lai Epson"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s. "
msgstr ""
"Nếu bạn đang sử dụng máy chủ bảo mật (HTTPS), hãy bảo đảm bạn đã chấp nhận "
"chứng chỉ theo cách thủ công bằng cách truy cập %s."

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
#: model:ir.model.fields,help:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Địa chỉ IP nội bộ của máy in hóa đơn Epson "

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "No paper was detected by the printer"
msgstr "Máy in không phát hiện giấy"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "Please check if the printer has enough paper and is ready to print."
msgstr "Vui lòng kiểm tra xem máy in có đủ giấy và sẵn sàng in không. "

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Cấu hình máy tính tiền"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_printer
msgid "Point of Sale Printer"
msgstr "Máy in POS"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_res_config_settings__pos_epson_printer_ip
msgid "Pos Epson Printer Ip"
msgstr "IP máy in Epson POS"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "Loại máy in"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "Printing failed"
msgstr "In không thành công"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"Máy in hóa đơn Epson sẽ được sử dụng thay vì máy in hóa đơn kết nối với hộp "
"IoT. "

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "The following error code was given by the printer:"
msgstr "Máy in hiển thị mã lỗi sau:"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "Máy in đã được kết nối thành công, nhưng không thể in."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "To find more details on the error reason, please search online for:"
msgstr "Để biết thêm chi tiết về lý do lỗi, vui lòng tra cứu online:"

#. module: pos_epson_printer
#: model:ir.model.fields.selection,name:pos_epson_printer.selection__pos_printer__printer_type__epson_epos
msgid "Use an Epson printer"
msgstr "Sử dụng máy in Epson"
