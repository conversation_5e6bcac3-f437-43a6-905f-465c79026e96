# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
msgid ""
"%(employee)s does not have a contract from %(date_start)s to %(date_end)s."
msgstr ""

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
msgid ""
"%(employee)s has multiple contracts from %(date_start)s to %(date_end)s. A "
"work entry cannot overlap multiple contracts."
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\" title=\"Warning\"/>You are not "
"allowed to regenerate validated work entries"
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "<i class=\"fa fa-info-circle me-1\" title=\"Hint\"/>"
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<span class=\"text-muted\">Warning: The work entry regeneration will delete "
"all manual changes on the selected period.</span>"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Allow the work entry type to be linked with time off types."
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Cancel"
msgstr "ڕەتکردنەوە"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_compensatory
msgid "Compensatory Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__contract_id
msgid "Contract"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_uid
msgid "Created by"
msgstr "دروستکراوە لەلایەن..."

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_date
msgid "Created on"
msgstr "دروستکراوە لە"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date_message
msgid "Earliest Available Date Message"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date
msgid "Earliest date"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__employee_id
msgid "Employee"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__employee_ids
msgid "Employees"
msgstr ""

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar_model.js:0
msgid "Everybody's work entries"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_extra_hours
msgid "Extra Hours"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_from
msgid "From"
msgstr "لە"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_contract_view_form_inherit_work_entry
msgid "Fully Flexible"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.actions.server,name:hr_work_entry_contract.ir_cron_generate_missing_work_entries_ir_actions_server
msgid "Generate Missing Work Entries"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_from
msgid "Generated From"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_to
msgid "Generated To"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_leave
msgid "Generic Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry
msgid "HR Work Entry"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_home_working
msgid "Home Working"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
msgid ""
"In order to regenerate the work entries, you need to provide the wizard with"
" an employee_id, a date_from and a date_to. In addition to that, the time "
"interval defined by date_from and date_to must not contain any validated "
"work entries."
msgstr ""

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/components/work_entry_source_field/work_entry_source_field.js:0
msgid ""
"Invalid option: For fully flexible calendars, the work entry source cannot "
"be 'Working Hours'."
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__last_generation_date
msgid "Last Generation Date"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_uid
msgid "Last Updated by"
msgstr "دوایین نوێکردنەوە لەلایەن..."

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_date
msgid "Last Updated on"
msgstr "دوایین نوێکردنەوە لە..."

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date_message
msgid "Latest Available Date Message"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date
msgid "Latest date"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_long_leave
msgid "Long Term Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_legal_leave
msgid "Paid Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_regeneration_wizard
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr ""

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Regenerate Work Entries"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_resource_calendar
msgid "Resource Working Time"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__search_criteria_completed
msgid "Search Criteria Completed"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_sick_leave
msgid "Sick Time Off"
msgstr ""

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_contract.py:0
msgid ""
"Sorry, generating work entries from cancelled contracts is not allowed."
msgstr ""

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
msgid ""
"The from date must be >= '%(earliest_available_date)s' and the to date must "
"be <= '%(latest_available_date)s', which correspond to the generated work "
"entries time interval."
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_contract_view_form_inherit
msgid "This work entry cannot be validated. The work entry type is undefined."
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_to
msgid "To"
msgstr "بۆ"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_unpaid_leave
msgid "Unpaid"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__valid
msgid "Valid"
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Work Entries"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__validated_work_entry_ids
msgid "Work Entries Within Interval"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.actions.act_window,name:hr_work_entry_contract.hr_work_entry_regeneration_wizard_action
msgid "Work Entry Regeneration"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid "Work Entry Source"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__work_entry_source_calendar_invalid
msgid "Work Entry Source Calendar Invalid"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields.selection,name:hr_work_entry_contract.selection__hr_contract__work_entry_source__calendar
msgid "Working Schedule"
msgstr ""
