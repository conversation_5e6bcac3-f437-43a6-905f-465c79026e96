# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <geor<PERSON>_ta<PERSON><PERSON><PERSON>@yahoo.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>si<PERSON>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON> P<PERSON>llou, 2025
# <PERSON>ralamp<PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <j.polydor<PERSON>@dileanity.com>, 2025\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
msgid "%(name)s Sequence Resupply Subcontractor"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
msgid "%(name)s Sequence subcontracting"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal
msgid "/my/productions"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "All"
msgstr "Όλα"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_bom
msgid "Bill of Material"
msgstr "Κατάσταση Υλικών"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__type
msgid "BoM Type"
msgstr "Τύπος Κ.Υ."

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__bom_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__bom_ids
msgid "BoMs for which the Partner is one of the subcontractors"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid "Bom Product"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_change_production_qty
msgid "Change Production Qty"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid ""
"Check this box to create a new dedicated subcontracting location for this "
"company. Note that standard subcontracting routes will be adapted so as to "
"take these into account automatically."
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid ""
"Choose a vendor of type subcontractor if you want to subcontract the product"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Consumed"
msgstr "Αναλωθέντα"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_partner
msgid "Contact"
msgstr "Επαφή"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Continue"
msgstr "Συνέχεια"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Deadline Date"
msgstr "Ημερομηνία Τελευταίας Προθεσμίας"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Demand"
msgstr "Ζήτηση"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Description"
msgstr "Περιγραφή"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__move_line_raw_ids
msgid "Detail Component"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Details"
msgstr "Λεπτομέρειες"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Discard"
msgstr "Απόρριψη"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_action_record_components
msgid "Display Action Record Components"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__facultative
msgid "Facultative"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_productions
msgid "Follow manufacturing orders you have to fulfill"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontracting_has_been_recorded
msgid "Has been recorded?"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__hide
msgid "Hide"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
msgid ""
"In order to manage stock accurately, subcontracting locations must be type "
"Internal, linked to the appropriate company."
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_filter
msgid "Incoming transfer"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_location
msgid "Inventory Locations"
msgstr "Τοποθεσίες Αποθέματος"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_quant__is_subcontract
msgid "Is Subcontract"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid "Is a Subcontracting Location?"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid ""
"List of Products used in the BoM, used to filter the list of products in the"
" subcontracting portal view"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__production_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__production_ids
msgid "MRP Productions for which the Partner is the subcontractor"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move_line.py:0
msgid ""
"Make sure you validate or adapt the related resupply picking to your "
"subcontractor in order to avoid inconsistencies in your stock."
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__mandatory
msgid "Mandatory"
msgstr "Υποχρεωτικό"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_production
msgid "Manufacturing Order"
msgstr "Εντολή Παραγωγής"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_productions
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Orders"
msgstr "Εντολές Παραγωγής"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Reference"
msgstr "Αναφορά Παραγωγής"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "Name"
msgstr "Περιγραφή"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "Newest"
msgstr "Νεότερα"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
msgid "Nothing to record"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_quant.py:0
msgid "Operation not supported"
msgstr "Η λειτουργία δεν υποστηρίζεται."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Operations"
msgstr "Ενέργειες"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Order"
msgstr "Παραγγελία"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
msgid ""
"Portal users cannot create a stock move with a state 'Done' or change the "
"current state to 'Done'."
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Κινήσεις Ειδών (Γραμμή Κίνησης Αποθέματος)"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_product
msgid "Product Variant"
msgstr "Μεταβλητή Είδους"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_menu_production
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Productions"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_quant
msgid "Quants"
msgstr "Quants"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
msgid "Raw Materials for %s"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "Ready"
msgstr "Έτοιμη"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Record Production"
msgstr "Καταγραφή Παραγωγής"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Record components"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Register components for subcontracted product"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr "Αναπλήρωση Κατόπιν Παραγγελίας (MTO)"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_route_id
msgid "Resupply Subcontractor"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_replenish_mixin.py:0
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:stock.route,name:mrp_subcontracting.route_resupply_subcontractor_mto
msgid "Resupply Subcontractor on Order"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply Subcontractors"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking
msgid "Return Picking"
msgstr "Επιστροφή Συλλογής"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Scheduled Date"
msgstr "Προγραμματισμένη Ημερομηνία"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__show_subcontracting_details_visible
msgid "Show Subcontracting Details Visible"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Source Document"
msgstr "Έγγραφο Πηγή"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "State"
msgstr "Νομός/Πολιτεία"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move
msgid "Stock Move"
msgstr "Κίνηση Αποθέματος"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__picking_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__picking_ids
msgid "Stock Pickings for which the Partner is the subcontractor"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_rule
msgid "Stock Rule"
msgstr "Κανόνας Αποθέματος"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
msgid "Subcontract"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid "Subcontracted"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "Subcontracted manufacturing orders cannot be merged."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__mrp_bom__type__subcontract
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Subcontracting"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/res_company.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__subcontracting_location_id
msgid "Subcontracting Location"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.quant_subcontracting_search_view
msgid "Subcontracting Locations"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_mto_pull_id
msgid "Subcontracting MTO Rule"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_pull_id
msgid "Subcontracting MTS Rule"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_type_id
msgid "Subcontracting Operation Type"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.actions.act_window,name:mrp_subcontracting.subcontracting_portal_view_production_action
msgid "Subcontracting Portal"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_resupply_type_id
msgid "Subcontracting Resupply Operation Type"
msgstr ""

#. module: mrp_subcontracting
#. odoo-javascript
#: code:addons/mrp_subcontracting/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
msgid "Subcontracting:"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/report/mrp_report_bom_structure.py:0
msgid "Subcontracting: %s"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontractor_id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__subcontractor_ids
msgid "Subcontractor"
msgstr "Υπεργολάβος"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid "Subcontractor Location"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__subcontractor_ids
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.view_partner_mrp_subcontracting_filter
msgid "Subcontractors"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Τιμοκατάλογος Προμηθευτή"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__is_subcontract
msgid "The move is a subcontract receipt"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,help:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid ""
"The stock location used as source and destination when sending        goods "
"to this contact during a subcontracting process."
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "There are currently no productions for your account."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
msgid ""
"There shouldn't be multiple productions to record for the same subcontracted"
" move."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "This MO isn't related to a subcontracted move"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_form_view
msgid "Total Consumed"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_picking
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__incoming_picking
msgid "Transfer"
msgstr "Μεταφορά"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__subcontractor_id
msgid "Used to restrict access to the portal user through Record Rules"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_warehouse
msgid "Warehouse"
msgstr "Αποθήκη"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_bom.py:0
msgid ""
"You can not set a Bill of Material with operations or by-product line as "
"subcontracting."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
msgid "You cannot alter the company's subcontracting location"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "You cannot write on fields %s in mrp.production."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "You must enter a serial number for %s"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "You must enter a serial number for each line of %s"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "e.g. PO0032"
msgstr "π.χ. PO0032"
