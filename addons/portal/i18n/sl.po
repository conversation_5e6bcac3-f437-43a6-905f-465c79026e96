# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
# 
# Translators:
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "\" to validate your action."
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "%s days overdue"
msgstr "%s dni zamude"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "1. Enter your password to confirm you own this account"
msgstr "1. Vnesite svoje geslo in potrdite, da ste lastnik tega računa"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"2. Confirm you want to delete your account by\n"
"                                        copying down your login ("
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid ""
"<i class=\"fa fa-fw fa-id-card-o me-1 small text-primary text-primary-"
"emphasis\"/> My Account"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-sign-out me-1 small text-primary text-primary-"
"emphasis\"/> Logout"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-th me-1 small text-primary text-primary-emphasis\"/> "
"Apps"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "<i class=\"fa fa-pencil\"/> Edit information"
msgstr "<i class=\"fa fa-pencil\"/> Uredi podatke"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Nazaj v način urejanja"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Dokumentacija\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Država...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">select...</option>"
msgstr "<option value=\"\">izberite...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"<small class=\"form-text text-muted\">\n"
"                Company name, VAT Number and country can not be changed once document(s) have been issued for your account.\n"
"                <br/>Please contact us directly for that operation.\n"
"            </small>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Filtriraj po:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Združi po:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Razvrsti po:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>Odprto </strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<t t-set=\"user_name\" t-value=\"object.user_id.name or object.partner_id.name or ''\"/>\n"
"            <t t-set=\"company_name\" t-value=\"object.user_id.company_id.name or object.partner_id.company_id.name or ''\"/>\n"
"            <t t-set=\"signup_url\" t-value=\"object.user_id.partner_id and object.user_id.partner_id._get_signup_url() or object.partner_id and object.partner_id._get_signup_url()\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"user_name\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"user_name\">Marc Demo</t>,<br/> <br/>\n"
"                        Welcome to <t t-out=\"company_name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        An account has been created for you with the following login: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Click on the button below to pick a password and activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"signup_url\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company_name\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "API Key Description"
msgstr "API Opis tipke"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "API Key Ready"
msgstr "API Ključ pripravljen"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.js:0
msgid "Accept & Sign"
msgstr "Sprejmi & Podpiši"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "Opozorilo ob dostopu"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Account deleted!"
msgstr "Račun izbrisan!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "Dodaj opombo"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Add attachment"
msgstr "Dodaj priponko"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "Dodajte stike za skupno rabo dokumenta ..."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "Dodajte dodatno vsebino za prikaz v e-pošti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "Dodano"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__exist
msgid "Already Registered"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Are you sure you want to do this?"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Cancel"
msgstr "Prekliči"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "Spremeni geslo"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Sprememba številke DDV ni dovoljena, ko so dokumenti izdani za tvoj konto. "
"Za to operacijo se obrni neposredno na nas."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Sprememba imena podjetja ni dovoljena po izdaji dokumentov za vaš račun. Za "
"to operacijo se obrnite neposredno na nas."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing the country is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Check failed"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "City"
msgstr "Kraj"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Click here to see your document."
msgstr "Kliknite tukaj in si oglejte dokument."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.side_content
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Close"
msgstr "Zaključi"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Company Name"
msgstr "Naziv družbe"

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Configure your connection parameters"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm"
msgstr "Potrdi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm Password"
msgstr "Potrdite geslo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Connection &amp; Security"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "Contact"
msgstr "Stik"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "Podrobnosti stika"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "Stiki"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid "Could not save file <strong>%s</strong>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Country"
msgstr "Država"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr ""

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "URL portala za stranke"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Customers can generate API Keys"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "Spoštovani"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Delete"
msgstr "Izbriši"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Delete Account"
msgstr "Izbriši račun"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "Opis"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Details"
msgstr "Podrobnosti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Disable your account, preventing any further login.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-triangle text-danger\"/>\n"
"                                            This action cannot be undone.\n"
"                                        </b>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Discard"
msgstr "Opusti"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due in %s days"
msgstr "Zapade čez %s dni"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due today"
msgstr "Rok za oddajo je danes"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Email"
msgstr "E-pošta"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Email Address already taken by another user"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "Obravnava z elektronsko pošto"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Enter a description of and purpose for the key."
msgstr "Vnesite opis in namen ključa."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Expiration Date"
msgstr "Datum izteka"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Forgot password?"
msgstr "Ste pozabili geslo?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Give a duration for the key's validity"
msgstr "Določite trajanje veljavnosti ključa"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Go Back"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "Dovolite dostop do portala"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmerjanje"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "Domov"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Important:"
msgstr "Pomembno:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "Interni uporabnik"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ko
msgid "Invalid"
msgstr "Neveljavno"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Invalid Email Address"
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr "Neveljavna e-pošta! Prosimo, vnesi veljaven e-poštni naslov."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid report type: %s"
msgstr "Neveljavna vrsta poročila: %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "Sporočilo povabila"

#. module: portal
#: model:mail.template,description:portal.mail_template_data_portal_welcome
msgid "Invitation email to contacts to create a user account"
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_share.py:0
msgid "Invitation to access %s"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "Najnovejša avtentikacija"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Leave a comment"
msgstr "Pustite komentar"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "Povezava"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Log out from all devices"
msgstr "Odjava iz vseh naprav"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "Sporočilo"

#. module: portal
#. odoo-python
#: code:addons/portal/models/mail_thread.py:0
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Multi company reports are not supported."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "My account"
msgstr "Moj račun"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Name"
msgstr "Naziv"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Name your key"
msgstr "Poimenujte svoj ključ"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New API Key"
msgstr "Nov ključ API-ja"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Next"
msgstr "Naprej"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "Opomba"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "Odoo logotip"

#. module: portal
#. odoo-python
#: code:addons/portal/models/res_users_apikeys_description.py:0
msgid "Only internal and portal users can create API keys"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "Nekaj je šlo narobe. Poskusite znova naložiti stran in se prijaviti."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "Partnerji"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password"
msgstr "Geslo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Phone"
msgstr "Telefon"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Please enter your password to confirm you own this account"
msgstr "Vnesite svoje geslo in potrdite, da ste lastnik tega računa."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Portal Access Management"
msgstr "Upravljanje dostopov portala"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "URL dostop do portala"

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "Nastavitev uporabnika portala"

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "Uporablja tehnologijo"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Previous"
msgstr "Nazaj"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Put my email and phone in a block list to make sure I'm never contacted "
"again"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_ir_qweb
msgid "Qweb"
msgstr "QWeb"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "Prejemniki"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "Povezan dokument"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "ID povezanega dokumenta"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "Povezan dokumentni model"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Revoke All Sessions"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Save"
msgstr "Shrani"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "Obseg"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "Iskanje"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "Varnost"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Security Control"
msgstr "Varnostni nadzor"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "Varnostni žeton"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr "V spodnji listi izberite, kateri kontakti bodo pripadali portalu."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Send"
msgstr "Pošlji"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
msgid "Send message"
msgstr "Pošlji sporočilo"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "Skupna raba dokumenta"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "Prikaz kot opcijsko dedovanje"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "Prijava"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.frontend_layout
msgid "Skip to Content"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Some required fields are empty."
msgstr "Nekatera obvezna polja so prazna."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "State / Province"
msgstr "Zvezna država / provinca"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email_state
msgid "Status"
msgstr "Status"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street"
msgstr "Ulica"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street 2"
msgstr "Ulica 2"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Thank You!"
msgstr "Hvala Vam!"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr "Priponka ne obstaja ali pa nimate pravic za dostop do nje."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" does not have a valid email."
msgstr "Stik \"%s\" nima veljavnega e-poštnega naslova."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" has the same email as an existing user"
msgstr "Stik \"%s\" ima enak e-poštni naslov kot obstoječi uporabnik"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key cannot be retrieved later and provides"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key will be deleted once this period has elapsed."
msgstr "Ključ bo izbrisan po preteku tega obdobja."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The new password and its confirmation must be identical."
msgstr "Novo geslo in njegova potrditev morata biti identična."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "Staro geslo ni pravilno, geslo ni bilo spremenjeno."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" already has the portal access."
msgstr "Partner \"%s\"že ima dostop do portala."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" has no portal access or is internal."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "This document does not exist."
msgstr "Ta dokument ne obstaja."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "To je predogled portala za stranke."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This text is included at the end of the email sent to new portal users."
msgstr ""

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr ""
"To besedilo je vključeno v e-sporočilo, poslano novim uporabnikom portala."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "Uporabnik"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "Uporabniki"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "VAT Number"
msgstr "DDV številka"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ok
msgid "Valid"
msgstr "Velja"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Valid Email Address"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "Prikaz"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "Sporočila iz spletne strani"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_category__website_message_ids
#: model:ir.model.fields,help:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "Kronologija komunikacij spletne strani"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "What's this key for?"
msgstr "Čemu je namenjen ta ključ?"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "Čarovnik"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Write a message..."
msgstr "Napiši sporočilo..."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/chatter/core/composer_patch.js:0
msgid "Write a message…"
msgstr "Napiši sporočilo..."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Write down your key"
msgstr "Zapišite si svoj ključ"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Wrong password."
msgstr "Napačno geslo."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "You cannot leave any password empty."
msgstr "Geslo ne more biti prazno"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "You must be"
msgstr "Morate biti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "You should enter \""
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr ""

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid ""
"Your account at {{ object.user_id.company_id.name or "
"object.partner_id.company_id.name }}"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "Vaš kontakt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Zip / Postal Code"
msgstr "Poštna številka"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "full access"
msgstr "Polni dostop"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "has invited you to access the following"
msgstr "vas je povabil/a k dostopu do sledečega"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "logged in"
msgstr "prijavljeni"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "to post a comment."
msgstr "za komentiranje."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "to your user account, it is very important to store it securely."
msgstr ""
