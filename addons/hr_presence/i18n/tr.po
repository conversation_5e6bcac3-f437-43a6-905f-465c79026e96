# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_presence
# 
# Translators:
# <PERSON>re <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>ü<PERSON>ün, 2025
# <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
msgid "%(name)s has been noted as %(state)s today"
msgstr ""

#. module: hr_presence
#: model:mail.template,body_html:hr_presence.mail_template_presence
msgid ""
"<div>\n"
"                    Dear <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"We hope this message finds you well. It has come to our attention that you are currently not present at work, and there is no record of a time off request from you. If this absence is due to an oversight on our part, we sincerely apologize for any confusion.\n"
"Please take the necessary steps to address this unplanned absence. Should you have any questions or need assistance, do not hesitate to reach out to your manager or the HR department at your earliest convenience.\n"
"Thank you for your prompt attention to this matter.\n"
"                    <br/>Best Regards,<br/><br/>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    Sayın <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"Umarız her şey yolundadır. Şu anda iş yerinde bulunmadığınızı ve sizden gelen bir izin talebi kaydı olmadığını fark ettik. Bu durum bizim tarafımızdan bir dikkatsizlikten kaynaklanıyorsa, karışıklık için içtenlikle özür dileriz.\n"
"Lütfen bu planlanmamış devamsızlığı ele almak için gerekli adımları atın. Herhangi bir sorunuz olması veya yardıma ihtiyaç duymanız halinde, en kısa sürede yöneticinize veya İK departmanına ulaşmaktan çekinmeyin.\n"
"Bu konu ile acilen ilgilendiğiniz için teşekkür ederiz.\n"
"                    <br/>Saygılarımızla,<br/><br/>\n"
"                </div>\n"
"            "

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__absent
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Absent"
msgstr "Mevcut Değil"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.action_hr_employee_presence_log
msgid "Add a log note"
msgstr ""

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_hr_employee_base
msgid "Basic Employee"
msgstr "Temel Personel"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__create_uid
msgid "Create Uid"
msgstr "Kullanıcı Kimliği Oluştur"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.action_hr_employee_presence_time_off
msgid "Create a Time Off"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__email_sent
msgid "Email Sent"
msgstr "E-posta gönderildi"

#. module: hr_presence
#: model:sms.template,name:hr_presence.sms_template_data_hr_presence
msgid "Employee: Presence Reminder"
msgstr "Personel: Hazır Bulunma Hatırlatması"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_cron_presence_control_ir_actions_server
msgid "HR Presence: cron"
msgstr "İK Varlığı: cron"

#. module: hr_presence
#: model:mail.template,name:hr_presence.mail_template_presence
msgid "HR: Employee Absence email"
msgstr "İK: Çalışan Devamsızlığı e-postası"

#. module: hr_presence
#: model:sms.template,body:hr_presence.sms_template_data_hr_presence
msgid ""
"Hi, we noticed you're not at work and no time-off was submitted. If this is "
"an oversight from us, we apologize. Please contact your manager or HR ASAP. "
"Thanks"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_company__hr_presence_last_compute_date
msgid "Hr Presence Last Compute Date"
msgstr "İk Hazır Bulunma Son İşlem Tarihi"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__hr_presence_state_display
msgid "Hr Presence State Display"
msgstr "İk Hazır Bulunma Durumu Ekranı"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__ip
msgid "IP Address"
msgstr "IP Adresi"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__ip_connected
msgid "Ip Connected"
msgstr "IP Bağlantılı"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__manually_set_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__manually_set_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__manually_set_presence
msgid "Manually Set Presence"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__manually_set_present
msgid "Manually Set Present"
msgstr "Manuel olarak Ayarla"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__out_of_working_hour
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__out_of_working_hour
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__out_of_working_hour
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Out of Working Hours"
msgstr ""

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_tree
msgid "Presence"
msgstr "Hazır Bulunma"

#. module: hr_presence
#. odoo-javascript
#: code:addons/hr_presence/static/src/search/hr_presence_cog_menu/hr_presence_cog_menu.xml:0
msgid "Presence Control"
msgstr ""

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Presence/Absence"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__present
msgid "Present"
msgstr "Mevcut"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
msgid "Send SMS"
msgstr "SMS Gönder"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.action_hr_employee_presence_sms
msgid "Send a SMS"
msgstr ""

#. module: hr_presence
#: model:mail.template,description:hr_presence.mail_template_presence
msgid ""
"Sent manually in presence module when an employee wasn't working despite not"
" being off"
msgstr ""
"Bir çalışan izinli olmamasına rağmen çalışmadığında varlık modülünde manuel "
"olarak gönderilir"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.action_hr_employee_presence_absent
msgid "Set Absent"
msgstr ""

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.action_hr_employee_presence_present
msgid "Set Present"
msgstr ""

#. module: hr_presence
#: model:mail.template,subject:hr_presence.mail_template_presence
msgid "Unexpected Absence"
msgstr "Beklenmedik Devamsızlık"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
msgid "Unplanned Absence"
msgstr ""

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_users_log
msgid "Users Log"
msgstr "Kullanıcı Kayıtları"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
msgid ""
"We hope this message finds you well. It has come to our attention that you are currently not present at work, and there is no record of a time off request from you. If this absence is due to an oversight on our part, we sincerely apologize for any confusion.\n"
"Please take the necessary steps to address this unplanned absence. Should you have any questions or need assistance, do not hesitate to reach out to your manager or the HR department at your earliest convenience.\n"
"Thank you for your prompt attention to this matter."
msgstr ""
"Umarız her şey yolundadır. Şu anda iş yerinde bulunmadığınızı ve sizden gelen bir izin talebi kaydı olmadığını fark ettik. Bu durum bizim tarafımızdan bir dikkatsizlikten kaynaklanıyorsa, karışıklık için içtenlikle özür dileriz.\n"
"Lütfen bu planlanmamış devamsızlığı ele almak için gerekli adımları atın. Herhangi bir sorunuz olması veya yardıma ihtiyaç duymanız halinde, en kısa sürede yöneticinize veya İK  departmanına ulaşmaktan çekinmeyin.\n"
"Bu konu ile acilen ilgilendiğiniz için teşekkür ederiz."

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
msgid "You don't have the right to do this. Please contact an Administrator."
msgstr "Bunu yapma hakkınız yok. Lütfen bir Yöneticiye başvurun."

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_ir_websocket
msgid "websocket message handling"
msgstr "websocket mesaj işleme"
