# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_recaptcha
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Generate reCAPTCHA v3 keys"
msgstr "<i class=\"oi oi-arrow-right\"/> Gerar chaves reCAPTCHA v3"

#. module: google_recaptcha
#: model:ir.model.fields,help:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid ""
"By default, should be one of 0.1, 0.3, 0.7, 0.9.\n"
"1.0 is very likely a good interaction, 0.0 is very likely a bot"
msgstr ""
"Por padrão, deve ser 0,1, 0,3, 0,7 ou 0,9.\n"
"1,0 muito provavelmente é uma boa interação, 0,0 muito provavelmente é um bot."

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_ir_http
msgid "HTTP Routing"
msgstr "Roteamento HTTP"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid "Minimum score"
msgstr "Pontuação mínima"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "No recaptcha site key set."
msgstr "Nenhuma chave de site recaptcha definida."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Privacy Policy"
msgstr "Política de privacidade"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid ""
"Protect your forms from spam and abuse. If no keys are provided, no checks "
"will be done."
msgstr ""
"Proteja seus formulários contra spam e abuso. Se nenhuma chave for "
"fornecida, nenhuma verificação será feita."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Protected by reCAPTCHA,"
msgstr "Protegido por reCAPTCHA,"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_private_key
msgid "Secret Key"
msgstr "Chave secreta"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_public_key
msgid "Site Key"
msgstr "Chave do site"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Terms of Service"
msgstr "Termos de serviço"

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha private key is invalid."
msgstr "A chave privada do reCaptcha é inválida."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha token is invalid."
msgstr "O token reCaptcha é inválido."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "The recaptcha site key is invalid."
msgstr "A chave do site recaptcha é inválida."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The request is invalid or malformed."
msgstr "A solicitação é inválida ou malformada."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "Your request has timed out, please retry."
msgstr "A sua solicitação atingiu o tempo limite. Tente novamente."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "apply."
msgstr "se aplicam."
