# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_pdf_quote_builder
# 
# Translators:
# <PERSON><PERSON>, 2024
# krnkris, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Eiler Attila, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: gezza <<EMAIL>>, 2025\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,print_report_name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Ajánlat - %s' % (object.name)) or "
"'Rendelés - %s' % (object.name)"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span class=\"pe-2\">Templates:</span>"
msgstr "<span class=\"pe-2\">Sablonok:</span>"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span>Document type:</span>"
msgstr "<span>Dokumentum típus:</span>"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in product documents can't be linked to a quotation"
" document."
msgstr ""
"Termék dokumentumokon használt űrlapmező nem kapcsolható ajánlati "
"dokumentumhoz."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in quotation documents can't be linked to a product"
" document."
msgstr ""
"Ajánlati dokumentumokon használt űrlapmező nem kapcsolható termék "
"dokumentumhoz."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__access_token
msgid "Access Token"
msgstr "Hozzáférési token"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__active
msgid "Active"
msgstr "Aktív"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "All"
msgstr "Összes"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/controllers/quotation_document.py:0
msgid "All files uploaded"
msgstr "Minden fájl feltöltve"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""
"Lehetővé teszi dokumentumok megosztását a vevőkkel az értékesítés során.\n"
"Ajánlatadás során: a dokumentum elküldésre kerül és elérhetővé válik a vevő számára is.\n"
"pld: hasznos lehet terméket bemutató fájlok megosztására.\n"
"Rendelés megerősítésekor: a dokumentum elküldésre kerül és elérhetővé válik a vevő számára is.\n"
"pld: hasznos lehet kézikönyvek vagy webshopban vásárolt digitális tartalmak megosztására. \n"
"Ajánlaton belül: a dokumentum az ajánlati pdf fejléce és az ajánlati táblázat közé kerül beszúrásra. "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Archived"
msgstr "Archivált"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Attached To"
msgstr "Csatolva a következőhöz"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__local_url
msgid "Attachment URL"
msgstr "Melléklet URL"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__available_product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__available_product_document_ids
msgid "Available Product Documents"
msgstr "Elérhető termék dokumentumok"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__checksum
msgid "Checksum/SHA1"
msgstr "Ellenőrző összeg/SHA1"

#. module: sale_pdf_quote_builder
#. odoo-javascript
#: code:addons/sale_pdf_quote_builder/static/src/js/custom_content_kanban_like_widget/custom_field_card/custom_field_card.js:0
msgid "Click to write content for the PDF quote..."
msgstr "Kattintson PDF ajánlat tartalmának megírásához..."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__company_id
msgid "Company"
msgstr "Vállalat"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Configure dynamic fields"
msgstr "Dinamikus mezők konfigurálása"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
msgid "Configure the path to fill the form fields."
msgstr "Űrlapmezők kitöltéséhez használt útvonal beállítása."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Creation"
msgstr "Létrehozás"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "Customizable"
msgstr "Testreszabható"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__customizable_pdf_form_fields
msgid "Customizable PDF Form Fields"
msgstr "Testreszabható PDF űrlap mezők"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__db_datas
msgid "Database Data"
msgstr "Adatbázis adat"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Delete"
msgstr "Törlés"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__description
msgid "Description"
msgstr "Leírás"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__display_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__document_type
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__document_type
msgid "Document Type"
msgstr "Dokumentum típus"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Document type"
msgstr "Dokumentum típus"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Download"
msgstr "Letöltés"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Download examples"
msgstr "Példák letöltése"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Edit"
msgstr "Szerkesztés"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__datas
msgid "File Content (base64)"
msgstr "Fájltartalom (base64)"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__raw
msgid "File Content (raw)"
msgstr "Fájltartalom (nyers)"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__file_size
msgid "File Size"
msgstr "Fájlméret"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__footer
msgid "Footer"
msgstr "Lábléc"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "Form Field Name"
msgstr "Űrlap mező neve"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Form Fields"
msgstr "Űrlap mezők"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__form_field_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__form_field_ids
msgid "Form Fields Included"
msgstr "Használt űrlapmezők"

#. module: sale_pdf_quote_builder
#: model:ir.model.constraint,message:sale_pdf_quote_builder.constraint_sale_pdf_form_field_unique_name_per_doc_type
msgid "Form field name must be unique for a given document type."
msgstr "Űrlapmező nevének egyedinek kell lennie egy dokumentum típusban."

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_pdf_form_field
msgid "Form fields of inside quotation documents."
msgstr "Ajánlati dokumentum űrlapmezői."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Group By"
msgstr "Csoportosítás"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__header
msgid "Header"
msgstr "Fejléc"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__quotation_document
msgid "Header/Footer"
msgstr "Fejléc/Lábléc"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__quotation_document_ids
msgid "Headers and footers"
msgstr "Fejlécek és láblécek"

#. module: sale_pdf_quote_builder
#: model:ir.actions.act_window,name:sale_pdf_quote_builder.quotation_document_action
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__quotation_document_ids
#: model:ir.ui.menu,name:sale_pdf_quote_builder.sale_menu_quotation_document_action
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.res_config_settings_view_form
msgid "Headers/Footers"
msgstr "Fejlécek/Láblécek"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "History"
msgstr "Előzmények"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__id
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__id
msgid "ID"
msgstr "ID"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__active
msgid ""
"If unchecked, it will allow you to hide the header or footer without "
"removing it."
msgstr ""
"Ha be van jelölve, akkor elrejthető a fejléc vagy a lábléc annak "
"eltávolítása nélkül."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_height
msgid "Image Height"
msgstr "Kép magassága"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_src
msgid "Image Src"
msgstr "Kép forrása"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_width
msgid "Image Width"
msgstr "Kép szélessége"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__index_content
msgid "Indexed Content"
msgstr "Indexált tartalom"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__product_document__attached_on_sale__inside
msgid "Inside quote pdf"
msgstr "Ajánlati pdf-en belül"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. A form field name in a header or a "
"footer can not start with \"sol_id_\"."
msgstr ""
"Érvénytelen űrlapmező név: %(field_name)s. Fejlécen vagy láblécen szereplő "
"űrlapmező neve nem kezdődhet ezzel: \"sol_id_\"."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. It should only contain "
"alphanumerics, hyphens or underscores."
msgstr ""
"Érvénytelen űrlapmező név: %(field_name)s. Csak alfanumerikus karakterek, "
"kötőjelek vagy alsóvonások engedélyezettek."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid path %(path)s. It should only contain alphanumerics, hyphens, "
"underscores or points."
msgstr ""
"Érvénytelen útvonal: %(path)s. Csak alfanumerikus karakterek, kötőjelek vagy"
" pontok engedélyezettek."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__is_pdf_quote_builder_available
msgid "Is Pdf Quote Builder Available"
msgstr "Elérhető-e a PDF ajánlatkészítő"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__public
msgid "Is public document"
msgstr "Nyilvános dokumentum"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/utils.py:0
msgid ""
"It seems that we're not able to process this pdf inside a quotation. It is "
"either encrypted, or encoded in a format we do not support."
msgstr ""
"Nem sikerült feldolgozni a pdf-et az ajánlatban mert az vagy titkosított "
"vagy nem támogatott formátumban kódolt."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__key
msgid "Key"
msgstr "Kulcs"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Mark fields as safe to fill in the quote."
msgstr "Mezők biztonságosnak jelölése az ajánlat kitöltéséhez."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__mimetype
msgid "Mime Type"
msgstr "MIME típus"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__name
msgid "Name"
msgstr "Név"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "No"
msgstr "Nem"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "Only PDF documents can be attached inside a quote."
msgstr "Csak PDF dokumentumokat lehet csatolni az ajánlathoz."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Only PDF documents can be used as header or footer."
msgstr "Csak PDF dokumentumokat lehet használni fejlécként vagy láblécként."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Eredeti (nem átméretezett, nem optimalizált) mellékletek"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid "Path"
msgstr "Útvonal"

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid ""
"Personalize your quotes with catchy header and footer pages\n"
"                <br>\n"
"                to boost your sales."
msgstr ""
"Tegyen egyénivé az ajánlatait megkapó fejléc és lábléc oldalakkal\n"
"                <br>\n"
"                az értékesítése növeléséhez."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "Please use only relational fields until the last value of your path."
msgstr "Kérjük, csak relációs mezőket használjon az útvonal utolsó értékéig."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
msgid "Product"
msgstr "Termék"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_product_document
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__product_document
msgid "Product Document"
msgstr "Termék dokumentum"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__product_document_ids
msgid "Product Documents"
msgstr "Termék dokumentumok"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid ""
"Provide header pages and footer pages to compose an attractive quotation with more information\n"
"                        about your company, your products and you services. <br/>\n"
"                        The pdf of your quotes will be built by putting together header pages, product descriptions,\n"
"                        details of the quote and then the footer pages. <br/>\n"
"                        If empty, it will use those define in the company settings. <br/>"
msgstr ""
"Használjon fejléc és lábléc oldalakat vonzó ajánlatok elkészítéséhez bővített infomációkkal\n"
"                        a vállalatról, termékekről és szolgáltatásokról. <br/>\n"
"                        Az ajánlati PDF összeállítása során egyesítésre kerülnek a fejléc oldalak, termék leírások,\n"
"                        az ajánlat részletei és végül a lábléc oldalak. <br/>\n"
"                        Ha üres, akkor a vállalati beállításoknál megadott kerül használatra. <br/>"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid "Quotation / Order"
msgstr "Ajánlat / Rendelés"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Document"
msgstr "Ajánlat dokumentum"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__quotation_document_ids
msgid "Quotation Documents"
msgstr "Ajánlati dokumentumok"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Template"
msgstr "Ajánlati sablon"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__quotation_template_ids
msgid "Quotation Templates"
msgstr "Ajánlati sablonok"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_quotation_document
msgid "Quotation's Headers & Footers"
msgstr "Ajánlati fejlécek és láblécek"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_form_inherit_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Quote Builder"
msgstr "Ajánlat építő"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__ir_attachment_id
msgid "Related attachment"
msgstr "Kapcsolódó melléklet"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_ir_actions_report
msgid "Report Action"
msgstr "Kimutatás művelet"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_field
msgid "Resource Field"
msgstr "Erőforrásmező"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_id
msgid "Resource ID"
msgstr "Erőforrás azonosító"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_model
msgid "Resource Model"
msgstr "Erőforrás modell"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_name
msgid "Resource Name"
msgstr "Erőforrás neve"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr "Értékesítés : Megtekinthető"

#. module: sale_pdf_quote_builder
#: model:ir.actions.server,name:sale_pdf_quote_builder.cron_post_upgrade_assign_missing_form_fields_ir_actions_server
msgid "Sale Pdf Quote Builder: assign form fields to documents post upgrade"
msgstr ""
"Értékesítési PDF ajánlat építő: rendeljen űrlapmezőket dokumentumokhoz "
"frissítés után"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order
msgid "Sales Order"
msgstr "Vevői rendelés"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "Vevői rendelés sor"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__sequence
msgid "Sequence"
msgstr "Sorszám"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__store_fname
msgid "Stored Filename"
msgstr "Tárolt fájlnév"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "The field %(field_name)s doesn't exist on model %(model_name)s"
msgstr "%(field_name)s mező nem létezik ebben a modellben: %(model_name)s"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "The form field name as written in the PDF."
msgstr "Az űrlapmező megjelenített neve a PDF dokumentumon."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid ""
"The path to follow to dynamically fill the form field. \n"
"Leave empty to be able to customized it in the quotation form."
msgstr ""
"Az űrlapmezők dinamikus kitöltéséhez vezető útvonal. \n"
"Hagyja üresen az ajánlati űrlapon történő testreszabáshoz."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
msgid ""
"The product documents for this order line that will be merged in the PDF "
"quote."
msgstr ""
"A rendelési sor termék dokumentumai, melyek összefűzésre kerülnek a PDF "
"ajánlatban."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__theme_template_id
msgid "Theme Template"
msgstr "Téma sablon"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "This document"
msgstr "Ez a dokumentum"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__type
msgid "Type"
msgstr "Típus"

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid "Upload quotation headers and footers"
msgstr "Ajánlati fejlécek és láblécek feltöltése"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__url
msgid "Url"
msgstr "Url"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Visible to all"
msgstr "Látható mindenkinek"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__voice_ids
msgid "Voice"
msgstr "Hang"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__website_id
msgid "Website"
msgstr "Weboldal"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "When attached inside a quote, the document must be a file, not a URL."
msgstr ""
"Ha ajánlaton belül kerül csatolásra, akkor a dokumentum csak fájl lehet, URL"
" nem lehetséges."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "Yes"
msgstr "Igen"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Feltölthet egy fájlt a számítógépéről, vagy másolhat/beilleszthet egy, a "
"fájlra mutató internetes hivatkozást."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "on"
msgstr "ezen:"
