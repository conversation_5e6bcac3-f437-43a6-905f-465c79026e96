# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_pdf_quote_builder
# 
# Translators:
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,print_report_name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Presupuesto - %s' % (object.name)) "
"or 'Pedido - %s' % (object.name)"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span class=\"pe-2\">Templates:</span>"
msgstr "<span class=\"pe-2\">Plantillas:</span>"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span>Document type:</span>"
msgstr "<span>Tipo de documento:</span>"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in product documents can't be linked to a quotation"
" document."
msgstr ""
"Un campo de formulario configurado como usado en documentos de producto no "
"puede estar vinculado a un documento de presupuesto."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in quotation documents can't be linked to a product"
" document."
msgstr ""
"Un campo de formulario configurado como usado en documentos de presupuesto "
"no puede estar vinculado a un documento de producto."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__active
msgid "Active"
msgstr "Activo"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "All"
msgstr "Todos"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/controllers/quotation_document.py:0
msgid "All files uploaded"
msgstr "Todos los archivos subidos"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""
"Le permite compartir el documento con sus clientes dentro de una venta.\n"
"Déjelo vacío si no desea compartir este documento con su cliente.\n"
"En el presupuesto: los clientes recibirán el documento y podrán visualizarlo en cualquier momento.\n"
"Esta opción puede ser útil, por ejemplo, para compartir archivos de descripción del producto.\n"
"En la confirmación de la orden: los clientes recibirán el documento y podrán visualizarlo.\n"
"Esta opción puede ser útil, por ejemplo, para compartir el manual de usuario o algún contenido digital que se haya adquirido mediante comercio electrónico. \n"
"Dentro del presupuesto: el documento se incluirá en el PDF del presupuesto\n"
"y del pedido de venta entre el encabezado y la tabla de presupuesto."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Archived"
msgstr "Archivado"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Attached To"
msgstr "Adjuntado a"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__local_url
msgid "Attachment URL"
msgstr "URL del archivo adjunto"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__available_product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__available_product_document_ids
msgid "Available Product Documents"
msgstr "Documentos de producto disponibles"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__checksum
msgid "Checksum/SHA1"
msgstr "Suma de verificación/SHA1"

#. module: sale_pdf_quote_builder
#. odoo-javascript
#: code:addons/sale_pdf_quote_builder/static/src/js/custom_content_kanban_like_widget/custom_field_card/custom_field_card.js:0
msgid "Click to write content for the PDF quote..."
msgstr "Haga clic para escribir el contenido del presupuesto en PDF..."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__company_id
msgid "Company"
msgstr "Compañía"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Configure dynamic fields"
msgstr "Configurar campos dinámicos"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
msgid "Configure the path to fill the form fields."
msgstr "Configure la ruta para completar los campos del formulario."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_date
msgid "Created on"
msgstr "Creado el"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Creation"
msgstr "Creación"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "Customizable"
msgstr "Personalizable"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__customizable_pdf_form_fields
msgid "Customizable PDF Form Fields"
msgstr "Campos de formulario en PDF personalizables"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__db_datas
msgid "Database Data"
msgstr "Datos de la base de datos"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__description
msgid "Description"
msgstr "Descripción"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__display_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__document_type
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__document_type
msgid "Document Type"
msgstr "Tipo de documento"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Document type"
msgstr "Tipo de documento"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Download"
msgstr "Descargar"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Download examples"
msgstr "Descargar ejemplos"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Edit"
msgstr "Editar"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__datas
msgid "File Content (base64)"
msgstr "Contenido del archivo (base64)"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__raw
msgid "File Content (raw)"
msgstr "Contenido del archivo (raw)"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__file_size
msgid "File Size"
msgstr "Tamaño del archivo"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__footer
msgid "Footer"
msgstr "Pie de página"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "Form Field Name"
msgstr "Nombre del campo del formulario"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Form Fields"
msgstr "Campos del formulario"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__form_field_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__form_field_ids
msgid "Form Fields Included"
msgstr "Campos de formulario incluidos"

#. module: sale_pdf_quote_builder
#: model:ir.model.constraint,message:sale_pdf_quote_builder.constraint_sale_pdf_form_field_unique_name_per_doc_type
msgid "Form field name must be unique for a given document type."
msgstr ""
"El nombre del campo de formulario debe ser único para un tipo de documento "
"específico."

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_pdf_form_field
msgid "Form fields of inside quotation documents."
msgstr "Campos de formulario dentro de los documentos de presupuesto."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Group By"
msgstr "Agrupar por"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__header
msgid "Header"
msgstr "Encabezado"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__quotation_document
msgid "Header/Footer"
msgstr "Encabezado y pie de página"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__quotation_document_ids
msgid "Headers and footers"
msgstr "Encabezados y pies de página"

#. module: sale_pdf_quote_builder
#: model:ir.actions.act_window,name:sale_pdf_quote_builder.quotation_document_action
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__quotation_document_ids
#: model:ir.ui.menu,name:sale_pdf_quote_builder.sale_menu_quotation_document_action
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.res_config_settings_view_form
msgid "Headers/Footers"
msgstr "Encabezados/pies de página"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "History"
msgstr "Historial"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__id
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__id
msgid "ID"
msgstr "ID"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__active
msgid ""
"If unchecked, it will allow you to hide the header or footer without "
"removing it."
msgstr ""
"Si no se encuentra seleccionado, permite ocultar el encabezado o pie de "
"página sin eliminarlo."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_height
msgid "Image Height"
msgstr "Altura de imagen"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_src
msgid "Image Src"
msgstr "Fuente de la imagen"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_width
msgid "Image Width"
msgstr "Ancho de la imagen"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__index_content
msgid "Indexed Content"
msgstr "Contenido indexado"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__product_document__attached_on_sale__inside
msgid "Inside quote pdf"
msgstr "Dentro de PDF de presupuesto"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. A form field name in a header or a "
"footer can not start with \"sol_id_\"."
msgstr ""
"El nombre del campo del formulario %(field_name)s no es válido, estos "
"nombres no pueden iniciar con “sol_id_” si están en el encabezado o el pie "
"de página."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. It should only contain "
"alphanumerics, hyphens or underscores."
msgstr ""
"El nombre del campo del formulario %(field_name)s no es válido, solo debe "
"incluir caracteres alfanuméricos, guiones y guiones bajos."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid path %(path)s. It should only contain alphanumerics, hyphens, "
"underscores or points."
msgstr ""
"La ruta %(path)s no es válida, solo debe incluir caracteres alfanuméricos, "
"guiones, guiones bajos y puntos."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__is_pdf_quote_builder_available
msgid "Is Pdf Quote Builder Available"
msgstr "¿El creador de presupuestos en PDF está disponible?"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__public
msgid "Is public document"
msgstr "Es un documento publico"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/utils.py:0
msgid ""
"It seems that we're not able to process this pdf inside a quotation. It is "
"either encrypted, or encoded in a format we do not support."
msgstr ""
"No fue posible procesar este PDF dentro de un presupuesto. Está encriptado o"
" codificado en un formato incompatible."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__key
msgid "Key"
msgstr "Clave"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Mark fields as safe to fill in the quote."
msgstr "Marque los campos como seguros para completarlos en el presupuesto."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__mimetype
msgid "Mime Type"
msgstr "Tipo de MIME"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__name
msgid "Name"
msgstr "Nombre"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "No"
msgstr "No"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "Only PDF documents can be attached inside a quote."
msgstr "Solo puede adjuntar documentos PDF dentro de un presupuesto."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Only PDF documents can be used as header or footer."
msgstr "Solo puede utilizar documentos PDF como encabezado o pie de página."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Archivo adjunto original (no optimizado, sin tamaño ajustado)"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid "Path"
msgstr "Ruta"

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid ""
"Personalize your quotes with catchy header and footer pages\n"
"                <br>\n"
"                to boost your sales."
msgstr ""
"Personalice sus presupuestos con encabezados y pies de página llamativos\n"
"                <br>\n"
"                para aumentar sus ventas."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "Please use only relational fields until the last value of your path."
msgstr "Solo use campos relacionales hasta el último valor de su ruta."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
msgid "Product"
msgstr "Producto"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_product_document
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__product_document
msgid "Product Document"
msgstr "Documento del producto"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__product_document_ids
msgid "Product Documents"
msgstr "Documentos del producto"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid ""
"Provide header pages and footer pages to compose an attractive quotation with more information\n"
"                        about your company, your products and you services. <br/>\n"
"                        The pdf of your quotes will be built by putting together header pages, product descriptions,\n"
"                        details of the quote and then the footer pages. <br/>\n"
"                        If empty, it will use those define in the company settings. <br/>"
msgstr ""
"Proporcione páginas de encabezado y páginas de pie de página para crear un presupuesto atractivo\n"
"                            con más información sobre su empresa, sus productos y sus servicios.<br/>\n"
"                            El PDF de sus presupuestos se creará juntando las páginas de encabezado,\n"
"                            las descripciones de los productos, los detalles del presupuesto y luego los pies de página.<br/>\n"
"                            Si está vacío, utilizará los definidos en la configuración de la empresa. <br/>"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid "Quotation / Order"
msgstr "Presupuesto / Pedido"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Document"
msgstr "Documento del presupuesto"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__quotation_document_ids
msgid "Quotation Documents"
msgstr "Documentos del presupuesto"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Template"
msgstr "Plantilla de presupuesto"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__quotation_template_ids
msgid "Quotation Templates"
msgstr "Plantillas de presupuesto"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_quotation_document
msgid "Quotation's Headers & Footers"
msgstr "Encabezados y pies de página del presupuesto"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_form_inherit_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Quote Builder"
msgstr "Creador de presupuestos"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__ir_attachment_id
msgid "Related attachment"
msgstr "Adjunto relacionado"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_ir_actions_report
msgid "Report Action"
msgstr "Acción de informe"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_field
msgid "Resource Field"
msgstr "Campo del recurso"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_id
msgid "Resource ID"
msgstr "ID del recurso"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_model
msgid "Resource Model"
msgstr "Modelo del recurso"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_name
msgid "Resource Name"
msgstr "Nombre del recurso"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr "Ventas: visible en"

#. module: sale_pdf_quote_builder
#: model:ir.actions.server,name:sale_pdf_quote_builder.cron_post_upgrade_assign_missing_form_fields_ir_actions_server
msgid "Sale Pdf Quote Builder: assign form fields to documents post upgrade"
msgstr ""
"Creador de presupuestos en PDF de ventas: asignar campos de formulario a "
"documentos después de la actualización"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order
msgid "Sales Order"
msgstr "Pedido de venta"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de pedido de venta"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__store_fname
msgid "Stored Filename"
msgstr "Nombre del archivo almacenado"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "The field %(field_name)s doesn't exist on model %(model_name)s"
msgstr "El campo %(field_name)s no existe en el modelo %(model_name)s"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "The form field name as written in the PDF."
msgstr "El nombre del campo del formulario tal como está escrito en el PDF."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid ""
"The path to follow to dynamically fill the form field. \n"
"Leave empty to be able to customized it in the quotation form."
msgstr ""
"La ruta a seguir para rellenar dinámicamente el campo del formulario. \n"
"Déjela vacía para personalizarlo en el formulario de presupuesto."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
msgid ""
"The product documents for this order line that will be merged in the PDF "
"quote."
msgstr ""
"Los documentos de producto para esta línea del pedido que se fusionarán en "
"el presupuesto en PDF."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__theme_template_id
msgid "Theme Template"
msgstr "Plantilla del tema"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "This document"
msgstr "Este documento"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__type
msgid "Type"
msgstr "Tipo"

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid "Upload quotation headers and footers"
msgstr "Subir encabezados y pies de página del presupuesto"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__url
msgid "Url"
msgstr "URL"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Visible to all"
msgstr "Visible para todos"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__voice_ids
msgid "Voice"
msgstr "Voz"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__website_id
msgid "Website"
msgstr "Sitio web"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "When attached inside a quote, the document must be a file, not a URL."
msgstr ""
"Cuando se adjunta dentro de un presupuesto, el documento debe ser un "
"archivo, no una URL."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "Yes"
msgstr "Sí"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Puede subir un archivo de su ordenador o copiar/pegar un enlace de internet "
"a su archivo."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "on"
msgstr "el"
